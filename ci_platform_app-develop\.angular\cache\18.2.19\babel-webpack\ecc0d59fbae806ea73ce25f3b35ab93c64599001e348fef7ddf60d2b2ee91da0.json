{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Arabic (Saudi Arabia) [ar-sa]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/xsoh\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '١',\n      2: '٢',\n      3: '٣',\n      4: '٤',\n      5: '٥',\n      6: '٦',\n      7: '٧',\n      8: '٨',\n      9: '٩',\n      0: '٠'\n    },\n    numberMap = {\n      '١': '1',\n      '٢': '2',\n      '٣': '3',\n      '٤': '4',\n      '٥': '5',\n      '٦': '6',\n      '٧': '7',\n      '٨': '8',\n      '٩': '9',\n      '٠': '0'\n    };\n  var arSa = moment.defineLocale('ar-sa', {\n    months: 'يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر'.split('_'),\n    monthsShort: 'يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر'.split('_'),\n    weekdays: 'الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n    weekdaysShort: 'أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت'.split('_'),\n    weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ص|م/,\n    isPM: function (input) {\n      return 'م' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ص';\n      } else {\n        return 'م';\n      }\n    },\n    calendar: {\n      sameDay: '[اليوم على الساعة] LT',\n      nextDay: '[غدا على الساعة] LT',\n      nextWeek: 'dddd [على الساعة] LT',\n      lastDay: '[أمس على الساعة] LT',\n      lastWeek: 'dddd [على الساعة] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'في %s',\n      past: 'منذ %s',\n      s: 'ثوان',\n      ss: '%d ثانية',\n      m: 'دقيقة',\n      mm: '%d دقائق',\n      h: 'ساعة',\n      hh: '%d ساعات',\n      d: 'يوم',\n      dd: '%d أيام',\n      M: 'شهر',\n      MM: '%d أشهر',\n      y: 'سنة',\n      yy: '%d سنوات'\n    },\n    preparse: function (string) {\n      return string.replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {\n        return numberMap[match];\n      }).replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      }).replace(/,/g, '،');\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return arSa;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "arSa", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "match", "postformat", "week", "dow", "doy"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/moment/locale/ar-sa.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Arabic (Saudi Arabia) [ar-sa]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/xsoh\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '١',\n            2: '٢',\n            3: '٣',\n            4: '٤',\n            5: '٥',\n            6: '٦',\n            7: '٧',\n            8: '٨',\n            9: '٩',\n            0: '٠',\n        },\n        numberMap = {\n            '١': '1',\n            '٢': '2',\n            '٣': '3',\n            '٤': '4',\n            '٥': '5',\n            '٦': '6',\n            '٧': '7',\n            '٨': '8',\n            '٩': '9',\n            '٠': '0',\n        };\n\n    var arSa = moment.defineLocale('ar-sa', {\n        months: 'يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر'.split(\n            '_'\n        ),\n        monthsShort:\n            'يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر'.split(\n                '_'\n            ),\n        weekdays: 'الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n        weekdaysShort: 'أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت'.split('_'),\n        weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /ص|م/,\n        isPM: function (input) {\n            return 'م' === input;\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'ص';\n            } else {\n                return 'م';\n            }\n        },\n        calendar: {\n            sameDay: '[اليوم على الساعة] LT',\n            nextDay: '[غدا على الساعة] LT',\n            nextWeek: 'dddd [على الساعة] LT',\n            lastDay: '[أمس على الساعة] LT',\n            lastWeek: 'dddd [على الساعة] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'في %s',\n            past: 'منذ %s',\n            s: 'ثوان',\n            ss: '%d ثانية',\n            m: 'دقيقة',\n            mm: '%d دقائق',\n            h: 'ساعة',\n            hh: '%d ساعات',\n            d: 'يوم',\n            dd: '%d أيام',\n            M: 'شهر',\n            MM: '%d أشهر',\n            y: 'سنة',\n            yy: '%d سنوات',\n        },\n        preparse: function (string) {\n            return string\n                .replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {\n                    return numberMap[match];\n                })\n                .replace(/،/g, ',');\n        },\n        postformat: function (string) {\n            return string\n                .replace(/\\d/g, function (match) {\n                    return symbolMap[match];\n                })\n                .replace(/,/g, '،');\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 6, // The week that contains Jan 6th is the first week of the year.\n        },\n    });\n\n    return arSa;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;EAEL,IAAIC,IAAI,GAAGH,MAAM,CAACI,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,4EAA4E,CAACC,KAAK,CACtF,GACJ,CAAC;IACDC,WAAW,EACP,4EAA4E,CAACD,KAAK,CAC9E,GACJ,CAAC;IACLE,QAAQ,EAAE,qDAAqD,CAACF,KAAK,CAAC,GAAG,CAAC;IAC1EG,aAAa,EAAE,uCAAuC,CAACH,KAAK,CAAC,GAAG,CAAC;IACjEI,WAAW,EAAE,eAAe,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvCK,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,KAAK;IACpBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,GAAG,KAAKA,KAAK;IACxB,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,GAAG;MACd,CAAC,MAAM;QACH,OAAO,GAAG;MACd;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,sBAAsB;MAChCC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,sBAAsB;MAChCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CACRC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACvC,OAAOjD,SAAS,CAACiD,KAAK,CAAC;MAC3B,CAAC,CAAC,CACDD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC3B,CAAC;IACDE,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CACRC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC7B,OAAOlD,SAAS,CAACkD,KAAK,CAAC;MAC3B,CAAC,CAAC,CACDD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC3B,CAAC;IACDG,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOpD,IAAI;AAEf,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}