{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ChangeDetectionStrategy, Directive, Input, Output, HostListener, HostBinding, NgModule } from '@angular/core';\nimport { filter } from 'rxjs/operators';\nimport * as i1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport { isBs3 } from 'ngx-bootstrap/utils';\nimport * as i2 from '@angular/animations';\nimport { style, animate } from '@angular/animations';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { PositioningService } from 'ngx-bootstrap/positioning';\n\n/** Default dropdown configuration */\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  dropdown: a0\n});\nclass BsDropdownConfig {\n  constructor() {\n    /** default dropdown auto closing behavior */\n    this.autoClose = true;\n    /** default dropdown auto closing behavior */\n    this.insideClick = false;\n    /** turn on/off animation */\n    this.isAnimated = false;\n    /** value true of stopOnClickPropagation allows event stopPropagation*/\n    this.stopOnClickPropagation = false;\n  }\n}\nBsDropdownConfig.ɵfac = function BsDropdownConfig_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || BsDropdownConfig)();\n};\nBsDropdownConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BsDropdownConfig,\n  factory: BsDropdownConfig.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass BsDropdownState {\n  constructor() {\n    this.direction = 'down';\n    this.autoClose = true;\n    this.insideClick = false;\n    this.isAnimated = false;\n    this.stopOnClickPropagation = false;\n    this.isOpenChange = new EventEmitter();\n    this.isDisabledChange = new EventEmitter();\n    this.toggleClick = new EventEmitter();\n    this.counts = 0;\n    this.dropdownMenu = new Promise(resolve => {\n      this.resolveDropdownMenu = resolve;\n    });\n  }\n}\nBsDropdownState.ɵfac = function BsDropdownState_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || BsDropdownState)();\n};\nBsDropdownState.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BsDropdownState,\n  factory: BsDropdownState.ɵfac,\n  providedIn: 'platform'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownState, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nconst DROPDOWN_ANIMATION_TIMING = '220ms cubic-bezier(0, 0, 0.2, 1)';\nconst dropdownAnimation = [style({\n  height: 0,\n  overflow: 'hidden'\n}), animate(DROPDOWN_ANIMATION_TIMING, style({\n  height: '*',\n  overflow: 'hidden'\n}))];\n\n// todo: revert ngClass to [class] when false positive angular-cli issue is fixed\n//          [class.dropdown]=\"direction === 'down'\"-->\nclass BsDropdownContainerComponent {\n  constructor(_state, cd, _renderer, _element, _builder) {\n    this._state = _state;\n    this.cd = cd;\n    this._renderer = _renderer;\n    this._element = _element;\n    this.isOpen = false;\n    this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n    this._subscription = _state.isOpenChange.subscribe(value => {\n      this.isOpen = value;\n      const dropdown = this._element.nativeElement.querySelector('.dropdown-menu');\n      this._renderer.addClass(this._element.nativeElement.querySelector('div'), 'open');\n      if (dropdown && !isBs3()) {\n        this._renderer.addClass(dropdown, 'show');\n        if (dropdown.classList.contains('dropdown-menu-right') || dropdown.classList.contains('dropdown-menu-end')) {\n          this._renderer.setStyle(dropdown, 'left', 'auto');\n          this._renderer.setStyle(dropdown, 'right', '0');\n        }\n        if (this.direction === 'up') {\n          this._renderer.setStyle(dropdown, 'top', 'auto');\n          this._renderer.setStyle(dropdown, 'transform', 'translateY(-101%)');\n        }\n      }\n      if (dropdown && this._state.isAnimated) {\n        this._factoryDropDownAnimation.create(dropdown).play();\n      }\n      this.cd.markForCheck();\n      this.cd.detectChanges();\n    });\n  }\n  get direction() {\n    return this._state.direction;\n  }\n  /** @internal */\n  _contains(el) {\n    return this._element.nativeElement.contains(el);\n  }\n  ngOnDestroy() {\n    this._subscription.unsubscribe();\n  }\n}\nBsDropdownContainerComponent.ɵfac = function BsDropdownContainerComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || BsDropdownContainerComponent)(i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.AnimationBuilder));\n};\nBsDropdownContainerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: BsDropdownContainerComponent,\n  selectors: [[\"bs-dropdown-container\"]],\n  hostAttrs: [2, \"display\", \"block\", \"position\", \"absolute\", \"z-index\", \"1040\"],\n  ngContentSelectors: _c0,\n  decls: 2,\n  vars: 9,\n  consts: [[3, \"ngClass\"]],\n  template: function BsDropdownContainerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"dropup\", ctx.direction === \"up\")(\"show\", ctx.isOpen)(\"open\", ctx.isOpen);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, ctx.direction === \"down\"));\n    }\n  },\n  dependencies: [i3.NgClass],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'bs-dropdown-container',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        style: 'display:block;position: absolute;z-index: 1040'\n      },\n      template: `\n    <div [class.dropup]=\"direction === 'up'\"\n         [ngClass]=\"{dropdown: direction === 'down'}\"\n         [class.show]=\"isOpen\"\n         [class.open]=\"isOpen\"><ng-content></ng-content>\n    </div>\n  `\n    }]\n  }], function () {\n    return [{\n      type: BsDropdownState\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i2.AnimationBuilder\n    }];\n  }, null);\n})();\nclass BsDropdownDirective {\n  constructor(_elementRef, _renderer, _viewContainerRef, _cis, _state, _config, _builder) {\n    this._elementRef = _elementRef;\n    this._renderer = _renderer;\n    this._viewContainerRef = _viewContainerRef;\n    this._cis = _cis;\n    this._state = _state;\n    this._config = _config;\n    /**\n     * This attribute indicates that the dropdown should be opened upwards\n     */\n    this.dropup = false;\n    // todo: move to component loader\n    this._isInlineOpen = false;\n    this._isDisabled = false;\n    this._subscriptions = [];\n    this._isInited = false;\n    // set initial dropdown state from config\n    this._state.autoClose = this._config.autoClose;\n    this._state.insideClick = this._config.insideClick;\n    this._state.isAnimated = this._config.isAnimated;\n    this._state.stopOnClickPropagation = this._config.stopOnClickPropagation;\n    this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n    // create dropdown component loader\n    this._dropdown = this._cis.createLoader(this._elementRef, this._viewContainerRef, this._renderer).provide({\n      provide: BsDropdownState,\n      useValue: this._state\n    });\n    this.onShown = this._dropdown.onShown;\n    this.onHidden = this._dropdown.onHidden;\n    this.isOpenChange = this._state.isOpenChange;\n  }\n  /**\n   * Indicates that dropdown will be closed on item or document click,\n   * and after pressing ESC\n   */\n  set autoClose(value) {\n    this._state.autoClose = value;\n  }\n  get autoClose() {\n    return this._state.autoClose;\n  }\n  /**\n   * Indicates that dropdown will be animated\n   */\n  set isAnimated(value) {\n    this._state.isAnimated = value;\n  }\n  get isAnimated() {\n    return this._state.isAnimated;\n  }\n  /**\n   * This attribute indicates that the dropdown shouldn't close on inside click when autoClose is set to true\n   */\n  set insideClick(value) {\n    this._state.insideClick = value;\n  }\n  get insideClick() {\n    return this._state.insideClick;\n  }\n  /**\n   * Disables dropdown toggle and hides dropdown menu if opened\n   */\n  set isDisabled(value) {\n    this._isDisabled = value;\n    this._state.isDisabledChange.emit(value);\n    if (value) {\n      this.hide();\n    }\n  }\n  get isDisabled() {\n    return this._isDisabled;\n  }\n  /**\n   * Returns whether or not the popover is currently being shown\n   */\n  get isOpen() {\n    if (this._showInline) {\n      return this._isInlineOpen;\n    }\n    return this._dropdown.isShown;\n  }\n  set isOpen(value) {\n    if (value) {\n      this.show();\n    } else {\n      this.hide();\n    }\n  }\n  get isBs4() {\n    return !isBs3();\n  }\n  get _showInline() {\n    return !this.container;\n  }\n  ngOnInit() {\n    // fix: seems there are an issue with `routerLinkActive`\n    // which result in duplicated call ngOnInit without call to ngOnDestroy\n    // read more: https://github.com/valor-software/ngx-bootstrap/issues/1885\n    if (this._isInited) {\n      return;\n    }\n    this._isInited = true;\n    // attach DOM listeners\n    this._dropdown.listen({\n      // because of dropdown inline mode\n      outsideClick: false,\n      triggers: this.triggers,\n      show: () => this.show()\n    });\n    // toggle visibility on toggle element click\n    this._subscriptions.push(this._state.toggleClick.subscribe(value => this.toggle(value)));\n    // hide dropdown if set disabled while opened\n    this._subscriptions.push(this._state.isDisabledChange.pipe(filter(value => value)).subscribe((/*value: boolean*/) => this.hide()));\n  }\n  /**\n   * Opens an element’s popover. This is considered a “manual” triggering of\n   * the popover.\n   */\n  show() {\n    if (this.isOpen || this.isDisabled) {\n      return;\n    }\n    if (this._showInline) {\n      if (!this._inlinedMenu) {\n        this._state.dropdownMenu.then(dropdownMenu => {\n          this._dropdown.attachInline(dropdownMenu.viewContainer, dropdownMenu.templateRef);\n          this._inlinedMenu = this._dropdown._inlineViewRef;\n          this.addBs4Polyfills();\n          if (this._inlinedMenu) {\n            this._renderer.addClass(this._inlinedMenu.rootNodes[0].parentNode, 'open');\n          }\n          this.playAnimation();\n        })\n        // swallow errors\n        .catch();\n      }\n      this.addBs4Polyfills();\n      this._isInlineOpen = true;\n      this.onShown.emit(true);\n      this._state.isOpenChange.emit(true);\n      this.playAnimation();\n      return;\n    }\n    this._state.dropdownMenu.then(dropdownMenu => {\n      // check direction in which dropdown should be opened\n      const _dropup = this.dropup || typeof this.dropup !== 'undefined' && this.dropup;\n      this._state.direction = _dropup ? 'up' : 'down';\n      const _placement = this.placement || (_dropup ? 'top start' : 'bottom start');\n      // show dropdown\n      this._dropdown.attach(BsDropdownContainerComponent).to(this.container).position({\n        attachment: _placement\n      }).show({\n        content: dropdownMenu.templateRef,\n        placement: _placement\n      });\n      this._state.isOpenChange.emit(true);\n    })\n    // swallow error\n    .catch();\n  }\n  /**\n   * Closes an element’s popover. This is considered a “manual” triggering of\n   * the popover.\n   */\n  hide() {\n    if (!this.isOpen) {\n      return;\n    }\n    if (this._showInline) {\n      this.removeShowClass();\n      this.removeDropupStyles();\n      this._isInlineOpen = false;\n      this.onHidden.emit(true);\n    } else {\n      this._dropdown.hide();\n    }\n    this._state.isOpenChange.emit(false);\n  }\n  /**\n   * Toggles an element’s popover. This is considered a “manual” triggering of\n   * the popover. With parameter <code>true</code> allows toggling, with parameter <code>false</code>\n   * only hides opened dropdown. Parameter usage will be removed in ngx-bootstrap v3\n   */\n  toggle(value) {\n    if (this.isOpen || !value) {\n      return this.hide();\n    }\n    return this.show();\n  }\n  /** @internal */\n  _contains(event) {\n    // todo: valorkin fix typings\n    return this._elementRef.nativeElement.contains(event.target) || this._dropdown.instance && this._dropdown.instance._contains(event.target);\n  }\n  navigationClick(event) {\n    const ref = this._elementRef.nativeElement.querySelector('.dropdown-menu');\n    if (!ref) {\n      return;\n    }\n    const firstActive = this._elementRef.nativeElement.ownerDocument.activeElement;\n    const allRef = ref.querySelectorAll('.dropdown-item');\n    switch (event.keyCode) {\n      case 38:\n        if (this._state.counts > 0) {\n          allRef[--this._state.counts].focus();\n        }\n        break;\n      case 40:\n        if (this._state.counts + 1 < allRef.length) {\n          if (firstActive.classList !== allRef[this._state.counts].classList) {\n            allRef[this._state.counts].focus();\n          } else {\n            allRef[++this._state.counts].focus();\n          }\n        }\n        break;\n      default:\n    }\n    event.preventDefault();\n  }\n  ngOnDestroy() {\n    // clean up subscriptions and destroy dropdown\n    for (const sub of this._subscriptions) {\n      sub.unsubscribe();\n    }\n    this._dropdown.dispose();\n  }\n  addBs4Polyfills() {\n    if (!isBs3()) {\n      this.addShowClass();\n      this.checkRightAlignment();\n      this.addDropupStyles();\n    }\n  }\n  playAnimation() {\n    if (this._state.isAnimated && this._inlinedMenu) {\n      setTimeout(() => {\n        if (this._inlinedMenu) {\n          this._factoryDropDownAnimation.create(this._inlinedMenu.rootNodes[0]).play();\n        }\n      });\n    }\n  }\n  addShowClass() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      this._renderer.addClass(this._inlinedMenu.rootNodes[0], 'show');\n    }\n  }\n  removeShowClass() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      this._renderer.removeClass(this._inlinedMenu.rootNodes[0], 'show');\n    }\n  }\n  checkRightAlignment() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      const isRightAligned = this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-right') || this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-end');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'left', isRightAligned ? 'auto' : '0');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'right', isRightAligned ? '0' : 'auto');\n    }\n  }\n  addDropupStyles() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      // a little hack to not break support of bootstrap 4 beta\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'top', this.dropup ? 'auto' : '100%');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'transform', this.dropup ? 'translateY(-101%)' : 'translateY(0)');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'bottom', 'auto');\n    }\n  }\n  removeDropupStyles() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'top');\n      this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'transform');\n      this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'bottom');\n    }\n  }\n}\nBsDropdownDirective.ɵfac = function BsDropdownDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || BsDropdownDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.ComponentLoaderFactory), i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(BsDropdownConfig), i0.ɵɵdirectiveInject(i2.AnimationBuilder));\n};\nBsDropdownDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: BsDropdownDirective,\n  selectors: [[\"\", \"bsDropdown\", \"\"], [\"\", \"dropdown\", \"\"]],\n  hostVars: 6,\n  hostBindings: function BsDropdownDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown.arrowDown\", function BsDropdownDirective_keydown_arrowDown_HostBindingHandler($event) {\n        return ctx.navigationClick($event);\n      })(\"keydown.arrowUp\", function BsDropdownDirective_keydown_arrowUp_HostBindingHandler($event) {\n        return ctx.navigationClick($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"dropup\", ctx.dropup)(\"open\", ctx.isOpen)(\"show\", ctx.isOpen && ctx.isBs4);\n    }\n  },\n  inputs: {\n    placement: \"placement\",\n    triggers: \"triggers\",\n    container: \"container\",\n    dropup: \"dropup\",\n    autoClose: \"autoClose\",\n    isAnimated: \"isAnimated\",\n    insideClick: \"insideClick\",\n    isDisabled: \"isDisabled\",\n    isOpen: \"isOpen\"\n  },\n  outputs: {\n    isOpenChange: \"isOpenChange\",\n    onShown: \"onShown\",\n    onHidden: \"onHidden\"\n  },\n  exportAs: [\"bs-dropdown\"],\n  features: [i0.ɵɵProvidersFeature([BsDropdownState])]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[bsDropdown], [dropdown]',\n      exportAs: 'bs-dropdown',\n      providers: [BsDropdownState],\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        '[class.dropup]': 'dropup',\n        '[class.open]': 'isOpen',\n        '[class.show]': 'isOpen && isBs4'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i1.ComponentLoaderFactory\n    }, {\n      type: BsDropdownState\n    }, {\n      type: BsDropdownConfig\n    }, {\n      type: i2.AnimationBuilder\n    }];\n  }, {\n    placement: [{\n      type: Input\n    }],\n    triggers: [{\n      type: Input\n    }],\n    container: [{\n      type: Input\n    }],\n    dropup: [{\n      type: Input\n    }],\n    autoClose: [{\n      type: Input\n    }],\n    isAnimated: [{\n      type: Input\n    }],\n    insideClick: [{\n      type: Input\n    }],\n    isDisabled: [{\n      type: Input\n    }],\n    isOpen: [{\n      type: Input\n    }],\n    isOpenChange: [{\n      type: Output\n    }],\n    onShown: [{\n      type: Output\n    }],\n    onHidden: [{\n      type: Output\n    }],\n    navigationClick: [{\n      type: HostListener,\n      args: ['keydown.arrowDown', ['$event']]\n    }, {\n      type: HostListener,\n      args: ['keydown.arrowUp', ['$event']]\n    }]\n  });\n})();\nclass BsDropdownMenuDirective {\n  constructor(_state, _viewContainer, _templateRef) {\n    _state.resolveDropdownMenu({\n      templateRef: _templateRef,\n      viewContainer: _viewContainer\n    });\n  }\n}\nBsDropdownMenuDirective.ɵfac = function BsDropdownMenuDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || BsDropdownMenuDirective)(i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\nBsDropdownMenuDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: BsDropdownMenuDirective,\n  selectors: [[\"\", \"bsDropdownMenu\", \"\"], [\"\", \"dropdownMenu\", \"\"]],\n  exportAs: [\"bs-dropdown-menu\"]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownMenuDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[bsDropdownMenu],[dropdownMenu]',\n      exportAs: 'bs-dropdown-menu'\n    }]\n  }], function () {\n    return [{\n      type: BsDropdownState\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\nclass BsDropdownToggleDirective {\n  constructor(_changeDetectorRef, _dropdown, _element, _renderer, _state) {\n    this._changeDetectorRef = _changeDetectorRef;\n    this._dropdown = _dropdown;\n    this._element = _element;\n    this._renderer = _renderer;\n    this._state = _state;\n    this.isOpen = false;\n    this._subscriptions = [];\n    // sync is open value with state\n    this._subscriptions.push(this._state.isOpenChange.subscribe(value => {\n      this.isOpen = value;\n      if (value) {\n        this._documentClickListener = this._renderer.listen('document', 'click', event => {\n          if (this._state.autoClose && event.button !== 2 && !this._element.nativeElement.contains(event.target) && !(this._state.insideClick && this._dropdown._contains(event))) {\n            this._state.toggleClick.emit(false);\n            this._changeDetectorRef.detectChanges();\n          }\n        });\n        this._escKeyUpListener = this._renderer.listen(this._element.nativeElement, 'keyup.esc', () => {\n          if (this._state.autoClose) {\n            this._state.toggleClick.emit(false);\n            this._changeDetectorRef.detectChanges();\n          }\n        });\n      } else {\n        this._documentClickListener && this._documentClickListener();\n        this._escKeyUpListener && this._escKeyUpListener();\n      }\n    }));\n    // populate disabled state\n    this._subscriptions.push(this._state.isDisabledChange.subscribe(value => this.isDisabled = value || void 0));\n  }\n  onClick(event) {\n    if (this._state.stopOnClickPropagation) {\n      event.stopPropagation();\n    }\n    if (this.isDisabled) {\n      return;\n    }\n    this._state.toggleClick.emit(true);\n  }\n  ngOnDestroy() {\n    if (this._documentClickListener) {\n      this._documentClickListener();\n    }\n    if (this._escKeyUpListener) {\n      this._escKeyUpListener();\n    }\n    for (const sub of this._subscriptions) {\n      sub.unsubscribe();\n    }\n  }\n}\nBsDropdownToggleDirective.ɵfac = function BsDropdownToggleDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || BsDropdownToggleDirective)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(BsDropdownDirective), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(BsDropdownState));\n};\nBsDropdownToggleDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: BsDropdownToggleDirective,\n  selectors: [[\"\", \"bsDropdownToggle\", \"\"], [\"\", \"dropdownToggle\", \"\"]],\n  hostVars: 3,\n  hostBindings: function BsDropdownToggleDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function BsDropdownToggleDirective_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-haspopup\", true)(\"disabled\", ctx.isDisabled)(\"aria-expanded\", ctx.isOpen);\n    }\n  },\n  exportAs: [\"bs-dropdown-toggle\"]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownToggleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[bsDropdownToggle],[dropdownToggle]',\n      exportAs: 'bs-dropdown-toggle',\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        '[attr.aria-haspopup]': 'true'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: BsDropdownDirective\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: BsDropdownState\n    }];\n  }, {\n    isDisabled: [{\n      type: HostBinding,\n      args: ['attr.disabled']\n    }],\n    isOpen: [{\n      type: HostBinding,\n      args: ['attr.aria-expanded']\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\nclass BsDropdownModule {\n  static forRoot() {\n    return {\n      ngModule: BsDropdownModule,\n      providers: [ComponentLoaderFactory, PositioningService, BsDropdownState]\n    };\n  }\n}\nBsDropdownModule.ɵfac = function BsDropdownModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || BsDropdownModule)();\n};\nBsDropdownModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BsDropdownModule\n});\nBsDropdownModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [BsDropdownMenuDirective, BsDropdownToggleDirective, BsDropdownContainerComponent, BsDropdownDirective],\n      exports: [BsDropdownMenuDirective, BsDropdownToggleDirective, BsDropdownDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsDropdownConfig, BsDropdownContainerComponent, BsDropdownDirective, BsDropdownMenuDirective, BsDropdownModule, BsDropdownState, BsDropdownToggleDirective };", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "Component", "ChangeDetectionStrategy", "Directive", "Input", "Output", "HostListener", "HostBinding", "NgModule", "filter", "i1", "ComponentLoaderFactory", "isBs3", "i2", "style", "animate", "i3", "CommonModule", "PositioningService", "_c0", "_c1", "a0", "dropdown", "BsDropdownConfig", "constructor", "autoClose", "insideClick", "isAnimated", "stopOnClickPropagation", "ɵfac", "BsDropdownConfig_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "BsDropdownState", "direction", "isOpenChange", "isDisabledChange", "toggleClick", "counts", "dropdownMenu", "Promise", "resolve", "resolveDropdownMenu", "BsDropdownState_Factory", "DROPDOWN_ANIMATION_TIMING", "dropdownAnimation", "height", "overflow", "BsDropdownContainerComponent", "_state", "cd", "_renderer", "_element", "_builder", "isOpen", "_factoryDropDownAnimation", "build", "_subscription", "subscribe", "value", "nativeElement", "querySelector", "addClass", "classList", "contains", "setStyle", "create", "play", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "_contains", "el", "ngOnDestroy", "unsubscribe", "BsDropdownContainerComponent_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "Renderer2", "ElementRef", "AnimationBuilder", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "ngContentSelectors", "decls", "vars", "consts", "template", "BsDropdownContainerComponent_Template", "rf", "ctx", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵclassProp", "ɵɵproperty", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "encapsulation", "changeDetection", "selector", "OnPush", "host", "BsDropdownDirective", "_elementRef", "_viewContainerRef", "_cis", "_config", "dropup", "_isInlineOpen", "_isDisabled", "_subscriptions", "_isInited", "_dropdown", "createLoader", "provide", "useValue", "onShown", "onHidden", "isDisabled", "emit", "hide", "_showInline", "isShown", "show", "isBs4", "container", "ngOnInit", "listen", "outsideClick", "triggers", "push", "toggle", "pipe", "_inlinedMenu", "then", "attachInline", "viewContainer", "templateRef", "_inlineViewRef", "addBs4Polyfills", "rootNodes", "parentNode", "playAnimation", "catch", "_dropup", "_placement", "placement", "attach", "to", "position", "attachment", "content", "removeShowClass", "removeDropupStyles", "event", "target", "instance", "navigationClick", "ref", "firstActive", "ownerDocument", "activeElement", "allRef", "querySelectorAll", "keyCode", "focus", "length", "preventDefault", "sub", "dispose", "addShowClass", "checkRightAlignment", "addDropupStyles", "setTimeout", "removeClass", "isRightAligned", "removeStyle", "BsDropdownDirective_Factory", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "BsDropdownDirective_HostBindings", "ɵɵlistener", "BsDropdownDirective_keydown_arrowDown_HostBindingHandler", "$event", "BsDropdownDirective_keydown_arrowUp_HostBindingHandler", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "providers", "BsDropdownMenuDirective", "_viewContainer", "_templateRef", "BsDropdownMenuDirective_Factory", "TemplateRef", "BsDropdownToggleDirective", "_changeDetectorRef", "_documentClickListener", "button", "_escKeyUpListener", "onClick", "stopPropagation", "BsDropdownToggleDirective_Factory", "BsDropdownToggleDirective_HostBindings", "BsDropdownToggleDirective_click_HostBindingHandler", "ɵɵattribute", "BsDropdownModule", "forRoot", "ngModule", "BsDropdownModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/ngx-bootstrap/dropdown/fesm2020/ngx-bootstrap-dropdown.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ChangeDetectionStrategy, Directive, Input, Output, HostListener, HostBinding, NgModule } from '@angular/core';\nimport { filter } from 'rxjs/operators';\nimport * as i1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport { isBs3 } from 'ngx-bootstrap/utils';\nimport * as i2 from '@angular/animations';\nimport { style, animate } from '@angular/animations';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { PositioningService } from 'ngx-bootstrap/positioning';\n\n/** Default dropdown configuration */\nclass BsDropdownConfig {\n    constructor() {\n        /** default dropdown auto closing behavior */\n        this.autoClose = true;\n        /** default dropdown auto closing behavior */\n        this.insideClick = false;\n        /** turn on/off animation */\n        this.isAnimated = false;\n        /** value true of stopOnClickPropagation allows event stopPropagation*/\n        this.stopOnClickPropagation = false;\n    }\n}\nBsDropdownConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nBsDropdownConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownConfig, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownConfig, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n\nclass BsDropdownState {\n    constructor() {\n        this.direction = 'down';\n        this.autoClose = true;\n        this.insideClick = false;\n        this.isAnimated = false;\n        this.stopOnClickPropagation = false;\n        this.isOpenChange = new EventEmitter();\n        this.isDisabledChange = new EventEmitter();\n        this.toggleClick = new EventEmitter();\n        this.counts = 0;\n        this.dropdownMenu = new Promise(resolve => {\n            this.resolveDropdownMenu = resolve;\n        });\n    }\n}\nBsDropdownState.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownState, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nBsDropdownState.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownState, providedIn: 'platform' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownState, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform' }]\n        }], ctorParameters: function () { return []; } });\n\nconst DROPDOWN_ANIMATION_TIMING = '220ms cubic-bezier(0, 0, 0.2, 1)';\nconst dropdownAnimation = [\n    style({ height: 0, overflow: 'hidden' }),\n    animate(DROPDOWN_ANIMATION_TIMING, style({ height: '*', overflow: 'hidden' }))\n];\n\n// todo: revert ngClass to [class] when false positive angular-cli issue is fixed\n//          [class.dropdown]=\"direction === 'down'\"-->\nclass BsDropdownContainerComponent {\n    constructor(_state, cd, _renderer, _element, _builder) {\n        this._state = _state;\n        this.cd = cd;\n        this._renderer = _renderer;\n        this._element = _element;\n        this.isOpen = false;\n        this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n        this._subscription = _state.isOpenChange.subscribe((value) => {\n            this.isOpen = value;\n            const dropdown = this._element.nativeElement.querySelector('.dropdown-menu');\n            this._renderer.addClass(this._element.nativeElement.querySelector('div'), 'open');\n            if (dropdown && !isBs3()) {\n                this._renderer.addClass(dropdown, 'show');\n                if (dropdown.classList.contains('dropdown-menu-right') || dropdown.classList.contains('dropdown-menu-end')) {\n                    this._renderer.setStyle(dropdown, 'left', 'auto');\n                    this._renderer.setStyle(dropdown, 'right', '0');\n                }\n                if (this.direction === 'up') {\n                    this._renderer.setStyle(dropdown, 'top', 'auto');\n                    this._renderer.setStyle(dropdown, 'transform', 'translateY(-101%)');\n                }\n            }\n            if (dropdown && this._state.isAnimated) {\n                this._factoryDropDownAnimation.create(dropdown)\n                    .play();\n            }\n            this.cd.markForCheck();\n            this.cd.detectChanges();\n        });\n    }\n    get direction() {\n        return this._state.direction;\n    }\n    /** @internal */\n    _contains(el) {\n        return this._element.nativeElement.contains(el);\n    }\n    ngOnDestroy() {\n        this._subscription.unsubscribe();\n    }\n}\nBsDropdownContainerComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownContainerComponent, deps: [{ token: BsDropdownState }, { token: i0.ChangeDetectorRef }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i2.AnimationBuilder }], target: i0.ɵɵFactoryTarget.Component });\nBsDropdownContainerComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: BsDropdownContainerComponent, selector: \"bs-dropdown-container\", host: { styleAttribute: \"display:block;position: absolute;z-index: 1040\" }, ngImport: i0, template: `\n    <div [class.dropup]=\"direction === 'up'\"\n         [ngClass]=\"{dropdown: direction === 'down'}\"\n         [class.show]=\"isOpen\"\n         [class.open]=\"isOpen\"><ng-content></ng-content>\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: i3.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownContainerComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'bs-dropdown-container',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n                    host: {\n                        style: 'display:block;position: absolute;z-index: 1040'\n                    },\n                    template: `\n    <div [class.dropup]=\"direction === 'up'\"\n         [ngClass]=\"{dropdown: direction === 'down'}\"\n         [class.show]=\"isOpen\"\n         [class.open]=\"isOpen\"><ng-content></ng-content>\n    </div>\n  `\n                }]\n        }], ctorParameters: function () { return [{ type: BsDropdownState }, { type: i0.ChangeDetectorRef }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i2.AnimationBuilder }]; } });\n\nclass BsDropdownDirective {\n    constructor(_elementRef, _renderer, _viewContainerRef, _cis, _state, _config, _builder) {\n        this._elementRef = _elementRef;\n        this._renderer = _renderer;\n        this._viewContainerRef = _viewContainerRef;\n        this._cis = _cis;\n        this._state = _state;\n        this._config = _config;\n        /**\n         * This attribute indicates that the dropdown should be opened upwards\n         */\n        this.dropup = false;\n        // todo: move to component loader\n        this._isInlineOpen = false;\n        this._isDisabled = false;\n        this._subscriptions = [];\n        this._isInited = false;\n        // set initial dropdown state from config\n        this._state.autoClose = this._config.autoClose;\n        this._state.insideClick = this._config.insideClick;\n        this._state.isAnimated = this._config.isAnimated;\n        this._state.stopOnClickPropagation = this._config.stopOnClickPropagation;\n        this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n        // create dropdown component loader\n        this._dropdown = this._cis\n            .createLoader(this._elementRef, this._viewContainerRef, this._renderer)\n            .provide({ provide: BsDropdownState, useValue: this._state });\n        this.onShown = this._dropdown.onShown;\n        this.onHidden = this._dropdown.onHidden;\n        this.isOpenChange = this._state.isOpenChange;\n    }\n    /**\n     * Indicates that dropdown will be closed on item or document click,\n     * and after pressing ESC\n     */\n    set autoClose(value) {\n        this._state.autoClose = value;\n    }\n    get autoClose() {\n        return this._state.autoClose;\n    }\n    /**\n     * Indicates that dropdown will be animated\n     */\n    set isAnimated(value) {\n        this._state.isAnimated = value;\n    }\n    get isAnimated() {\n        return this._state.isAnimated;\n    }\n    /**\n     * This attribute indicates that the dropdown shouldn't close on inside click when autoClose is set to true\n     */\n    set insideClick(value) {\n        this._state.insideClick = value;\n    }\n    get insideClick() {\n        return this._state.insideClick;\n    }\n    /**\n     * Disables dropdown toggle and hides dropdown menu if opened\n     */\n    set isDisabled(value) {\n        this._isDisabled = value;\n        this._state.isDisabledChange.emit(value);\n        if (value) {\n            this.hide();\n        }\n    }\n    get isDisabled() {\n        return this._isDisabled;\n    }\n    /**\n     * Returns whether or not the popover is currently being shown\n     */\n    get isOpen() {\n        if (this._showInline) {\n            return this._isInlineOpen;\n        }\n        return this._dropdown.isShown;\n    }\n    set isOpen(value) {\n        if (value) {\n            this.show();\n        }\n        else {\n            this.hide();\n        }\n    }\n    get isBs4() {\n        return !isBs3();\n    }\n    get _showInline() {\n        return !this.container;\n    }\n    ngOnInit() {\n        // fix: seems there are an issue with `routerLinkActive`\n        // which result in duplicated call ngOnInit without call to ngOnDestroy\n        // read more: https://github.com/valor-software/ngx-bootstrap/issues/1885\n        if (this._isInited) {\n            return;\n        }\n        this._isInited = true;\n        // attach DOM listeners\n        this._dropdown.listen({\n            // because of dropdown inline mode\n            outsideClick: false,\n            triggers: this.triggers,\n            show: () => this.show()\n        });\n        // toggle visibility on toggle element click\n        this._subscriptions.push(this._state.toggleClick.subscribe((value) => this.toggle(value)));\n        // hide dropdown if set disabled while opened\n        this._subscriptions.push(this._state.isDisabledChange\n            .pipe(filter((value) => value))\n            .subscribe(( /*value: boolean*/) => this.hide()));\n    }\n    /**\n     * Opens an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n    show() {\n        if (this.isOpen || this.isDisabled) {\n            return;\n        }\n        if (this._showInline) {\n            if (!this._inlinedMenu) {\n                this._state.dropdownMenu.then((dropdownMenu) => {\n                    this._dropdown.attachInline(dropdownMenu.viewContainer, dropdownMenu.templateRef);\n                    this._inlinedMenu = this._dropdown._inlineViewRef;\n                    this.addBs4Polyfills();\n                    if (this._inlinedMenu) {\n                        this._renderer.addClass(this._inlinedMenu.rootNodes[0].parentNode, 'open');\n                    }\n                    this.playAnimation();\n                })\n                    // swallow errors\n                    .catch();\n            }\n            this.addBs4Polyfills();\n            this._isInlineOpen = true;\n            this.onShown.emit(true);\n            this._state.isOpenChange.emit(true);\n            this.playAnimation();\n            return;\n        }\n        this._state.dropdownMenu.then(dropdownMenu => {\n            // check direction in which dropdown should be opened\n            const _dropup = this.dropup ||\n                (typeof this.dropup !== 'undefined' && this.dropup);\n            this._state.direction = _dropup ? 'up' : 'down';\n            const _placement = this.placement || (_dropup ? 'top start' : 'bottom start');\n            // show dropdown\n            this._dropdown\n                .attach(BsDropdownContainerComponent)\n                .to(this.container)\n                .position({ attachment: _placement })\n                .show({\n                content: dropdownMenu.templateRef,\n                placement: _placement\n            });\n            this._state.isOpenChange.emit(true);\n        })\n            // swallow error\n            .catch();\n    }\n    /**\n     * Closes an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n    hide() {\n        if (!this.isOpen) {\n            return;\n        }\n        if (this._showInline) {\n            this.removeShowClass();\n            this.removeDropupStyles();\n            this._isInlineOpen = false;\n            this.onHidden.emit(true);\n        }\n        else {\n            this._dropdown.hide();\n        }\n        this._state.isOpenChange.emit(false);\n    }\n    /**\n     * Toggles an element’s popover. This is considered a “manual” triggering of\n     * the popover. With parameter <code>true</code> allows toggling, with parameter <code>false</code>\n     * only hides opened dropdown. Parameter usage will be removed in ngx-bootstrap v3\n     */\n    toggle(value) {\n        if (this.isOpen || !value) {\n            return this.hide();\n        }\n        return this.show();\n    }\n    /** @internal */\n    _contains(event) {\n        // todo: valorkin fix typings\n        return this._elementRef.nativeElement.contains(event.target) ||\n            (this._dropdown.instance && this._dropdown.instance._contains(event.target));\n    }\n    navigationClick(event) {\n        const ref = this._elementRef.nativeElement.querySelector('.dropdown-menu');\n        if (!ref) {\n            return;\n        }\n        const firstActive = this._elementRef.nativeElement.ownerDocument.activeElement;\n        const allRef = ref.querySelectorAll('.dropdown-item');\n        switch (event.keyCode) {\n            case 38:\n                if (this._state.counts > 0) {\n                    allRef[--this._state.counts].focus();\n                }\n                break;\n            case 40:\n                if (this._state.counts + 1 < allRef.length) {\n                    if (firstActive.classList !== allRef[this._state.counts].classList) {\n                        allRef[this._state.counts].focus();\n                    }\n                    else {\n                        allRef[++this._state.counts].focus();\n                    }\n                }\n                break;\n            default:\n        }\n        event.preventDefault();\n    }\n    ngOnDestroy() {\n        // clean up subscriptions and destroy dropdown\n        for (const sub of this._subscriptions) {\n            sub.unsubscribe();\n        }\n        this._dropdown.dispose();\n    }\n    addBs4Polyfills() {\n        if (!isBs3()) {\n            this.addShowClass();\n            this.checkRightAlignment();\n            this.addDropupStyles();\n        }\n    }\n    playAnimation() {\n        if (this._state.isAnimated && this._inlinedMenu) {\n            setTimeout(() => {\n                if (this._inlinedMenu) {\n                    this._factoryDropDownAnimation.create(this._inlinedMenu.rootNodes[0]).play();\n                }\n            });\n        }\n    }\n    addShowClass() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            this._renderer.addClass(this._inlinedMenu.rootNodes[0], 'show');\n        }\n    }\n    removeShowClass() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            this._renderer.removeClass(this._inlinedMenu.rootNodes[0], 'show');\n        }\n    }\n    checkRightAlignment() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            const isRightAligned = this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-right') || this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-end');\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'left', isRightAligned ? 'auto' : '0');\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'right', isRightAligned ? '0' : 'auto');\n        }\n    }\n    addDropupStyles() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            // a little hack to not break support of bootstrap 4 beta\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'top', this.dropup ? 'auto' : '100%');\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'transform', this.dropup ? 'translateY(-101%)' : 'translateY(0)');\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'bottom', 'auto');\n        }\n    }\n    removeDropupStyles() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'top');\n            this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'transform');\n            this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'bottom');\n        }\n    }\n}\nBsDropdownDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownDirective, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ViewContainerRef }, { token: i1.ComponentLoaderFactory }, { token: BsDropdownState }, { token: BsDropdownConfig }, { token: i2.AnimationBuilder }], target: i0.ɵɵFactoryTarget.Directive });\nBsDropdownDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: BsDropdownDirective, selector: \"[bsDropdown], [dropdown]\", inputs: { placement: \"placement\", triggers: \"triggers\", container: \"container\", dropup: \"dropup\", autoClose: \"autoClose\", isAnimated: \"isAnimated\", insideClick: \"insideClick\", isDisabled: \"isDisabled\", isOpen: \"isOpen\" }, outputs: { isOpenChange: \"isOpenChange\", onShown: \"onShown\", onHidden: \"onHidden\" }, host: { listeners: { \"keydown.arrowDown\": \"navigationClick($event)\", \"keydown.arrowUp\": \"navigationClick($event)\" }, properties: { \"class.dropup\": \"dropup\", \"class.open\": \"isOpen\", \"class.show\": \"isOpen && isBs4\" } }, providers: [BsDropdownState], exportAs: [\"bs-dropdown\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[bsDropdown], [dropdown]',\n                    exportAs: 'bs-dropdown',\n                    providers: [BsDropdownState],\n                    // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n                    host: {\n                        '[class.dropup]': 'dropup',\n                        '[class.open]': 'isOpen',\n                        '[class.show]': 'isOpen && isBs4'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ViewContainerRef }, { type: i1.ComponentLoaderFactory }, { type: BsDropdownState }, { type: BsDropdownConfig }, { type: i2.AnimationBuilder }]; }, propDecorators: { placement: [{\n                type: Input\n            }], triggers: [{\n                type: Input\n            }], container: [{\n                type: Input\n            }], dropup: [{\n                type: Input\n            }], autoClose: [{\n                type: Input\n            }], isAnimated: [{\n                type: Input\n            }], insideClick: [{\n                type: Input\n            }], isDisabled: [{\n                type: Input\n            }], isOpen: [{\n                type: Input\n            }], isOpenChange: [{\n                type: Output\n            }], onShown: [{\n                type: Output\n            }], onHidden: [{\n                type: Output\n            }], navigationClick: [{\n                type: HostListener,\n                args: ['keydown.arrowDown', ['$event']]\n            }, {\n                type: HostListener,\n                args: ['keydown.arrowUp', ['$event']]\n            }] } });\n\nclass BsDropdownMenuDirective {\n    constructor(_state, _viewContainer, _templateRef) {\n        _state.resolveDropdownMenu({\n            templateRef: _templateRef,\n            viewContainer: _viewContainer\n        });\n    }\n}\nBsDropdownMenuDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownMenuDirective, deps: [{ token: BsDropdownState }, { token: i0.ViewContainerRef }, { token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nBsDropdownMenuDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: BsDropdownMenuDirective, selector: \"[bsDropdownMenu],[dropdownMenu]\", exportAs: [\"bs-dropdown-menu\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownMenuDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[bsDropdownMenu],[dropdownMenu]',\n                    exportAs: 'bs-dropdown-menu'\n                }]\n        }], ctorParameters: function () { return [{ type: BsDropdownState }, { type: i0.ViewContainerRef }, { type: i0.TemplateRef }]; } });\n\nclass BsDropdownToggleDirective {\n    constructor(_changeDetectorRef, _dropdown, _element, _renderer, _state) {\n        this._changeDetectorRef = _changeDetectorRef;\n        this._dropdown = _dropdown;\n        this._element = _element;\n        this._renderer = _renderer;\n        this._state = _state;\n        this.isOpen = false;\n        this._subscriptions = [];\n        // sync is open value with state\n        this._subscriptions.push(this._state.isOpenChange.subscribe((value) => {\n            this.isOpen = value;\n            if (value) {\n                this._documentClickListener = this._renderer.listen('document', 'click', (event) => {\n                    if (this._state.autoClose && event.button !== 2 &&\n                        !this._element.nativeElement.contains(event.target) &&\n                        !(this._state.insideClick && this._dropdown._contains(event))) {\n                        this._state.toggleClick.emit(false);\n                        this._changeDetectorRef.detectChanges();\n                    }\n                });\n                this._escKeyUpListener = this._renderer.listen(this._element.nativeElement, 'keyup.esc', () => {\n                    if (this._state.autoClose) {\n                        this._state.toggleClick.emit(false);\n                        this._changeDetectorRef.detectChanges();\n                    }\n                });\n            }\n            else {\n                this._documentClickListener && this._documentClickListener();\n                this._escKeyUpListener && this._escKeyUpListener();\n            }\n        }));\n        // populate disabled state\n        this._subscriptions.push(this._state.isDisabledChange\n            .subscribe((value) => this.isDisabled = value || void 0));\n    }\n    onClick(event) {\n        if (this._state.stopOnClickPropagation) {\n            event.stopPropagation();\n        }\n        if (this.isDisabled) {\n            return;\n        }\n        this._state.toggleClick.emit(true);\n    }\n    ngOnDestroy() {\n        if (this._documentClickListener) {\n            this._documentClickListener();\n        }\n        if (this._escKeyUpListener) {\n            this._escKeyUpListener();\n        }\n        for (const sub of this._subscriptions) {\n            sub.unsubscribe();\n        }\n    }\n}\nBsDropdownToggleDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownToggleDirective, deps: [{ token: i0.ChangeDetectorRef }, { token: BsDropdownDirective }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: BsDropdownState }], target: i0.ɵɵFactoryTarget.Directive });\nBsDropdownToggleDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: BsDropdownToggleDirective, selector: \"[bsDropdownToggle],[dropdownToggle]\", host: { listeners: { \"click\": \"onClick($event)\" }, properties: { \"attr.aria-haspopup\": \"true\", \"attr.disabled\": \"this.isDisabled\", \"attr.aria-expanded\": \"this.isOpen\" } }, exportAs: [\"bs-dropdown-toggle\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownToggleDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[bsDropdownToggle],[dropdownToggle]',\n                    exportAs: 'bs-dropdown-toggle',\n                    // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n                    host: {\n                        '[attr.aria-haspopup]': 'true'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: BsDropdownDirective }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: BsDropdownState }]; }, propDecorators: { isDisabled: [{\n                type: HostBinding,\n                args: ['attr.disabled']\n            }], isOpen: [{\n                type: HostBinding,\n                args: ['attr.aria-expanded']\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\n\nclass BsDropdownModule {\n    static forRoot() {\n        return {\n            ngModule: BsDropdownModule,\n            providers: [\n                ComponentLoaderFactory,\n                PositioningService,\n                BsDropdownState\n            ]\n        };\n    }\n}\nBsDropdownModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBsDropdownModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownModule, declarations: [BsDropdownMenuDirective,\n        BsDropdownToggleDirective,\n        BsDropdownContainerComponent,\n        BsDropdownDirective], imports: [CommonModule], exports: [BsDropdownMenuDirective,\n        BsDropdownToggleDirective,\n        BsDropdownDirective] });\nBsDropdownModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BsDropdownModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    declarations: [\n                        BsDropdownMenuDirective,\n                        BsDropdownToggleDirective,\n                        BsDropdownContainerComponent,\n                        BsDropdownDirective\n                    ],\n                    exports: [\n                        BsDropdownMenuDirective,\n                        BsDropdownToggleDirective,\n                        BsDropdownDirective\n                    ]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsDropdownConfig, BsDropdownContainerComponent, BsDropdownDirective, BsDropdownMenuDirective, BsDropdownModule, BsDropdownState, BsDropdownToggleDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,eAAe;AAC3J,SAASC,MAAM,QAAQ,gBAAgB;AACvC,OAAO,KAAKC,EAAE,MAAM,gCAAgC;AACpD,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,SAASC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;AACpD,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,2BAA2B;;AAE9D;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,QAAA,EAAAD;AAAA;AACA,MAAME,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,sBAAsB,GAAG,KAAK;EACvC;AACJ;AACAL,gBAAgB,CAACM,IAAI,YAAAC,yBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAwFR,gBAAgB;AAAA,CAAoD;AACjLA,gBAAgB,CAACS,KAAK,kBAD6ElC,EAAE,CAAAmC,kBAAA;EAAAC,KAAA,EACYX,gBAAgB;EAAAY,OAAA,EAAhBZ,gBAAgB,CAAAM,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACxJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFmGvC,EAAE,CAAAwC,iBAAA,CAEVf,gBAAgB,EAAc,CAAC;IAC9GgB,IAAI,EAAExC,UAAU;IAChByC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,eAAe,CAAC;EAClBjB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkB,SAAS,GAAG,MAAM;IACvB,IAAI,CAACjB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACe,YAAY,GAAG,IAAI3C,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC4C,gBAAgB,GAAG,IAAI5C,YAAY,CAAC,CAAC;IAC1C,IAAI,CAAC6C,WAAW,GAAG,IAAI7C,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC8C,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,YAAY,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAI;MACvC,IAAI,CAACC,mBAAmB,GAAGD,OAAO;IACtC,CAAC,CAAC;EACN;AACJ;AACAR,eAAe,CAACZ,IAAI,YAAAsB,wBAAApB,iBAAA;EAAA,YAAAA,iBAAA,IAAwFU,eAAe;AAAA,CAAoD;AAC/KA,eAAe,CAACT,KAAK,kBA1B8ElC,EAAE,CAAAmC,kBAAA;EAAAC,KAAA,EA0BWO,eAAe;EAAAN,OAAA,EAAfM,eAAe,CAAAZ,IAAA;EAAAO,UAAA,EAAc;AAAU,EAAG;AAC1J;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3BmGvC,EAAE,CAAAwC,iBAAA,CA2BVG,eAAe,EAAc,CAAC;IAC7GF,IAAI,EAAExC,UAAU;IAChByC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAW,CAAC;EACrC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMgB,yBAAyB,GAAG,kCAAkC;AACpE,MAAMC,iBAAiB,GAAG,CACtBvC,KAAK,CAAC;EAAEwC,MAAM,EAAE,CAAC;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAAC,EACxCxC,OAAO,CAACqC,yBAAyB,EAAEtC,KAAK,CAAC;EAAEwC,MAAM,EAAE,GAAG;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAAC,CAAC,CACjF;;AAED;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/BhC,WAAWA,CAACiC,MAAM,EAAEC,EAAE,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACnD,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,yBAAyB,GAAGF,QAAQ,CAACG,KAAK,CAACX,iBAAiB,CAAC;IAClE,IAAI,CAACY,aAAa,GAAGR,MAAM,CAACd,YAAY,CAACuB,SAAS,CAAEC,KAAK,IAAK;MAC1D,IAAI,CAACL,MAAM,GAAGK,KAAK;MACnB,MAAM7C,QAAQ,GAAG,IAAI,CAACsC,QAAQ,CAACQ,aAAa,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC5E,IAAI,CAACV,SAAS,CAACW,QAAQ,CAAC,IAAI,CAACV,QAAQ,CAACQ,aAAa,CAACC,aAAa,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC;MACjF,IAAI/C,QAAQ,IAAI,CAACV,KAAK,CAAC,CAAC,EAAE;QACtB,IAAI,CAAC+C,SAAS,CAACW,QAAQ,CAAChD,QAAQ,EAAE,MAAM,CAAC;QACzC,IAAIA,QAAQ,CAACiD,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IAAIlD,QAAQ,CAACiD,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;UACxG,IAAI,CAACb,SAAS,CAACc,QAAQ,CAACnD,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;UACjD,IAAI,CAACqC,SAAS,CAACc,QAAQ,CAACnD,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAC;QACnD;QACA,IAAI,IAAI,CAACoB,SAAS,KAAK,IAAI,EAAE;UACzB,IAAI,CAACiB,SAAS,CAACc,QAAQ,CAACnD,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC;UAChD,IAAI,CAACqC,SAAS,CAACc,QAAQ,CAACnD,QAAQ,EAAE,WAAW,EAAE,mBAAmB,CAAC;QACvE;MACJ;MACA,IAAIA,QAAQ,IAAI,IAAI,CAACmC,MAAM,CAAC9B,UAAU,EAAE;QACpC,IAAI,CAACoC,yBAAyB,CAACW,MAAM,CAACpD,QAAQ,CAAC,CAC1CqD,IAAI,CAAC,CAAC;MACf;MACA,IAAI,CAACjB,EAAE,CAACkB,YAAY,CAAC,CAAC;MACtB,IAAI,CAAClB,EAAE,CAACmB,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA,IAAInC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACe,MAAM,CAACf,SAAS;EAChC;EACA;EACAoC,SAASA,CAACC,EAAE,EAAE;IACV,OAAO,IAAI,CAACnB,QAAQ,CAACQ,aAAa,CAACI,QAAQ,CAACO,EAAE,CAAC;EACnD;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACf,aAAa,CAACgB,WAAW,CAAC,CAAC;EACpC;AACJ;AACAzB,4BAA4B,CAAC3B,IAAI,YAAAqD,qCAAAnD,iBAAA;EAAA,YAAAA,iBAAA,IAAwFyB,4BAA4B,EAlFlD1D,EAAE,CAAAqF,iBAAA,CAkFkE1C,eAAe,GAlFnF3C,EAAE,CAAAqF,iBAAA,CAkF8FrF,EAAE,CAACsF,iBAAiB,GAlFpHtF,EAAE,CAAAqF,iBAAA,CAkF+HrF,EAAE,CAACuF,SAAS,GAlF7IvF,EAAE,CAAAqF,iBAAA,CAkFwJrF,EAAE,CAACwF,UAAU,GAlFvKxF,EAAE,CAAAqF,iBAAA,CAkFkLtE,EAAE,CAAC0E,gBAAgB;AAAA,CAA4C;AACtV/B,4BAA4B,CAACgC,IAAI,kBAnFkE1F,EAAE,CAAA2F,iBAAA;EAAAlD,IAAA,EAmFQiB,4BAA4B;EAAAkC,SAAA;EAAAC,SAAA;EAAAC,kBAAA,EAAAzE,GAAA;EAAA0E,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAnFtCpG,EAAE,CAAAsG,eAAA;MAAFtG,EAAE,CAAAuG,cAAA,YAuFvE,CAAC;MAvFoEvG,EAAE,CAAAwG,YAAA,EAuF9C,CAAC;MAvF2CxG,EAAE,CAAAyG,YAAA,CAwF5F,CAAC;IAAA;IAAA,IAAAL,EAAA;MAxFyFpG,EAAE,CAAA0G,WAAA,WAAAL,GAAA,CAAAzD,SAAA,SAoF1D,CAAC,SAAAyD,GAAA,CAAArC,MAEf,CAAC,SAAAqC,GAAA,CAAArC,MACD,CAAC;MAvFqEhE,EAAE,CAAA2G,UAAA,YAAF3G,EAAE,CAAA4G,eAAA,IAAAtF,GAAA,EAAA+E,GAAA,CAAAzD,SAAA,YAqFjD,CAAC;IAAA;EAAA;EAAAiE,YAAA,GAIU3F,EAAE,CAAC4F,OAAO;EAAAC,aAAA;EAAAC,eAAA;AAAA,EAA+G;AACxL;EAAA,QAAAzE,SAAA,oBAAAA,SAAA,KA1FmGvC,EAAE,CAAAwC,iBAAA,CA0FVkB,4BAA4B,EAAc,CAAC;IAC1HjB,IAAI,EAAEtC,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCuE,QAAQ,EAAE,uBAAuB;MACjCD,eAAe,EAAE5G,uBAAuB,CAAC8G,MAAM;MAC/C;MACAC,IAAI,EAAE;QACFnG,KAAK,EAAE;MACX,CAAC;MACDkF,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzD,IAAI,EAAEE;IAAgB,CAAC,EAAE;MAAEF,IAAI,EAAEzC,EAAE,CAACsF;IAAkB,CAAC,EAAE;MAAE7C,IAAI,EAAEzC,EAAE,CAACuF;IAAU,CAAC,EAAE;MAAE9C,IAAI,EAAEzC,EAAE,CAACwF;IAAW,CAAC,EAAE;MAAE/C,IAAI,EAAE1B,EAAE,CAAC0E;IAAiB,CAAC,CAAC;EAAE,CAAC;AAAA;AAE/L,MAAM2B,mBAAmB,CAAC;EACtB1F,WAAWA,CAAC2F,WAAW,EAAExD,SAAS,EAAEyD,iBAAiB,EAAEC,IAAI,EAAE5D,MAAM,EAAE6D,OAAO,EAAEzD,QAAQ,EAAE;IACpF,IAAI,CAACsD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACxD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACyD,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC5D,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC6D,OAAO,GAAGA,OAAO;IACtB;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAAClE,MAAM,CAAChC,SAAS,GAAG,IAAI,CAAC6F,OAAO,CAAC7F,SAAS;IAC9C,IAAI,CAACgC,MAAM,CAAC/B,WAAW,GAAG,IAAI,CAAC4F,OAAO,CAAC5F,WAAW;IAClD,IAAI,CAAC+B,MAAM,CAAC9B,UAAU,GAAG,IAAI,CAAC2F,OAAO,CAAC3F,UAAU;IAChD,IAAI,CAAC8B,MAAM,CAAC7B,sBAAsB,GAAG,IAAI,CAAC0F,OAAO,CAAC1F,sBAAsB;IACxE,IAAI,CAACmC,yBAAyB,GAAGF,QAAQ,CAACG,KAAK,CAACX,iBAAiB,CAAC;IAClE;IACA,IAAI,CAACuE,SAAS,GAAG,IAAI,CAACP,IAAI,CACrBQ,YAAY,CAAC,IAAI,CAACV,WAAW,EAAE,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACzD,SAAS,CAAC,CACtEmE,OAAO,CAAC;MAAEA,OAAO,EAAErF,eAAe;MAAEsF,QAAQ,EAAE,IAAI,CAACtE;IAAO,CAAC,CAAC;IACjE,IAAI,CAACuE,OAAO,GAAG,IAAI,CAACJ,SAAS,CAACI,OAAO;IACrC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACL,SAAS,CAACK,QAAQ;IACvC,IAAI,CAACtF,YAAY,GAAG,IAAI,CAACc,MAAM,CAACd,YAAY;EAChD;EACA;AACJ;AACA;AACA;EACI,IAAIlB,SAASA,CAAC0C,KAAK,EAAE;IACjB,IAAI,CAACV,MAAM,CAAChC,SAAS,GAAG0C,KAAK;EACjC;EACA,IAAI1C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACgC,MAAM,CAAChC,SAAS;EAChC;EACA;AACJ;AACA;EACI,IAAIE,UAAUA,CAACwC,KAAK,EAAE;IAClB,IAAI,CAACV,MAAM,CAAC9B,UAAU,GAAGwC,KAAK;EAClC;EACA,IAAIxC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC8B,MAAM,CAAC9B,UAAU;EACjC;EACA;AACJ;AACA;EACI,IAAID,WAAWA,CAACyC,KAAK,EAAE;IACnB,IAAI,CAACV,MAAM,CAAC/B,WAAW,GAAGyC,KAAK;EACnC;EACA,IAAIzC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC+B,MAAM,CAAC/B,WAAW;EAClC;EACA;AACJ;AACA;EACI,IAAIwG,UAAUA,CAAC/D,KAAK,EAAE;IAClB,IAAI,CAACsD,WAAW,GAAGtD,KAAK;IACxB,IAAI,CAACV,MAAM,CAACb,gBAAgB,CAACuF,IAAI,CAAChE,KAAK,CAAC;IACxC,IAAIA,KAAK,EAAE;MACP,IAAI,CAACiE,IAAI,CAAC,CAAC;IACf;EACJ;EACA,IAAIF,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACT,WAAW;EAC3B;EACA;AACJ;AACA;EACI,IAAI3D,MAAMA,CAAA,EAAG;IACT,IAAI,IAAI,CAACuE,WAAW,EAAE;MAClB,OAAO,IAAI,CAACb,aAAa;IAC7B;IACA,OAAO,IAAI,CAACI,SAAS,CAACU,OAAO;EACjC;EACA,IAAIxE,MAAMA,CAACK,KAAK,EAAE;IACd,IAAIA,KAAK,EAAE;MACP,IAAI,CAACoE,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD,IAAI,CAACH,IAAI,CAAC,CAAC;IACf;EACJ;EACA,IAAII,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC5H,KAAK,CAAC,CAAC;EACnB;EACA,IAAIyH,WAAWA,CAAA,EAAG;IACd,OAAO,CAAC,IAAI,CAACI,SAAS;EAC1B;EACAC,QAAQA,CAAA,EAAG;IACP;IACA;IACA;IACA,IAAI,IAAI,CAACf,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,SAAS,CAACe,MAAM,CAAC;MAClB;MACAC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBN,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACA,IAAI,CAAC;IAC1B,CAAC,CAAC;IACF;IACA,IAAI,CAACb,cAAc,CAACoB,IAAI,CAAC,IAAI,CAACrF,MAAM,CAACZ,WAAW,CAACqB,SAAS,CAAEC,KAAK,IAAK,IAAI,CAAC4E,MAAM,CAAC5E,KAAK,CAAC,CAAC,CAAC;IAC1F;IACA,IAAI,CAACuD,cAAc,CAACoB,IAAI,CAAC,IAAI,CAACrF,MAAM,CAACb,gBAAgB,CAChDoG,IAAI,CAACvI,MAAM,CAAE0D,KAAK,IAAKA,KAAK,CAAC,CAAC,CAC9BD,SAAS,CAAC,CAAE,uBAAuB,IAAI,CAACkE,IAAI,CAAC,CAAC,CAAC,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACIG,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACzE,MAAM,IAAI,IAAI,CAACoE,UAAU,EAAE;MAChC;IACJ;IACA,IAAI,IAAI,CAACG,WAAW,EAAE;MAClB,IAAI,CAAC,IAAI,CAACY,YAAY,EAAE;QACpB,IAAI,CAACxF,MAAM,CAACV,YAAY,CAACmG,IAAI,CAAEnG,YAAY,IAAK;UAC5C,IAAI,CAAC6E,SAAS,CAACuB,YAAY,CAACpG,YAAY,CAACqG,aAAa,EAAErG,YAAY,CAACsG,WAAW,CAAC;UACjF,IAAI,CAACJ,YAAY,GAAG,IAAI,CAACrB,SAAS,CAAC0B,cAAc;UACjD,IAAI,CAACC,eAAe,CAAC,CAAC;UACtB,IAAI,IAAI,CAACN,YAAY,EAAE;YACnB,IAAI,CAACtF,SAAS,CAACW,QAAQ,CAAC,IAAI,CAAC2E,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,CAACC,UAAU,EAAE,MAAM,CAAC;UAC9E;UACA,IAAI,CAACC,aAAa,CAAC,CAAC;QACxB,CAAC;QACG;QAAA,CACCC,KAAK,CAAC,CAAC;MAChB;MACA,IAAI,CAACJ,eAAe,CAAC,CAAC;MACtB,IAAI,CAAC/B,aAAa,GAAG,IAAI;MACzB,IAAI,CAACQ,OAAO,CAACG,IAAI,CAAC,IAAI,CAAC;MACvB,IAAI,CAAC1E,MAAM,CAACd,YAAY,CAACwF,IAAI,CAAC,IAAI,CAAC;MACnC,IAAI,CAACuB,aAAa,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACjG,MAAM,CAACV,YAAY,CAACmG,IAAI,CAACnG,YAAY,IAAI;MAC1C;MACA,MAAM6G,OAAO,GAAG,IAAI,CAACrC,MAAM,IACtB,OAAO,IAAI,CAACA,MAAM,KAAK,WAAW,IAAI,IAAI,CAACA,MAAO;MACvD,IAAI,CAAC9D,MAAM,CAACf,SAAS,GAAGkH,OAAO,GAAG,IAAI,GAAG,MAAM;MAC/C,MAAMC,UAAU,GAAG,IAAI,CAACC,SAAS,KAAKF,OAAO,GAAG,WAAW,GAAG,cAAc,CAAC;MAC7E;MACA,IAAI,CAAChC,SAAS,CACTmC,MAAM,CAACvG,4BAA4B,CAAC,CACpCwG,EAAE,CAAC,IAAI,CAACvB,SAAS,CAAC,CAClBwB,QAAQ,CAAC;QAAEC,UAAU,EAAEL;MAAW,CAAC,CAAC,CACpCtB,IAAI,CAAC;QACN4B,OAAO,EAAEpH,YAAY,CAACsG,WAAW;QACjCS,SAAS,EAAED;MACf,CAAC,CAAC;MACF,IAAI,CAACpG,MAAM,CAACd,YAAY,CAACwF,IAAI,CAAC,IAAI,CAAC;IACvC,CAAC;IACG;IAAA,CACCwB,KAAK,CAAC,CAAC;EAChB;EACA;AACJ;AACA;AACA;EACIvB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACtE,MAAM,EAAE;MACd;IACJ;IACA,IAAI,IAAI,CAACuE,WAAW,EAAE;MAClB,IAAI,CAAC+B,eAAe,CAAC,CAAC;MACtB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC7C,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACS,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAACP,SAAS,CAACQ,IAAI,CAAC,CAAC;IACzB;IACA,IAAI,CAAC3E,MAAM,CAACd,YAAY,CAACwF,IAAI,CAAC,KAAK,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACIY,MAAMA,CAAC5E,KAAK,EAAE;IACV,IAAI,IAAI,CAACL,MAAM,IAAI,CAACK,KAAK,EAAE;MACvB,OAAO,IAAI,CAACiE,IAAI,CAAC,CAAC;IACtB;IACA,OAAO,IAAI,CAACG,IAAI,CAAC,CAAC;EACtB;EACA;EACAzD,SAASA,CAACwF,KAAK,EAAE;IACb;IACA,OAAO,IAAI,CAACnD,WAAW,CAAC/C,aAAa,CAACI,QAAQ,CAAC8F,KAAK,CAACC,MAAM,CAAC,IACvD,IAAI,CAAC3C,SAAS,CAAC4C,QAAQ,IAAI,IAAI,CAAC5C,SAAS,CAAC4C,QAAQ,CAAC1F,SAAS,CAACwF,KAAK,CAACC,MAAM,CAAE;EACpF;EACAE,eAAeA,CAACH,KAAK,EAAE;IACnB,MAAMI,GAAG,GAAG,IAAI,CAACvD,WAAW,CAAC/C,aAAa,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAC1E,IAAI,CAACqG,GAAG,EAAE;MACN;IACJ;IACA,MAAMC,WAAW,GAAG,IAAI,CAACxD,WAAW,CAAC/C,aAAa,CAACwG,aAAa,CAACC,aAAa;IAC9E,MAAMC,MAAM,GAAGJ,GAAG,CAACK,gBAAgB,CAAC,gBAAgB,CAAC;IACrD,QAAQT,KAAK,CAACU,OAAO;MACjB,KAAK,EAAE;QACH,IAAI,IAAI,CAACvH,MAAM,CAACX,MAAM,GAAG,CAAC,EAAE;UACxBgI,MAAM,CAAC,EAAE,IAAI,CAACrH,MAAM,CAACX,MAAM,CAAC,CAACmI,KAAK,CAAC,CAAC;QACxC;QACA;MACJ,KAAK,EAAE;QACH,IAAI,IAAI,CAACxH,MAAM,CAACX,MAAM,GAAG,CAAC,GAAGgI,MAAM,CAACI,MAAM,EAAE;UACxC,IAAIP,WAAW,CAACpG,SAAS,KAAKuG,MAAM,CAAC,IAAI,CAACrH,MAAM,CAACX,MAAM,CAAC,CAACyB,SAAS,EAAE;YAChEuG,MAAM,CAAC,IAAI,CAACrH,MAAM,CAACX,MAAM,CAAC,CAACmI,KAAK,CAAC,CAAC;UACtC,CAAC,MACI;YACDH,MAAM,CAAC,EAAE,IAAI,CAACrH,MAAM,CAACX,MAAM,CAAC,CAACmI,KAAK,CAAC,CAAC;UACxC;QACJ;QACA;MACJ;IACJ;IACAX,KAAK,CAACa,cAAc,CAAC,CAAC;EAC1B;EACAnG,WAAWA,CAAA,EAAG;IACV;IACA,KAAK,MAAMoG,GAAG,IAAI,IAAI,CAAC1D,cAAc,EAAE;MACnC0D,GAAG,CAACnG,WAAW,CAAC,CAAC;IACrB;IACA,IAAI,CAAC2C,SAAS,CAACyD,OAAO,CAAC,CAAC;EAC5B;EACA9B,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC3I,KAAK,CAAC,CAAC,EAAE;MACV,IAAI,CAAC0K,YAAY,CAAC,CAAC;MACnB,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;EACJ;EACA9B,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACjG,MAAM,CAAC9B,UAAU,IAAI,IAAI,CAACsH,YAAY,EAAE;MAC7CwC,UAAU,CAAC,MAAM;QACb,IAAI,IAAI,CAACxC,YAAY,EAAE;UACnB,IAAI,CAAClF,yBAAyB,CAACW,MAAM,CAAC,IAAI,CAACuE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC7E,IAAI,CAAC,CAAC;QAChF;MACJ,CAAC,CAAC;IACN;EACJ;EACA2G,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACrC,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD,IAAI,CAAC7F,SAAS,CAACW,QAAQ,CAAC,IAAI,CAAC2E,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;IACnE;EACJ;EACAY,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACnB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD,IAAI,CAAC7F,SAAS,CAAC+H,WAAW,CAAC,IAAI,CAACzC,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;IACtE;EACJ;EACA+B,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACtC,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD,MAAMmC,cAAc,GAAG,IAAI,CAAC1C,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,CAACjF,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAACyE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,CAACjF,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC;MACzK,IAAI,CAACb,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACwE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,EAAEmC,cAAc,GAAG,MAAM,GAAG,GAAG,CAAC;MAC9F,IAAI,CAAChI,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACwE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,EAAEmC,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC;IACnG;EACJ;EACAH,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACvC,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD;MACA,IAAI,CAAC7F,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACwE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAACjC,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;MAC7F,IAAI,CAAC5D,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACwE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,CAACjC,MAAM,GAAG,mBAAmB,GAAG,eAAe,CAAC;MACzH,IAAI,CAAC5D,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACwE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC;IAC7E;EACJ;EACAa,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACpB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD,IAAI,CAAC7F,SAAS,CAACiI,WAAW,CAAC,IAAI,CAAC3C,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;MACjE,IAAI,CAAC7F,SAAS,CAACiI,WAAW,CAAC,IAAI,CAAC3C,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC;MACvE,IAAI,CAAC7F,SAAS,CAACiI,WAAW,CAAC,IAAI,CAAC3C,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;IACxE;EACJ;AACJ;AACAtC,mBAAmB,CAACrF,IAAI,YAAAgK,4BAAA9J,iBAAA;EAAA,YAAAA,iBAAA,IAAwFmF,mBAAmB,EA1YhCpH,EAAE,CAAAqF,iBAAA,CA0YgDrF,EAAE,CAACwF,UAAU,GA1Y/DxF,EAAE,CAAAqF,iBAAA,CA0Y0ErF,EAAE,CAACuF,SAAS,GA1YxFvF,EAAE,CAAAqF,iBAAA,CA0YmGrF,EAAE,CAACgM,gBAAgB,GA1YxHhM,EAAE,CAAAqF,iBAAA,CA0YmIzE,EAAE,CAACC,sBAAsB,GA1Y9Jb,EAAE,CAAAqF,iBAAA,CA0YyK1C,eAAe,GA1Y1L3C,EAAE,CAAAqF,iBAAA,CA0YqM5D,gBAAgB,GA1YvNzB,EAAE,CAAAqF,iBAAA,CA0YkOtE,EAAE,CAAC0E,gBAAgB;AAAA,CAA4C;AACtY2B,mBAAmB,CAAC6E,IAAI,kBA3Y2EjM,EAAE,CAAAkM,iBAAA;EAAAzJ,IAAA,EA2YD2E,mBAAmB;EAAAxB,SAAA;EAAAuG,QAAA;EAAAC,YAAA,WAAAC,iCAAAjG,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3YpBpG,EAAE,CAAAsM,UAAA,+BAAAC,yDAAAC,MAAA;QAAA,OA2YDnG,GAAA,CAAAsE,eAAA,CAAA6B,MAAsB,CAAC;MAAA,CAAL,CAAC,6BAAAC,uDAAAD,MAAA;QAAA,OAAnBnG,GAAA,CAAAsE,eAAA,CAAA6B,MAAsB,CAAC;MAAA,CAAL,CAAC;IAAA;IAAA,IAAApG,EAAA;MA3YpBpG,EAAE,CAAA0G,WAAA,WAAAL,GAAA,CAAAoB,MA2YiB,CAAC,SAAApB,GAAA,CAAArC,MAAD,CAAC,SAAAqC,GAAA,CAAArC,MAAA,IAAAqC,GAAA,CAAAqC,KAAD,CAAC;IAAA;EAAA;EAAAgE,MAAA;IAAA1C,SAAA;IAAAjB,QAAA;IAAAJ,SAAA;IAAAlB,MAAA;IAAA9F,SAAA;IAAAE,UAAA;IAAAD,WAAA;IAAAwG,UAAA;IAAApE,MAAA;EAAA;EAAA2I,OAAA;IAAA9J,YAAA;IAAAqF,OAAA;IAAAC,QAAA;EAAA;EAAAyE,QAAA;EAAAC,QAAA,GA3YpB7M,EAAE,CAAA8M,kBAAA,CA2YklB,CAACnK,eAAe,CAAC;AAAA,EAA4C;AACpvB;EAAA,QAAAJ,SAAA,oBAAAA,SAAA,KA5YmGvC,EAAE,CAAAwC,iBAAA,CA4YV4E,mBAAmB,EAAc,CAAC;IACjH3E,IAAI,EAAEpC,SAAS;IACfqC,IAAI,EAAE,CAAC;MACCuE,QAAQ,EAAE,0BAA0B;MACpC2F,QAAQ,EAAE,aAAa;MACvBG,SAAS,EAAE,CAACpK,eAAe,CAAC;MAC5B;MACAwE,IAAI,EAAE;QACF,gBAAgB,EAAE,QAAQ;QAC1B,cAAc,EAAE,QAAQ;QACxB,cAAc,EAAE;MACpB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE1E,IAAI,EAAEzC,EAAE,CAACwF;IAAW,CAAC,EAAE;MAAE/C,IAAI,EAAEzC,EAAE,CAACuF;IAAU,CAAC,EAAE;MAAE9C,IAAI,EAAEzC,EAAE,CAACgM;IAAiB,CAAC,EAAE;MAAEvJ,IAAI,EAAE7B,EAAE,CAACC;IAAuB,CAAC,EAAE;MAAE4B,IAAI,EAAEE;IAAgB,CAAC,EAAE;MAAEF,IAAI,EAAEhB;IAAiB,CAAC,EAAE;MAAEgB,IAAI,EAAE1B,EAAE,CAAC0E;IAAiB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEuE,SAAS,EAAE,CAAC;MAC/QvH,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEyI,QAAQ,EAAE,CAAC;MACXtG,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEqI,SAAS,EAAE,CAAC;MACZlG,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEmH,MAAM,EAAE,CAAC;MACThF,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEqB,SAAS,EAAE,CAAC;MACZc,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEuB,UAAU,EAAE,CAAC;MACbY,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEsB,WAAW,EAAE,CAAC;MACda,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAE8H,UAAU,EAAE,CAAC;MACb3F,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAE0D,MAAM,EAAE,CAAC;MACTvB,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEuC,YAAY,EAAE,CAAC;MACfJ,IAAI,EAAElC;IACV,CAAC,CAAC;IAAE2H,OAAO,EAAE,CAAC;MACVzF,IAAI,EAAElC;IACV,CAAC,CAAC;IAAE4H,QAAQ,EAAE,CAAC;MACX1F,IAAI,EAAElC;IACV,CAAC,CAAC;IAAEoK,eAAe,EAAE,CAAC;MAClBlI,IAAI,EAAEjC,YAAY;MAClBkC,IAAI,EAAE,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC;IAC1C,CAAC,EAAE;MACCD,IAAI,EAAEjC,YAAY;MAClBkC,IAAI,EAAE,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC;IACxC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsK,uBAAuB,CAAC;EAC1BtL,WAAWA,CAACiC,MAAM,EAAEsJ,cAAc,EAAEC,YAAY,EAAE;IAC9CvJ,MAAM,CAACP,mBAAmB,CAAC;MACvBmG,WAAW,EAAE2D,YAAY;MACzB5D,aAAa,EAAE2D;IACnB,CAAC,CAAC;EACN;AACJ;AACAD,uBAAuB,CAACjL,IAAI,YAAAoL,gCAAAlL,iBAAA;EAAA,YAAAA,iBAAA,IAAwF+K,uBAAuB,EAjcxChN,EAAE,CAAAqF,iBAAA,CAicwD1C,eAAe,GAjczE3C,EAAE,CAAAqF,iBAAA,CAicoFrF,EAAE,CAACgM,gBAAgB,GAjczGhM,EAAE,CAAAqF,iBAAA,CAicoHrF,EAAE,CAACoN,WAAW;AAAA,CAA4C;AACnRJ,uBAAuB,CAACf,IAAI,kBAlcuEjM,EAAE,CAAAkM,iBAAA;EAAAzJ,IAAA,EAkcGuK,uBAAuB;EAAApH,SAAA;EAAAgH,QAAA;AAAA,EAA8F;AAC7N;EAAA,QAAArK,SAAA,oBAAAA,SAAA,KAncmGvC,EAAE,CAAAwC,iBAAA,CAmcVwK,uBAAuB,EAAc,CAAC;IACrHvK,IAAI,EAAEpC,SAAS;IACfqC,IAAI,EAAE,CAAC;MACCuE,QAAQ,EAAE,iCAAiC;MAC3C2F,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEnK,IAAI,EAAEE;IAAgB,CAAC,EAAE;MAAEF,IAAI,EAAEzC,EAAE,CAACgM;IAAiB,CAAC,EAAE;MAAEvJ,IAAI,EAAEzC,EAAE,CAACoN;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;AAExI,MAAMC,yBAAyB,CAAC;EAC5B3L,WAAWA,CAAC4L,kBAAkB,EAAExF,SAAS,EAAEhE,QAAQ,EAAED,SAAS,EAAEF,MAAM,EAAE;IACpE,IAAI,CAAC2J,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACxF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAChE,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACK,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC4D,cAAc,GAAG,EAAE;IACxB;IACA,IAAI,CAACA,cAAc,CAACoB,IAAI,CAAC,IAAI,CAACrF,MAAM,CAACd,YAAY,CAACuB,SAAS,CAAEC,KAAK,IAAK;MACnE,IAAI,CAACL,MAAM,GAAGK,KAAK;MACnB,IAAIA,KAAK,EAAE;QACP,IAAI,CAACkJ,sBAAsB,GAAG,IAAI,CAAC1J,SAAS,CAACgF,MAAM,CAAC,UAAU,EAAE,OAAO,EAAG2B,KAAK,IAAK;UAChF,IAAI,IAAI,CAAC7G,MAAM,CAAChC,SAAS,IAAI6I,KAAK,CAACgD,MAAM,KAAK,CAAC,IAC3C,CAAC,IAAI,CAAC1J,QAAQ,CAACQ,aAAa,CAACI,QAAQ,CAAC8F,KAAK,CAACC,MAAM,CAAC,IACnD,EAAE,IAAI,CAAC9G,MAAM,CAAC/B,WAAW,IAAI,IAAI,CAACkG,SAAS,CAAC9C,SAAS,CAACwF,KAAK,CAAC,CAAC,EAAE;YAC/D,IAAI,CAAC7G,MAAM,CAACZ,WAAW,CAACsF,IAAI,CAAC,KAAK,CAAC;YACnC,IAAI,CAACiF,kBAAkB,CAACvI,aAAa,CAAC,CAAC;UAC3C;QACJ,CAAC,CAAC;QACF,IAAI,CAAC0I,iBAAiB,GAAG,IAAI,CAAC5J,SAAS,CAACgF,MAAM,CAAC,IAAI,CAAC/E,QAAQ,CAACQ,aAAa,EAAE,WAAW,EAAE,MAAM;UAC3F,IAAI,IAAI,CAACX,MAAM,CAAChC,SAAS,EAAE;YACvB,IAAI,CAACgC,MAAM,CAACZ,WAAW,CAACsF,IAAI,CAAC,KAAK,CAAC;YACnC,IAAI,CAACiF,kBAAkB,CAACvI,aAAa,CAAC,CAAC;UAC3C;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACwI,sBAAsB,IAAI,IAAI,CAACA,sBAAsB,CAAC,CAAC;QAC5D,IAAI,CAACE,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC,CAAC;IACH;IACA,IAAI,CAAC7F,cAAc,CAACoB,IAAI,CAAC,IAAI,CAACrF,MAAM,CAACb,gBAAgB,CAChDsB,SAAS,CAAEC,KAAK,IAAK,IAAI,CAAC+D,UAAU,GAAG/D,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;EACjE;EACAqJ,OAAOA,CAAClD,KAAK,EAAE;IACX,IAAI,IAAI,CAAC7G,MAAM,CAAC7B,sBAAsB,EAAE;MACpC0I,KAAK,CAACmD,eAAe,CAAC,CAAC;IAC3B;IACA,IAAI,IAAI,CAACvF,UAAU,EAAE;MACjB;IACJ;IACA,IAAI,CAACzE,MAAM,CAACZ,WAAW,CAACsF,IAAI,CAAC,IAAI,CAAC;EACtC;EACAnD,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACqI,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;IACA,IAAI,IAAI,CAACE,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;IAC5B;IACA,KAAK,MAAMnC,GAAG,IAAI,IAAI,CAAC1D,cAAc,EAAE;MACnC0D,GAAG,CAACnG,WAAW,CAAC,CAAC;IACrB;EACJ;AACJ;AACAkI,yBAAyB,CAACtL,IAAI,YAAA6L,kCAAA3L,iBAAA;EAAA,YAAAA,iBAAA,IAAwFoL,yBAAyB,EArgB5CrN,EAAE,CAAAqF,iBAAA,CAqgB4DrF,EAAE,CAACsF,iBAAiB,GArgBlFtF,EAAE,CAAAqF,iBAAA,CAqgB6F+B,mBAAmB,GArgBlHpH,EAAE,CAAAqF,iBAAA,CAqgB6HrF,EAAE,CAACwF,UAAU,GArgB5IxF,EAAE,CAAAqF,iBAAA,CAqgBuJrF,EAAE,CAACuF,SAAS,GArgBrKvF,EAAE,CAAAqF,iBAAA,CAqgBgL1C,eAAe;AAAA,CAA4C;AAChV0K,yBAAyB,CAACpB,IAAI,kBAtgBqEjM,EAAE,CAAAkM,iBAAA;EAAAzJ,IAAA,EAsgBK4K,yBAAyB;EAAAzH,SAAA;EAAAuG,QAAA;EAAAC,YAAA,WAAAyB,uCAAAzH,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAtgBhCpG,EAAE,CAAAsM,UAAA,mBAAAwB,mDAAAtB,MAAA;QAAA,OAsgBKnG,GAAA,CAAAqH,OAAA,CAAAlB,MAAc,CAAC;MAAA,CAAS,CAAC;IAAA;IAAA,IAAApG,EAAA;MAtgBhCpG,EAAE,CAAA+N,WAAA,kBAsgBK,IAAI,cAAA1H,GAAA,CAAA+B,UAAA,mBAAA/B,GAAA,CAAArC,MAAA;IAAA;EAAA;EAAA4I,QAAA;AAAA,EAAqS;AACnZ;EAAA,QAAArK,SAAA,oBAAAA,SAAA,KAvgBmGvC,EAAE,CAAAwC,iBAAA,CAugBV6K,yBAAyB,EAAc,CAAC;IACvH5K,IAAI,EAAEpC,SAAS;IACfqC,IAAI,EAAE,CAAC;MACCuE,QAAQ,EAAE,qCAAqC;MAC/C2F,QAAQ,EAAE,oBAAoB;MAC9B;MACAzF,IAAI,EAAE;QACF,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE1E,IAAI,EAAEzC,EAAE,CAACsF;IAAkB,CAAC,EAAE;MAAE7C,IAAI,EAAE2E;IAAoB,CAAC,EAAE;MAAE3E,IAAI,EAAEzC,EAAE,CAACwF;IAAW,CAAC,EAAE;MAAE/C,IAAI,EAAEzC,EAAE,CAACuF;IAAU,CAAC,EAAE;MAAE9C,IAAI,EAAEE;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEyF,UAAU,EAAE,CAAC;MAChN3F,IAAI,EAAEhC,WAAW;MACjBiC,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEsB,MAAM,EAAE,CAAC;MACTvB,IAAI,EAAEhC,WAAW;MACjBiC,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEgL,OAAO,EAAE,CAAC;MACVjL,IAAI,EAAEjC,YAAY;MAClBkC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsL,gBAAgB,CAAC;EACnB,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,gBAAgB;MAC1BjB,SAAS,EAAE,CACPlM,sBAAsB,EACtBO,kBAAkB,EAClBuB,eAAe;IAEvB,CAAC;EACL;AACJ;AACAqL,gBAAgB,CAACjM,IAAI,YAAAoM,yBAAAlM,iBAAA;EAAA,YAAAA,iBAAA,IAAwF+L,gBAAgB;AAAA,CAAkD;AAC/KA,gBAAgB,CAACI,IAAI,kBAziB8EpO,EAAE,CAAAqO,gBAAA;EAAA5L,IAAA,EAyiBSuL;AAAgB,EAK/F;AAC/BA,gBAAgB,CAACM,IAAI,kBA/iB8EtO,EAAE,CAAAuO,gBAAA;EAAAC,OAAA,GA+iBqCrN,YAAY;AAAA,EAAI;AAC1J;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KAhjBmGvC,EAAE,CAAAwC,iBAAA,CAgjBVwL,gBAAgB,EAAc,CAAC;IAC9GvL,IAAI,EAAE/B,QAAQ;IACdgC,IAAI,EAAE,CAAC;MACC8L,OAAO,EAAE,CAACrN,YAAY,CAAC;MACvBsN,YAAY,EAAE,CACVzB,uBAAuB,EACvBK,yBAAyB,EACzB3J,4BAA4B,EAC5B0D,mBAAmB,CACtB;MACDsH,OAAO,EAAE,CACL1B,uBAAuB,EACvBK,yBAAyB,EACzBjG,mBAAmB;IAE3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS3F,gBAAgB,EAAEiC,4BAA4B,EAAE0D,mBAAmB,EAAE4F,uBAAuB,EAAEgB,gBAAgB,EAAErL,eAAe,EAAE0K,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}