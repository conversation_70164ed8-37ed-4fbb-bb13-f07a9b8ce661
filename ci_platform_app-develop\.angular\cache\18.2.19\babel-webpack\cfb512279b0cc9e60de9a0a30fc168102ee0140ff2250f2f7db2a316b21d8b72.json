{"ast": null, "code": "import windowViewport from './window-viewport';\nconst boundingOffset = element => {\n  if (!element.getBoundingClientRect) {\n    const viewport = windowViewport(element);\n    return {\n      bottom: viewport.height,\n      left: 0,\n      right: viewport.width,\n      top: 0\n    };\n  }\n  const {\n    bottom,\n    left,\n    right,\n    top\n  } = element.getBoundingClientRect();\n  return {\n    bottom,\n    left,\n    right,\n    top\n  };\n};\nexport default boundingOffset;", "map": {"version": 3, "names": ["windowViewport", "boundingOffset", "element", "getBoundingClientRect", "viewport", "bottom", "height", "left", "right", "width", "top"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/bounding-offset.js"], "sourcesContent": ["import windowViewport from './window-viewport';\n\nconst boundingOffset = (element) => {\n    if (!element.getBoundingClientRect) {\n        const viewport = windowViewport(element);\n        return {\n            bottom: viewport.height,\n            left: 0,\n            right: viewport.width,\n            top: 0\n        };\n    }\n\n    const { bottom, left, right, top } = element.getBoundingClientRect();\n\n    return {\n        bottom,\n        left,\n        right,\n        top\n    };\n};\n\nexport default boundingOffset;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,mBAAmB;AAE9C,MAAMC,cAAc,GAAIC,OAAO,IAAK;EAChC,IAAI,CAACA,OAAO,CAACC,qBAAqB,EAAE;IAChC,MAAMC,QAAQ,GAAGJ,cAAc,CAACE,OAAO,CAAC;IACxC,OAAO;MACHG,MAAM,EAAED,QAAQ,CAACE,MAAM;MACvBC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAEJ,QAAQ,CAACK,KAAK;MACrBC,GAAG,EAAE;IACT,CAAC;EACL;EAEA,MAAM;IAAEL,MAAM;IAAEE,IAAI;IAAEC,KAAK;IAAEE;EAAI,CAAC,GAAGR,OAAO,CAACC,qBAAqB,CAAC,CAAC;EAEpE,OAAO;IACHE,MAAM;IACNE,IAAI;IACJC,KAAK;IACLE;EACJ,CAAC;AACL,CAAC;AAED,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}