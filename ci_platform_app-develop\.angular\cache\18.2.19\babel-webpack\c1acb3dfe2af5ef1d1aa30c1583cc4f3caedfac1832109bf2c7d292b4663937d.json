{"ast": null, "code": "import { fieldList } from './field-list';\nconst setterCache = {};\n// tslint:disable-next-line:no-string-literal\nsetterCache['undefined'] = obj => obj;\nconst defaultValue = (nextField, options) => options && options.arrays && !isNaN(Number(nextField)) ? [] : {};\n/**\n * @hidden\n */\nexport function setter(field) {\n  if (setterCache[field]) {\n    return setterCache[field];\n  }\n  const fields = fieldList(field);\n  setterCache[field] = (obj, value, options) => {\n    let root = obj;\n    const depth = fields.length - 1;\n    for (let idx = 0; idx < depth && root; idx++) {\n      root = root[fields[idx]] = root[fields[idx]] || defaultValue(fields[idx + 1], options);\n    }\n    root[fields[depth]] = value;\n  };\n  return setterCache[field];\n}", "map": {"version": 3, "names": ["fieldList", "setter<PERSON><PERSON>", "obj", "defaultValue", "nextField", "options", "arrays", "isNaN", "Number", "setter", "field", "fields", "value", "root", "depth", "length", "idx"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/@progress/kendo-common/dist/es2015/accessors/setter.js"], "sourcesContent": ["import { fieldList } from './field-list';\nconst setterCache = {};\n// tslint:disable-next-line:no-string-literal\nsetterCache['undefined'] = obj => obj;\nconst defaultValue = (nextField, options) => options && options.arrays && !isNaN(Number(nextField)) ? [] : {};\n/**\n * @hidden\n */\nexport function setter(field) {\n    if (setterCache[field]) {\n        return setterCache[field];\n    }\n    const fields = fieldList(field);\n    setterCache[field] = (obj, value, options) => {\n        let root = obj;\n        const depth = fields.length - 1;\n        for (let idx = 0; idx < depth && root; idx++) {\n            root = root[fields[idx]] = root[fields[idx]] || defaultValue(fields[idx + 1], options);\n        }\n        root[fields[depth]] = value;\n    };\n    return setterCache[field];\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,MAAMC,WAAW,GAAG,CAAC,CAAC;AACtB;AACAA,WAAW,CAAC,WAAW,CAAC,GAAGC,GAAG,IAAIA,GAAG;AACrC,MAAMC,YAAY,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAKA,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAI,CAACC,KAAK,CAACC,MAAM,CAACJ,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC7G;AACA;AACA;AACA,OAAO,SAASK,MAAMA,CAACC,KAAK,EAAE;EAC1B,IAAIT,WAAW,CAACS,KAAK,CAAC,EAAE;IACpB,OAAOT,WAAW,CAACS,KAAK,CAAC;EAC7B;EACA,MAAMC,MAAM,GAAGX,SAAS,CAACU,KAAK,CAAC;EAC/BT,WAAW,CAACS,KAAK,CAAC,GAAG,CAACR,GAAG,EAAEU,KAAK,EAAEP,OAAO,KAAK;IAC1C,IAAIQ,IAAI,GAAGX,GAAG;IACd,MAAMY,KAAK,GAAGH,MAAM,CAACI,MAAM,GAAG,CAAC;IAC/B,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,KAAK,IAAID,IAAI,EAAEG,GAAG,EAAE,EAAE;MAC1CH,IAAI,GAAGA,IAAI,CAACF,MAAM,CAACK,GAAG,CAAC,CAAC,GAAGH,IAAI,CAACF,MAAM,CAACK,GAAG,CAAC,CAAC,IAAIb,YAAY,CAACQ,MAAM,CAACK,GAAG,GAAG,CAAC,CAAC,EAAEX,OAAO,CAAC;IAC1F;IACAQ,IAAI,CAACF,MAAM,CAACG,KAAK,CAAC,CAAC,GAAGF,KAAK;EAC/B,CAAC;EACD,OAAOX,WAAW,CAACS,KAAK,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}