{"ast": null, "code": "import { RouterOutlet } from \"@angular/router\";\nimport { NgToastModule } from \"ng-angular-popup\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ng-angular-popup\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'FrontEnd';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵelement(1, \"router-outlet\")(2, \"lib-ng-toast\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [RouterOutlet, NgToastModule, i1.NgToastComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJhcHAuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLDRKQUE0SiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterOutlet", "NgToastModule", "AppComponent", "constructor", "title", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "i1", "NgToastComponent", "styles"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\app.component.ts", "B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, CUSTOM_ELEMENTS_SCHEMA } from \"@angular/core\"\nimport { RouterOutlet } from \"@angular/router\";\nimport { NgToastModule } from \"ng-angular-popup\";\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [RouterOutlet, NgToastModule],\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css'],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\n})\nexport class AppComponent {\n  title = 'FrontEnd';\n}\n", "<div>\n  <router-outlet></router-outlet>\n  <lib-ng-toast></lib-ng-toast>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,kBAAkB;;;AAUhD,OAAM,MAAOC,YAAY;EARzBC,YAAA;IASE,KAAAC,KAAK,GAAG,UAAU;;;;uCADPF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZzBN,EAAA,CAAAQ,cAAA,UAAK;UAEHR,EADA,CAAAS,SAAA,oBAA+B,mBACF;UAC/BT,EAAA,CAAAU,YAAA,EAAM;;;qBDIMlB,YAAY,EAAEC,aAAa,EAAAkB,EAAA,CAAAC,gBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}