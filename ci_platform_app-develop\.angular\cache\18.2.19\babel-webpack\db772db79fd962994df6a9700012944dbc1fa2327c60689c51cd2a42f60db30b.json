{"ast": null, "code": "export default function ownerDocument(element) {\n  return element.ownerDocument || element.document || element;\n}", "map": {"version": 3, "names": ["ownerDocument", "element", "document"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/owner-document.js"], "sourcesContent": ["export default function ownerDocument(element) {\n    return element.ownerDocument || element.document || element;\n}\n"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,OAAO,EAAE;EAC3C,OAAOA,OAAO,CAACD,aAAa,IAAIC,OAAO,CAACC,QAAQ,IAAID,OAAO;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}