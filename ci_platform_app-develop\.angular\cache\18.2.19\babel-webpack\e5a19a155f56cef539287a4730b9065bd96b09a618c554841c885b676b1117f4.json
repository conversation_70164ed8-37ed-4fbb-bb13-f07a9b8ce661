{"ast": null, "code": "import { environment } from \"../../../environments/environment\";\nexport const API_BASE_URL = environment.apiBaseUrl;\nexport const IMAGE_BASE_URL = environment.apiBaseUrl;\nexport const APP_CONFIG = {\n  apiBaseUrl: `${API_BASE_URL}/api`,\n  imageBaseUrl: IMAGE_BASE_URL,\n  tokenKey: \"access_Token\",\n  defaultPageSize: 10,\n  toastDuration: 3000\n};", "map": {"version": 3, "names": ["environment", "API_BASE_URL", "apiBaseUrl", "IMAGE_BASE_URL", "APP_CONFIG", "imageBaseUrl", "<PERSON><PERSON><PERSON>", "defaultPageSize", "toastDuration"], "sources": ["C:\\Users\\<USER>\\Downloads\\ci_platfrom_app-develop\\ci_platfrom_app-develop\\src\\app\\main\\configs\\environment.config.ts"], "sourcesContent": ["import { environment } from \"../../../environments/environment\"\n\nexport const API_BASE_URL = environment.apiBaseUrl\nexport const IMAGE_BASE_URL = environment.apiBaseUrl\n\nexport const APP_CONFIG = {\n  apiBaseUrl: `${API_BASE_URL}/api`,\n  imageBaseUrl: IMAGE_BASE_URL,\n  tokenKey: \"access_Token\",\n  defaultPageSize: 10,\n  toastDuration: 3000,\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mCAAmC;AAE/D,OAAO,MAAMC,YAAY,GAAGD,WAAW,CAACE,UAAU;AAClD,OAAO,MAAMC,cAAc,GAAGH,WAAW,CAACE,UAAU;AAEpD,OAAO,MAAME,UAAU,GAAG;EACxBF,UAAU,EAAE,GAAGD,YAAY,MAAM;EACjCI,YAAY,EAAEF,cAAc;EAC5BG,QAAQ,EAAE,cAAc;EACxBC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE;CAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}