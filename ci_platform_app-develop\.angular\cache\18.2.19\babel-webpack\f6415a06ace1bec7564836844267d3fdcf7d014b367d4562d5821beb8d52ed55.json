{"ast": null, "code": "import offsetParent from './offset-parent';\nimport elementScrollPosition from './element-scroll-position';\nexport default function parentScrollPosition(element) {\n  const parent = offsetParent(element);\n  return parent ? elementScrollPosition(parent) : {\n    x: 0,\n    y: 0\n  };\n}", "map": {"version": 3, "names": ["offsetParent", "elementScrollPosition", "parentScrollPosition", "element", "parent", "x", "y"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/parent-scroll-position.js"], "sourcesContent": ["import offsetParent from './offset-parent';\nimport elementScrollPosition from './element-scroll-position';\n\nexport default function parentScrollPosition(element) {\n    const parent = offsetParent(element);\n\n    return parent ? elementScrollPosition(parent) : { x: 0, y: 0 };\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,qBAAqB,MAAM,2BAA2B;AAE7D,eAAe,SAASC,oBAAoBA,CAACC,OAAO,EAAE;EAClD,MAAMC,MAAM,GAAGJ,YAAY,CAACG,OAAO,CAAC;EAEpC,OAAOC,MAAM,GAAGH,qBAAqB,CAACG,MAAM,CAAC,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}