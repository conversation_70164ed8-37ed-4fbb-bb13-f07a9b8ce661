{"ast": null, "code": "export default function applyLocationOffset(rect, location, isOffsetBody) {\n  let {\n    top,\n    left\n  } = rect;\n  if (isOffsetBody) {\n    left = 0;\n    top = 0;\n  }\n  return {\n    top: top + location.top,\n    left: left + location.left,\n    height: rect.height,\n    width: rect.width\n  };\n}", "map": {"version": 3, "names": ["applyLocationOffset", "rect", "location", "isOffsetBody", "top", "left", "height", "width"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/apply-location-offset.js"], "sourcesContent": ["export default function applyLocationOffset(rect, location, isOffsetBody) {\n    let { top, left } = rect;\n\n    if (isOffsetBody) {\n        left = 0;\n        top = 0;\n    }\n\n    return {\n        top: top + location.top,\n        left: left + location.left,\n        height: rect.height,\n        width: rect.width\n    };\n}\n"], "mappings": "AAAA,eAAe,SAASA,mBAAmBA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EACtE,IAAI;IAAEC,GAAG;IAAEC;EAAK,CAAC,GAAGJ,IAAI;EAExB,IAAIE,YAAY,EAAE;IACdE,IAAI,GAAG,CAAC;IACRD,GAAG,GAAG,CAAC;EACX;EAEA,OAAO;IACHA,GAAG,EAAEA,GAAG,GAAGF,QAAQ,CAACE,GAAG;IACvBC,IAAI,EAAEA,IAAI,GAAGH,QAAQ,CAACG,IAAI;IAC1BC,MAAM,EAAEL,IAAI,CAACK,MAAM;IACnBC,KAAK,EAAEN,IAAI,CAACM;EAChB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}