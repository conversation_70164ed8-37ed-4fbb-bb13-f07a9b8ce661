{"ast": null, "code": "const eitherRect = (rect, offset) => {\n  if (!rect) {\n    return {\n      height: 0,\n      left: offset.left,\n      top: offset.top,\n      width: 0\n    };\n  }\n  return rect;\n};\nconst scaleRect = (rect, scale) => {\n  if (!rect || scale === 1) {\n    return rect;\n  }\n  return {\n    height: rect.height / scale,\n    left: rect.left / scale,\n    top: rect.top / scale,\n    width: rect.width / scale\n  };\n};\nconst removeStackingOffset = (rect, stackingOffset) => {\n  if (!stackingOffset) {\n    return rect;\n  }\n  const result = {\n    height: rect.height,\n    left: rect.left - stackingOffset.left,\n    top: rect.top - stackingOffset.top,\n    width: rect.width\n  };\n  return result;\n};\nfunction memoize(fun) {\n  let result;\n  let called = false;\n  return (...args) => {\n    if (called) {\n      return result;\n    }\n    result = fun(...args);\n    called = true;\n    return result;\n  };\n}\nconst hasRelativeStackingContext = memoize(elementSource => {\n  if (!canUseDOM()) {\n    return false;\n  }\n\n  // Component need to pass element to make sure document owner is correct.\n  // This however might be performance hit if checked for example on each drag event.\n  const currentDocument = elementSource ? elementSource.ownerDocument : document;\n  if (!currentDocument || !currentDocument.body) {\n    return false;\n  }\n  const top = 10;\n  const parent = currentDocument.createElement(\"div\");\n  parent.style.transform = \"matrix(10, 0, 0, 10, 0, 0)\";\n  parent.innerHTML = `<div style=\"position: fixed; top: ${top}px;\">child</div>`;\n  currentDocument.body.appendChild(parent);\n  const isDifferent = parent.children[0].getBoundingClientRect().top !== top;\n  currentDocument.body.removeChild(parent);\n  return isDifferent;\n});\nconst canUseDOM = () => Boolean(\n// from fbjs\ntypeof window !== 'undefined' && window.document && window.document.createElement);\nconst utils = {\n  eitherRect,\n  scaleRect,\n  removeStackingOffset,\n  hasRelativeStackingContext,\n  canUseDOM\n};\nexport default utils;", "map": {"version": 3, "names": ["eitherRect", "rect", "offset", "height", "left", "top", "width", "scaleRect", "scale", "removeStackingOffset", "stackingOffset", "result", "memoize", "fun", "called", "args", "hasRelativeStackingContext", "elementSource", "canUseDOM", "currentDocument", "ownerDocument", "document", "body", "parent", "createElement", "style", "transform", "innerHTML", "append<PERSON><PERSON><PERSON>", "isDifferent", "children", "getBoundingClientRect", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "window", "utils"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/utils.js"], "sourcesContent": ["\nconst eitherRect = (rect, offset) => {\n    if (!rect) {\n        return { height: 0, left: offset.left, top: offset.top, width: 0 };\n    }\n\n    return rect;\n};\n\nconst scaleRect = (rect, scale) => {\n    if (!rect || scale === 1) {\n        return rect;\n    }\n\n    return {\n        height: rect.height / scale,\n        left: rect.left / scale,\n        top: rect.top / scale,\n        width: rect.width / scale\n    };\n};\n\nconst removeStackingOffset = (rect, stackingOffset) => {\n    if (!stackingOffset) { return rect; }\n\n    const result = {\n        height: rect.height,\n        left: rect.left - stackingOffset.left,\n        top: rect.top - stackingOffset.top,\n        width: rect.width\n    };\n\n    return result;\n};\n\nfunction memoize(fun) {\n    let result;\n    let called = false;\n\n    return (...args) => {\n        if (called) {\n            return result;\n        }\n\n        result = fun(...args);\n        called = true;\n        return result;\n    };\n}\n\nconst hasRelativeStackingContext = memoize((elementSource) => {\n    if (!canUseDOM()) { return false; }\n\n    // Component need to pass element to make sure document owner is correct.\n    // This however might be performance hit if checked for example on each drag event.\n    const currentDocument = elementSource ? elementSource.ownerDocument : document;\n\n    if (!currentDocument || !currentDocument.body) { return false; }\n\n    const top = 10;\n    const parent = currentDocument.createElement(\"div\");\n    parent.style.transform = \"matrix(10, 0, 0, 10, 0, 0)\";\n    parent.innerHTML = `<div style=\"position: fixed; top: ${top}px;\">child</div>`;\n\n    currentDocument.body.appendChild(parent);\n\n    const isDifferent = parent.children[0].getBoundingClientRect().top !== top;\n\n    currentDocument.body.removeChild(parent);\n\n    return isDifferent;\n});\n\nconst canUseDOM = () => Boolean(\n    // from fbjs\n    typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement\n);\n\nconst utils = {\n    eitherRect,\n    scaleRect,\n    removeStackingOffset,\n    hasRelativeStackingContext,\n    canUseDOM\n};\n\nexport default utils;"], "mappings": "AACA,MAAMA,UAAU,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;EACjC,IAAI,CAACD,IAAI,EAAE;IACP,OAAO;MAAEE,MAAM,EAAE,CAAC;MAAEC,IAAI,EAAEF,MAAM,CAACE,IAAI;MAAEC,GAAG,EAAEH,MAAM,CAACG,GAAG;MAAEC,KAAK,EAAE;IAAE,CAAC;EACtE;EAEA,OAAOL,IAAI;AACf,CAAC;AAED,MAAMM,SAAS,GAAGA,CAACN,IAAI,EAAEO,KAAK,KAAK;EAC/B,IAAI,CAACP,IAAI,IAAIO,KAAK,KAAK,CAAC,EAAE;IACtB,OAAOP,IAAI;EACf;EAEA,OAAO;IACHE,MAAM,EAAEF,IAAI,CAACE,MAAM,GAAGK,KAAK;IAC3BJ,IAAI,EAAEH,IAAI,CAACG,IAAI,GAAGI,KAAK;IACvBH,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGG,KAAK;IACrBF,KAAK,EAAEL,IAAI,CAACK,KAAK,GAAGE;EACxB,CAAC;AACL,CAAC;AAED,MAAMC,oBAAoB,GAAGA,CAACR,IAAI,EAAES,cAAc,KAAK;EACnD,IAAI,CAACA,cAAc,EAAE;IAAE,OAAOT,IAAI;EAAE;EAEpC,MAAMU,MAAM,GAAG;IACXR,MAAM,EAAEF,IAAI,CAACE,MAAM;IACnBC,IAAI,EAAEH,IAAI,CAACG,IAAI,GAAGM,cAAc,CAACN,IAAI;IACrCC,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGK,cAAc,CAACL,GAAG;IAClCC,KAAK,EAAEL,IAAI,CAACK;EAChB,CAAC;EAED,OAAOK,MAAM;AACjB,CAAC;AAED,SAASC,OAAOA,CAACC,GAAG,EAAE;EAClB,IAAIF,MAAM;EACV,IAAIG,MAAM,GAAG,KAAK;EAElB,OAAO,CAAC,GAAGC,IAAI,KAAK;IAChB,IAAID,MAAM,EAAE;MACR,OAAOH,MAAM;IACjB;IAEAA,MAAM,GAAGE,GAAG,CAAC,GAAGE,IAAI,CAAC;IACrBD,MAAM,GAAG,IAAI;IACb,OAAOH,MAAM;EACjB,CAAC;AACL;AAEA,MAAMK,0BAA0B,GAAGJ,OAAO,CAAEK,aAAa,IAAK;EAC1D,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;;EAElC;EACA;EACA,MAAMC,eAAe,GAAGF,aAAa,GAAGA,aAAa,CAACG,aAAa,GAAGC,QAAQ;EAE9E,IAAI,CAACF,eAAe,IAAI,CAACA,eAAe,CAACG,IAAI,EAAE;IAAE,OAAO,KAAK;EAAE;EAE/D,MAAMjB,GAAG,GAAG,EAAE;EACd,MAAMkB,MAAM,GAAGJ,eAAe,CAACK,aAAa,CAAC,KAAK,CAAC;EACnDD,MAAM,CAACE,KAAK,CAACC,SAAS,GAAG,4BAA4B;EACrDH,MAAM,CAACI,SAAS,GAAG,qCAAqCtB,GAAG,kBAAkB;EAE7Ec,eAAe,CAACG,IAAI,CAACM,WAAW,CAACL,MAAM,CAAC;EAExC,MAAMM,WAAW,GAAGN,MAAM,CAACO,QAAQ,CAAC,CAAC,CAAC,CAACC,qBAAqB,CAAC,CAAC,CAAC1B,GAAG,KAAKA,GAAG;EAE1Ec,eAAe,CAACG,IAAI,CAACU,WAAW,CAACT,MAAM,CAAC;EAExC,OAAOM,WAAW;AACtB,CAAC,CAAC;AAEF,MAAMX,SAAS,GAAGA,CAAA,KAAMe,OAAO;AAC3B;AACA,OAAOC,MAAM,KAAK,WAAW,IAC7BA,MAAM,CAACb,QAAQ,IACfa,MAAM,CAACb,QAAQ,CAACG,aACpB,CAAC;AAED,MAAMW,KAAK,GAAG;EACVnC,UAAU;EACVO,SAAS;EACTE,oBAAoB;EACpBO,0BAA0B;EAC1BE;AACJ,CAAC;AAED,eAAeiB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}