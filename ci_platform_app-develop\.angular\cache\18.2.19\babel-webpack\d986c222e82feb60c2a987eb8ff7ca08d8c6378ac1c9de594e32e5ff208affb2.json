{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Pipe, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nfunction PaginationControlsComponent_ul_3_li_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_1_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.previous());\n    })(\"click\", function PaginationControlsComponent_ul_3_li_1_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.previous());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previousLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previousLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_1_a_1_Template, 4, 2, \"a\", 10)(2, PaginationControlsComponent_ul_3_li_1_span_2_Template, 4, 2, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"disabled\", p_r3.isFirstPage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", 1 < p_r3.getCurrent());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.isFirstPage());\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_4_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_4_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const page_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵnextContext(2);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.setCurrent(page_r6.value));\n    })(\"click\", function PaginationControlsComponent_ul_3_li_4_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const page_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵnextContext(2);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.setCurrent(page_r6.value));\n    });\n    i0.ɵɵelementStart(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.screenReaderPageLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r6.label === \"...\" ? page_r6.label : i0.ɵɵpipeBind2(5, 2, page_r6.label, \"\"));\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 16)(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const page_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.screenReaderCurrentLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r6.label === \"...\" ? page_r6.label : i0.ɵɵpipeBind2(6, 2, page_r6.label, \"\"));\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_4_a_1_Template, 6, 5, \"a\", 10)(2, PaginationControlsComponent_ul_3_li_4_ng_container_2_Template, 7, 5, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r6 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"current\", p_r3.getCurrent() === page_r6.value)(\"ellipsis\", page_r6.label === \"...\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.getCurrent() !== page_r6.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.getCurrent() === page_r6.value);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_5_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_5_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.next());\n    })(\"click\", function PaginationControlsComponent_ul_3_li_5_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.next());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.nextLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.nextLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 17);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_5_a_1_Template, 4, 2, \"a\", 10)(2, PaginationControlsComponent_ul_3_li_5_span_2_Template, 4, 2, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"disabled\", p_r3.isLastPage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !p_r3.isLastPage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.isLastPage());\n  }\n}\nfunction PaginationControlsComponent_ul_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 4);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_1_Template, 3, 4, \"li\", 5);\n    i0.ɵɵelementStart(2, \"li\", 6);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PaginationControlsComponent_ul_3_li_4_Template, 3, 6, \"li\", 7)(5, PaginationControlsComponent_ul_3_li_5_Template, 3, 4, \"li\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"responsive\", ctx_r3.responsive);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.directionLinks);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", p_r3.getCurrent(), \" / \", p_r3.getLastPage(), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", p_r3.pages)(\"ngForTrackBy\", ctx_r3.trackByIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.directionLinks);\n  }\n}\nclass PaginationService {\n  constructor() {\n    this.change = new EventEmitter();\n    this.instances = {};\n    this.DEFAULT_ID = 'DEFAULT_PAGINATION_ID';\n  }\n  defaultId() {\n    return this.DEFAULT_ID;\n  }\n  /**\r\n   * Register a PaginationInstance with this service. Returns a\r\n   * boolean value signifying whether the instance is new or\r\n   * updated (true = new or updated, false = unchanged).\r\n   */\n  register(instance) {\n    if (instance.id == null) {\n      instance.id = this.DEFAULT_ID;\n    }\n    if (!this.instances[instance.id]) {\n      this.instances[instance.id] = instance;\n      return true;\n    } else {\n      return this.updateInstance(instance);\n    }\n  }\n  /**\r\n   * Check each property of the instance and update any that have changed. Return\r\n   * true if any changes were made, else return false.\r\n   */\n  updateInstance(instance) {\n    let changed = false;\n    for (let prop in this.instances[instance.id]) {\n      if (instance[prop] !== this.instances[instance.id][prop]) {\n        this.instances[instance.id][prop] = instance[prop];\n        changed = true;\n      }\n    }\n    return changed;\n  }\n  /**\r\n   * Returns the current page number.\r\n   */\n  getCurrentPage(id) {\n    if (this.instances[id]) {\n      return this.instances[id].currentPage;\n    }\n    return 1;\n  }\n  /**\r\n   * Sets the current page number.\r\n   */\n  setCurrentPage(id, page) {\n    if (this.instances[id]) {\n      let instance = this.instances[id];\n      let maxPage = Math.ceil(instance.totalItems / instance.itemsPerPage);\n      if (page <= maxPage && 1 <= page) {\n        this.instances[id].currentPage = page;\n        this.change.emit(id);\n      }\n    }\n  }\n  /**\r\n   * Sets the value of instance.totalItems\r\n   */\n  setTotalItems(id, totalItems) {\n    if (this.instances[id] && 0 <= totalItems) {\n      this.instances[id].totalItems = totalItems;\n      this.change.emit(id);\n    }\n  }\n  /**\r\n   * Sets the value of instance.itemsPerPage.\r\n   */\n  setItemsPerPage(id, itemsPerPage) {\n    if (this.instances[id]) {\n      this.instances[id].itemsPerPage = itemsPerPage;\n      this.change.emit(id);\n    }\n  }\n  /**\r\n   * Returns a clone of the pagination instance object matching the id. If no\r\n   * id specified, returns the instance corresponding to the default id.\r\n   */\n  getInstance(id = this.DEFAULT_ID) {\n    if (this.instances[id]) {\n      return this.clone(this.instances[id]);\n    }\n    return {};\n  }\n  /**\r\n   * Perform a shallow clone of an object.\r\n   */\n  clone(obj) {\n    var target = {};\n    for (var i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        target[i] = obj[i];\n      }\n    }\n    return target;\n  }\n}\nconst LARGE_NUMBER = Number.MAX_SAFE_INTEGER;\nclass PaginatePipe {\n  constructor(service) {\n    this.service = service;\n    // store the values from the last time the pipe was invoked\n    this.state = {};\n  }\n  transform(collection, args) {\n    // When an observable is passed through the AsyncPipe, it will output\n    // `null` until the subscription resolves. In this case, we want to\n    // use the cached data from the `state` object to prevent the NgFor\n    // from flashing empty until the real values arrive.\n    if (!(collection instanceof Array)) {\n      let _id = args.id || this.service.defaultId();\n      if (this.state[_id]) {\n        return this.state[_id].slice;\n      } else {\n        return collection;\n      }\n    }\n    let serverSideMode = args.totalItems && args.totalItems !== collection.length;\n    let instance = this.createInstance(collection, args);\n    let id = instance.id;\n    let start, end;\n    let perPage = instance.itemsPerPage;\n    let emitChange = this.service.register(instance);\n    if (!serverSideMode && collection instanceof Array) {\n      perPage = +perPage || LARGE_NUMBER;\n      start = (instance.currentPage - 1) * perPage;\n      end = start + perPage;\n      let isIdentical = this.stateIsIdentical(id, collection, start, end);\n      if (isIdentical) {\n        return this.state[id].slice;\n      } else {\n        let slice = collection.slice(start, end);\n        this.saveState(id, collection, slice, start, end);\n        this.service.change.emit(id);\n        return slice;\n      }\n    } else {\n      if (emitChange) {\n        this.service.change.emit(id);\n      }\n      // save the state for server-side collection to avoid null\n      // flash as new data loads.\n      this.saveState(id, collection, collection, start, end);\n      return collection;\n    }\n  }\n  /**\r\n   * Create an PaginationInstance object, using defaults for any optional properties not supplied.\r\n   */\n  createInstance(collection, config) {\n    this.checkConfig(config);\n    return {\n      id: config.id != null ? config.id : this.service.defaultId(),\n      itemsPerPage: +config.itemsPerPage || 0,\n      currentPage: +config.currentPage || 1,\n      totalItems: +config.totalItems || collection.length\n    };\n  }\n  /**\r\n   * Ensure the argument passed to the filter contains the required properties.\r\n   */\n  checkConfig(config) {\n    const required = ['itemsPerPage', 'currentPage'];\n    const missing = required.filter(prop => !(prop in config));\n    if (0 < missing.length) {\n      throw new Error(`PaginatePipe: Argument is missing the following required properties: ${missing.join(', ')}`);\n    }\n  }\n  /**\r\n   * To avoid returning a brand new array each time the pipe is run, we store the state of the sliced\r\n   * array for a given id. This means that the next time the pipe is run on this collection & id, we just\r\n   * need to check that the collection, start and end points are all identical, and if so, return the\r\n   * last sliced array.\r\n   */\n  saveState(id, collection, slice, start, end) {\n    this.state[id] = {\n      collection,\n      size: collection.length,\n      slice,\n      start,\n      end\n    };\n  }\n  /**\r\n   * For a given id, returns true if the collection, size, start and end values are identical.\r\n   */\n  stateIsIdentical(id, collection, start, end) {\n    let state = this.state[id];\n    if (!state) {\n      return false;\n    }\n    let isMetaDataIdentical = state.size === collection.length && state.start === start && state.end === end;\n    if (!isMetaDataIdentical) {\n      return false;\n    }\n    return state.slice.every((element, index) => element === collection[start + index]);\n  }\n}\nPaginatePipe.ɵfac = function PaginatePipe_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || PaginatePipe)(i0.ɵɵdirectiveInject(PaginationService, 16));\n};\nPaginatePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"paginate\",\n  type: PaginatePipe,\n  pure: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PaginatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'paginate',\n      pure: false\n    }]\n  }], function () {\n    return [{\n      type: PaginationService\n    }];\n  }, null);\n})();\n\n/**\r\n * The default template and styles for the pagination links are borrowed directly\r\n * from Zurb Foundation 6: http://foundation.zurb.com/sites/docs/pagination.html\r\n */\nconst DEFAULT_TEMPLATE = `\n    <pagination-template  #p=\"paginationApi\"\n                         [id]=\"id\"\n                         [maxSize]=\"maxSize\"\n                         (pageChange)=\"pageChange.emit($event)\"\n                         (pageBoundsCorrection)=\"pageBoundsCorrection.emit($event)\">\n    <nav role=\"navigation\" [attr.aria-label]=\"screenReaderPaginationLabel\">\n    <ul class=\"ngx-pagination\" \n        [class.responsive]=\"responsive\"\n        *ngIf=\"!(autoHide && p.pages.length <= 1)\">\n\n        <li class=\"pagination-previous\" [class.disabled]=\"p.isFirstPage()\" *ngIf=\"directionLinks\"> \n            <a tabindex=\"0\" *ngIf=\"1 < p.getCurrent()\" (keyup.enter)=\"p.previous()\" (click)=\"p.previous()\">\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </a>\n            <span *ngIf=\"p.isFirstPage()\" aria-disabled=\"true\">\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </span>\n        </li> \n\n        <li class=\"small-screen\">\n            {{ p.getCurrent() }} / {{ p.getLastPage() }}\n        </li>\n\n        <li [class.current]=\"p.getCurrent() === page.value\" \n            [class.ellipsis]=\"page.label === '...'\"\n            *ngFor=\"let page of p.pages; trackBy: trackByIndex\">\n            <a tabindex=\"0\" (keyup.enter)=\"p.setCurrent(page.value)\" (click)=\"p.setCurrent(page.value)\" *ngIf=\"p.getCurrent() !== page.value\">\n                <span class=\"show-for-sr\">{{ screenReaderPageLabel }} </span>\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span>\n            </a>\n            <ng-container *ngIf=\"p.getCurrent() === page.value\">\n              <span aria-live=\"polite\">\n                <span class=\"show-for-sr\">{{ screenReaderCurrentLabel }} </span>\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span> \n              </span>\n            </ng-container>\n        </li>\n\n        <li class=\"pagination-next\" [class.disabled]=\"p.isLastPage()\" *ngIf=\"directionLinks\">\n            <a tabindex=\"0\" *ngIf=\"!p.isLastPage()\" (keyup.enter)=\"p.next()\" (click)=\"p.next()\">\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </a>\n            <span *ngIf=\"p.isLastPage()\" aria-disabled=\"true\">\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </span>\n        </li>\n\n    </ul>\n    </nav>\n    </pagination-template>\n    `;\nconst DEFAULT_STYLES = `\n.ngx-pagination {\n  margin-left: 0;\n  margin-bottom: 1rem; }\n  .ngx-pagination::before, .ngx-pagination::after {\n    content: ' ';\n    display: table; }\n  .ngx-pagination::after {\n    clear: both; }\n  .ngx-pagination li {\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    -ms-user-select: none;\n    margin-right: 0.0625rem;\n    border-radius: 0; }\n  .ngx-pagination li {\n    display: inline-block; }\n  .ngx-pagination a,\n  .ngx-pagination button {\n    color: #0a0a0a; \n    display: block;\n    padding: 0.1875rem 0.625rem;\n    border-radius: 0; }\n    .ngx-pagination a:hover,\n    .ngx-pagination button:hover {\n      background: #e6e6e6; }\n  .ngx-pagination .current {\n    padding: 0.1875rem 0.625rem;\n    background: #2199e8;\n    color: #fefefe;\n    cursor: default; }\n  .ngx-pagination .disabled {\n    padding: 0.1875rem 0.625rem;\n    color: #cacaca;\n    cursor: default; } \n    .ngx-pagination .disabled:hover {\n      background: transparent; }\n  .ngx-pagination a, .ngx-pagination button {\n    cursor: pointer; }\n\n.ngx-pagination .pagination-previous a::before,\n.ngx-pagination .pagination-previous.disabled::before { \n  content: '«';\n  display: inline-block;\n  margin-right: 0.5rem; }\n\n.ngx-pagination .pagination-next a::after,\n.ngx-pagination .pagination-next.disabled::after {\n  content: '»';\n  display: inline-block;\n  margin-left: 0.5rem; }\n\n.ngx-pagination .show-for-sr {\n  position: absolute !important;\n  width: 1px;\n  height: 1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0); }\n.ngx-pagination .small-screen {\n  display: none; }\n@media screen and (max-width: 601px) {\n  .ngx-pagination.responsive .small-screen {\n    display: inline-block; } \n  .ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next) {\n    display: none; }\n}\n  `;\n\n/**\r\n * This directive is what powers all pagination controls components, including the default one.\r\n * It exposes an API which is hooked up to the PaginationService to keep the PaginatePipe in sync\r\n * with the pagination controls.\r\n */\nclass PaginationControlsDirective {\n  constructor(service, changeDetectorRef) {\n    this.service = service;\n    this.changeDetectorRef = changeDetectorRef;\n    this.maxSize = 7;\n    this.pageChange = new EventEmitter();\n    this.pageBoundsCorrection = new EventEmitter();\n    this.pages = [];\n    this.changeSub = this.service.change.subscribe(id => {\n      if (this.id === id) {\n        this.updatePageLinks();\n        this.changeDetectorRef.markForCheck();\n        this.changeDetectorRef.detectChanges();\n      }\n    });\n  }\n  ngOnInit() {\n    if (this.id === undefined) {\n      this.id = this.service.defaultId();\n    }\n    this.updatePageLinks();\n  }\n  ngOnChanges(changes) {\n    this.updatePageLinks();\n  }\n  ngOnDestroy() {\n    this.changeSub.unsubscribe();\n  }\n  /**\r\n   * Go to the previous page\r\n   */\n  previous() {\n    this.checkValidId();\n    this.setCurrent(this.getCurrent() - 1);\n  }\n  /**\r\n   * Go to the next page\r\n   */\n  next() {\n    this.checkValidId();\n    this.setCurrent(this.getCurrent() + 1);\n  }\n  /**\r\n   * Returns true if current page is first page\r\n   */\n  isFirstPage() {\n    return this.getCurrent() === 1;\n  }\n  /**\r\n   * Returns true if current page is last page\r\n   */\n  isLastPage() {\n    return this.getLastPage() === this.getCurrent();\n  }\n  /**\r\n   * Set the current page number.\r\n   */\n  setCurrent(page) {\n    this.pageChange.emit(page);\n  }\n  /**\r\n   * Get the current page number.\r\n   */\n  getCurrent() {\n    return this.service.getCurrentPage(this.id);\n  }\n  /**\r\n   * Returns the last page number\r\n   */\n  getLastPage() {\n    let inst = this.service.getInstance(this.id);\n    if (inst.totalItems < 1) {\n      // when there are 0 or fewer (an error case) items, there are no \"pages\" as such,\n      // but it makes sense to consider a single, empty page as the last page.\n      return 1;\n    }\n    return Math.ceil(inst.totalItems / inst.itemsPerPage);\n  }\n  getTotalItems() {\n    return this.service.getInstance(this.id).totalItems;\n  }\n  checkValidId() {\n    if (this.service.getInstance(this.id).id == null) {\n      console.warn(`PaginationControlsDirective: the specified id \"${this.id}\" does not match any registered PaginationInstance`);\n    }\n  }\n  /**\r\n   * Updates the page links and checks that the current page is valid. Should run whenever the\r\n   * PaginationService.change stream emits a value matching the current ID, or when any of the\r\n   * input values changes.\r\n   */\n  updatePageLinks() {\n    let inst = this.service.getInstance(this.id);\n    const correctedCurrentPage = this.outOfBoundCorrection(inst);\n    if (correctedCurrentPage !== inst.currentPage) {\n      setTimeout(() => {\n        this.pageBoundsCorrection.emit(correctedCurrentPage);\n        this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\n      });\n    } else {\n      this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\n    }\n  }\n  /**\r\n   * Checks that the instance.currentPage property is within bounds for the current page range.\r\n   * If not, return a correct value for currentPage, or the current value if OK.\r\n   */\n  outOfBoundCorrection(instance) {\n    const totalPages = Math.ceil(instance.totalItems / instance.itemsPerPage);\n    if (totalPages < instance.currentPage && 0 < totalPages) {\n      return totalPages;\n    } else if (instance.currentPage < 1) {\n      return 1;\n    }\n    return instance.currentPage;\n  }\n  /**\r\n   * Returns an array of Page objects to use in the pagination controls.\r\n   */\n  createPageArray(currentPage, itemsPerPage, totalItems, paginationRange) {\n    // paginationRange could be a string if passed from attribute, so cast to number.\n    paginationRange = +paginationRange;\n    let pages = [];\n    // Return 1 as default page number\n    // Make sense to show 1 instead of empty when there are no items\n    const totalPages = Math.max(Math.ceil(totalItems / itemsPerPage), 1);\n    const halfWay = Math.ceil(paginationRange / 2);\n    const isStart = currentPage <= halfWay;\n    const isEnd = totalPages - halfWay < currentPage;\n    const isMiddle = !isStart && !isEnd;\n    let ellipsesNeeded = paginationRange < totalPages;\n    let i = 1;\n    while (i <= totalPages && i <= paginationRange) {\n      let label;\n      let pageNumber = this.calculatePageNumber(i, currentPage, paginationRange, totalPages);\n      let openingEllipsesNeeded = i === 2 && (isMiddle || isEnd);\n      let closingEllipsesNeeded = i === paginationRange - 1 && (isMiddle || isStart);\n      if (ellipsesNeeded && (openingEllipsesNeeded || closingEllipsesNeeded)) {\n        label = '...';\n      } else {\n        label = pageNumber;\n      }\n      pages.push({\n        label: label,\n        value: pageNumber\n      });\n      i++;\n    }\n    return pages;\n  }\n  /**\r\n   * Given the position in the sequence of pagination links [i],\r\n   * figure out what page number corresponds to that position.\r\n   */\n  calculatePageNumber(i, currentPage, paginationRange, totalPages) {\n    let halfWay = Math.ceil(paginationRange / 2);\n    if (i === paginationRange) {\n      return totalPages;\n    } else if (i === 1) {\n      return i;\n    } else if (paginationRange < totalPages) {\n      if (totalPages - halfWay < currentPage) {\n        return totalPages - paginationRange + i;\n      } else if (halfWay < currentPage) {\n        return currentPage - halfWay + i;\n      } else {\n        return i;\n      }\n    } else {\n      return i;\n    }\n  }\n}\nPaginationControlsDirective.ɵfac = function PaginationControlsDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || PaginationControlsDirective)(i0.ɵɵdirectiveInject(PaginationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nPaginationControlsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: PaginationControlsDirective,\n  selectors: [[\"pagination-template\"], [\"\", \"pagination-template\", \"\"]],\n  inputs: {\n    id: \"id\",\n    maxSize: \"maxSize\"\n  },\n  outputs: {\n    pageChange: \"pageChange\",\n    pageBoundsCorrection: \"pageBoundsCorrection\"\n  },\n  exportAs: [\"paginationApi\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PaginationControlsDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'pagination-template,[pagination-template]',\n      exportAs: 'paginationApi'\n    }]\n  }], function () {\n    return [{\n      type: PaginationService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    id: [{\n      type: Input\n    }],\n    maxSize: [{\n      type: Input\n    }],\n    pageChange: [{\n      type: Output\n    }],\n    pageBoundsCorrection: [{\n      type: Output\n    }]\n  });\n})();\nfunction coerceToBoolean(input) {\n  return !!input && input !== 'false';\n}\n/**\r\n * The default pagination controls component. Actually just a default implementation of a custom template.\r\n */\nclass PaginationControlsComponent {\n  constructor() {\n    this.maxSize = 7;\n    this.previousLabel = 'Previous';\n    this.nextLabel = 'Next';\n    this.screenReaderPaginationLabel = 'Pagination';\n    this.screenReaderPageLabel = 'page';\n    this.screenReaderCurrentLabel = `You're on page`;\n    this.pageChange = new EventEmitter();\n    this.pageBoundsCorrection = new EventEmitter();\n    this._directionLinks = true;\n    this._autoHide = false;\n    this._responsive = false;\n  }\n  get directionLinks() {\n    return this._directionLinks;\n  }\n  set directionLinks(value) {\n    this._directionLinks = coerceToBoolean(value);\n  }\n  get autoHide() {\n    return this._autoHide;\n  }\n  set autoHide(value) {\n    this._autoHide = coerceToBoolean(value);\n  }\n  get responsive() {\n    return this._responsive;\n  }\n  set responsive(value) {\n    this._responsive = coerceToBoolean(value);\n  }\n  trackByIndex(index) {\n    return index;\n  }\n}\nPaginationControlsComponent.ɵfac = function PaginationControlsComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || PaginationControlsComponent)();\n};\nPaginationControlsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: PaginationControlsComponent,\n  selectors: [[\"pagination-controls\"]],\n  inputs: {\n    id: \"id\",\n    maxSize: \"maxSize\",\n    directionLinks: \"directionLinks\",\n    autoHide: \"autoHide\",\n    responsive: \"responsive\",\n    previousLabel: \"previousLabel\",\n    nextLabel: \"nextLabel\",\n    screenReaderPaginationLabel: \"screenReaderPaginationLabel\",\n    screenReaderPageLabel: \"screenReaderPageLabel\",\n    screenReaderCurrentLabel: \"screenReaderCurrentLabel\"\n  },\n  outputs: {\n    pageChange: \"pageChange\",\n    pageBoundsCorrection: \"pageBoundsCorrection\"\n  },\n  decls: 4,\n  vars: 4,\n  consts: [[\"p\", \"paginationApi\"], [3, \"pageChange\", \"pageBoundsCorrection\", \"id\", \"maxSize\"], [\"role\", \"navigation\"], [\"class\", \"ngx-pagination\", 3, \"responsive\", 4, \"ngIf\"], [1, \"ngx-pagination\"], [\"class\", \"pagination-previous\", 3, \"disabled\", 4, \"ngIf\"], [1, \"small-screen\"], [3, \"current\", \"ellipsis\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-next\", 3, \"disabled\", 4, \"ngIf\"], [1, \"pagination-previous\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\", 4, \"ngIf\"], [\"aria-disabled\", \"true\", 4, \"ngIf\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\"], [1, \"show-for-sr\"], [\"aria-disabled\", \"true\"], [4, \"ngIf\"], [\"aria-live\", \"polite\"], [1, \"pagination-next\"]],\n  template: function PaginationControlsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵelementStart(0, \"pagination-template\", 1, 0);\n      i0.ɵɵlistener(\"pageChange\", function PaginationControlsComponent_Template_pagination_template_pageChange_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.pageChange.emit($event));\n      })(\"pageBoundsCorrection\", function PaginationControlsComponent_Template_pagination_template_pageBoundsCorrection_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.pageBoundsCorrection.emit($event));\n      });\n      i0.ɵɵelementStart(2, \"nav\", 2);\n      i0.ɵɵtemplate(3, PaginationControlsComponent_ul_3_Template, 6, 8, \"ul\", 3);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      const p_r3 = i0.ɵɵreference(1);\n      i0.ɵɵproperty(\"id\", ctx.id)(\"maxSize\", ctx.maxSize);\n      i0.ɵɵadvance(2);\n      i0.ɵɵattribute(\"aria-label\", ctx.screenReaderPaginationLabel);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !(ctx.autoHide && p_r3.pages.length <= 1));\n    }\n  },\n  dependencies: [PaginationControlsDirective, i2.NgIf, i2.NgForOf, i2.DecimalPipe],\n  styles: [\".ngx-pagination{margin-left:0;margin-bottom:1rem}.ngx-pagination:before,.ngx-pagination:after{content:\\\" \\\";display:table}.ngx-pagination:after{clear:both}.ngx-pagination li{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;margin-right:.0625rem;border-radius:0}.ngx-pagination li{display:inline-block}.ngx-pagination a,.ngx-pagination button{color:#0a0a0a;display:block;padding:.1875rem .625rem;border-radius:0}.ngx-pagination a:hover,.ngx-pagination button:hover{background:#e6e6e6}.ngx-pagination .current{padding:.1875rem .625rem;background:#2199e8;color:#fefefe;cursor:default}.ngx-pagination .disabled{padding:.1875rem .625rem;color:#cacaca;cursor:default}.ngx-pagination .disabled:hover{background:transparent}.ngx-pagination a,.ngx-pagination button{cursor:pointer}.ngx-pagination .pagination-previous a:before,.ngx-pagination .pagination-previous.disabled:before{content:\\\"\\\\ab\\\";display:inline-block;margin-right:.5rem}.ngx-pagination .pagination-next a:after,.ngx-pagination .pagination-next.disabled:after{content:\\\"\\\\bb\\\";display:inline-block;margin-left:.5rem}.ngx-pagination .show-for-sr{position:absolute!important;width:1px;height:1px;overflow:hidden;clip:rect(0,0,0,0)}.ngx-pagination .small-screen{display:none}@media screen and (max-width: 601px){.ngx-pagination.responsive .small-screen{display:inline-block}.ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next){display:none}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PaginationControlsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'pagination-controls',\n      template: DEFAULT_TEMPLATE,\n      styles: [DEFAULT_STYLES],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }],\n    maxSize: [{\n      type: Input\n    }],\n    directionLinks: [{\n      type: Input\n    }],\n    autoHide: [{\n      type: Input\n    }],\n    responsive: [{\n      type: Input\n    }],\n    previousLabel: [{\n      type: Input\n    }],\n    nextLabel: [{\n      type: Input\n    }],\n    screenReaderPaginationLabel: [{\n      type: Input\n    }],\n    screenReaderPageLabel: [{\n      type: Input\n    }],\n    screenReaderCurrentLabel: [{\n      type: Input\n    }],\n    pageChange: [{\n      type: Output\n    }],\n    pageBoundsCorrection: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxPaginationModule {}\nNgxPaginationModule.ɵfac = function NgxPaginationModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxPaginationModule)();\n};\nNgxPaginationModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxPaginationModule\n});\nNgxPaginationModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [PaginationService],\n  imports: [[CommonModule]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxPaginationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective],\n      providers: [PaginationService],\n      exports: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective]\n    }]\n  }], null, null);\n})();\n\n/*\r\n * Public API Surface of ngx-pagination\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NgxPaginationModule, PaginatePipe, PaginationControlsComponent, PaginationControlsDirective, PaginationService };", "map": {"version": 3, "names": ["i0", "EventEmitter", "<PERSON><PERSON>", "Directive", "Input", "Output", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "NgModule", "i2", "CommonModule", "PaginationControlsComponent_ul_3_li_1_a_1_Template", "rf", "ctx", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "PaginationControlsComponent_ul_3_li_1_a_1_Template_a_keyup_enter_0_listener", "ɵɵrestoreView", "ɵɵnextContext", "p_r3", "ɵɵreference", "ɵɵresetView", "previous", "PaginationControlsComponent_ul_3_li_1_a_1_Template_a_click_0_listener", "ɵɵtext", "ɵɵelementEnd", "ctx_r3", "ɵɵadvance", "ɵɵtextInterpolate1", "previousLabel", "ɵɵtextInterpolate", "screenReaderPageLabel", "PaginationControlsComponent_ul_3_li_1_span_2_Template", "PaginationControlsComponent_ul_3_li_1_Template", "ɵɵtemplate", "ɵɵclassProp", "isFirstPage", "ɵɵproperty", "get<PERSON>urrent", "PaginationControlsComponent_ul_3_li_4_a_1_Template", "_r5", "PaginationControlsComponent_ul_3_li_4_a_1_Template_a_keyup_enter_0_listener", "page_r6", "$implicit", "setCurrent", "value", "PaginationControlsComponent_ul_3_li_4_a_1_Template_a_click_0_listener", "ɵɵpipe", "label", "ɵɵpipeBind2", "PaginationControlsComponent_ul_3_li_4_ng_container_2_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "screenReaderCurrentLabel", "PaginationControlsComponent_ul_3_li_4_Template", "PaginationControlsComponent_ul_3_li_5_a_1_Template", "_r7", "PaginationControlsComponent_ul_3_li_5_a_1_Template_a_keyup_enter_0_listener", "next", "PaginationControlsComponent_ul_3_li_5_a_1_Template_a_click_0_listener", "next<PERSON><PERSON><PERSON>", "PaginationControlsComponent_ul_3_li_5_span_2_Template", "PaginationControlsComponent_ul_3_li_5_Template", "isLastPage", "PaginationControlsComponent_ul_3_Template", "responsive", "directionLinks", "ɵɵtextInterpolate2", "getLastPage", "pages", "trackByIndex", "PaginationService", "constructor", "change", "instances", "DEFAULT_ID", "defaultId", "register", "instance", "id", "updateInstance", "changed", "prop", "getCurrentPage", "currentPage", "setCurrentPage", "page", "maxPage", "Math", "ceil", "totalItems", "itemsPerPage", "emit", "setTotalItems", "setItemsPerPage", "getInstance", "clone", "obj", "target", "i", "hasOwnProperty", "LARGE_NUMBER", "Number", "MAX_SAFE_INTEGER", "PaginatePipe", "service", "state", "transform", "collection", "args", "Array", "_id", "slice", "serverSideMode", "length", "createInstance", "start", "end", "perPage", "emitChange", "isIdentical", "stateIsIdentical", "saveState", "config", "checkConfig", "required", "missing", "filter", "Error", "join", "size", "isMetaDataIdentical", "every", "element", "index", "ɵfac", "PaginatePipe_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "ngDevMode", "ɵsetClassMetadata", "DEFAULT_TEMPLATE", "DEFAULT_STYLES", "PaginationControlsDirective", "changeDetectorRef", "maxSize", "pageChange", "pageBoundsCorrection", "changeSub", "subscribe", "updatePageLinks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "ngOnInit", "undefined", "ngOnChanges", "changes", "ngOnDestroy", "unsubscribe", "checkValidId", "inst", "getTotalItems", "console", "warn", "correctedCurrentPage", "outOfBoundCorrection", "setTimeout", "createPageArray", "totalPages", "paginationRange", "max", "halfWay", "isStart", "isEnd", "isMiddle", "ellipsesNeeded", "pageNumber", "calculatePageNumber", "openingEllipsesNeeded", "closingEllipsesNeeded", "push", "PaginationControlsDirective_Factory", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "outputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "selector", "coerceToBoolean", "input", "PaginationControlsComponent", "screenReaderPaginationLabel", "_directionLinks", "_autoHide", "_responsive", "autoHide", "PaginationControlsComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "decls", "vars", "consts", "template", "PaginationControlsComponent_Template", "_r1", "PaginationControlsComponent_Template_pagination_template_pageChange_0_listener", "$event", "PaginationControlsComponent_Template_pagination_template_pageBoundsCorrection_0_listener", "ɵɵattribute", "dependencies", "NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DecimalPipe", "styles", "encapsulation", "changeDetection", "OnPush", "None", "NgxPaginationModule", "NgxPaginationModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "declarations", "exports"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/ngx-pagination/fesm2020/ngx-pagination.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Pipe, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass PaginationService {\r\n    constructor() {\r\n        this.change = new EventEmitter();\r\n        this.instances = {};\r\n        this.DEFAULT_ID = 'DEFAULT_PAGINATION_ID';\r\n    }\r\n    defaultId() { return this.DEFAULT_ID; }\r\n    /**\r\n     * Register a PaginationInstance with this service. Returns a\r\n     * boolean value signifying whether the instance is new or\r\n     * updated (true = new or updated, false = unchanged).\r\n     */\r\n    register(instance) {\r\n        if (instance.id == null) {\r\n            instance.id = this.DEFAULT_ID;\r\n        }\r\n        if (!this.instances[instance.id]) {\r\n            this.instances[instance.id] = instance;\r\n            return true;\r\n        }\r\n        else {\r\n            return this.updateInstance(instance);\r\n        }\r\n    }\r\n    /**\r\n     * Check each property of the instance and update any that have changed. Return\r\n     * true if any changes were made, else return false.\r\n     */\r\n    updateInstance(instance) {\r\n        let changed = false;\r\n        for (let prop in this.instances[instance.id]) {\r\n            if (instance[prop] !== this.instances[instance.id][prop]) {\r\n                this.instances[instance.id][prop] = instance[prop];\r\n                changed = true;\r\n            }\r\n        }\r\n        return changed;\r\n    }\r\n    /**\r\n     * Returns the current page number.\r\n     */\r\n    getCurrentPage(id) {\r\n        if (this.instances[id]) {\r\n            return this.instances[id].currentPage;\r\n        }\r\n        return 1;\r\n    }\r\n    /**\r\n     * Sets the current page number.\r\n     */\r\n    setCurrentPage(id, page) {\r\n        if (this.instances[id]) {\r\n            let instance = this.instances[id];\r\n            let maxPage = Math.ceil(instance.totalItems / instance.itemsPerPage);\r\n            if (page <= maxPage && 1 <= page) {\r\n                this.instances[id].currentPage = page;\r\n                this.change.emit(id);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * Sets the value of instance.totalItems\r\n     */\r\n    setTotalItems(id, totalItems) {\r\n        if (this.instances[id] && 0 <= totalItems) {\r\n            this.instances[id].totalItems = totalItems;\r\n            this.change.emit(id);\r\n        }\r\n    }\r\n    /**\r\n     * Sets the value of instance.itemsPerPage.\r\n     */\r\n    setItemsPerPage(id, itemsPerPage) {\r\n        if (this.instances[id]) {\r\n            this.instances[id].itemsPerPage = itemsPerPage;\r\n            this.change.emit(id);\r\n        }\r\n    }\r\n    /**\r\n     * Returns a clone of the pagination instance object matching the id. If no\r\n     * id specified, returns the instance corresponding to the default id.\r\n     */\r\n    getInstance(id = this.DEFAULT_ID) {\r\n        if (this.instances[id]) {\r\n            return this.clone(this.instances[id]);\r\n        }\r\n        return {};\r\n    }\r\n    /**\r\n     * Perform a shallow clone of an object.\r\n     */\r\n    clone(obj) {\r\n        var target = {};\r\n        for (var i in obj) {\r\n            if (obj.hasOwnProperty(i)) {\r\n                target[i] = obj[i];\r\n            }\r\n        }\r\n        return target;\r\n    }\r\n}\n\nconst LARGE_NUMBER = Number.MAX_SAFE_INTEGER;\r\nclass PaginatePipe {\r\n    constructor(service) {\r\n        this.service = service;\r\n        // store the values from the last time the pipe was invoked\r\n        this.state = {};\r\n    }\r\n    transform(collection, args) {\r\n        // When an observable is passed through the AsyncPipe, it will output\r\n        // `null` until the subscription resolves. In this case, we want to\r\n        // use the cached data from the `state` object to prevent the NgFor\r\n        // from flashing empty until the real values arrive.\r\n        if (!(collection instanceof Array)) {\r\n            let _id = args.id || this.service.defaultId();\r\n            if (this.state[_id]) {\r\n                return this.state[_id].slice;\r\n            }\r\n            else {\r\n                return collection;\r\n            }\r\n        }\r\n        let serverSideMode = args.totalItems && args.totalItems !== collection.length;\r\n        let instance = this.createInstance(collection, args);\r\n        let id = instance.id;\r\n        let start, end;\r\n        let perPage = instance.itemsPerPage;\r\n        let emitChange = this.service.register(instance);\r\n        if (!serverSideMode && collection instanceof Array) {\r\n            perPage = +perPage || LARGE_NUMBER;\r\n            start = (instance.currentPage - 1) * perPage;\r\n            end = start + perPage;\r\n            let isIdentical = this.stateIsIdentical(id, collection, start, end);\r\n            if (isIdentical) {\r\n                return this.state[id].slice;\r\n            }\r\n            else {\r\n                let slice = collection.slice(start, end);\r\n                this.saveState(id, collection, slice, start, end);\r\n                this.service.change.emit(id);\r\n                return slice;\r\n            }\r\n        }\r\n        else {\r\n            if (emitChange) {\r\n                this.service.change.emit(id);\r\n            }\r\n            // save the state for server-side collection to avoid null\r\n            // flash as new data loads.\r\n            this.saveState(id, collection, collection, start, end);\r\n            return collection;\r\n        }\r\n    }\r\n    /**\r\n     * Create an PaginationInstance object, using defaults for any optional properties not supplied.\r\n     */\r\n    createInstance(collection, config) {\r\n        this.checkConfig(config);\r\n        return {\r\n            id: config.id != null ? config.id : this.service.defaultId(),\r\n            itemsPerPage: +config.itemsPerPage || 0,\r\n            currentPage: +config.currentPage || 1,\r\n            totalItems: +config.totalItems || collection.length\r\n        };\r\n    }\r\n    /**\r\n     * Ensure the argument passed to the filter contains the required properties.\r\n     */\r\n    checkConfig(config) {\r\n        const required = ['itemsPerPage', 'currentPage'];\r\n        const missing = required.filter(prop => !(prop in config));\r\n        if (0 < missing.length) {\r\n            throw new Error(`PaginatePipe: Argument is missing the following required properties: ${missing.join(', ')}`);\r\n        }\r\n    }\r\n    /**\r\n     * To avoid returning a brand new array each time the pipe is run, we store the state of the sliced\r\n     * array for a given id. This means that the next time the pipe is run on this collection & id, we just\r\n     * need to check that the collection, start and end points are all identical, and if so, return the\r\n     * last sliced array.\r\n     */\r\n    saveState(id, collection, slice, start, end) {\r\n        this.state[id] = {\r\n            collection,\r\n            size: collection.length,\r\n            slice,\r\n            start,\r\n            end\r\n        };\r\n    }\r\n    /**\r\n     * For a given id, returns true if the collection, size, start and end values are identical.\r\n     */\r\n    stateIsIdentical(id, collection, start, end) {\r\n        let state = this.state[id];\r\n        if (!state) {\r\n            return false;\r\n        }\r\n        let isMetaDataIdentical = state.size === collection.length &&\r\n            state.start === start &&\r\n            state.end === end;\r\n        if (!isMetaDataIdentical) {\r\n            return false;\r\n        }\r\n        return state.slice.every((element, index) => element === collection[start + index]);\r\n    }\r\n}\r\nPaginatePipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginatePipe, deps: [{ token: PaginationService }], target: i0.ɵɵFactoryTarget.Pipe });\r\nPaginatePipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginatePipe, name: \"paginate\", pure: false });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginatePipe, decorators: [{\r\n            type: Pipe,\r\n            args: [{\r\n                    name: 'paginate',\r\n                    pure: false\r\n                }]\r\n        }], ctorParameters: function () { return [{ type: PaginationService }]; } });\n\n/**\r\n * The default template and styles for the pagination links are borrowed directly\r\n * from Zurb Foundation 6: http://foundation.zurb.com/sites/docs/pagination.html\r\n */\r\nconst DEFAULT_TEMPLATE = `\r\n    <pagination-template  #p=\"paginationApi\"\r\n                         [id]=\"id\"\r\n                         [maxSize]=\"maxSize\"\r\n                         (pageChange)=\"pageChange.emit($event)\"\r\n                         (pageBoundsCorrection)=\"pageBoundsCorrection.emit($event)\">\r\n    <nav role=\"navigation\" [attr.aria-label]=\"screenReaderPaginationLabel\">\r\n    <ul class=\"ngx-pagination\" \r\n        [class.responsive]=\"responsive\"\r\n        *ngIf=\"!(autoHide && p.pages.length <= 1)\">\r\n\r\n        <li class=\"pagination-previous\" [class.disabled]=\"p.isFirstPage()\" *ngIf=\"directionLinks\"> \r\n            <a tabindex=\"0\" *ngIf=\"1 < p.getCurrent()\" (keyup.enter)=\"p.previous()\" (click)=\"p.previous()\">\r\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\r\n            </a>\r\n            <span *ngIf=\"p.isFirstPage()\" aria-disabled=\"true\">\r\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\r\n            </span>\r\n        </li> \r\n\r\n        <li class=\"small-screen\">\r\n            {{ p.getCurrent() }} / {{ p.getLastPage() }}\r\n        </li>\r\n\r\n        <li [class.current]=\"p.getCurrent() === page.value\" \r\n            [class.ellipsis]=\"page.label === '...'\"\r\n            *ngFor=\"let page of p.pages; trackBy: trackByIndex\">\r\n            <a tabindex=\"0\" (keyup.enter)=\"p.setCurrent(page.value)\" (click)=\"p.setCurrent(page.value)\" *ngIf=\"p.getCurrent() !== page.value\">\r\n                <span class=\"show-for-sr\">{{ screenReaderPageLabel }} </span>\r\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span>\r\n            </a>\r\n            <ng-container *ngIf=\"p.getCurrent() === page.value\">\r\n              <span aria-live=\"polite\">\r\n                <span class=\"show-for-sr\">{{ screenReaderCurrentLabel }} </span>\r\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span> \r\n              </span>\r\n            </ng-container>\r\n        </li>\r\n\r\n        <li class=\"pagination-next\" [class.disabled]=\"p.isLastPage()\" *ngIf=\"directionLinks\">\r\n            <a tabindex=\"0\" *ngIf=\"!p.isLastPage()\" (keyup.enter)=\"p.next()\" (click)=\"p.next()\">\r\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\r\n            </a>\r\n            <span *ngIf=\"p.isLastPage()\" aria-disabled=\"true\">\r\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\r\n            </span>\r\n        </li>\r\n\r\n    </ul>\r\n    </nav>\r\n    </pagination-template>\r\n    `;\r\nconst DEFAULT_STYLES = `\r\n.ngx-pagination {\r\n  margin-left: 0;\r\n  margin-bottom: 1rem; }\r\n  .ngx-pagination::before, .ngx-pagination::after {\r\n    content: ' ';\r\n    display: table; }\r\n  .ngx-pagination::after {\r\n    clear: both; }\r\n  .ngx-pagination li {\r\n    -moz-user-select: none;\r\n    -webkit-user-select: none;\r\n    -ms-user-select: none;\r\n    margin-right: 0.0625rem;\r\n    border-radius: 0; }\r\n  .ngx-pagination li {\r\n    display: inline-block; }\r\n  .ngx-pagination a,\r\n  .ngx-pagination button {\r\n    color: #0a0a0a; \r\n    display: block;\r\n    padding: 0.1875rem 0.625rem;\r\n    border-radius: 0; }\r\n    .ngx-pagination a:hover,\r\n    .ngx-pagination button:hover {\r\n      background: #e6e6e6; }\r\n  .ngx-pagination .current {\r\n    padding: 0.1875rem 0.625rem;\r\n    background: #2199e8;\r\n    color: #fefefe;\r\n    cursor: default; }\r\n  .ngx-pagination .disabled {\r\n    padding: 0.1875rem 0.625rem;\r\n    color: #cacaca;\r\n    cursor: default; } \r\n    .ngx-pagination .disabled:hover {\r\n      background: transparent; }\r\n  .ngx-pagination a, .ngx-pagination button {\r\n    cursor: pointer; }\r\n\r\n.ngx-pagination .pagination-previous a::before,\r\n.ngx-pagination .pagination-previous.disabled::before { \r\n  content: '«';\r\n  display: inline-block;\r\n  margin-right: 0.5rem; }\r\n\r\n.ngx-pagination .pagination-next a::after,\r\n.ngx-pagination .pagination-next.disabled::after {\r\n  content: '»';\r\n  display: inline-block;\r\n  margin-left: 0.5rem; }\r\n\r\n.ngx-pagination .show-for-sr {\r\n  position: absolute !important;\r\n  width: 1px;\r\n  height: 1px;\r\n  overflow: hidden;\r\n  clip: rect(0, 0, 0, 0); }\r\n.ngx-pagination .small-screen {\r\n  display: none; }\r\n@media screen and (max-width: 601px) {\r\n  .ngx-pagination.responsive .small-screen {\r\n    display: inline-block; } \r\n  .ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next) {\r\n    display: none; }\r\n}\r\n  `;\n\n/**\r\n * This directive is what powers all pagination controls components, including the default one.\r\n * It exposes an API which is hooked up to the PaginationService to keep the PaginatePipe in sync\r\n * with the pagination controls.\r\n */\r\nclass PaginationControlsDirective {\r\n    constructor(service, changeDetectorRef) {\r\n        this.service = service;\r\n        this.changeDetectorRef = changeDetectorRef;\r\n        this.maxSize = 7;\r\n        this.pageChange = new EventEmitter();\r\n        this.pageBoundsCorrection = new EventEmitter();\r\n        this.pages = [];\r\n        this.changeSub = this.service.change\r\n            .subscribe(id => {\r\n            if (this.id === id) {\r\n                this.updatePageLinks();\r\n                this.changeDetectorRef.markForCheck();\r\n                this.changeDetectorRef.detectChanges();\r\n            }\r\n        });\r\n    }\r\n    ngOnInit() {\r\n        if (this.id === undefined) {\r\n            this.id = this.service.defaultId();\r\n        }\r\n        this.updatePageLinks();\r\n    }\r\n    ngOnChanges(changes) {\r\n        this.updatePageLinks();\r\n    }\r\n    ngOnDestroy() {\r\n        this.changeSub.unsubscribe();\r\n    }\r\n    /**\r\n     * Go to the previous page\r\n     */\r\n    previous() {\r\n        this.checkValidId();\r\n        this.setCurrent(this.getCurrent() - 1);\r\n    }\r\n    /**\r\n     * Go to the next page\r\n     */\r\n    next() {\r\n        this.checkValidId();\r\n        this.setCurrent(this.getCurrent() + 1);\r\n    }\r\n    /**\r\n     * Returns true if current page is first page\r\n     */\r\n    isFirstPage() {\r\n        return this.getCurrent() === 1;\r\n    }\r\n    /**\r\n     * Returns true if current page is last page\r\n     */\r\n    isLastPage() {\r\n        return this.getLastPage() === this.getCurrent();\r\n    }\r\n    /**\r\n     * Set the current page number.\r\n     */\r\n    setCurrent(page) {\r\n        this.pageChange.emit(page);\r\n    }\r\n    /**\r\n     * Get the current page number.\r\n     */\r\n    getCurrent() {\r\n        return this.service.getCurrentPage(this.id);\r\n    }\r\n    /**\r\n     * Returns the last page number\r\n     */\r\n    getLastPage() {\r\n        let inst = this.service.getInstance(this.id);\r\n        if (inst.totalItems < 1) {\r\n            // when there are 0 or fewer (an error case) items, there are no \"pages\" as such,\r\n            // but it makes sense to consider a single, empty page as the last page.\r\n            return 1;\r\n        }\r\n        return Math.ceil(inst.totalItems / inst.itemsPerPage);\r\n    }\r\n    getTotalItems() {\r\n        return this.service.getInstance(this.id).totalItems;\r\n    }\r\n    checkValidId() {\r\n        if (this.service.getInstance(this.id).id == null) {\r\n            console.warn(`PaginationControlsDirective: the specified id \"${this.id}\" does not match any registered PaginationInstance`);\r\n        }\r\n    }\r\n    /**\r\n     * Updates the page links and checks that the current page is valid. Should run whenever the\r\n     * PaginationService.change stream emits a value matching the current ID, or when any of the\r\n     * input values changes.\r\n     */\r\n    updatePageLinks() {\r\n        let inst = this.service.getInstance(this.id);\r\n        const correctedCurrentPage = this.outOfBoundCorrection(inst);\r\n        if (correctedCurrentPage !== inst.currentPage) {\r\n            setTimeout(() => {\r\n                this.pageBoundsCorrection.emit(correctedCurrentPage);\r\n                this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\r\n            });\r\n        }\r\n        else {\r\n            this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\r\n        }\r\n    }\r\n    /**\r\n     * Checks that the instance.currentPage property is within bounds for the current page range.\r\n     * If not, return a correct value for currentPage, or the current value if OK.\r\n     */\r\n    outOfBoundCorrection(instance) {\r\n        const totalPages = Math.ceil(instance.totalItems / instance.itemsPerPage);\r\n        if (totalPages < instance.currentPage && 0 < totalPages) {\r\n            return totalPages;\r\n        }\r\n        else if (instance.currentPage < 1) {\r\n            return 1;\r\n        }\r\n        return instance.currentPage;\r\n    }\r\n    /**\r\n     * Returns an array of Page objects to use in the pagination controls.\r\n     */\r\n    createPageArray(currentPage, itemsPerPage, totalItems, paginationRange) {\r\n        // paginationRange could be a string if passed from attribute, so cast to number.\r\n        paginationRange = +paginationRange;\r\n        let pages = [];\r\n        // Return 1 as default page number\r\n        // Make sense to show 1 instead of empty when there are no items\r\n        const totalPages = Math.max(Math.ceil(totalItems / itemsPerPage), 1);\r\n        const halfWay = Math.ceil(paginationRange / 2);\r\n        const isStart = currentPage <= halfWay;\r\n        const isEnd = totalPages - halfWay < currentPage;\r\n        const isMiddle = !isStart && !isEnd;\r\n        let ellipsesNeeded = paginationRange < totalPages;\r\n        let i = 1;\r\n        while (i <= totalPages && i <= paginationRange) {\r\n            let label;\r\n            let pageNumber = this.calculatePageNumber(i, currentPage, paginationRange, totalPages);\r\n            let openingEllipsesNeeded = (i === 2 && (isMiddle || isEnd));\r\n            let closingEllipsesNeeded = (i === paginationRange - 1 && (isMiddle || isStart));\r\n            if (ellipsesNeeded && (openingEllipsesNeeded || closingEllipsesNeeded)) {\r\n                label = '...';\r\n            }\r\n            else {\r\n                label = pageNumber;\r\n            }\r\n            pages.push({\r\n                label: label,\r\n                value: pageNumber\r\n            });\r\n            i++;\r\n        }\r\n        return pages;\r\n    }\r\n    /**\r\n     * Given the position in the sequence of pagination links [i],\r\n     * figure out what page number corresponds to that position.\r\n     */\r\n    calculatePageNumber(i, currentPage, paginationRange, totalPages) {\r\n        let halfWay = Math.ceil(paginationRange / 2);\r\n        if (i === paginationRange) {\r\n            return totalPages;\r\n        }\r\n        else if (i === 1) {\r\n            return i;\r\n        }\r\n        else if (paginationRange < totalPages) {\r\n            if (totalPages - halfWay < currentPage) {\r\n                return totalPages - paginationRange + i;\r\n            }\r\n            else if (halfWay < currentPage) {\r\n                return currentPage - halfWay + i;\r\n            }\r\n            else {\r\n                return i;\r\n            }\r\n        }\r\n        else {\r\n            return i;\r\n        }\r\n    }\r\n}\r\nPaginationControlsDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginationControlsDirective, deps: [{ token: PaginationService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\r\nPaginationControlsDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.9\", type: PaginationControlsDirective, selector: \"pagination-template,[pagination-template]\", inputs: { id: \"id\", maxSize: \"maxSize\" }, outputs: { pageChange: \"pageChange\", pageBoundsCorrection: \"pageBoundsCorrection\" }, exportAs: [\"paginationApi\"], usesOnChanges: true, ngImport: i0 });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginationControlsDirective, decorators: [{\r\n            type: Directive,\r\n            args: [{\r\n                    selector: 'pagination-template,[pagination-template]',\r\n                    exportAs: 'paginationApi'\r\n                }]\r\n        }], ctorParameters: function () { return [{ type: PaginationService }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { id: [{\r\n                type: Input\r\n            }], maxSize: [{\r\n                type: Input\r\n            }], pageChange: [{\r\n                type: Output\r\n            }], pageBoundsCorrection: [{\r\n                type: Output\r\n            }] } });\n\nfunction coerceToBoolean(input) {\r\n    return !!input && input !== 'false';\r\n}\r\n/**\r\n * The default pagination controls component. Actually just a default implementation of a custom template.\r\n */\r\nclass PaginationControlsComponent {\r\n    constructor() {\r\n        this.maxSize = 7;\r\n        this.previousLabel = 'Previous';\r\n        this.nextLabel = 'Next';\r\n        this.screenReaderPaginationLabel = 'Pagination';\r\n        this.screenReaderPageLabel = 'page';\r\n        this.screenReaderCurrentLabel = `You're on page`;\r\n        this.pageChange = new EventEmitter();\r\n        this.pageBoundsCorrection = new EventEmitter();\r\n        this._directionLinks = true;\r\n        this._autoHide = false;\r\n        this._responsive = false;\r\n    }\r\n    get directionLinks() {\r\n        return this._directionLinks;\r\n    }\r\n    set directionLinks(value) {\r\n        this._directionLinks = coerceToBoolean(value);\r\n    }\r\n    get autoHide() {\r\n        return this._autoHide;\r\n    }\r\n    set autoHide(value) {\r\n        this._autoHide = coerceToBoolean(value);\r\n    }\r\n    get responsive() {\r\n        return this._responsive;\r\n    }\r\n    set responsive(value) {\r\n        this._responsive = coerceToBoolean(value);\r\n    }\r\n    trackByIndex(index) {\r\n        return index;\r\n    }\r\n}\r\nPaginationControlsComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginationControlsComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\r\nPaginationControlsComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.9\", type: PaginationControlsComponent, selector: \"pagination-controls\", inputs: { id: \"id\", maxSize: \"maxSize\", directionLinks: \"directionLinks\", autoHide: \"autoHide\", responsive: \"responsive\", previousLabel: \"previousLabel\", nextLabel: \"nextLabel\", screenReaderPaginationLabel: \"screenReaderPaginationLabel\", screenReaderPageLabel: \"screenReaderPageLabel\", screenReaderCurrentLabel: \"screenReaderCurrentLabel\" }, outputs: { pageChange: \"pageChange\", pageBoundsCorrection: \"pageBoundsCorrection\" }, ngImport: i0, template: \"\\n    <pagination-template  #p=\\\"paginationApi\\\"\\n                         [id]=\\\"id\\\"\\n                         [maxSize]=\\\"maxSize\\\"\\n                         (pageChange)=\\\"pageChange.emit($event)\\\"\\n                         (pageBoundsCorrection)=\\\"pageBoundsCorrection.emit($event)\\\">\\n    <nav role=\\\"navigation\\\" [attr.aria-label]=\\\"screenReaderPaginationLabel\\\">\\n    <ul class=\\\"ngx-pagination\\\" \\n        [class.responsive]=\\\"responsive\\\"\\n        *ngIf=\\\"!(autoHide && p.pages.length <= 1)\\\">\\n\\n        <li class=\\\"pagination-previous\\\" [class.disabled]=\\\"p.isFirstPage()\\\" *ngIf=\\\"directionLinks\\\"> \\n            <a tabindex=\\\"0\\\" *ngIf=\\\"1 < p.getCurrent()\\\" (keyup.enter)=\\\"p.previous()\\\" (click)=\\\"p.previous()\\\">\\n                {{ previousLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </a>\\n            <span *ngIf=\\\"p.isFirstPage()\\\" aria-disabled=\\\"true\\\">\\n                {{ previousLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </span>\\n        </li> \\n\\n        <li class=\\\"small-screen\\\">\\n            {{ p.getCurrent() }} / {{ p.getLastPage() }}\\n        </li>\\n\\n        <li [class.current]=\\\"p.getCurrent() === page.value\\\" \\n            [class.ellipsis]=\\\"page.label === '...'\\\"\\n            *ngFor=\\\"let page of p.pages; trackBy: trackByIndex\\\">\\n            <a tabindex=\\\"0\\\" (keyup.enter)=\\\"p.setCurrent(page.value)\\\" (click)=\\\"p.setCurrent(page.value)\\\" *ngIf=\\\"p.getCurrent() !== page.value\\\">\\n                <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }} </span>\\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span>\\n            </a>\\n            <ng-container *ngIf=\\\"p.getCurrent() === page.value\\\">\\n              <span aria-live=\\\"polite\\\">\\n                <span class=\\\"show-for-sr\\\">{{ screenReaderCurrentLabel }} </span>\\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span> \\n              </span>\\n            </ng-container>\\n        </li>\\n\\n        <li class=\\\"pagination-next\\\" [class.disabled]=\\\"p.isLastPage()\\\" *ngIf=\\\"directionLinks\\\">\\n            <a tabindex=\\\"0\\\" *ngIf=\\\"!p.isLastPage()\\\" (keyup.enter)=\\\"p.next()\\\" (click)=\\\"p.next()\\\">\\n                 {{ nextLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </a>\\n            <span *ngIf=\\\"p.isLastPage()\\\" aria-disabled=\\\"true\\\">\\n                 {{ nextLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </span>\\n        </li>\\n\\n    </ul>\\n    </nav>\\n    </pagination-template>\\n    \", isInline: true, styles: [\".ngx-pagination{margin-left:0;margin-bottom:1rem}.ngx-pagination:before,.ngx-pagination:after{content:\\\" \\\";display:table}.ngx-pagination:after{clear:both}.ngx-pagination li{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;margin-right:.0625rem;border-radius:0}.ngx-pagination li{display:inline-block}.ngx-pagination a,.ngx-pagination button{color:#0a0a0a;display:block;padding:.1875rem .625rem;border-radius:0}.ngx-pagination a:hover,.ngx-pagination button:hover{background:#e6e6e6}.ngx-pagination .current{padding:.1875rem .625rem;background:#2199e8;color:#fefefe;cursor:default}.ngx-pagination .disabled{padding:.1875rem .625rem;color:#cacaca;cursor:default}.ngx-pagination .disabled:hover{background:transparent}.ngx-pagination a,.ngx-pagination button{cursor:pointer}.ngx-pagination .pagination-previous a:before,.ngx-pagination .pagination-previous.disabled:before{content:\\\"\\\\ab\\\";display:inline-block;margin-right:.5rem}.ngx-pagination .pagination-next a:after,.ngx-pagination .pagination-next.disabled:after{content:\\\"\\\\bb\\\";display:inline-block;margin-left:.5rem}.ngx-pagination .show-for-sr{position:absolute!important;width:1px;height:1px;overflow:hidden;clip:rect(0,0,0,0)}.ngx-pagination .small-screen{display:none}@media screen and (max-width: 601px){.ngx-pagination.responsive .small-screen{display:inline-block}.ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next){display:none}}\\n\"], directives: [{ type: PaginationControlsDirective, selector: \"pagination-template,[pagination-template]\", inputs: [\"id\", \"maxSize\"], outputs: [\"pageChange\", \"pageBoundsCorrection\"], exportAs: [\"paginationApi\"] }, { type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], pipes: { \"number\": i2.DecimalPipe }, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginationControlsComponent, decorators: [{\r\n            type: Component,\r\n            args: [{\r\n                    selector: 'pagination-controls',\r\n                    template: DEFAULT_TEMPLATE,\r\n                    styles: [DEFAULT_STYLES],\r\n                    changeDetection: ChangeDetectionStrategy.OnPush,\r\n                    encapsulation: ViewEncapsulation.None\r\n                }]\r\n        }], propDecorators: { id: [{\r\n                type: Input\r\n            }], maxSize: [{\r\n                type: Input\r\n            }], directionLinks: [{\r\n                type: Input\r\n            }], autoHide: [{\r\n                type: Input\r\n            }], responsive: [{\r\n                type: Input\r\n            }], previousLabel: [{\r\n                type: Input\r\n            }], nextLabel: [{\r\n                type: Input\r\n            }], screenReaderPaginationLabel: [{\r\n                type: Input\r\n            }], screenReaderPageLabel: [{\r\n                type: Input\r\n            }], screenReaderCurrentLabel: [{\r\n                type: Input\r\n            }], pageChange: [{\r\n                type: Output\r\n            }], pageBoundsCorrection: [{\r\n                type: Output\r\n            }] } });\n\nclass NgxPaginationModule {\r\n}\r\nNgxPaginationModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: NgxPaginationModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\r\nNgxPaginationModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: NgxPaginationModule, declarations: [PaginatePipe,\r\n        PaginationControlsComponent,\r\n        PaginationControlsDirective], imports: [CommonModule], exports: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective] });\r\nNgxPaginationModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: NgxPaginationModule, providers: [PaginationService], imports: [[CommonModule]] });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: NgxPaginationModule, decorators: [{\r\n            type: NgModule,\r\n            args: [{\r\n                    imports: [CommonModule],\r\n                    declarations: [\r\n                        PaginatePipe,\r\n                        PaginationControlsComponent,\r\n                        PaginationControlsDirective\r\n                    ],\r\n                    providers: [PaginationService],\r\n                    exports: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective]\r\n                }]\r\n        }] });\n\n/*\r\n * Public API Surface of ngx-pagination\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NgxPaginationModule, PaginatePipe, PaginationControlsComponent, PaginationControlsDirective, PaginationService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,QAAQ,QAAQ,eAAe;AAC7I,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,mDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAkNgDf,EAAE,CAAAgB,gBAAA;IAAFhB,EAAE,CAAAiB,cAAA,WA8XquC,CAAC;IA9XxuCjB,EAAE,CAAAkB,UAAA,yBAAAC,4EAAA;MAAFnB,EAAE,CAAAoB,aAAA,CAAAL,GAAA;MAAFf,EAAE,CAAAqB,aAAA;MAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;MAAA,OAAFvB,EAAE,CAAAwB,WAAA,CA8X8rCF,IAAA,CAAAG,QAAA,CAAW,CAAC;IAAA,CAAC,CAAC,mBAAAC,sEAAA;MA9X9sC1B,EAAE,CAAAoB,aAAA,CAAAL,GAAA;MAAFf,EAAE,CAAAqB,aAAA;MAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;MAAA,OAAFvB,EAAE,CAAAwB,WAAA,CA8XutCF,IAAA,CAAAG,QAAA,CAAW,CAAC;IAAA,CAAC,CAAC;IA9XvuCzB,EAAE,CAAA2B,MAAA,EA8X2wC,CAAC;IA9X9wC3B,EAAE,CAAAiB,cAAA,cA8XuyC,CAAC;IA9X1yCjB,EAAE,CAAA2B,MAAA,EA8Xk0C,CAAC;IA9Xr0C3B,EAAE,CAAA4B,YAAA,CA8Xy0C,CAAC,CAAiB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgB,MAAA,GA9X91C7B,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAA8B,SAAA,CA8X2wC,CAAC;IA9X9wC9B,EAAE,CAAA+B,kBAAA,MAAAF,MAAA,CAAAG,aAAA,KA8X2wC,CAAC;IA9X9wChC,EAAE,CAAA8B,SAAA,EA8Xk0C,CAAC;IA9Xr0C9B,EAAE,CAAAiC,iBAAA,CAAAJ,MAAA,CAAAK,qBA8Xk0C,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Xr0Cb,EAAE,CAAAiB,cAAA,cA8Xg6C,CAAC;IA9Xn6CjB,EAAE,CAAA2B,MAAA,EA8Xs8C,CAAC;IA9Xz8C3B,EAAE,CAAAiB,cAAA,cA8Xk+C,CAAC;IA9Xr+CjB,EAAE,CAAA2B,MAAA,EA8X6/C,CAAC;IA9XhgD3B,EAAE,CAAA4B,YAAA,CA8XogD,CAAC,CAAoB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgB,MAAA,GA9X5hD7B,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAA8B,SAAA,CA8Xs8C,CAAC;IA9Xz8C9B,EAAE,CAAA+B,kBAAA,MAAAF,MAAA,CAAAG,aAAA,KA8Xs8C,CAAC;IA9Xz8ChC,EAAE,CAAA8B,SAAA,EA8X6/C,CAAC;IA9XhgD9B,EAAE,CAAAiC,iBAAA,CAAAJ,MAAA,CAAAK,qBA8X6/C,CAAC;EAAA;AAAA;AAAA,SAAAE,+CAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9XhgDb,EAAE,CAAAiB,cAAA,WA8X+mC,CAAC;IA9XlnCjB,EAAE,CAAAqC,UAAA,IAAAzB,kDAAA,eA8XquC,CAAC,IAAAuB,qDAAA,kBAA0L,CAAC;IA9Xn6CnC,EAAE,CAAA4B,YAAA,CA8XwiD,CAAC;EAAA;EAAA,IAAAf,EAAA;IA9X3iDb,EAAE,CAAAqB,aAAA;IAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;IAAFvB,EAAE,CAAAsC,WAAA,aAAAhB,IAAA,CAAAiB,WAAA,EA8XqlC,CAAC;IA9XxlCvC,EAAE,CAAA8B,SAAA,CA8X0qC,CAAC;IA9X7qC9B,EAAE,CAAAwC,UAAA,aAAAlB,IAAA,CAAAmB,UAAA,EA8X0qC,CAAC;IA9X7qCzC,EAAE,CAAA8B,SAAA,CA8Xs4C,CAAC;IA9Xz4C9B,EAAE,CAAAwC,UAAA,SAAAlB,IAAA,CAAAiB,WAAA,EA8Xs4C,CAAC;EAAA;AAAA;AAAA,SAAAG,mDAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA8B,GAAA,GA9Xz4C3C,EAAE,CAAAgB,gBAAA;IAAFhB,EAAE,CAAAiB,cAAA,WA8X8+D,CAAC;IA9Xj/DjB,EAAE,CAAAkB,UAAA,yBAAA0B,4EAAA;MAAF5C,EAAE,CAAAoB,aAAA,CAAAuB,GAAA;MAAA,MAAAE,OAAA,GAAF7C,EAAE,CAAAqB,aAAA,GAAAyB,SAAA;MAAF9C,EAAE,CAAAqB,aAAA;MAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;MAAA,OAAFvB,EAAE,CAAAwB,WAAA,CA8Xu4DF,IAAA,CAAAyB,UAAA,CAAAF,OAAA,CAAAG,KAAuB,CAAC;IAAA,CAAC,CAAC,mBAAAC,sEAAA;MA9Xn6DjD,EAAE,CAAAoB,aAAA,CAAAuB,GAAA;MAAA,MAAAE,OAAA,GAAF7C,EAAE,CAAAqB,aAAA,GAAAyB,SAAA;MAAF9C,EAAE,CAAAqB,aAAA;MAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;MAAA,OAAFvB,EAAE,CAAAwB,WAAA,CA8X46DF,IAAA,CAAAyB,UAAA,CAAAF,OAAA,CAAAG,KAAuB,CAAC;IAAA,CAAC,CAAC;IA9Xx8DhD,EAAE,CAAAiB,cAAA,cA8X4hE,CAAC;IA9X/hEjB,EAAE,CAAA2B,MAAA,EA8XwjE,CAAC;IA9X3jE3B,EAAE,CAAA4B,YAAA,CA8X+jE,CAAC;IA9XlkE5B,EAAE,CAAAiB,cAAA,UA8XulE,CAAC;IA9X1lEjB,EAAE,CAAA2B,MAAA,EA8X2pE,CAAC;IA9X9pE3B,EAAE,CAAAkD,MAAA;IAAFlD,EAAE,CAAA4B,YAAA,CA8XkqE,CAAC,CAAiB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgC,OAAA,GA9XvrE7C,EAAE,CAAAqB,aAAA,GAAAyB,SAAA;IAAA,MAAAjB,MAAA,GAAF7B,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAA8B,SAAA,EA8XwjE,CAAC;IA9X3jE9B,EAAE,CAAA+B,kBAAA,KAAAF,MAAA,CAAAK,qBAAA,KA8XwjE,CAAC;IA9X3jElC,EAAE,CAAA8B,SAAA,EA8X2pE,CAAC;IA9X9pE9B,EAAE,CAAAiC,iBAAA,CAAAY,OAAA,CAAAM,KAAA,aAAAN,OAAA,CAAAM,KAAA,GAAFnD,EAAE,CAAAoD,WAAA,OAAAP,OAAA,CAAAM,KAAA,KA8X2pE,CAAC;EAAA;AAAA;AAAA,SAAAE,8DAAAxC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9X9pEb,EAAE,CAAAsD,uBAAA,EA8XwvE,CAAC;IA9X3vEtD,EAAE,CAAAiB,cAAA,cA8XmyE,CAAC,cAA6C,CAAC;IA9Xp1EjB,EAAE,CAAA2B,MAAA,EA8Xg3E,CAAC;IA9Xn3E3B,EAAE,CAAA4B,YAAA,CA8Xu3E,CAAC;IA9X13E5B,EAAE,CAAAiB,cAAA,UA8X+4E,CAAC;IA9Xl5EjB,EAAE,CAAA2B,MAAA,EA8Xm9E,CAAC;IA9Xt9E3B,EAAE,CAAAkD,MAAA;IAAFlD,EAAE,CAAA4B,YAAA,CA8X09E,CAAC,CAAuB,CAAC;IA9Xr/E5B,EAAE,CAAAuD,qBAAA;EAAA;EAAA,IAAA1C,EAAA;IAAA,MAAAgC,OAAA,GAAF7C,EAAE,CAAAqB,aAAA,GAAAyB,SAAA;IAAA,MAAAjB,MAAA,GAAF7B,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAA8B,SAAA,EA8Xg3E,CAAC;IA9Xn3E9B,EAAE,CAAA+B,kBAAA,KAAAF,MAAA,CAAA2B,wBAAA,KA8Xg3E,CAAC;IA9Xn3ExD,EAAE,CAAA8B,SAAA,EA8Xm9E,CAAC;IA9Xt9E9B,EAAE,CAAAiC,iBAAA,CAAAY,OAAA,CAAAM,KAAA,aAAAN,OAAA,CAAAM,KAAA,GAAFnD,EAAE,CAAAoD,WAAA,OAAAP,OAAA,CAAAM,KAAA,KA8Xm9E,CAAC;EAAA;AAAA;AAAA,SAAAM,+CAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Xt9Eb,EAAE,CAAAiB,cAAA,QA8Xs1D,CAAC;IA9Xz1DjB,EAAE,CAAAqC,UAAA,IAAAK,kDAAA,eA8X8+D,CAAC,IAAAW,6DAAA,0BAAyQ,CAAC;IA9X3vErD,EAAE,CAAA4B,YAAA,CA8X8hF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgC,OAAA,GAAA/B,GAAA,CAAAgC,SAAA;IA9XjiF9C,EAAE,CAAAqB,aAAA;IAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;IAAFvB,EAAE,CAAAsC,WAAA,YAAAhB,IAAA,CAAAmB,UAAA,OAAAI,OAAA,CAAAG,KA8X0tD,CAAC,aAAAH,OAAA,CAAAM,KAAA,UAAuD,CAAC;IA9XrxDnD,EAAE,CAAA8B,SAAA,CA8X2+D,CAAC;IA9X9+D9B,EAAE,CAAAwC,UAAA,SAAAlB,IAAA,CAAAmB,UAAA,OAAAI,OAAA,CAAAG,KA8X2+D,CAAC;IA9X9+DhD,EAAE,CAAA8B,SAAA,CA8XqvE,CAAC;IA9XxvE9B,EAAE,CAAAwC,UAAA,SAAAlB,IAAA,CAAAmB,UAAA,OAAAI,OAAA,CAAAG,KA8XqvE,CAAC;EAAA;AAAA;AAAA,SAAAU,mDAAA7C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA8C,GAAA,GA9XxvE3D,EAAE,CAAAgB,gBAAA;IAAFhB,EAAE,CAAAiB,cAAA,WA8X+uF,CAAC;IA9XlvFjB,EAAE,CAAAkB,UAAA,yBAAA0C,4EAAA;MAAF5D,EAAE,CAAAoB,aAAA,CAAAuC,GAAA;MAAF3D,EAAE,CAAAqB,aAAA;MAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;MAAA,OAAFvB,EAAE,CAAAwB,WAAA,CA8XgtFF,IAAA,CAAAuC,IAAA,CAAO,CAAC;IAAA,CAAC,CAAC,mBAAAC,sEAAA;MA9X5tF9D,EAAE,CAAAoB,aAAA,CAAAuC,GAAA;MAAF3D,EAAE,CAAAqB,aAAA;MAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;MAAA,OAAFvB,EAAE,CAAAwB,WAAA,CA8XquFF,IAAA,CAAAuC,IAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IA9XjvF7D,EAAE,CAAA2B,MAAA,EA8XkxF,CAAC;IA9XrxF3B,EAAE,CAAAiB,cAAA,cA8X8yF,CAAC;IA9XjzFjB,EAAE,CAAA2B,MAAA,EA8Xy0F,CAAC;IA9X50F3B,EAAE,CAAA4B,YAAA,CA8Xg1F,CAAC,CAAiB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgB,MAAA,GA9Xr2F7B,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAA8B,SAAA,CA8XkxF,CAAC;IA9XrxF9B,EAAE,CAAA+B,kBAAA,MAAAF,MAAA,CAAAkC,SAAA,KA8XkxF,CAAC;IA9XrxF/D,EAAE,CAAA8B,SAAA,EA8Xy0F,CAAC;IA9X50F9B,EAAE,CAAAiC,iBAAA,CAAAJ,MAAA,CAAAK,qBA8Xy0F,CAAC;EAAA;AAAA;AAAA,SAAA8B,sDAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9X50Fb,EAAE,CAAAiB,cAAA,cA8Xs6F,CAAC;IA9Xz6FjB,EAAE,CAAA2B,MAAA,EA8Xy8F,CAAC;IA9X58F3B,EAAE,CAAAiB,cAAA,cA8Xq+F,CAAC;IA9Xx+FjB,EAAE,CAAA2B,MAAA,EA8XggG,CAAC;IA9XngG3B,EAAE,CAAA4B,YAAA,CA8XugG,CAAC,CAAoB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgB,MAAA,GA9X/hG7B,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAA8B,SAAA,CA8Xy8F,CAAC;IA9X58F9B,EAAE,CAAA+B,kBAAA,MAAAF,MAAA,CAAAkC,SAAA,KA8Xy8F,CAAC;IA9X58F/D,EAAE,CAAA8B,SAAA,EA8XggG,CAAC;IA9XngG9B,EAAE,CAAAiC,iBAAA,CAAAJ,MAAA,CAAAK,qBA8XggG,CAAC;EAAA;AAAA;AAAA,SAAA+B,+CAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9XngGb,EAAE,CAAAiB,cAAA,YA8XqoF,CAAC;IA9XxoFjB,EAAE,CAAAqC,UAAA,IAAAqB,kDAAA,eA8X+uF,CAAC,IAAAM,qDAAA,kBAAsL,CAAC;IA9Xz6FhE,EAAE,CAAA4B,YAAA,CA8X2iG,CAAC;EAAA;EAAA,IAAAf,EAAA;IA9X9iGb,EAAE,CAAAqB,aAAA;IAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;IAAFvB,EAAE,CAAAsC,WAAA,aAAAhB,IAAA,CAAA4C,UAAA,EA8X2mF,CAAC;IA9X9mFlE,EAAE,CAAA8B,SAAA,CA8X4rF,CAAC;IA9X/rF9B,EAAE,CAAAwC,UAAA,UAAAlB,IAAA,CAAA4C,UAAA,EA8X4rF,CAAC;IA9X/rFlE,EAAE,CAAA8B,SAAA,CA8X44F,CAAC;IA9X/4F9B,EAAE,CAAAwC,UAAA,SAAAlB,IAAA,CAAA4C,UAAA,EA8X44F,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9X/4Fb,EAAE,CAAAiB,cAAA,WA8XmgC,CAAC;IA9XtgCjB,EAAE,CAAAqC,UAAA,IAAAD,8CAAA,eA8X+mC,CAAC;IA9XlnCpC,EAAE,CAAAiB,cAAA,WA8XglD,CAAC;IA9XnlDjB,EAAE,CAAA2B,MAAA,EA8XopD,CAAC;IA9XvpD3B,EAAE,CAAA4B,YAAA,CA8XypD,CAAC;IA9X5pD5B,EAAE,CAAAqC,UAAA,IAAAoB,8CAAA,eA8Xs1D,CAAC,IAAAQ,8CAAA,eAA8yB,CAAC;IA9XxoFjE,EAAE,CAAA4B,YAAA,CA8XwjG,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgB,MAAA,GA9X3jG7B,EAAE,CAAAqB,aAAA;IAAA,MAAAC,IAAA,GAAFtB,EAAE,CAAAuB,WAAA;IAAFvB,EAAE,CAAAsC,WAAA,eAAAT,MAAA,CAAAuC,UA8X48B,CAAC;IA9X/8BpE,EAAE,CAAA8B,SAAA,CA8X4mC,CAAC;IA9X/mC9B,EAAE,CAAAwC,UAAA,SAAAX,MAAA,CAAAwC,cA8X4mC,CAAC;IA9X/mCrE,EAAE,CAAA8B,SAAA,EA8XopD,CAAC;IA9XvpD9B,EAAE,CAAAsE,kBAAA,MAAAhD,IAAA,CAAAmB,UAAA,WAAAnB,IAAA,CAAAiD,WAAA,OA8XopD,CAAC;IA9XvpDvE,EAAE,CAAA8B,SAAA,CA8X8zD,CAAC;IA9Xj0D9B,EAAE,CAAAwC,UAAA,YAAAlB,IAAA,CAAAkD,KA8X8zD,CAAC,iBAAA3C,MAAA,CAAA4C,YAAoB,CAAC;IA9Xt1DzE,EAAE,CAAA8B,SAAA,CA8XkoF,CAAC;IA9XroF9B,EAAE,CAAAwC,UAAA,SAAAX,MAAA,CAAAwC,cA8XkoF,CAAC;EAAA;AAAA;AA9kBpuF,MAAMK,iBAAiB,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAI3E,YAAY,CAAC,CAAC;IAChC,IAAI,CAAC4E,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,UAAU,GAAG,uBAAuB;EAC7C;EACAC,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACD,UAAU;EAAE;EACtC;AACJ;AACA;AACA;AACA;EACIE,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAIA,QAAQ,CAACC,EAAE,IAAI,IAAI,EAAE;MACrBD,QAAQ,CAACC,EAAE,GAAG,IAAI,CAACJ,UAAU;IACjC;IACA,IAAI,CAAC,IAAI,CAACD,SAAS,CAACI,QAAQ,CAACC,EAAE,CAAC,EAAE;MAC9B,IAAI,CAACL,SAAS,CAACI,QAAQ,CAACC,EAAE,CAAC,GAAGD,QAAQ;MACtC,OAAO,IAAI;IACf,CAAC,MACI;MACD,OAAO,IAAI,CAACE,cAAc,CAACF,QAAQ,CAAC;IACxC;EACJ;EACA;AACJ;AACA;AACA;EACIE,cAAcA,CAACF,QAAQ,EAAE;IACrB,IAAIG,OAAO,GAAG,KAAK;IACnB,KAAK,IAAIC,IAAI,IAAI,IAAI,CAACR,SAAS,CAACI,QAAQ,CAACC,EAAE,CAAC,EAAE;MAC1C,IAAID,QAAQ,CAACI,IAAI,CAAC,KAAK,IAAI,CAACR,SAAS,CAACI,QAAQ,CAACC,EAAE,CAAC,CAACG,IAAI,CAAC,EAAE;QACtD,IAAI,CAACR,SAAS,CAACI,QAAQ,CAACC,EAAE,CAAC,CAACG,IAAI,CAAC,GAAGJ,QAAQ,CAACI,IAAI,CAAC;QAClDD,OAAO,GAAG,IAAI;MAClB;IACJ;IACA,OAAOA,OAAO;EAClB;EACA;AACJ;AACA;EACIE,cAAcA,CAACJ,EAAE,EAAE;IACf,IAAI,IAAI,CAACL,SAAS,CAACK,EAAE,CAAC,EAAE;MACpB,OAAO,IAAI,CAACL,SAAS,CAACK,EAAE,CAAC,CAACK,WAAW;IACzC;IACA,OAAO,CAAC;EACZ;EACA;AACJ;AACA;EACIC,cAAcA,CAACN,EAAE,EAAEO,IAAI,EAAE;IACrB,IAAI,IAAI,CAACZ,SAAS,CAACK,EAAE,CAAC,EAAE;MACpB,IAAID,QAAQ,GAAG,IAAI,CAACJ,SAAS,CAACK,EAAE,CAAC;MACjC,IAAIQ,OAAO,GAAGC,IAAI,CAACC,IAAI,CAACX,QAAQ,CAACY,UAAU,GAAGZ,QAAQ,CAACa,YAAY,CAAC;MACpE,IAAIL,IAAI,IAAIC,OAAO,IAAI,CAAC,IAAID,IAAI,EAAE;QAC9B,IAAI,CAACZ,SAAS,CAACK,EAAE,CAAC,CAACK,WAAW,GAAGE,IAAI;QACrC,IAAI,CAACb,MAAM,CAACmB,IAAI,CAACb,EAAE,CAAC;MACxB;IACJ;EACJ;EACA;AACJ;AACA;EACIc,aAAaA,CAACd,EAAE,EAAEW,UAAU,EAAE;IAC1B,IAAI,IAAI,CAAChB,SAAS,CAACK,EAAE,CAAC,IAAI,CAAC,IAAIW,UAAU,EAAE;MACvC,IAAI,CAAChB,SAAS,CAACK,EAAE,CAAC,CAACW,UAAU,GAAGA,UAAU;MAC1C,IAAI,CAACjB,MAAM,CAACmB,IAAI,CAACb,EAAE,CAAC;IACxB;EACJ;EACA;AACJ;AACA;EACIe,eAAeA,CAACf,EAAE,EAAEY,YAAY,EAAE;IAC9B,IAAI,IAAI,CAACjB,SAAS,CAACK,EAAE,CAAC,EAAE;MACpB,IAAI,CAACL,SAAS,CAACK,EAAE,CAAC,CAACY,YAAY,GAAGA,YAAY;MAC9C,IAAI,CAAClB,MAAM,CAACmB,IAAI,CAACb,EAAE,CAAC;IACxB;EACJ;EACA;AACJ;AACA;AACA;EACIgB,WAAWA,CAAChB,EAAE,GAAG,IAAI,CAACJ,UAAU,EAAE;IAC9B,IAAI,IAAI,CAACD,SAAS,CAACK,EAAE,CAAC,EAAE;MACpB,OAAO,IAAI,CAACiB,KAAK,CAAC,IAAI,CAACtB,SAAS,CAACK,EAAE,CAAC,CAAC;IACzC;IACA,OAAO,CAAC,CAAC;EACb;EACA;AACJ;AACA;EACIiB,KAAKA,CAACC,GAAG,EAAE;IACP,IAAIC,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,IAAIC,CAAC,IAAIF,GAAG,EAAE;MACf,IAAIA,GAAG,CAACG,cAAc,CAACD,CAAC,CAAC,EAAE;QACvBD,MAAM,CAACC,CAAC,CAAC,GAAGF,GAAG,CAACE,CAAC,CAAC;MACtB;IACJ;IACA,OAAOD,MAAM;EACjB;AACJ;AAEA,MAAMG,YAAY,GAAGC,MAAM,CAACC,gBAAgB;AAC5C,MAAMC,YAAY,CAAC;EACfhC,WAAWA,CAACiC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACAC,SAASA,CAACC,UAAU,EAAEC,IAAI,EAAE;IACxB;IACA;IACA;IACA;IACA,IAAI,EAAED,UAAU,YAAYE,KAAK,CAAC,EAAE;MAChC,IAAIC,GAAG,GAAGF,IAAI,CAAC9B,EAAE,IAAI,IAAI,CAAC0B,OAAO,CAAC7B,SAAS,CAAC,CAAC;MAC7C,IAAI,IAAI,CAAC8B,KAAK,CAACK,GAAG,CAAC,EAAE;QACjB,OAAO,IAAI,CAACL,KAAK,CAACK,GAAG,CAAC,CAACC,KAAK;MAChC,CAAC,MACI;QACD,OAAOJ,UAAU;MACrB;IACJ;IACA,IAAIK,cAAc,GAAGJ,IAAI,CAACnB,UAAU,IAAImB,IAAI,CAACnB,UAAU,KAAKkB,UAAU,CAACM,MAAM;IAC7E,IAAIpC,QAAQ,GAAG,IAAI,CAACqC,cAAc,CAACP,UAAU,EAAEC,IAAI,CAAC;IACpD,IAAI9B,EAAE,GAAGD,QAAQ,CAACC,EAAE;IACpB,IAAIqC,KAAK,EAAEC,GAAG;IACd,IAAIC,OAAO,GAAGxC,QAAQ,CAACa,YAAY;IACnC,IAAI4B,UAAU,GAAG,IAAI,CAACd,OAAO,CAAC5B,QAAQ,CAACC,QAAQ,CAAC;IAChD,IAAI,CAACmC,cAAc,IAAIL,UAAU,YAAYE,KAAK,EAAE;MAChDQ,OAAO,GAAG,CAACA,OAAO,IAAIjB,YAAY;MAClCe,KAAK,GAAG,CAACtC,QAAQ,CAACM,WAAW,GAAG,CAAC,IAAIkC,OAAO;MAC5CD,GAAG,GAAGD,KAAK,GAAGE,OAAO;MACrB,IAAIE,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC1C,EAAE,EAAE6B,UAAU,EAAEQ,KAAK,EAAEC,GAAG,CAAC;MACnE,IAAIG,WAAW,EAAE;QACb,OAAO,IAAI,CAACd,KAAK,CAAC3B,EAAE,CAAC,CAACiC,KAAK;MAC/B,CAAC,MACI;QACD,IAAIA,KAAK,GAAGJ,UAAU,CAACI,KAAK,CAACI,KAAK,EAAEC,GAAG,CAAC;QACxC,IAAI,CAACK,SAAS,CAAC3C,EAAE,EAAE6B,UAAU,EAAEI,KAAK,EAAEI,KAAK,EAAEC,GAAG,CAAC;QACjD,IAAI,CAACZ,OAAO,CAAChC,MAAM,CAACmB,IAAI,CAACb,EAAE,CAAC;QAC5B,OAAOiC,KAAK;MAChB;IACJ,CAAC,MACI;MACD,IAAIO,UAAU,EAAE;QACZ,IAAI,CAACd,OAAO,CAAChC,MAAM,CAACmB,IAAI,CAACb,EAAE,CAAC;MAChC;MACA;MACA;MACA,IAAI,CAAC2C,SAAS,CAAC3C,EAAE,EAAE6B,UAAU,EAAEA,UAAU,EAAEQ,KAAK,EAAEC,GAAG,CAAC;MACtD,OAAOT,UAAU;IACrB;EACJ;EACA;AACJ;AACA;EACIO,cAAcA,CAACP,UAAU,EAAEe,MAAM,EAAE;IAC/B,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IACxB,OAAO;MACH5C,EAAE,EAAE4C,MAAM,CAAC5C,EAAE,IAAI,IAAI,GAAG4C,MAAM,CAAC5C,EAAE,GAAG,IAAI,CAAC0B,OAAO,CAAC7B,SAAS,CAAC,CAAC;MAC5De,YAAY,EAAE,CAACgC,MAAM,CAAChC,YAAY,IAAI,CAAC;MACvCP,WAAW,EAAE,CAACuC,MAAM,CAACvC,WAAW,IAAI,CAAC;MACrCM,UAAU,EAAE,CAACiC,MAAM,CAACjC,UAAU,IAAIkB,UAAU,CAACM;IACjD,CAAC;EACL;EACA;AACJ;AACA;EACIU,WAAWA,CAACD,MAAM,EAAE;IAChB,MAAME,QAAQ,GAAG,CAAC,cAAc,EAAE,aAAa,CAAC;IAChD,MAAMC,OAAO,GAAGD,QAAQ,CAACE,MAAM,CAAC7C,IAAI,IAAI,EAAEA,IAAI,IAAIyC,MAAM,CAAC,CAAC;IAC1D,IAAI,CAAC,GAAGG,OAAO,CAACZ,MAAM,EAAE;MACpB,MAAM,IAAIc,KAAK,CAAC,wEAAwEF,OAAO,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACjH;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIP,SAASA,CAAC3C,EAAE,EAAE6B,UAAU,EAAEI,KAAK,EAAEI,KAAK,EAAEC,GAAG,EAAE;IACzC,IAAI,CAACX,KAAK,CAAC3B,EAAE,CAAC,GAAG;MACb6B,UAAU;MACVsB,IAAI,EAAEtB,UAAU,CAACM,MAAM;MACvBF,KAAK;MACLI,KAAK;MACLC;IACJ,CAAC;EACL;EACA;AACJ;AACA;EACII,gBAAgBA,CAAC1C,EAAE,EAAE6B,UAAU,EAAEQ,KAAK,EAAEC,GAAG,EAAE;IACzC,IAAIX,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC3B,EAAE,CAAC;IAC1B,IAAI,CAAC2B,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;IACA,IAAIyB,mBAAmB,GAAGzB,KAAK,CAACwB,IAAI,KAAKtB,UAAU,CAACM,MAAM,IACtDR,KAAK,CAACU,KAAK,KAAKA,KAAK,IACrBV,KAAK,CAACW,GAAG,KAAKA,GAAG;IACrB,IAAI,CAACc,mBAAmB,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,OAAOzB,KAAK,CAACM,KAAK,CAACoB,KAAK,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAKD,OAAO,KAAKzB,UAAU,CAACQ,KAAK,GAAGkB,KAAK,CAAC,CAAC;EACvF;AACJ;AACA9B,YAAY,CAAC+B,IAAI,YAAAC,qBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAwFjC,YAAY,EAAtB3G,EAAE,CAAA6I,iBAAA,CAAsCnE,iBAAiB;AAAA,CAAuC;AAC/LiC,YAAY,CAACmC,KAAK,kBAD6E9I,EAAE,CAAA+I,YAAA;EAAAC,IAAA;EAAAC,IAAA,EACMtC,YAAY;EAAAuC,IAAA;AAAA,EAAkC;AACrJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAF+FnJ,EAAE,CAAAoJ,iBAAA,CAENzC,YAAY,EAAc,CAAC;IAC1GsC,IAAI,EAAE/I,IAAI;IACV8G,IAAI,EAAE,CAAC;MACCgC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAED,IAAI,EAAEvE;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEjF;AACA;AACA;AACA;AACA,MAAM2E,gBAAgB,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,MAAMC,cAAc,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,MAAMC,2BAA2B,CAAC;EAC9B5E,WAAWA,CAACiC,OAAO,EAAE4C,iBAAiB,EAAE;IACpC,IAAI,CAAC5C,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC4C,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,UAAU,GAAG,IAAIzJ,YAAY,CAAC,CAAC;IACpC,IAAI,CAAC0J,oBAAoB,GAAG,IAAI1J,YAAY,CAAC,CAAC;IAC9C,IAAI,CAACuE,KAAK,GAAG,EAAE;IACf,IAAI,CAACoF,SAAS,GAAG,IAAI,CAAChD,OAAO,CAAChC,MAAM,CAC/BiF,SAAS,CAAC3E,EAAE,IAAI;MACjB,IAAI,IAAI,CAACA,EAAE,KAAKA,EAAE,EAAE;QAChB,IAAI,CAAC4E,eAAe,CAAC,CAAC;QACtB,IAAI,CAACN,iBAAiB,CAACO,YAAY,CAAC,CAAC;QACrC,IAAI,CAACP,iBAAiB,CAACQ,aAAa,CAAC,CAAC;MAC1C;IACJ,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC/E,EAAE,KAAKgF,SAAS,EAAE;MACvB,IAAI,CAAChF,EAAE,GAAG,IAAI,CAAC0B,OAAO,CAAC7B,SAAS,CAAC,CAAC;IACtC;IACA,IAAI,CAAC+E,eAAe,CAAC,CAAC;EAC1B;EACAK,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACN,eAAe,CAAC,CAAC;EAC1B;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACT,SAAS,CAACU,WAAW,CAAC,CAAC;EAChC;EACA;AACJ;AACA;EACI7I,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC8I,YAAY,CAAC,CAAC;IACnB,IAAI,CAACxH,UAAU,CAAC,IAAI,CAACN,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;EACIoB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC0G,YAAY,CAAC,CAAC;IACnB,IAAI,CAACxH,UAAU,CAAC,IAAI,CAACN,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;EACIF,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACE,UAAU,CAAC,CAAC,KAAK,CAAC;EAClC;EACA;AACJ;AACA;EACIyB,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACK,WAAW,CAAC,CAAC,KAAK,IAAI,CAAC9B,UAAU,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACIM,UAAUA,CAAC0C,IAAI,EAAE;IACb,IAAI,CAACiE,UAAU,CAAC3D,IAAI,CAACN,IAAI,CAAC;EAC9B;EACA;AACJ;AACA;EACIhD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACmE,OAAO,CAACtB,cAAc,CAAC,IAAI,CAACJ,EAAE,CAAC;EAC/C;EACA;AACJ;AACA;EACIX,WAAWA,CAAA,EAAG;IACV,IAAIiG,IAAI,GAAG,IAAI,CAAC5D,OAAO,CAACV,WAAW,CAAC,IAAI,CAAChB,EAAE,CAAC;IAC5C,IAAIsF,IAAI,CAAC3E,UAAU,GAAG,CAAC,EAAE;MACrB;MACA;MACA,OAAO,CAAC;IACZ;IACA,OAAOF,IAAI,CAACC,IAAI,CAAC4E,IAAI,CAAC3E,UAAU,GAAG2E,IAAI,CAAC1E,YAAY,CAAC;EACzD;EACA2E,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC7D,OAAO,CAACV,WAAW,CAAC,IAAI,CAAChB,EAAE,CAAC,CAACW,UAAU;EACvD;EACA0E,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC3D,OAAO,CAACV,WAAW,CAAC,IAAI,CAAChB,EAAE,CAAC,CAACA,EAAE,IAAI,IAAI,EAAE;MAC9CwF,OAAO,CAACC,IAAI,CAAC,kDAAkD,IAAI,CAACzF,EAAE,oDAAoD,CAAC;IAC/H;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI4E,eAAeA,CAAA,EAAG;IACd,IAAIU,IAAI,GAAG,IAAI,CAAC5D,OAAO,CAACV,WAAW,CAAC,IAAI,CAAChB,EAAE,CAAC;IAC5C,MAAM0F,oBAAoB,GAAG,IAAI,CAACC,oBAAoB,CAACL,IAAI,CAAC;IAC5D,IAAII,oBAAoB,KAAKJ,IAAI,CAACjF,WAAW,EAAE;MAC3CuF,UAAU,CAAC,MAAM;QACb,IAAI,CAACnB,oBAAoB,CAAC5D,IAAI,CAAC6E,oBAAoB,CAAC;QACpD,IAAI,CAACpG,KAAK,GAAG,IAAI,CAACuG,eAAe,CAACP,IAAI,CAACjF,WAAW,EAAEiF,IAAI,CAAC1E,YAAY,EAAE0E,IAAI,CAAC3E,UAAU,EAAE,IAAI,CAAC4D,OAAO,CAAC;MACzG,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACjF,KAAK,GAAG,IAAI,CAACuG,eAAe,CAACP,IAAI,CAACjF,WAAW,EAAEiF,IAAI,CAAC1E,YAAY,EAAE0E,IAAI,CAAC3E,UAAU,EAAE,IAAI,CAAC4D,OAAO,CAAC;IACzG;EACJ;EACA;AACJ;AACA;AACA;EACIoB,oBAAoBA,CAAC5F,QAAQ,EAAE;IAC3B,MAAM+F,UAAU,GAAGrF,IAAI,CAACC,IAAI,CAACX,QAAQ,CAACY,UAAU,GAAGZ,QAAQ,CAACa,YAAY,CAAC;IACzE,IAAIkF,UAAU,GAAG/F,QAAQ,CAACM,WAAW,IAAI,CAAC,GAAGyF,UAAU,EAAE;MACrD,OAAOA,UAAU;IACrB,CAAC,MACI,IAAI/F,QAAQ,CAACM,WAAW,GAAG,CAAC,EAAE;MAC/B,OAAO,CAAC;IACZ;IACA,OAAON,QAAQ,CAACM,WAAW;EAC/B;EACA;AACJ;AACA;EACIwF,eAAeA,CAACxF,WAAW,EAAEO,YAAY,EAAED,UAAU,EAAEoF,eAAe,EAAE;IACpE;IACAA,eAAe,GAAG,CAACA,eAAe;IAClC,IAAIzG,KAAK,GAAG,EAAE;IACd;IACA;IACA,MAAMwG,UAAU,GAAGrF,IAAI,CAACuF,GAAG,CAACvF,IAAI,CAACC,IAAI,CAACC,UAAU,GAAGC,YAAY,CAAC,EAAE,CAAC,CAAC;IACpE,MAAMqF,OAAO,GAAGxF,IAAI,CAACC,IAAI,CAACqF,eAAe,GAAG,CAAC,CAAC;IAC9C,MAAMG,OAAO,GAAG7F,WAAW,IAAI4F,OAAO;IACtC,MAAME,KAAK,GAAGL,UAAU,GAAGG,OAAO,GAAG5F,WAAW;IAChD,MAAM+F,QAAQ,GAAG,CAACF,OAAO,IAAI,CAACC,KAAK;IACnC,IAAIE,cAAc,GAAGN,eAAe,GAAGD,UAAU;IACjD,IAAI1E,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,IAAI0E,UAAU,IAAI1E,CAAC,IAAI2E,eAAe,EAAE;MAC5C,IAAI9H,KAAK;MACT,IAAIqI,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAACnF,CAAC,EAAEf,WAAW,EAAE0F,eAAe,EAAED,UAAU,CAAC;MACtF,IAAIU,qBAAqB,GAAIpF,CAAC,KAAK,CAAC,KAAKgF,QAAQ,IAAID,KAAK,CAAE;MAC5D,IAAIM,qBAAqB,GAAIrF,CAAC,KAAK2E,eAAe,GAAG,CAAC,KAAKK,QAAQ,IAAIF,OAAO,CAAE;MAChF,IAAIG,cAAc,KAAKG,qBAAqB,IAAIC,qBAAqB,CAAC,EAAE;QACpExI,KAAK,GAAG,KAAK;MACjB,CAAC,MACI;QACDA,KAAK,GAAGqI,UAAU;MACtB;MACAhH,KAAK,CAACoH,IAAI,CAAC;QACPzI,KAAK,EAAEA,KAAK;QACZH,KAAK,EAAEwI;MACX,CAAC,CAAC;MACFlF,CAAC,EAAE;IACP;IACA,OAAO9B,KAAK;EAChB;EACA;AACJ;AACA;AACA;EACIiH,mBAAmBA,CAACnF,CAAC,EAAEf,WAAW,EAAE0F,eAAe,EAAED,UAAU,EAAE;IAC7D,IAAIG,OAAO,GAAGxF,IAAI,CAACC,IAAI,CAACqF,eAAe,GAAG,CAAC,CAAC;IAC5C,IAAI3E,CAAC,KAAK2E,eAAe,EAAE;MACvB,OAAOD,UAAU;IACrB,CAAC,MACI,IAAI1E,CAAC,KAAK,CAAC,EAAE;MACd,OAAOA,CAAC;IACZ,CAAC,MACI,IAAI2E,eAAe,GAAGD,UAAU,EAAE;MACnC,IAAIA,UAAU,GAAGG,OAAO,GAAG5F,WAAW,EAAE;QACpC,OAAOyF,UAAU,GAAGC,eAAe,GAAG3E,CAAC;MAC3C,CAAC,MACI,IAAI6E,OAAO,GAAG5F,WAAW,EAAE;QAC5B,OAAOA,WAAW,GAAG4F,OAAO,GAAG7E,CAAC;MACpC,CAAC,MACI;QACD,OAAOA,CAAC;MACZ;IACJ,CAAC,MACI;MACD,OAAOA,CAAC;IACZ;EACJ;AACJ;AACAiD,2BAA2B,CAACb,IAAI,YAAAmD,oCAAAjD,iBAAA;EAAA,YAAAA,iBAAA,IAAwFW,2BAA2B,EAjUpDvJ,EAAE,CAAA6I,iBAAA,CAiUoEnE,iBAAiB,GAjUvF1E,EAAE,CAAA6I,iBAAA,CAiUkG7I,EAAE,CAAC8L,iBAAiB;AAAA,CAA4C;AACnQvC,2BAA2B,CAACwC,IAAI,kBAlU+D/L,EAAE,CAAAgM,iBAAA;EAAA/C,IAAA,EAkUWM,2BAA2B;EAAA0C,SAAA;EAAAC,MAAA;IAAAhH,EAAA;IAAAuE,OAAA;EAAA;EAAA0C,OAAA;IAAAzC,UAAA;IAAAC,oBAAA;EAAA;EAAAyC,QAAA;EAAAC,QAAA,GAlUxCrM,EAAE,CAAAsM,oBAAA;AAAA,EAkU+R;AAChY;EAAA,QAAAnD,SAAA,oBAAAA,SAAA,KAnU+FnJ,EAAE,CAAAoJ,iBAAA,CAmUNG,2BAA2B,EAAc,CAAC;IACzHN,IAAI,EAAE9I,SAAS;IACf6G,IAAI,EAAE,CAAC;MACCuF,QAAQ,EAAE,2CAA2C;MACrDH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEnD,IAAI,EAAEvE;IAAkB,CAAC,EAAE;MAAEuE,IAAI,EAAEjJ,EAAE,CAAC8L;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE5G,EAAE,EAAE,CAAC;MAC1H+D,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAEqJ,OAAO,EAAE,CAAC;MACVR,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAEsJ,UAAU,EAAE,CAAC;MACbT,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAEsJ,oBAAoB,EAAE,CAAC;MACvBV,IAAI,EAAE5I;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,SAASmM,eAAeA,CAACC,KAAK,EAAE;EAC5B,OAAO,CAAC,CAACA,KAAK,IAAIA,KAAK,KAAK,OAAO;AACvC;AACA;AACA;AACA;AACA,MAAMC,2BAA2B,CAAC;EAC9B/H,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8E,OAAO,GAAG,CAAC;IAChB,IAAI,CAACzH,aAAa,GAAG,UAAU;IAC/B,IAAI,CAAC+B,SAAS,GAAG,MAAM;IACvB,IAAI,CAAC4I,2BAA2B,GAAG,YAAY;IAC/C,IAAI,CAACzK,qBAAqB,GAAG,MAAM;IACnC,IAAI,CAACsB,wBAAwB,GAAG,gBAAgB;IAChD,IAAI,CAACkG,UAAU,GAAG,IAAIzJ,YAAY,CAAC,CAAC;IACpC,IAAI,CAAC0J,oBAAoB,GAAG,IAAI1J,YAAY,CAAC,CAAC;IAC9C,IAAI,CAAC2M,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,KAAK;EAC5B;EACA,IAAIzI,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACuI,eAAe;EAC/B;EACA,IAAIvI,cAAcA,CAACrB,KAAK,EAAE;IACtB,IAAI,CAAC4J,eAAe,GAAGJ,eAAe,CAACxJ,KAAK,CAAC;EACjD;EACA,IAAI+J,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,SAAS;EACzB;EACA,IAAIE,QAAQA,CAAC/J,KAAK,EAAE;IAChB,IAAI,CAAC6J,SAAS,GAAGL,eAAe,CAACxJ,KAAK,CAAC;EAC3C;EACA,IAAIoB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC0I,WAAW;EAC3B;EACA,IAAI1I,UAAUA,CAACpB,KAAK,EAAE;IAClB,IAAI,CAAC8J,WAAW,GAAGN,eAAe,CAACxJ,KAAK,CAAC;EAC7C;EACAyB,YAAYA,CAACgE,KAAK,EAAE;IAChB,OAAOA,KAAK;EAChB;AACJ;AACAiE,2BAA2B,CAAChE,IAAI,YAAAsE,oCAAApE,iBAAA;EAAA,YAAAA,iBAAA,IAAwF8D,2BAA2B;AAAA,CAAmD;AACtMA,2BAA2B,CAACO,IAAI,kBA9X+DjN,EAAE,CAAAkN,iBAAA;EAAAjE,IAAA,EA8XWyD,2BAA2B;EAAAT,SAAA;EAAAC,MAAA;IAAAhH,EAAA;IAAAuE,OAAA;IAAApF,cAAA;IAAA0I,QAAA;IAAA3I,UAAA;IAAApC,aAAA;IAAA+B,SAAA;IAAA4I,2BAAA;IAAAzK,qBAAA;IAAAsB,wBAAA;EAAA;EAAA2I,OAAA;IAAAzC,UAAA;IAAAC,oBAAA;EAAA;EAAAwD,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qCAAA1M,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAA,MAAA2M,GAAA,GA9XxCxN,EAAE,CAAAgB,gBAAA;MAAFhB,EAAE,CAAAiB,cAAA,+BA8X6yB,CAAC;MA9XhzBjB,EAAE,CAAAkB,UAAA,wBAAAuM,+EAAAC,MAAA;QAAF1N,EAAE,CAAAoB,aAAA,CAAAoM,GAAA;QAAA,OAAFxN,EAAE,CAAAwB,WAAA,CA8X6rBV,GAAA,CAAA4I,UAAA,CAAA3D,IAAA,CAAA2H,MAAsB,CAAC;MAAA,CAAC,CAAC,kCAAAC,yFAAAD,MAAA;QA9XxtB1N,EAAE,CAAAoB,aAAA,CAAAoM,GAAA;QAAA,OAAFxN,EAAE,CAAAwB,WAAA,CA8X0wBV,GAAA,CAAA6I,oBAAA,CAAA5D,IAAA,CAAA2H,MAAgC,CAAC;MAAA,CAAC,CAAC;MA9X/yB1N,EAAE,CAAAiB,cAAA,YA8X83B,CAAC;MA9Xj4BjB,EAAE,CAAAqC,UAAA,IAAA8B,yCAAA,eA8XmgC,CAAC;MA9XtgCnE,EAAE,CAAA4B,YAAA,CA8XokG,CAAC,CAA2B,CAAC;IAAA;IAAA,IAAAf,EAAA;MAAA,MAAAS,IAAA,GA9XnmGtB,EAAE,CAAAuB,WAAA;MAAFvB,EAAE,CAAAwC,UAAA,OAAA1B,GAAA,CAAAoE,EA8XkmB,CAAC,YAAApE,GAAA,CAAA2I,OAA+C,CAAC;MA9XrpBzJ,EAAE,CAAA8B,SAAA,EA8X63B,CAAC;MA9Xh4B9B,EAAE,CAAA4N,WAAA,eAAA9M,GAAA,CAAA6L,2BAAA;MAAF3M,EAAE,CAAA8B,SAAA,CA8XggC,CAAC;MA9XngC9B,EAAE,CAAAwC,UAAA,WAAA1B,GAAA,CAAAiM,QAAA,IAAAzL,IAAA,CAAAkD,KAAA,CAAA6C,MAAA,MA8XggC,CAAC;IAAA;EAAA;EAAAwG,YAAA,GAAglHtE,2BAA2B,EAA4K7I,EAAE,CAACoN,IAAI,EAA0EpN,EAAE,CAACqN,OAAO,EAA4GrN,EAAE,CAACsN,WAAW;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA,EAAmG;AACjrK;EAAA,QAAAhF,SAAA,oBAAAA,SAAA,KA/X+FnJ,EAAE,CAAAoJ,iBAAA,CA+XNsD,2BAA2B,EAAc,CAAC;IACzHzD,IAAI,EAAE3I,SAAS;IACf0G,IAAI,EAAE,CAAC;MACCuF,QAAQ,EAAE,qBAAqB;MAC/Be,QAAQ,EAAEjE,gBAAgB;MAC1B4E,MAAM,EAAE,CAAC3E,cAAc,CAAC;MACxB6E,eAAe,EAAE5N,uBAAuB,CAAC6N,MAAM;MAC/CF,aAAa,EAAE1N,iBAAiB,CAAC6N;IACrC,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEnJ,EAAE,EAAE,CAAC;MACnB+D,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAEqJ,OAAO,EAAE,CAAC;MACVR,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAEiE,cAAc,EAAE,CAAC;MACjB4E,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAE2M,QAAQ,EAAE,CAAC;MACX9D,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAEgE,UAAU,EAAE,CAAC;MACb6E,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAE4B,aAAa,EAAE,CAAC;MAChBiH,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAE2D,SAAS,EAAE,CAAC;MACZkF,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAEuM,2BAA2B,EAAE,CAAC;MAC9B1D,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAE8B,qBAAqB,EAAE,CAAC;MACxB+G,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAEoD,wBAAwB,EAAE,CAAC;MAC3ByF,IAAI,EAAE7I;IACV,CAAC,CAAC;IAAEsJ,UAAU,EAAE,CAAC;MACbT,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAEsJ,oBAAoB,EAAE,CAAC;MACvBV,IAAI,EAAE5I;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiO,mBAAmB,CAAC;AAE1BA,mBAAmB,CAAC5F,IAAI,YAAA6F,4BAAA3F,iBAAA;EAAA,YAAAA,iBAAA,IAAwF0F,mBAAmB;AAAA,CAAkD;AACrLA,mBAAmB,CAACE,IAAI,kBArauExO,EAAE,CAAAyO,gBAAA;EAAAxF,IAAA,EAqagBqF;AAAmB,EAEe;AACnJA,mBAAmB,CAACI,IAAI,kBAxauE1O,EAAE,CAAA2O,gBAAA;EAAAC,SAAA,EAwagD,CAAClK,iBAAiB,CAAC;EAAAmK,OAAA,GAAY,CAAClO,YAAY,CAAC;AAAA,EAAI;AAClM;EAAA,QAAAwI,SAAA,oBAAAA,SAAA,KAza+FnJ,EAAE,CAAAoJ,iBAAA,CAyaNkF,mBAAmB,EAAc,CAAC;IACjHrF,IAAI,EAAExI,QAAQ;IACduG,IAAI,EAAE,CAAC;MACC6H,OAAO,EAAE,CAAClO,YAAY,CAAC;MACvBmO,YAAY,EAAE,CACVnI,YAAY,EACZ+F,2BAA2B,EAC3BnD,2BAA2B,CAC9B;MACDqF,SAAS,EAAE,CAAClK,iBAAiB,CAAC;MAC9BqK,OAAO,EAAE,CAACpI,YAAY,EAAE+F,2BAA2B,EAAEnD,2BAA2B;IACpF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS+E,mBAAmB,EAAE3H,YAAY,EAAE+F,2BAA2B,EAAEnD,2BAA2B,EAAE7E,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}