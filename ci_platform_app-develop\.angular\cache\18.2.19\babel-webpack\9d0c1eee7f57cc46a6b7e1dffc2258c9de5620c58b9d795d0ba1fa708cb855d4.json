{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as moment from 'moment';\nimport { APP_CONFIG } from '../../configs/environment.config';\nimport { FooterComponent } from '../footer/footer.component';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { SearchingSortingComponent } from '../searching-sorting/searching-sorting.component';\nimport { NavbarComponent } from '../navbar/navbar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/client-mission.service\";\nimport * as i2 from \"ng-angular-popup\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../services/auth.service\";\nimport * as i6 from \"ngx-bootstrap/tabs\";\nfunction VolunteeringMissionComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118);\n    i0.ɵɵelement(1, \"img\", 119);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getMainImageUrl(), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction VolunteeringMissionComponent_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 120);\n    i0.ɵɵtext(1, \"Plant 10,000 Trees\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VolunteeringMissionComponent_p_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 121);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"FROM \", i0.ɵɵpipeBind2(2, 2, ctx_r1.missionDetail.startDate, \"dd/MM/yyyy\"), \" Until \", i0.ɵɵpipeBind2(3, 5, ctx_r1.missionDetail.endDate, \"dd/MM/yyyy\"), \"\");\n  }\n}\nfunction VolunteeringMissionComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"img\", 122);\n    i0.ɵɵtext(2, \"\\u00A0 \");\n    i0.ɵɵelementStart(3, \"span\", 16)(4, \"div\", 123);\n    i0.ɵɵelement(5, \"div\", 124);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"br\");\n    i0.ɵɵelementStart(7, \"span\", 17);\n    i0.ɵɵtext(8, \"8000 achieved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"br\")(10, \"br\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VolunteeringMissionComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"img\", 125);\n    i0.ɵɵtext(2, \"\\u00A0 \");\n    i0.ɵɵelementStart(3, \"span\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"br\");\n    i0.ɵɵelementStart(7, \"span\", 17);\n    i0.ɵɵtext(8, \"Deadline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"br\")(10, \"br\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.missionDetail.registrationDeadLine, \"dd/MM/yyyy\"));\n  }\n}\nfunction VolunteeringMissionComponent_p_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 44);\n    i0.ɵɵtext(1, \"Documents\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VolunteeringMissionComponent_div_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 126)(1, \"div\", 127);\n    i0.ɵɵelement(2, \"img\", 128);\n    i0.ɵɵelementStart(3, \"span\")(4, \"a\", 129);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"href\", ctx_r1.missionDoc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u00A0\", ctx_r1.missionDoc, \"\");\n  }\n}\nfunction VolunteeringMissionComponent_div_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"div\", 131);\n    i0.ɵɵelement(2, \"img\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 133)(4, \"span\", 134)(5, \"b\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 135);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 136);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"src\", item_r4.userImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r4.userFullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.commentDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.commentDescription);\n  }\n}\nfunction VolunteeringMissionComponent_ng_container_162_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 139);\n    i0.ɵɵelement(1, \"img\", 140);\n    i0.ɵɵelementStart(2, \"p\", 141);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"src\", item_r5.userImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.userName);\n  }\n}\nfunction VolunteeringMissionComponent_ng_container_162_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142)(1, \"p\", 143)(2, \"b\");\n    i0.ɵɵtext(3, \"No Recent Volunteers Avilable\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction VolunteeringMissionComponent_ng_container_162_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, VolunteeringMissionComponent_ng_container_162_div_1_Template, 4, 2, \"div\", 137)(2, VolunteeringMissionComponent_ng_container_162_div_2_Template, 4, 0, \"div\", 138);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const result_r6 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", result_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", result_r6.length === 0);\n  }\n}\nexport class VolunteeringMissionComponent {\n  constructor(_service, _toast, _activeRoute, _router, _datePipe, _adminservice) {\n    this._service = _service;\n    this._toast = _toast;\n    this._activeRoute = _activeRoute;\n    this._router = _router;\n    this._datePipe = _datePipe;\n    this._adminservice = _adminservice;\n    this.imageList = [];\n    this.recentVolunteerList = [];\n    this.loginUserId = 0;\n    this.btnText = 'Apply Now';\n    this.missionCommentList = [];\n    this.missionFavourite = false;\n    this.favImag = 'assets/Img/heart1.png';\n    this.favImag1 = 'assets/Img/heart11.png';\n    this.unsubscribe = [];\n    this.missionId = this._activeRoute.snapshot.paramMap.get('missionId');\n  }\n  ngOnInit() {\n    const currentUserSubscribe = this._adminservice.getCurrentUser().subscribe(data => {\n      const loginUserDetail = this._adminservice.getUserDetail();\n      data == null ? this.loginUserId = loginUserDetail.userId : this.loginUserId = data.userId;\n      data == null ? this.loginUserName = loginUserDetail.fullName : this.loginUserName = data.fullName;\n    });\n    if (this.missionId != null) {\n      this.fetchMissionDetail(this.missionId);\n    }\n    this.applyModal = new window.bootstrap.Modal(document.getElementById('applyMissionModal'));\n    this.getRecentVolunteerList();\n    this.unsubscribe.push(currentUserSubscribe);\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach(sb => sb.unsubscribe());\n  }\n  fetchMissionDetail(missionId) {\n    const value = {\n      missionId: missionId,\n      userId: this.loginUserId\n    };\n    const missionDetailSubscribe = this._service.missionDetailByMissionId(value).subscribe(data => {\n      if (data.result == 1) {\n        this.missionDetail = data.data;\n        this.imageList = this.missionDetail.missionImages.split(',');\n        // Gallery images removed - using simple image display\n        if (this.missionDetail.missionDocuments) {\n          this.missionDoc = this._service.imageUrl + '/' + this.missionDetail.missionDocuments;\n        }\n        this.btnText = this.missionDetail.missionApplyStatus == 'Applied' ? 'Already Apply' : 'Apply Now';\n        this.getMissionCommentList();\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration\n        });\n      }\n    }, err => this._toast.error({\n      detail: 'ERROR',\n      summary: err.message,\n      duration: APP_CONFIG.toastDuration\n    }));\n    this.unsubscribe.push(missionDetailSubscribe);\n  }\n  getImageUrls() {\n    const imageUrls = [];\n    for (const photo of this.imageList) {\n      imageUrls.push(this._service.imageUrl + '/' + photo.replaceAll('\\\\', '/'));\n    }\n    return imageUrls;\n  }\n  getMainImageUrl() {\n    if (this.imageList && this.imageList.length > 0) {\n      return this._service.imageUrl + '/' + this.imageList[0].replaceAll('\\\\', '/');\n    }\n    return '';\n  }\n  openApplyMissionModal(id) {\n    this.applyModal.show();\n    this.missionId = id;\n  }\n  closeApplyMissionModal() {\n    this.applyModal.hide();\n  }\n  applyMission(id) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        appliedDate: moment().format('yyyy-MM-DDTHH:mm:ssZ'),\n        status: false,\n        sheet: 1\n      };\n      const missionSubscribe = this._service.applyMission(value).subscribe(data => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data\n          });\n          setTimeout(() => {\n            this.closeApplyMissionModal();\n            this._router.navigate(['/home']);\n          }, 1000);\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration\n          });\n        }\n      }, err => this._toast.error({\n        detail: 'ERROR',\n        summary: err.message,\n        duration: APP_CONFIG.toastDuration\n      }));\n      this.unsubscribe.push(missionSubscribe);\n    }\n  }\n  postComment(commentdesc) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        CommentDescription: commentdesc,\n        commentDate: moment().format('yyyy-MM-DDTHH:mm:ssZ')\n      };\n      const missionCommentSubscribe = this._service.addMissionComment(value).subscribe(data => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data,\n            duration: APP_CONFIG.toastDuration\n          });\n          window.location.reload();\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration\n          });\n        }\n      }, err => this._toast.error({\n        detail: 'ERROR',\n        summary: err.message,\n        duration: APP_CONFIG.toastDuration\n      }));\n      this.unsubscribe.push(missionCommentSubscribe);\n    }\n  }\n  getMissionCommentList() {\n    const missionCommentSubscribe = this._service.missionCommentListByMissionId(this.missionDetail.id).subscribe(data => {\n      if (data.result == 1) {\n        this.missionCommentList = data.data;\n        this.missionCommentList = this.missionCommentList.map(x => {\n          return {\n            id: x.id,\n            commentDescription: x.commentDescription,\n            commentDate: x.commentDate ? this._datePipe.transform(x.commentDate, 'EEEE, MMMM d, y, h:mm a') : '',\n            missionId: x.missionId,\n            userId: x.userId,\n            userFullName: x.userFullName,\n            userImage: x.userImage ? this._service.imageUrl + '/' + x.userImage : 'assets/NoImg.png'\n          };\n        });\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration\n        });\n      }\n    }, err => this._toast.error({\n      detail: 'ERROR',\n      summary: err.message,\n      duration: APP_CONFIG.toastDuration\n    }));\n    this.unsubscribe.push(missionCommentSubscribe);\n  }\n  getMissionFavourite(missionId) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      this.missionFavourite = !this.missionFavourite;\n      const value = {\n        missionId: missionId,\n        userId: this.loginUserId\n      };\n      if (this.missionFavourite) {\n        const addMissionFavouriteSubscribe = this._service.addMissionFavourite(value).subscribe(data => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration\n            });\n          }\n        });\n        this.unsubscribe.push(addMissionFavouriteSubscribe);\n      } else {\n        const removeMissionFavouriteSubscribe = this._service.removeMissionFavourite(value).subscribe(data => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration\n            });\n          }\n        });\n        this.unsubscribe.push(removeMissionFavouriteSubscribe);\n      }\n    }\n  }\n  getRecentVolunteerList() {\n    const value = {\n      missionId: this.missionId,\n      userId: this.loginUserId\n    };\n    const volunteerListSubscribe = this._service.recentVolunteerList(value).subscribe(data => {\n      if (data.result == 1) {\n        this.recentVolunteerList = data.data;\n        this.recentVolunteerList = this.recentVolunteerList.map(x => {\n          return {\n            id: x.id,\n            missioId: x.missioId,\n            userId: x.userId,\n            userName: x.userName,\n            userImage: x.userImage ? this._service.imageUrl + '/' + x.userImage : 'assets/NoImg.png'\n          };\n        });\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration\n        });\n      }\n    });\n    this.unsubscribe.push(volunteerListSubscribe);\n  }\n  static {\n    this.ɵfac = function VolunteeringMissionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || VolunteeringMissionComponent)(i0.ɵɵdirectiveInject(i1.ClientMissionService), i0.ɵɵdirectiveInject(i2.NgToastService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.DatePipe), i0.ɵɵdirectiveInject(i5.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VolunteeringMissionComponent,\n      selectors: [[\"app-volunteering-mission\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 346,\n      vars: 28,\n      consts: [[\"commentdesc\", \"\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [1, \"container\"], [1, \"row\", 2, \"height\", \"650px\"], [1, \"col-sm-6\"], [\"class\", \"image-gallery\", 4, \"ngIf\"], [1, \"col-sm-6\", \"content\"], [1, \"heading\"], [1, \"detail\"], [1, \"mt-5\"], [1, \"bordert\"], [1, \"totalTree\"], [\"class\", \"totalTreegoal\", 4, \"ngIf\"], [\"class\", \"totalTreetime\", 4, \"ngIf\"], [1, \"SeatDeadLines\", \"row\"], [\"src\", \"assets/Img/Seats-left.png\"], [2, \"font-size\", \"24px !important\"], [2, \"margin-left\", \"40px\"], [\"class\", \"col-sm-6\", 4, \"ngIf\"], [2, \"width\", \"616px\", \"margin\", \"20px 100px 0px 61px\", \"border\", \"1px solid #757575\", \"opacity\", \"0.1\"], [1, \"row\"], [1, \"btnMission\"], [3, \"click\"], [\"alt\", \"NoImage\", 2, \"width\", \"30px\", 3, \"src\"], [1, \"Mission\"], [\"src\", \"assets/Img/add1.png\", \"alt\", \"NoImage\"], [1, \"totalRating\"], [1, \"fa\", \"fa-star\"], [1, \"col-sm-3\", \"carddetail\"], [1, \"top-left\"], [\"src\", \"assets/Img/pin1.png\", \"alt\", \"NoImage\"], [1, \"bottom-left\"], [2, \"color\", \"#757575 !important\"], [\"src\", \"assets/Img/web.png\", \"alt\", \"NoImage\"], [\"src\", \"assets/Img/organization.png\", \"alt\", \"NoImage\"], [1, \"d-grid\", \"card-footer\", \"align-items-center\", 2, \"display\", \"flex\", \"justify-content\", \"center\", \"margin-left\", \"20%\"], [\"type\", \"submit\", 1, \"btn-login\", 3, \"click\", \"disabled\"], [1, \"Login\"], [1, \"fa\", \"fa-arrow-right\", 2, \"margin-top\", \"5px !important\"], [1, \"row\", 2, \"height\", \"650px\", \"margin-top\", \"14%\"], [1, \"col-sm-8\"], [1, \"tab-panel\", \"p-3\"], [1, \"member-tabset\"], [\"heading\", \"Mission\"], [1, \"Introduction\"], [1, \"contentDetail\"], [\"class\", \"Introduction\", 4, \"ngIf\"], [\"class\", \"documentFile row mt-4\", 4, \"ngIf\"], [\"heading\", \"Organization\"], [\"heading\", \"Comments\"], [\"rows\", \"4\", \"placeholder\", \"Enter Your Comments\", 1, \"form-control\"], [1, \"mt-4\"], [\"type\", \"button\", 1, \"btn-login\", 3, \"click\"], [1, \"mt-4\", 2, \"height\", \"400px !important\", \"overflow-y\", \"auto\", \"max-height\", \"400px !important\", \"border\", \"none\"], [\"class\", \"card mt-2 col-sm-11 row\", \"style\", \"height: 100px !important;margin-left: 10px;\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-sm-4\"], [1, \"card\", \"cardInformation\"], [1, \"card-body\"], [1, \"cardBodyDetail\", 2, \"font-size\", \"22px !important\", \"font-weight\", \"700\"], [1, \"underLine\"], [1, \"cardBodyDetail\"], [2, \"margin-left\", \"5%\"], [2, \"margin-left\", \"6%\"], [1, \"col-sm-5\", 2, \"margin-left\", \"5%\"], [1, \"fa\", \"fa-star\", \"checked\"], [1, \"card\", \"userInformation\"], [4, \"ngIf\"], [1, \"container-fluid\"], [2, \"margin-top\", \"9%\", \"height\", \"1px\", \"border\", \"1px solid gray\", \"opacity\", \"0.1\"], [1, \"\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"justify-content\", \"center\", \"font-size\", \"22px\", \"font-weight\", \"400\", \"color\", \"#757575\", \"margin-top\", \"2%\"], [1, \"container-fluid\", 2, \"width\", \"fit-content\", \"height\", \"676px\"], [1, \"row\", \"mt-5\"], [1, \"col-sm-4\", \"row\", \"Rounded-Rectangle-2-copy\"], [1, \"card-header\", 2, \"width\", \"460px\", \"height\", \"220px\"], [\"src\", \"assets/Images/1.png\", \"alt\", \"NoImage\"], [1, \"bottom-leftimg\"], [1, \"fa\", \"fa-map-marker\"], [1, \"bottom-rightsimg\"], [\"src\", \"assets/Img/heart1.png\", \"alt\", \"NoImage\"], [1, \"bottom-rightimg\"], [1, \"fa\", \"fa-user-plus\", \"3-x\"], [1, \"centered\"], [1, \"content\"], [1, \"row\", 2, \"margin\", \"14px\"], [1, \"col-sm-7\", \"contentdetail\"], [1, \"col-sm-5\", 2, \"display\", \"flex\", \"justify-content\", \"flex-end\"], [1, \"text-center\", \"data\", \"py-3\"], [2, \"margin-top\", \"-12px\"], [1, \"SeatDeadLine\", \"row\"], [1, \"col-sm-12\"], [\"src\", \"assets/Img/Already-volunteered.png\", \"alt\", \"NoImage\", 2, \"margin-top\", \"-1%\"], [2, \"font-size\", \"24px !important\", \"margin-top\", \"2%\"], [2, \"margin-left\", \"34px\"], [2, \"border\", \"1px solid #e8e8e8\", \"width\", \"100%\"], [1, \"d-grid\", \"card-footer\", 2, \"margin-left\", \"32%\"], [\"type\", \"submit\", 1, \"btn-logins\"], [1, \"Logins\"], [\"src\", \"assets/Img/animal.png\", \"alt\", \"NoImage\"], [\"src\", \"assets/Img/Seats-left.png\", \"alt\", \"NoImage\", 2, \"margin-top\", \"-1%\"], [\"src\", \"assets/Img/achieved.png\", \"alt\", \"NoImage\", 2, \"margin-top\", \"-1%\", \"margin-left\", \"-8%\"], [1, \"progress\", 2, \"margin-top\", \"-10% !important\", \"margin-bottom\", \"-16% !important\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"25\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"bg-warning\", 2, \"width\", \"75%\"], [2, \"margin-left\", \"30px !important\"], [\"src\", \"assets/Img/Plantation.png\", \"alt\", \"NoImage\"], [\"id\", \"applyMissionModal\", \"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 2, \"margin-top\", \"8%\"], [\"role\", \"document\", 1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [\"type\", \"hidden\", \"value\", \"\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btnCancel\", 3, \"click\"], [1, \"bCancel\"], [\"type\", \"button\", 1, \"btnRemove\", 3, \"click\"], [1, \"bremove\"], [1, \"image-gallery\"], [\"alt\", \"Mission Image\", 1, \"img-fluid\", \"main-image\", 2, \"width\", \"100%\", \"height\", \"465px\", \"object-fit\", \"cover\", 3, \"src\"], [1, \"totalTreegoal\"], [1, \"totalTreetime\"], [\"src\", \"assets/Img/achieved.png\"], [1, \"progress\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"25\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"bg-warning\", 2, \"width\", \"50%\"], [\"src\", \"assets/Img/deadline.png\"], [1, \"documentFile\", \"row\", \"mt-4\"], [1, \"col-sm-3\", \"document\"], [\"src\", \"assets/Img/pdf.png\"], [2, \"color\", \"#414141\", \"text-decoration\", \"none\", 3, \"href\"], [1, \"card\", \"mt-2\", \"col-sm-11\", \"row\", 2, \"height\", \"100px !important\", \"margin-left\", \"10px\"], [1, \"col-sm-1\"], [\"alt\", \"NoImage\", \"onerror\", \"this.src='assets/NoUser.png'\", 1, \"userimg\", 3, \"src\"], [1, \"col-sm-10\"], [2, \"font-size\", \"16px\"], [2, \"font-size\", \"14px\"], [1, \"commentdisc\"], [\"class\", \"col-sm-4 row\", \"style\", \"margin-bottom: 4%;\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-sm-12 justify-content-center\", 4, \"ngIf\"], [1, \"col-sm-4\", \"row\", 2, \"margin-bottom\", \"4%\"], [\"alt\", \"NoImage\", 2, \"width\", \"90px\", \"height\", \"70px\", \"border-radius\", \"50%\", \"margin-left\", \"0%\", 3, \"src\"], [2, \"width\", \"120px\"], [1, \"col-sm-12\", \"justify-content-center\"], [1, \"text-danger\", 2, \"font-size\", \"18px\", \"text-align\", \"center\", \"margin-top\", \"50%\"]],\n      template: function VolunteeringMissionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelement(0, \"meta\", 1);\n          i0.ɵɵelementStart(1, \"div\");\n          i0.ɵɵelement(2, \"app-navbar\")(3, \"app-searching-sorting\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4);\n          i0.ɵɵtemplate(7, VolunteeringMissionComponent_div_7_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"p\", 7);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 8);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"div\", 11);\n          i0.ɵɵtemplate(16, VolunteeringMissionComponent_p_16_Template, 2, 0, \"p\", 12)(17, VolunteeringMissionComponent_p_17_Template, 4, 8, \"p\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 4);\n          i0.ɵɵelement(20, \"img\", 15);\n          i0.ɵɵtext(21, \"\\u00A0 \");\n          i0.ɵɵelementStart(22, \"span\", 16);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"br\");\n          i0.ɵɵelementStart(25, \"span\", 17);\n          i0.ɵɵtext(26, \"Seats left\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(27, VolunteeringMissionComponent_div_27_Template, 11, 0, \"div\", 18)(28, VolunteeringMissionComponent_div_28_Template, 11, 4, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"p\", 19);\n          i0.ɵɵelementStart(30, \"div\", 20)(31, \"div\", 4)(32, \"button\", 21)(33, \"span\", 22);\n          i0.ɵɵlistener(\"click\", function VolunteeringMissionComponent_Template_span_click_33_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getMissionFavourite(ctx.missionDetail.id));\n          });\n          i0.ɵɵelement(34, \"img\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\", 24);\n          i0.ɵɵtext(36, \"Add to Favourite\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 4)(38, \"button\", 21)(39, \"span\");\n          i0.ɵɵelement(40, \"img\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"span\", 24);\n          i0.ɵɵtext(42, \"Recommend to a Co-Worker \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(43, \"div\", 26);\n          i0.ɵɵelement(44, \"span\", 27)(45, \"span\", 27)(46, \"span\", 27)(47, \"span\", 27)(48, \"span\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 20)(50, \"div\", 28)(51, \"div\", 29);\n          i0.ɵɵelement(52, \"img\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 31)(54, \"span\", 32);\n          i0.ɵɵtext(55, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(56, \"br\");\n          i0.ɵɵelementStart(57, \"span\");\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 28)(60, \"div\", 29);\n          i0.ɵɵelement(61, \"img\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"div\", 31)(63, \"span\", 32);\n          i0.ɵɵtext(64, \"Theme\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"br\");\n          i0.ɵɵelementStart(66, \"span\");\n          i0.ɵɵtext(67);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(68, \"div\", 28)(69, \"div\", 29);\n          i0.ɵɵelement(70, \"img\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 31)(72, \"span\", 32);\n          i0.ɵɵtext(73, \"Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(74, \"br\");\n          i0.ɵɵelementStart(75, \"span\");\n          i0.ɵɵtext(76);\n          i0.ɵɵpipe(77, \"date\");\n          i0.ɵɵelement(78, \"br\");\n          i0.ɵɵtext(79);\n          i0.ɵɵpipe(80, \"date\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(81, \"div\", 28)(82, \"div\", 29);\n          i0.ɵɵelement(83, \"img\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 31)(85, \"span\", 32);\n          i0.ɵɵtext(86, \"Organization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(87, \"br\");\n          i0.ɵɵelementStart(88, \"span\");\n          i0.ɵɵtext(89);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(90, \"div\", 35)(91, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function VolunteeringMissionComponent_Template_button_click_91_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openApplyMissionModal(ctx.missionDetail.id));\n          });\n          i0.ɵɵelementStart(92, \"span\", 37);\n          i0.ɵɵtext(93);\n          i0.ɵɵelement(94, \"i\", 38);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(95, \"div\", 39)(96, \"div\", 40)(97, \"div\", 41)(98, \"tabset\", 42)(99, \"tab\", 43)(100, \"p\", 44);\n          i0.ɵɵtext(101, \"Introduction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"p\", 45);\n          i0.ɵɵtext(103, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. \");\n          i0.ɵɵelement(104, \"br\")(105, \"br\");\n          i0.ɵɵtext(106, \" Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. \");\n          i0.ɵɵelement(107, \"br\")(108, \"br\");\n          i0.ɵɵtext(109, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"p\", 44);\n          i0.ɵɵtext(111, \"Challenge\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"p\", 45);\n          i0.ɵɵtext(113, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. \");\n          i0.ɵɵelement(114, \"br\")(115, \"br\");\n          i0.ɵɵtext(116, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(117, VolunteeringMissionComponent_p_117_Template, 2, 0, \"p\", 46)(118, VolunteeringMissionComponent_div_118_Template, 6, 2, \"div\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"tab\", 48);\n          i0.ɵɵtext(120);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"tab\", 49);\n          i0.ɵɵelement(122, \"textarea\", 50, 0);\n          i0.ɵɵelementStart(124, \"div\", 51)(125, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function VolunteeringMissionComponent_Template_button_click_125_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const commentdesc_r3 = i0.ɵɵreference(123);\n            return i0.ɵɵresetView(ctx.postComment(commentdesc_r3.value));\n          });\n          i0.ɵɵelementStart(126, \"span\", 37);\n          i0.ɵɵtext(127, \"Post Comment\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(128, \"div\", 53);\n          i0.ɵɵtemplate(129, VolunteeringMissionComponent_div_129_Template, 11, 4, \"div\", 54);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(130, \"div\", 55)(131, \"div\", 56)(132, \"div\", 57)(133, \"p\", 58);\n          i0.ɵɵtext(134, \"Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(135, \"p\", 59);\n          i0.ɵɵelementStart(136, \"p\", 60);\n          i0.ɵɵtext(137, \"Skills\");\n          i0.ɵɵelementStart(138, \"span\", 61);\n          i0.ɵɵtext(139);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(140, \"p\", 59);\n          i0.ɵɵelementStart(141, \"p\", 60);\n          i0.ɵɵtext(142, \"Day \");\n          i0.ɵɵelementStart(143, \"span\", 62);\n          i0.ɵɵtext(144, \"\\u00A0Weekend only\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(145, \"p\", 59);\n          i0.ɵɵelementStart(146, \"p\", 60)(147, \"span\");\n          i0.ɵɵtext(148, \"Rating\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(149, \"span\", 63);\n          i0.ɵɵelement(150, \"span\", 64)(151, \"span\", 64)(152, \"span\", 64)(153, \"span\", 64)(154, \"span\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(155, \"\\u00A0 (by 125 volunteers) \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(156, \"div\", 65)(157, \"div\", 57)(158, \"p\", 58);\n          i0.ɵɵtext(159, \"Recent Volunteers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(160, \"p\", 59);\n          i0.ɵɵelementStart(161, \"div\", 20);\n          i0.ɵɵtemplate(162, VolunteeringMissionComponent_ng_container_162_Template, 3, 2, \"ng-container\", 66);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(163, \"div\", 67);\n          i0.ɵɵelement(164, \"p\", 68);\n          i0.ɵɵelementStart(165, \"div\", 69)(166, \"p\", 70);\n          i0.ɵɵtext(167, \" Related Mission \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(168, \"div\", 71)(169, \"div\", 72)(170, \"div\", 73)(171, \"div\", 74);\n          i0.ɵɵelement(172, \"img\", 75);\n          i0.ɵɵelementStart(173, \"div\", 76);\n          i0.ɵɵelement(174, \"i\", 77);\n          i0.ɵɵtext(175, \"\\u00A0Toronto\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(176, \"div\", 78);\n          i0.ɵɵelement(177, \"img\", 79);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(178, \"div\", 80);\n          i0.ɵɵelement(179, \"i\", 81);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"div\", 82);\n          i0.ɵɵtext(181, \"Environment\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(182, \"div\", 57)(183, \"p\", 7);\n          i0.ɵɵtext(184, \"Grow Trees \\u2013 On the path to environment sustainability\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(185, \"p\", 83);\n          i0.ɵɵtext(186, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(187, \"div\", 84)(188, \"div\", 85);\n          i0.ɵɵtext(189, \" CSE Network \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(190, \"div\", 86);\n          i0.ɵɵelement(191, \"span\", 64)(192, \"span\", 64)(193, \"span\", 64)(194, \"span\", 27)(195, \"span\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(196, \"div\")(197, \"div\", 10)(198, \"div\", 87)(199, \"p\", 88);\n          i0.ɵɵtext(200, \"Ongoing Opportunity\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(201, \"div\", 89)(202, \"div\", 90);\n          i0.ɵɵelement(203, \"img\", 91);\n          i0.ɵɵtext(204, \"\\u00A0 \");\n          i0.ɵɵelementStart(205, \"span\", 92);\n          i0.ɵɵtext(206, \"250\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(207, \"br\");\n          i0.ɵɵelementStart(208, \"span\", 93);\n          i0.ɵɵtext(209, \"Already volunteered\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(210, \"P\", 94);\n          i0.ɵɵelementStart(211, \"div\", 95)(212, \"button\", 96)(213, \"span\", 97);\n          i0.ɵɵtext(214, \"Apply \\u00A0\");\n          i0.ɵɵelement(215, \"i\", 38);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(216, \"div\", 73)(217, \"div\", 74);\n          i0.ɵɵelement(218, \"img\", 98);\n          i0.ɵɵelementStart(219, \"div\", 76);\n          i0.ɵɵelement(220, \"i\", 77);\n          i0.ɵɵtext(221, \"\\u00A0Cape Town\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(222, \"div\", 78);\n          i0.ɵɵelement(223, \"img\", 79);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(224, \"div\", 80);\n          i0.ɵɵelement(225, \"i\", 81);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(226, \"div\", 82);\n          i0.ɵɵtext(227, \"Environment\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(228, \"div\", 57)(229, \"p\", 7);\n          i0.ɵɵtext(230, \"Animal Welfare & save birds campaign \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(231, \"p\", 83);\n          i0.ɵɵtext(232, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(233, \"div\", 84)(234, \"div\", 85);\n          i0.ɵɵtext(235, \" JR Foundation \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(236, \"div\", 86);\n          i0.ɵɵelement(237, \"span\", 64)(238, \"span\", 64)(239, \"span\", 64)(240, \"span\", 27)(241, \"span\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(242, \"div\")(243, \"div\", 10)(244, \"div\", 87)(245, \"p\", 88);\n          i0.ɵɵtext(246, \"Plant 10,000 Trees\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(247, \"div\", 89)(248, \"div\", 4);\n          i0.ɵɵelement(249, \"img\", 99);\n          i0.ɵɵtext(250, \"\\u00A0 \");\n          i0.ɵɵelementStart(251, \"span\", 92);\n          i0.ɵɵtext(252, \"10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(253, \"br\");\n          i0.ɵɵelementStart(254, \"span\", 93);\n          i0.ɵɵtext(255, \"Seat left\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(256, \"div\", 4);\n          i0.ɵɵelement(257, \"img\", 100);\n          i0.ɵɵtext(258, \"\\u00A0 \");\n          i0.ɵɵelementStart(259, \"span\", 16)(260, \"div\", 101);\n          i0.ɵɵelement(261, \"div\", 102);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(262, \"br\");\n          i0.ɵɵelementStart(263, \"span\", 103);\n          i0.ɵɵtext(264, \"8000 achieved\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(265, \"P\", 94);\n          i0.ɵɵelementStart(266, \"div\", 95)(267, \"button\", 96)(268, \"span\", 97);\n          i0.ɵɵtext(269, \"Apply \\u00A0\");\n          i0.ɵɵelement(270, \"i\", 38);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(271, \"div\", 73)(272, \"div\", 74);\n          i0.ɵɵelement(273, \"img\", 104);\n          i0.ɵɵelementStart(274, \"div\", 76);\n          i0.ɵɵelement(275, \"i\", 77);\n          i0.ɵɵtext(276, \"\\u00A0Paris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(277, \"div\", 78);\n          i0.ɵɵelement(278, \"img\", 79);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(279, \"div\", 80);\n          i0.ɵɵelement(280, \"i\", 81);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(281, \"div\", 82);\n          i0.ɵɵtext(282, \"Environment\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(283, \"div\", 57)(284, \"p\", 7);\n          i0.ɵɵtext(285, \"Plantation and Afforestation programme \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(286, \"p\", 83);\n          i0.ɵɵtext(287, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(288, \"div\", 84)(289, \"div\", 85);\n          i0.ɵɵtext(290, \" Amaze Doctors \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(291, \"div\", 86);\n          i0.ɵɵelement(292, \"span\", 64)(293, \"span\", 64)(294, \"span\", 64)(295, \"span\", 27)(296, \"span\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(297, \"div\")(298, \"div\", 10)(299, \"div\", 87)(300, \"p\", 88);\n          i0.ɵɵtext(301, \"Plant 10,000 Trees\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(302, \"div\", 89)(303, \"div\", 4);\n          i0.ɵɵelement(304, \"img\", 99);\n          i0.ɵɵtext(305, \"\\u00A0 \");\n          i0.ɵɵelementStart(306, \"span\", 92);\n          i0.ɵɵtext(307, \"10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(308, \"br\");\n          i0.ɵɵelementStart(309, \"span\", 93);\n          i0.ɵɵtext(310, \"Seat left\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(311, \"div\", 4);\n          i0.ɵɵelement(312, \"img\", 100);\n          i0.ɵɵtext(313, \"\\u00A0 \");\n          i0.ɵɵelementStart(314, \"span\", 16)(315, \"div\", 101);\n          i0.ɵɵelement(316, \"div\", 102);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(317, \"br\");\n          i0.ɵɵelementStart(318, \"span\", 103);\n          i0.ɵɵtext(319, \"8000 achieved\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(320, \"P\", 94);\n          i0.ɵɵelementStart(321, \"div\", 95)(322, \"button\", 96)(323, \"span\", 97);\n          i0.ɵɵtext(324, \"Apply \\u00A0\");\n          i0.ɵɵelement(325, \"i\", 38);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵelementStart(326, \"div\", 9);\n          i0.ɵɵelement(327, \"app-footer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(328, \"div\", 105)(329, \"div\", 106)(330, \"div\", 107)(331, \"div\", 108)(332, \"h5\", 109);\n          i0.ɵɵtext(333, \"Confirm Delete\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(334, \"button\", 110);\n          i0.ɵɵlistener(\"click\", function VolunteeringMissionComponent_Template_button_click_334_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closeApplyMissionModal());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(335, \"div\", 111);\n          i0.ɵɵelement(336, \"input\", 112);\n          i0.ɵɵelementStart(337, \"h4\");\n          i0.ɵɵtext(338, \"Are you sure you want to apply this mission?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(339, \"div\", 113)(340, \"button\", 114);\n          i0.ɵɵlistener(\"click\", function VolunteeringMissionComponent_Template_button_click_340_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closeApplyMissionModal());\n          });\n          i0.ɵɵelementStart(341, \"span\", 115);\n          i0.ɵɵtext(342, \" Cancel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(343, \"button\", 116);\n          i0.ɵɵlistener(\"click\", function VolunteeringMissionComponent_Template_button_click_343_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.applyMission(ctx.missionId));\n          });\n          i0.ɵɵelementStart(344, \"span\", 117);\n          i0.ɵɵtext(345, \"Apply\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.imageList && ctx.imageList.length > 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.missionDetail.missionTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.missionDetail.missionDescription, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.missionDetail.missionType == \"Goal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.missionDetail.missionType == \"Time\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.missionDetail.totalSheets);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.missionDetail.missionType == \"Goal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.missionDetail.missionType == \"Time\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵpropertyInterpolate(\"src\", ctx.missionDetail.missionFavouriteStatus == \"0\" ? ctx.favImag : ctx.favImag1, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(24);\n          i0.ɵɵtextInterpolate(ctx.missionDetail.cityName);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.missionDetail.missionThemeName);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\"From\", i0.ɵɵpipeBind2(77, 22, ctx.missionDetail.startDate, \"dd/MM/yyyy\"), \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"Until \", i0.ɵɵpipeBind2(80, 25, ctx.missionDetail.endDate, \"dd/MM/yyyy\"), \"\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.missionDetail.missionOrganisationName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.missionDetail.missionApplyStatus == \"Applied\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.btnText, \" \\u00A0\");\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngIf\", ctx.missionDoc != undefined);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.missionDoc != undefined);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.missionDetail.missionOrganisationDetail);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.missionCommentList);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.missionDetail.missionSkillName);\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngIf\", ctx.recentVolunteerList);\n        }\n      },\n      dependencies: [FooterComponent, TabsModule, i6.TabDirective, i6.TabsetComponent, CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, SearchingSortingComponent, NavbarComponent],\n      styles: [\"\\n\\n@media screen and (max-width: 768px) {\\n  \\n\\n}\\n.container[_ngcontent-%COMP%] {\\n  width: 100%; \\n\\n}\\nimg[_ngcontent-%COMP%] {\\n  max-width: 100%; \\n\\n}\\n\\n\\n.button[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n}\\n\\n.content[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%] {\\n  width: 559px;\\n  height: 87px;\\n  margin: 49px 100px 0px 61px;\\n  font-family: NotoSans;\\n  font-size: 38px;\\n  font-weight: 200;\\n  text-align: left;\\n  color: #414141;\\n}\\n.content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%] {\\n  width: 615px;\\n  height: 45px;\\n  margin: 25px 43px 5px 62px;\\n  font-family: NotoSans;\\n  font-size: 17px;\\n  font-weight: 300;\\n\\n  text-align: left;\\n  color: #414141;\\n}\\n.bordert[_ngcontent-%COMP%] {\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  background-color: #fff;\\n  position: relative;\\n  margin-top: 25px;\\n  margin-left: 10%;\\n}\\n.content[_ngcontent-%COMP%]   .totalTreegoal[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -21px;\\n  left: 33%;\\n  width: fit-content;\\n  height: 38px;\\n  background-color: #fff;\\n  border: 1px solid rgba(0, 0, 0, 0.06);\\n  padding: 6px 29px 6px 37px;\\n  border-radius: 17.5px;\\n}\\n.totalTreetime[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -21px;\\n  left: 24%;\\n  width: fit-content;\\n  height: 38px;\\n  background-color: #fff;\\n  border: 1px solid rgba(0, 0, 0, 0.06);\\n  padding: 6px 29px 6px 37px;\\n  border-radius: 17.5px;\\n}\\n.progress[_ngcontent-%COMP%] {\\n  margin-left: 15%;\\n  margin-top: -12%;\\n  margin-bottom: -10%;\\n}\\n.btnMission[_ngcontent-%COMP%] {\\n  width: 306px;\\n  height: 50px;\\n  margin: 35px 100px 15px 61px;\\n  border-radius: 25px;\\n  border: solid 2px #757575;\\n  background-color: #fff;\\n}\\n\\n.Mission[_ngcontent-%COMP%] {\\n  width: 256px;\\n  height: 13px;\\n  margin: 2px 0 5px 9px;\\n  font-family: NotoSans;\\n  font-size: 17px;\\n  text-align: left;\\n  color: #757575;\\n}\\n.content[_ngcontent-%COMP%]   .totalRating[_ngcontent-%COMP%] {\\n  width: 197px;\\n  height: 42px;\\n  margin: 5px 4px 3px 292px;\\n  padding: 6px 29px 12px 37px;\\n}\\n.fa-star[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-right: 2px;\\n  margin-top: 5px;\\n  color: #ddd;\\n}\\n.checked[_ngcontent-%COMP%] {\\n  color: orange;\\n}\\n.top-left[_ngcontent-%COMP%] {\\n  top: 8px;\\n  left: 16px;\\n}\\n.bottom-left[_ngcontent-%COMP%] {\\n  margin-top: 5%;\\n  word-wrap: unset;\\n  width: 142px;\\n  font-weight: 16px;\\n}\\n.carddetail[_ngcontent-%COMP%] {\\n  width: 153px;\\n  height: 130px;\\n  margin: 15px -50px 30px 61px;\\n  padding: 16px 84px 18px 11px;\\n  border-radius: 3px;\\n  border: solid 1px #e8e8e8;\\n  background-color: #fff;\\n}\\n.btn-login[_ngcontent-%COMP%] {\\n  width: 273px;\\n  height: 48px;\\n  border-radius: 24px;\\n  border: solid 2px #f88634;\\n  background-color: white;\\n}\\n\\n.Login[_ngcontent-%COMP%] {\\n  width: 43px;\\n  height: 18px;\\n  font-size: 22px;\\n  text-align: left;\\n  color: #f88634;\\n}\\n\\n.Introduction[_ngcontent-%COMP%] {\\n  width: 144px;\\n  height: 20px;\\n  font-family: NotoSans;\\n  font-size: 25px;\\n  text-align: left;\\n  color: #414141;\\n}\\n.contentDetail[_ngcontent-%COMP%] {\\n  width: 891px;\\n  height: 232px;\\n  margin-top: 25px;\\n  font-family: NotoSans;\\n  font-size: 15px;\\n  font-weight: 300;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: 1.6;\\n  letter-spacing: normal;\\n  text-align: left;\\n  color: #414141;\\n}\\n.document[_ngcontent-%COMP%] {\\n  margin-right: 15px;\\n  margin-top: 15px;\\n  border-radius: 25px;\\n  padding: 4px 18px 4px 14px;\\n  text-align: center;\\n  border: solid 2px #757575;\\n  background-color: white;\\n  min-width: fit-content;\\n  max-width: fit-content;\\n}\\n.documentFile[_ngcontent-%COMP%] {\\n  font-family: NotoSans;\\n  margin-left: 10px;\\n  font-size: 15px;\\n  font-weight: 300;\\n  color: #414141;\\n}\\n.cardInformation[_ngcontent-%COMP%] {\\n  width: 461px;\\n  height: 225px;\\n  margin: 3px 234px 25px 40px;\\n  padding: 0px 31px 20px;\\n  border: solid 1px #e8e8e8;\\n}\\n.cardBodyDetail[_ngcontent-%COMP%] {\\n  width: 400px;\\n  height: 17px;\\n  margin: 0 5px 20px 0;\\n  font-family: NotoSans;\\n  font-size: 17px;\\n  text-align: left;\\n  color: #414141;\\n}\\n.underLine[_ngcontent-%COMP%] {\\n  width: 375px;\\n  height: 1px;\\n  margin-left: -1%;\\n  border: 1px solid #e8e8e8;\\n}\\n.userInformation[_ngcontent-%COMP%] {\\n  width: 461px;\\n  height: 500px;\\n  margin: 3px 234px 25px 40px;\\n  padding: 0px 31px 20px;\\n  border: 1px solid #e8e8e8;\\n}\\n.container-fluid[_ngcontent-%COMP%] {\\n  --bs-gutter-x: 0rem !important;\\n}\\n\\n.Rounded-Rectangle-2-copy[_ngcontent-%COMP%] {\\n  width: 460px;\\n  height: 634px;\\n  margin: 26px 30px 40px;\\n  padding: 0 0 22px;\\n  border-radius: 3px;\\n  box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.06);\\n  background-color: #fff;\\n}\\n.location[_ngcontent-%COMP%] {\\n  width: 615px;\\n  height: 95px;\\n  margin: 0 231px 29px 2px;\\n  font-size: 43px;\\n  text-align: left;\\n  color: #fff;\\n}\\n.card-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  text-align: center;\\n  color: white;\\n}\\n\\n.bottom-lefts[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 16px;\\n  width: 97px;\\n  height: 35px;\\n  margin: 0 0 60px;\\n  padding: 5px 13px 7px;\\n  opacity: 0.4;\\n  border-radius: 17.5px;\\n  background-color: black;\\n}\\n.bottom-rights[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  right: 16px;\\n  width: 48px;\\n  height: 40px;\\n  margin: 60px 0 11px 55px;\\n  padding: 9px 4px 3px;\\n  opacity: 0.4;\\n  border-radius: 55%;\\n  background-color: black;\\n}\\n.bottom-rightss[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 52px;\\n  right: 16px;\\n  width: 48px;\\n  height: 40px;\\n  margin: 60px 0 11px 55px;\\n  padding: 9px 4px 3px;\\n  opacity: 0.4;\\n  border-radius: 55%;\\n  background-color: black;\\n}\\n.centered[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 98%;\\n  border-top-left-radius: 12px;\\n  border-top-right-radius: 12px;\\n  background-color: white;\\n  font-size: 17px;\\n  text-align: center;\\n  color: #414141;\\n  font-family: NotoSans;\\n  left: 50%;\\n  width: 130px;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.card-body[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%] {\\n  width: 339px;\\n  height: 61px;\\n  margin: 13px 16px 14px 30px;\\n  font-family: NotoSans;\\n  font-size: 26px;\\n  line-height: 1.31;\\n  text-align: left;\\n  color: #414141;\\n}\\n\\n.card-body[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  width: 375px;\\n  height: 40px;\\n  margin: 14px 3px 16px 30px;\\n  font-family: NotoSans;\\n  font-size: 15px;\\n  font-weight: 300;\\n  line-height: 1.6;\\n  text-align: left;\\n  color: #757575;\\n}\\n.contentdetail[_ngcontent-%COMP%] {\\n  font-family: NotoSans;\\n  font-size: 17px;\\n  font-weight: 400;\\n  text-align: left;\\n  color: #414141;\\n}\\n.fa-star[_ngcontent-%COMP%] {\\n  margin-right: 2px;\\n  margin-top: 5px;\\n  color: #ddd;\\n}\\n.checked[_ngcontent-%COMP%] {\\n  color: orange;\\n}\\n.SeatDeadLines[_ngcontent-%COMP%] {\\n  width: 400px;\\n  height: 61px;\\n  margin: 25px 16px 14px 164px;\\n  font-family: NotoSans;\\n  font-size: 16px;\\n  line-height: 1.31;\\n  text-align: left;\\n  color: #414141;\\n}\\n.datedisplay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 66%;\\n  left: 25%;\\n  width: 278px;\\n  border-radius: 25px;\\n  border: 1px solid #e8e8e8;\\n  padding: 2px;\\n  font-size: 17px;\\n  text-align: center;\\n  color: #414141;\\n  font-family: NotoSans;\\n  transform: translate(-50%, -50%);\\n}\\n.SeatDeadLine[_ngcontent-%COMP%] {\\n  width: 400px;\\n  height: 61px;\\n  margin: 25px 16px 14px 16px;\\n  font-family: NotoSans;\\n  font-size: 16px;\\n  line-height: 1.31;\\n  text-align: left;\\n  color: #414141;\\n}\\n.btn-logins[_ngcontent-%COMP%] {\\n  width: 151px;\\n  height: 48px;\\n  border-radius: 24px;\\n  border: solid 2px #f88634;\\n  background-color: white;\\n}\\n\\n.Logins[_ngcontent-%COMP%] {\\n  width: 43px;\\n  height: 18px;\\n  font-size: 22px;\\n  text-align: left;\\n  color: #f88634;\\n}\\n.missionLabel[_ngcontent-%COMP%] {\\n  width: fit-content;\\n  height: 42px;\\n  margin: 40px 47px 5px 110px;\\n  padding: 6px 29px 12px 37px;\\n  border-radius: 20.5px;\\n  border: solid 1px #e8e8e8;\\n  background-color: #fff;\\n}\\n.bottom-leftimg[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 16px;\\n  width: 124px;\\n  height: 35px;\\n  margin: 0 0 60px;\\n  padding: 5px 13px 7px;\\n  opacity: 0.4;\\n  border-radius: 17.5px;\\n  background-color: black;\\n}\\n.bottom-rightimg[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 18px;\\n  right: 16px;\\n  width: 48px;\\n  height: 40px;\\n  margin: 60px 0 1px 55px;\\n  padding: 9px 4px 3px;\\n  opacity: 0.4;\\n  border-radius: 55%;\\n  background-color: black;\\n}\\n.bottom-rightsimg[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 52px;\\n  right: 16px;\\n  width: 48px;\\n  height: 40px;\\n  margin: 60px 0 11px 55px;\\n  padding: 9px 4px 3px;\\n  opacity: 0.4;\\n  border-radius: 55%;\\n  background-color: black;\\n}\\n\\n.userimg[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  margin-top: 30%;\\n  width: 60px;\\n  height: 60px;\\n}\\n.commentdisc[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  border: none;\\n}\\n.modal-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  border: none;\\n}\\n.modal-title[_ngcontent-%COMP%] {\\n  font-size: 21px;\\n}\\n.modal-content[_ngcontent-%COMP%] {\\n  border: 1px solid #d9d9d9 !important;\\n}\\n.modal-header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  padding: 10px 15px;\\n}\\n.modal-header[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  border: none;\\n}\\n.modal-header[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.modal-header[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 0;\\n}\\n.modal-header[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li.active[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #e12f27;\\n}\\n.modal-header[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  border: none;\\n}\\n.modal-header[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n}\\n\\n.btnCancel[_ngcontent-%COMP%] {\\n  width: 119px;\\n  height: 48px;\\n  border-radius: 24px;\\n  border: solid 2px #757575;\\n  background-color: #fff;\\n  margin-right: 15px;\\n}\\n.bcancel[_ngcontent-%COMP%] {\\n  width: 43px;\\n  height: 18px;\\n  font-size: 17px;\\n  text-align: left;\\n  color: #757575;\\n}\\n\\n.btnRemove[_ngcontent-%COMP%] {\\n  width: 119px;\\n  height: 48px;\\n  border-radius: 24px;\\n  border: solid 2px #f88634;\\n  background-color: white;\\n  margin-right: 15px;\\n}\\n.bremove[_ngcontent-%COMP%] {\\n  width: 43px;\\n  height: 18px;\\n  font-size: 17px;\\n  text-align: left;\\n  color: #f88634;\\n}\\n\\n  label.star {\\n  padding: 0 4px !important;\\n  font-size: 18px !important;\\n  color: #ddd !important;\\n}\\n  label.star::before {\\n  content: \\\"\\\\f005\\\" !important;\\n}\\n  label.star:hover {\\n  transform: rotate(0deg) scale(1.2) !important;\\n}\\n.bordert[_ngcontent-%COMP%] {\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  background-color: #fff;\\n  position: relative;\\n  margin-top: 25px;\\n  margin-left: 0px;\\n}\\n\\n.data[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -18px;\\n  left: 32%;\\n  background-color: #fff;\\n  border: 1px solid rgba(0, 0, 0, 0.06);\\n  padding: 0 10px;\\n  border-radius: 17.5px;\\n  height: 1px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "moment", "APP_CONFIG", "FooterComponent", "TabsModule", "SearchingSortingComponent", "NavbarComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "getMainImageUrl", "ɵɵsanitizeUrl", "ɵɵtext", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "missionDetail", "startDate", "endDate", "ɵɵtextInterpolate", "registrationDeadLine", "ɵɵpropertyInterpolate", "missionDoc", "ɵɵtextInterpolate1", "item_r4", "userImage", "userFullName", "commentDate", "commentDescription", "item_r5", "userName", "ɵɵelementContainerStart", "ɵɵtemplate", "VolunteeringMissionComponent_ng_container_162_div_1_Template", "VolunteeringMissionComponent_ng_container_162_div_2_Template", "result_r6", "length", "VolunteeringMissionComponent", "constructor", "_service", "_toast", "_activeRoute", "_router", "_datePipe", "_adminservice", "imageList", "recentVolunteerList", "loginUserId", "btnText", "missionCommentList", "missionFavourite", "favImag", "favImag1", "unsubscribe", "missionId", "snapshot", "paramMap", "get", "ngOnInit", "currentUserSubscribe", "getCurrentUser", "subscribe", "data", "loginUserDetail", "getUserDetail", "userId", "loginUserName", "fullName", "fetchMissionDetail", "applyModal", "window", "bootstrap", "Modal", "document", "getElementById", "getRecentVolunteerList", "push", "ngOnDestroy", "for<PERSON>ach", "sb", "value", "missionDetailSubscribe", "missionDetailByMissionId", "result", "missionImages", "split", "missionDocuments", "imageUrl", "missionApplyStatus", "getMissionCommentList", "error", "detail", "summary", "message", "duration", "toastDuration", "err", "getImageUrls", "imageUrls", "photo", "replaceAll", "openApplyMissionModal", "id", "show", "closeApplyMissionModal", "hide", "applyMission", "tokenDetail", "decodedToken", "userType", "navigate", "warning", "appliedDate", "format", "status", "sheet", "missionSubscribe", "success", "setTimeout", "postComment", "commentdesc", "CommentDescription", "missionCommentSubscribe", "addMissionComment", "location", "reload", "missionCommentListByMissionId", "map", "x", "transform", "getMissionFavourite", "addMissionFavouriteSubscribe", "addMissionFavourite", "removeMissionFavouriteSubscribe", "removeMissionFavourite", "volunteerListSubscribe", "missioId", "ɵɵdirectiveInject", "i1", "ClientMissionService", "i2", "NgToastService", "i3", "ActivatedRoute", "Router", "i4", "DatePipe", "i5", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "VolunteeringMissionComponent_Template", "rf", "ctx", "VolunteeringMissionComponent_div_7_Template", "VolunteeringMissionComponent_p_16_Template", "VolunteeringMissionComponent_p_17_Template", "VolunteeringMissionComponent_div_27_Template", "VolunteeringMissionComponent_div_28_Template", "ɵɵlistener", "VolunteeringMissionComponent_Template_span_click_33_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "VolunteeringMissionComponent_Template_button_click_91_listener", "VolunteeringMissionComponent_p_117_Template", "VolunteeringMissionComponent_div_118_Template", "VolunteeringMissionComponent_Template_button_click_125_listener", "commentdesc_r3", "ɵɵreference", "VolunteeringMissionComponent_div_129_Template", "VolunteeringMissionComponent_ng_container_162_Template", "VolunteeringMissionComponent_Template_button_click_334_listener", "VolunteeringMissionComponent_Template_button_click_340_listener", "VolunteeringMissionComponent_Template_button_click_343_listener", "missionTitle", "missionDescription", "missionType", "totalSheets", "missionFavouriteStatus", "cityName", "missionThemeName", "missionOrganisationName", "undefined", "missionOrganisationDetail", "missionSkillName", "i6", "TabDirective", "TabsetComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\components\\volunteering-mission\\volunteering-mission.component.ts", "B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\components\\volunteering-mission\\volunteering-mission.component.html"], "sourcesContent": ["import { CommonModule, DatePipe } from '@angular/common';\nimport { Component, OnDestroy, type OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\n\nimport * as moment from 'moment';\nimport { NgToastService } from 'ng-angular-popup';\nimport { AuthService } from '../../services/auth.service';\nimport { APP_CONFIG } from '../../configs/environment.config';\nimport { ClientMissionService } from '../../services/client-mission.service';\nimport { FooterComponent } from '../footer/footer.component';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { SearchingSortingComponent } from '../searching-sorting/searching-sorting.component';\nimport { NavbarComponent } from '../navbar/navbar.component';\nimport { Subscription } from 'rxjs';\ndeclare var window: any;\n@Component({\n  selector: 'app-volunteering-mission',\n  templateUrl: './volunteering-mission.component.html',\n  styleUrls: ['./volunteering-mission.component.css'],\n  standalone: true,\n  imports: [FooterComponent, TabsModule, CommonModule, SearchingSortingComponent, NavbarComponent]\n})\nexport class VolunteeringMissionComponent implements OnInit, OnDestroy {\n  applyModal: any;\n  missionId: any;\n  missionDetail: any;\n  imageList: any = [];\n  recentVolunteerList: any[] = [];\n  missionDoc: any;\n  loginUserId = 0;\n  loginUserName: any;\n  btnText: any = 'Apply Now';\n  missionCommentList: any[] = [];\n  missionFavourite = false;\n  favImag = 'assets/Img/heart1.png';\n  favImag1 = 'assets/Img/heart11.png';\n  private unsubscribe: Subscription[] = [];\n\n  constructor(\n    private _service: ClientMissionService,\n    private _toast: NgToastService,\n    private _activeRoute: ActivatedRoute,\n    private _router: Router,\n    private _datePipe: DatePipe,\n    private _adminservice: AuthService\n  ) {\n    this.missionId = this._activeRoute.snapshot.paramMap.get('missionId');\n  }\n\n  ngOnInit(): void {\n    const currentUserSubscribe = this._adminservice.getCurrentUser().subscribe((data: any) => {\n      const loginUserDetail = this._adminservice.getUserDetail();\n      data == null\n        ? (this.loginUserId = loginUserDetail.userId)\n        : (this.loginUserId = data.userId);\n      data == null\n        ? (this.loginUserName = loginUserDetail.fullName)\n        : (this.loginUserName = data.fullName);\n    });\n    if (this.missionId != null) {\n      this.fetchMissionDetail(this.missionId);\n    }\n\n    this.applyModal = new window.bootstrap.Modal(\n      document.getElementById('applyMissionModal')\n    );\n    this.getRecentVolunteerList();\n    this.unsubscribe.push(currentUserSubscribe);\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n  \n  fetchMissionDetail(missionId: any) {\n    const value = {\n      missionId: missionId,\n      userId: this.loginUserId,\n    };\n    const missionDetailSubscribe = this._service.missionDetailByMissionId(value).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.missionDetail = data.data;\n\n          this.imageList = this.missionDetail.missionImages.split(',');\n          // Gallery images removed - using simple image display\n          if (this.missionDetail.missionDocuments) {\n            this.missionDoc =\n              this._service.imageUrl + '/' + this.missionDetail.missionDocuments;\n          }\n          this.btnText =\n            this.missionDetail.missionApplyStatus == 'Applied'\n              ? 'Already Apply'\n              : 'Apply Now';\n          this.getMissionCommentList();\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(missionDetailSubscribe);\n  }\n\n  getImageUrls(): string[] {\n    const imageUrls: string[] = [];\n    for (const photo of this.imageList) {\n      imageUrls.push(this._service.imageUrl + '/' + photo.replaceAll('\\\\', '/'));\n    }\n    return imageUrls;\n  }\n\n  getMainImageUrl(): string {\n    if (this.imageList && this.imageList.length > 0) {\n      return this._service.imageUrl + '/' + this.imageList[0].replaceAll('\\\\', '/');\n    }\n    return '';\n  }\n\n  openApplyMissionModal(id: any) {\n    this.applyModal.show();\n    this.missionId = id;\n  }\n\n  closeApplyMissionModal() {\n    this.applyModal.hide();\n  }\n\n  applyMission(id: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        appliedDate: moment().format('yyyy-MM-DDTHH:mm:ssZ'),\n        status: false,\n        sheet: 1,\n      };\n      const missionSubscribe = this._service.applyMission(value).subscribe(\n        (data: any) => {\n          if (data.result == 1) {\n            this._toast.success({ detail: 'SUCCESS', summary: data.data });\n            setTimeout(() => {\n              this.closeApplyMissionModal();\n              this._router.navigate(['/home']);\n            }, 1000);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        },\n        (err) =>\n          this._toast.error({\n            detail: 'ERROR',\n            summary: err.message,\n            duration: APP_CONFIG.toastDuration,\n          })\n      );\n      this.unsubscribe.push(missionSubscribe);\n    }\n  }\n\n  postComment(commentdesc: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        CommentDescription: commentdesc,\n        commentDate: moment().format('yyyy-MM-DDTHH:mm:ssZ'),\n      };\n      const missionCommentSubscribe = this._service.addMissionComment(value).subscribe(\n        (data: any) => {\n          if (data.result == 1) {\n            this._toast.success({\n              detail: 'SUCCESS',\n              summary: data.data,\n              duration: APP_CONFIG.toastDuration,\n            });\n            window.location.reload();\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        },\n        (err) =>\n          this._toast.error({\n            detail: 'ERROR',\n            summary: err.message,\n            duration: APP_CONFIG.toastDuration,\n          })\n      );\n      this.unsubscribe.push(missionCommentSubscribe);\n    }\n  }\n\n  getMissionCommentList() {\n    const missionCommentSubscribe = this._service.missionCommentListByMissionId(this.missionDetail.id).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.missionCommentList = data.data;\n\n          this.missionCommentList = this.missionCommentList.map((x) => {\n            return {\n              id: x.id,\n              commentDescription: x.commentDescription,\n              commentDate: x.commentDate\n                ? this._datePipe.transform(\n                    x.commentDate,\n                    'EEEE, MMMM d, y, h:mm a'\n                  )\n                : '',\n              missionId: x.missionId,\n              userId: x.userId,\n              userFullName: x.userFullName,\n              userImage: x.userImage\n                ? this._service.imageUrl + '/' + x.userImage\n                : 'assets/NoImg.png',\n            };\n          });\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(missionCommentSubscribe);\n  }\n  \n  getMissionFavourite(missionId: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      this.missionFavourite = !this.missionFavourite;\n      const value = {\n        missionId: missionId,\n        userId: this.loginUserId,\n      };\n      if (this.missionFavourite) {\n        const addMissionFavouriteSubscribe = this._service.addMissionFavourite(value).subscribe((data: any) => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        });\n        this.unsubscribe.push(addMissionFavouriteSubscribe);\n      } else {\n        const removeMissionFavouriteSubscribe = this._service.removeMissionFavourite(value).subscribe((data: any) => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        });\n        this.unsubscribe.push(removeMissionFavouriteSubscribe);\n      }\n    }\n  }\n\n  getRecentVolunteerList() {\n    const value = {\n      missionId: this.missionId,\n      userId: this.loginUserId,\n    };\n    const volunteerListSubscribe = this._service.recentVolunteerList(value).subscribe((data: any) => {\n      if (data.result == 1) {\n        this.recentVolunteerList = data.data;\n        this.recentVolunteerList = this.recentVolunteerList.map((x) => {\n          return {\n            id: x.id,\n            missioId: x.missioId,\n            userId: x.userId,\n            userName: x.userName,\n            userImage: x.userImage\n              ? this._service.imageUrl + '/' + x.userImage\n              : 'assets/NoImg.png',\n          };\n        });\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(volunteerListSubscribe);\n  }\n}", "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n<div>\n  <app-navbar></app-navbar>\n  <app-searching-sorting></app-searching-sorting>\n</div>\n<div class=\"container\">\n  <div class=\"row\" style=\"height: 650px;\">\n      <div class=\"col-sm-6\">\n        <div class=\"image-gallery\" *ngIf=\"imageList && imageList.length > 0\">\n          <img [src]=\"getMainImageUrl()\"\n               alt=\"Mission Image\"\n               class=\"img-fluid main-image\"\n               style=\"width: 100%; height: 465px; object-fit: cover;\">\n        </div>\n      </div>\n      <div class=\"col-sm-6 content\">\n        <p class=\"heading\">{{missionDetail.missionTitle}}</p>\n          <p class=\"detail\"> {{missionDetail.missionDescription}}</p>\n              <div class=\"mt-5\">\n                <div class=\"bordert\">\n                  <div class=\"totalTree\">\n                      <p class=\"totalTreegoal\" *ngIf=\"missionDetail.missionType=='Goal'\">Plant 10,000 Trees</p>\n                      <p class=\"totalTreetime\" *ngIf=\"missionDetail.missionType=='Time'\">FROM {{missionDetail.startDate | date : 'dd/MM/yyyy'}} Until {{missionDetail.endDate | date:'dd/MM/yyyy'}}</p>\n                  </div>\n              </div>\n                <div class=\"SeatDeadLines row\">\n                  <div class=\"col-sm-6\">\n                    <img src=\"assets/Img/Seats-left.png\">&nbsp;\n                    <span style=\"font-size: 24px !important;\">{{missionDetail.totalSheets}}</span> <br/><span style=\"margin-left:40px\">Seats left</span>\n                  </div>\n                  <div class=\"col-sm-6\" *ngIf=\"missionDetail.missionType=='Goal'\">\n                    <img src=\"assets/Img/achieved.png\">&nbsp;\n                    <span style=\"font-size: 24px !important;\">\n                      <div class=\"progress\">\n                        <div class=\"progress-bar bg-warning\" role=\"progressbar\" style=\"width: 50%\" aria-valuenow=\"25\" aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\n                      </div>\n                    </span> <br/><span style=\"margin-left:40px;\">8000 achieved</span>\n                    <br/><br/>\n                  </div>\n                  <div class=\"col-sm-6\" *ngIf=\"missionDetail.missionType=='Time'\">\n                    <img src=\"assets/Img/deadline.png\">&nbsp;\n                    <span style=\"font-size: 24px !important;\">{{missionDetail.registrationDeadLine | date: 'dd/MM/yyyy'}}</span> <br/><span style=\"margin-left:40px\">Deadline</span>\n                    <br/><br/>\n                  </div>\n              </div>\n              <p style=\"width:616px;margin: 20px 100px 0px 61px;border:1px solid #757575;opacity: 0.1;\"></p>\n              <div class=\"row\">\n                    <div class=\"col-sm-6\">\n                       <button class=\"btnMission\"><span (click)=\"getMissionFavourite(missionDetail.id)\"><img style=\"width: 30px;\" src=\"{{missionDetail.missionFavouriteStatus=='0'? favImag : favImag1}}\" alt=\"NoImage\"></span><span class=\"Mission\">Add to Favourite</span></button>\n                    </div>\n                     <div class=\"col-sm-6\">\n                          <button class=\"btnMission\"><span><img src=\"assets/Img/add1.png\" alt=\"NoImage\"></span><span class=\"Mission\">Recommend to a Co-Worker </span></button>\n                     </div>\n               </div>\n               <div class=\"totalRating\">\n                  <span class=\"fa fa-star\"></span>\n                  <span class=\"fa fa-star\"></span>\n                  <span class=\"fa fa-star\"></span>\n                  <span class=\"fa fa-star\"></span>\n                  <span class=\"fa fa-star\"></span>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-sm-3 carddetail\">\n                  <div class=\"top-left\"><img src=\"assets/Img/pin1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-left\"><span style=\" color:#757575 !important;\">City</span><br/><span>{{missionDetail.cityName}}</span></div>\n                </div>\n                <div class=\"col-sm-3 carddetail\">\n                  <div class=\"top-left\"><img src=\"assets/Img/web.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-left\"><span style=\" color:#757575 !important;\">Theme</span><br/><span>{{missionDetail.missionThemeName}}</span></div>\n                </div>\n                <div class=\"col-sm-3 carddetail\">\n                  <div class=\"top-left\"><img src=\"assets/Img/pin1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-left\"><span style=\"color:#757575 !important;\">Date</span><br/><span>From{{missionDetail.startDate | date:'dd/MM/yyyy'}}<br/>Until {{missionDetail.endDate | date:'dd/MM/yyyy'}}</span></div>\n                </div>\n                <div class=\"col-sm-3 carddetail\">\n                  <div class=\"top-left\"><img src=\"assets/Img/organization.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-left\"><span style=\" color:#757575 !important;\">Organization</span><br/><span>{{missionDetail.missionOrganisationName}}</span></div>\n                </div>\n              </div>\n              <div class=\"d-grid card-footer align-items-center\" style=\"display: flex;justify-content: center;margin-left: 20%;\" >\n                <button class=\"btn-login\" type=\"submit\" [disabled]=\"missionDetail.missionApplyStatus=='Applied'\" (click)=\"openApplyMissionModal(missionDetail.id)\"><span class=\"Login\">\n                  {{btnText}} &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n              </div>\n             </div>\n\n      </div>\n  </div>\n  <div class=\"row\" style=\"height: 650px;margin-top: 14%;\">\n    <div class=\"col-sm-8\">\n      <div class=\"tab-panel p-3\">\n        <tabset class=\"member-tabset\">\n          <tab heading=\"Mission\">\n            <p class=\"Introduction\">Introduction</p>\n            <p class=\"contentDetail\">\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n              <br/><br/>\n              Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n              <br/><br/>\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n            </p>\n            <p class=\"Introduction\">Challenge</p>\n            <p class=\"contentDetail\">\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n              <br/><br/>\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n            </p>\n            <p class=\"Introduction\" *ngIf=\"missionDoc != undefined\">Documents</p>\n            <div class=\"documentFile row mt-4\" *ngIf=\"missionDoc != undefined\">\n              <div class=\"col-sm-3 document\"><img src=\"assets/Img/pdf.png\"><span><a href=\"{{missionDoc}}\" style=\"color: #414141;text-decoration: none;\">&nbsp;{{missionDoc}}</a></span></div>\n            </div>\n          </tab>\n          <tab heading=\"Organization\">{{missionDetail.missionOrganisationDetail}}</tab>\n          <tab heading=\"Comments\">\n            <textarea class=\"form-control\" rows=\"4\" #commentdesc placeholder=\"Enter Your Comments\"></textarea>\n            <div class=\"mt-4\">\n              <button class=\"btn-login\" type=\"button\" (click)=\"postComment(commentdesc.value)\"><span class=\"Login\">Post Comment</span></button>\n            </div>\n            <div class=\"mt-4\" style=\"height: 400px !important;overflow-y:auto;max-height: 400px !important;border: none;\">\n                <div class=\"card mt-2 col-sm-11 row\" style=\"height: 100px !important;margin-left: 10px;\" *ngFor=\"let item of missionCommentList\">\n                    <div class=\"col-sm-1\">\n                      <img class=\"userimg\" src=\"{{item.userImage}}\" alt=\"NoImage\" onerror=\"this.src='assets/NoUser.png'\">\n                    </div>\n                    <div class=\"col-sm-10\">\n                        <span style=\"font-size: 16px;\"><b>{{item.userFullName}}</b></span>\n                        <p style=\"font-size: 14px;\">{{item.commentDate }}</p>\n                        <!-- <p style=\"font-size: 14px;\">Monday,Octomber 21,2022,3:35pm</p> -->\n                        <p class=\"commentdisc\">{{item.commentDescription}}</p>\n                    </div>\n                </div>\n            </div>\n          </tab>\n        </tabset>\n  </div>\n    </div>\n    <div class=\"col-sm-4\">\n      <div class=\"card cardInformation\">\n        <div class=\"card-body\">\n          <p class=\"cardBodyDetail\" style=\"font-size: 22px !important;font-weight: 700;\">Information</p>\n          <p class=\"underLine\"></p>\n          <p class=\"cardBodyDetail\">Skills<span style=\"margin-left: 5%;\">{{missionDetail.missionSkillName}}</span></p>\n          <p class=\"underLine\"></p>\n          <p class=\"cardBodyDetail\">Day <span style=\"margin-left: 6%;\">&nbsp;Weekend only</span></p>\n          <p class=\"underLine\"></p>\n          <p class=\"cardBodyDetail\"><span>Rating</span>\n            <span class=\"col-sm-5\" style=\"margin-left: 5%;\">\n              <span class=\"fa fa-star checked\"></span>\n              <span class=\"fa fa-star checked\"></span>\n              <span class=\"fa fa-star checked\"></span>\n              <span class=\"fa fa-star checked\"></span>\n              <span class=\"fa fa-star\"></span>\n            </span>&nbsp;\n           (by 125 volunteers)\n          </p>\n        </div>\n      </div>\n\n      <div class=\"card userInformation\">\n        <div class=\"card-body\">\n          <p class=\"cardBodyDetail\" style=\"font-size: 22px !important;font-weight: 700;\">Recent Volunteers</p>\n          <p class=\"underLine\"></p>\n          <div class=\"row\">\n            <ng-container *ngIf=\"(recentVolunteerList) as result\">\n               <div class=\"col-sm-4 row\" style=\"margin-bottom: 4%;\" *ngFor=\"let item of result\">\n                   <img src=\"{{item.userImage}}\" alt=\"NoImage\" style=\" width: 90px;height: 70px;border-radius: 50%;margin-left: 0%;\">\n                   <p style=\"width: 120px;\">{{item.userName}}</p>\n                </div>\n                <div class=\"col-sm-12 justify-content-center\" *ngIf=\"result.length === 0\">\n                  <p class=\"text-danger\" style=\"font-size:18px;text-align: center;margin-top: 50%;\"><b>No Recent Volunteers Avilable</b></p>\n              </div>\n            </ng-container>\n          </div>\n       </div>\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"container-fluid\">\n  <p style=\"margin-top: 9%;height:1px;border: 1px solid gray;opacity: 0.1;\"></p>\n  <div class=\"\">\n      <p style=\"width:100%;display: flex;justify-content: center;font-size: 22px;font-weight: 400;color: #757575;margin-top: 2%;\">\n        Related Mission\n      </p>\n      <div class=\"container-fluid\" style=\"width: fit-content;height: 676px;\">\n        <div class=\"row mt-5\">\n          <div class=\"col-sm-4 row Rounded-Rectangle-2-copy\">\n            <div class=\"card-header\" style=\"width: 460px;height:220px;\">\n                  <img src=\"assets/Images/1.png\" alt=\"NoImage\">\n                  <div class=\"bottom-leftimg\"><i class=\"fa fa-map-marker\"></i>&nbsp;Toronto</div>\n                  <div class=\"bottom-rightsimg\"><img src=\"assets/Img/heart1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-rightimg\"><i class=\"fa fa-user-plus 3-x\"></i></div>\n                  <div class=\"centered\">Environment</div>\n            </div>\n            <div class=\"card-body\">\n                <p class=\"heading\">Grow Trees – On the path to\n                  environment sustainability</p>\n                <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore...</p>\n                <div class=\"row\" style=\"margin: 14px;\">\n                  <div class=\"col-sm-7 contentdetail\">\n                    CSE Network\n                  </div>\n                  <div class=\"col-sm-5\" style=\"display: flex;justify-content: flex-end;\">\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star\"></span>\n                    <span class=\"fa fa-star\"></span>\n                  </div>\n                </div>\n                <div>\n                  <!-- <div class=\"missionLabel\">\n                    <p>Ongoing Opportunity</p>\n                  </div> -->\n                  <div class=\"bordert\">\n                    <div class=\"text-center data py-3\">\n                      <p style=\"margin-top: -12px\">Ongoing Opportunity</p>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"SeatDeadLine row\">\n                    <div class=\"col-sm-12\">\n                      <img src=\"assets/Img/Already-volunteered.png\" alt=\"NoImage\" style=\"margin-top: -1%;\">&nbsp;\n                      <span style=\"font-size: 24px !important;margin-top: 2%;\">250</span> <br/><span style=\"margin-left:34px\">Already volunteered</span>\n                    </div>\n                </div>\n            </div>\n            <P style=\"border: 1px solid #e8e8e8;width:100%\"></P>\n            <div class=\"d-grid card-footer\"style=\"margin-left:32%\">\n              <button class=\"btn-logins\" type=\"submit\"><span class=\"Logins\">Apply &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n            </div>\n           </div>\n          <div class=\"col-sm-4 row Rounded-Rectangle-2-copy\">\n            <div class=\"card-header\" style=\"width: 460px;height:220px;\">\n                  <img src=\"assets/Img/animal.png\" alt=\"NoImage\">\n                  <div class=\"bottom-leftimg\"><i class=\"fa fa-map-marker\"></i>&nbsp;Cape Town</div>\n                  <div class=\"bottom-rightsimg\"><img src=\"assets/Img/heart1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-rightimg\"><i class=\"fa fa-user-plus 3-x\"></i></div>\n                  <div class=\"centered\">Environment</div>\n            </div>\n            <div class=\"card-body\">\n                <p class=\"heading\">Animal Welfare & save birds\n                  campaign\n                </p>\n                <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore...</p>\n                <div class=\"row\" style=\"margin: 14px;\">\n                  <div class=\"col-sm-7 contentdetail\">\n                    JR Foundation\n                  </div>\n                  <div class=\"col-sm-5\" style=\"display: flex;justify-content: flex-end;\">\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star\"></span>\n                    <span class=\"fa fa-star\"></span>\n                  </div>\n                </div>\n                <div>\n                  <!-- <div class=\"missionLabel\">\n                    <p>Plant 10,000 Trees</p>\n                  </div> -->\n                  <div class=\"bordert\">\n                    <div class=\"text-center data py-3\">\n                      <p style=\"margin-top: -12px\">Plant 10,000 Trees</p>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"SeatDeadLine row\">\n                    <div class=\"col-sm-6\">\n                      <img src=\"assets/Img/Seats-left.png\" alt=\"NoImage\" style=\"margin-top: -1%;\">&nbsp;\n                      <span style=\"font-size: 24px !important;margin-top: 2%;\">10</span> <br/><span style=\"margin-left:34px\">Seat left</span>\n                    </div>\n                    <div class=\"col-sm-6\">\n                      <img src=\"assets/Img/achieved.png\" alt=\"NoImage\" style=\"margin-top: -1%;margin-left: -8%;\">&nbsp;\n                      <span style=\"font-size: 24px !important;\">\n                        <div class=\"progress\" style=\"margin-top: -10% !important;margin-bottom: -16% !important\">\n                          <div class=\"progress-bar bg-warning\" role=\"progressbar\" style=\"width: 75%\" aria-valuenow=\"25\" aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\n                        </div>\n                      </span> <br/><span style=\"margin-left:30px !important;\">8000 achieved</span>\n                    </div>\n                </div>\n            </div>\n            <P style=\"border: 1px solid #e8e8e8;width:100%\"></P>\n            <div class=\"d-grid card-footer\"style=\"margin-left:32%\">\n              <button class=\"btn-logins\" type=\"submit\"><span class=\"Logins\">Apply &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n            </div>\n           </div>\n           <div class=\"col-sm-4 row Rounded-Rectangle-2-copy\">\n            <div class=\"card-header\" style=\"width: 460px;height:220px;\">\n                  <img src=\"assets/Img/Plantation.png\" alt=\"NoImage\">\n                  <div class=\"bottom-leftimg\"><i class=\"fa fa-map-marker\"></i>&nbsp;Paris</div>\n                  <div class=\"bottom-rightsimg\"><img src=\"assets/Img/heart1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-rightimg\"><i class=\"fa fa-user-plus 3-x\"></i></div>\n                  <div class=\"centered\">Environment</div>\n            </div>\n            <div class=\"card-body\">\n                <p class=\"heading\">Plantation and Afforestation\n                  programme\n                </p>\n                <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore...</p>\n                <div class=\"row\" style=\"margin: 14px;\">\n                  <div class=\"col-sm-7 contentdetail\">\n                    Amaze Doctors\n                  </div>\n                  <div class=\"col-sm-5\" style=\"display: flex;justify-content: flex-end;\">\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star\"></span>\n                    <span class=\"fa fa-star\"></span>\n                  </div>\n                </div>\n                <div>\n                  <!-- <div class=\"missionLabel\">\n                    <p>Plant 10,000 Trees</p>\n                </div> -->\n                <div class=\"bordert\">\n                  <div class=\"text-center data py-3\">\n                    <p style=\"margin-top: -12px\">Plant 10,000 Trees</p>\n                  </div>\n                </div>\n                </div>\n                <div class=\"SeatDeadLine row\">\n                  <div class=\"col-sm-6\">\n                    <img src=\"assets/Img/Seats-left.png\" alt=\"NoImage\" style=\"margin-top: -1%;\">&nbsp;\n                    <span style=\"font-size: 24px !important;margin-top: 2%;\">10</span> <br/><span style=\"margin-left:34px\">Seat left</span>\n                  </div>\n                  <div class=\"col-sm-6\">\n                    <img src=\"assets/Img/achieved.png\" alt=\"NoImage\" style=\"margin-top: -1%;margin-left: -8%;\">&nbsp;\n                    <span style=\"font-size: 24px !important;\">\n                      <div class=\"progress\" style=\"margin-top: -10% !important;margin-bottom: -16% !important\">\n                        <div class=\"progress-bar bg-warning\" role=\"progressbar\" style=\"width: 75%\" aria-valuenow=\"25\" aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\n                      </div>\n                    </span> <br/><span style=\"margin-left:30px !important;\">8000 achieved</span>\n                  </div>\n              </div>\n            <P style=\"border: 1px solid #e8e8e8;width:100%\"></P>\n            <div class=\"d-grid card-footer\"style=\"margin-left:32%\">\n              <button class=\"btn-logins\" type=\"submit\"><span class=\"Logins\">Apply &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n            </div>\n           </div>\n    </div>\n      </div>\n  </div>\n  </div>\n  <div class=\"mt-5\">\n    <app-footer></app-footer>\n  </div>\n\n\n  <div class=\"modal fade\" style=\"margin-top: 8%;\" id=\"applyMissionModal\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"exampleModalLabel\" aria-hidden=\"true\">\n    <div class=\"modal-dialog\" role=\"document\">\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h5 class=\"modal-title\" id=\"exampleModalLabel\">Confirm Delete</h5>\n          <button type=\"button\" class=\"btn-close\" data-dismiss=\"modal\" aria-label=\"Close\" (click)=\"closeApplyMissionModal()\">\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <input type=\"hidden\" value=\"\">\n           <h4>Are you sure you want to apply this mission?</h4>\n        </div>\n        <div class=\"modal-footer\">\n          <button type=\"button\" class=\"btnCancel\" data-dismiss=\"modal\" (click)=\"closeApplyMissionModal()\"><span class=\"bCancel\"> Cancel</span> </button>\n          <button type=\"button\" class=\"btnRemove\" (click)=\"applyMission(missionId)\"><span class=\"bremove\">Apply</span></button>\n        </div>\n      </div>\n    </div>\n  </div>\n"], "mappings": "AAAA,SAASA,YAAY,QAAkB,iBAAiB;AAIxD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,UAAU,QAAQ,kCAAkC;AAE7D,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,eAAe,QAAQ,4BAA4B;;;;;;;;;;ICJpDC,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAE,SAAA,eAG4D;IAC9DF,EAAA,CAAAG,YAAA,EAAM;;;;IAJCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,UAAA,QAAAC,MAAA,CAAAC,eAAA,IAAAP,EAAA,CAAAQ,aAAA,CAAyB;;;;;IAYlBR,EAAA,CAAAC,cAAA,aAAmE;IAAAD,EAAA,CAAAS,MAAA,yBAAkB;IAAAT,EAAA,CAAAG,YAAA,EAAI;;;;;IACzFH,EAAA,CAAAC,cAAA,aAAmE;IAAAD,EAAA,CAAAS,MAAA,GAA0G;;;IAAAT,EAAA,CAAAG,YAAA,EAAI;;;;IAA9GH,EAAA,CAAAI,SAAA,EAA0G;IAA1GJ,EAAA,CAAAU,kBAAA,UAAAV,EAAA,CAAAW,WAAA,OAAAL,MAAA,CAAAM,aAAA,CAAAC,SAAA,4BAAAb,EAAA,CAAAW,WAAA,OAAAL,MAAA,CAAAM,aAAA,CAAAE,OAAA,oBAA0G;;;;;IAQjLd,EAAA,CAAAC,cAAA,aAAgE;IAC9DD,EAAA,CAAAE,SAAA,eAAmC;IAAAF,EAAA,CAAAS,MAAA,cACnC;IACET,EADF,CAAAC,cAAA,eAA0C,eAClB;IACpBD,EAAA,CAAAE,SAAA,eAA0I;IAE9IF,EADE,CAAAG,YAAA,EAAM,EACD;IAACH,EAAA,CAAAE,SAAA,SAAK;IAAAF,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAS,MAAA,oBAAa;IAAAT,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAL,CAAAE,SAAA,SAAK,UAAK;IACZF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,aAAgE;IAC9DD,EAAA,CAAAE,SAAA,eAAmC;IAAAF,EAAA,CAAAS,MAAA,cACnC;IAAAT,EAAA,CAAAC,cAAA,eAA0C;IAAAD,EAAA,CAAAS,MAAA,GAA2D;;IAAAT,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,SAAA,SAAK;IAAAF,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAS,MAAA,eAAQ;IAAAT,EAAA,CAAAG,YAAA,EAAO;IAC3JH,EAAL,CAAAE,SAAA,SAAK,UAAK;IACZF,EAAA,CAAAG,YAAA,EAAM;;;;IAFsCH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAW,WAAA,OAAAL,MAAA,CAAAM,aAAA,CAAAI,oBAAA,gBAA2D;;;;;IAiE7GhB,EAAA,CAAAC,cAAA,YAAwD;IAAAD,EAAA,CAAAS,MAAA,gBAAS;IAAAT,EAAA,CAAAG,YAAA,EAAI;;;;;IAEnEH,EADF,CAAAC,cAAA,eAAmE,eAClC;IAAAD,EAAA,CAAAE,SAAA,eAA8B;IAAMF,EAAN,CAAAC,cAAA,WAAM,aAAuE;IAAAD,EAAA,CAAAS,MAAA,GAAoB;IAChKT,EADgK,CAAAG,YAAA,EAAI,EAAO,EAAM,EAC3K;;;;IADkEH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAiB,qBAAA,SAAAX,MAAA,CAAAY,UAAA,EAAAlB,EAAA,CAAAQ,aAAA,CAAqB;IAA+CR,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAmB,kBAAA,WAAAb,MAAA,CAAAY,UAAA,KAAoB;;;;;IAWxJlB,EADJ,CAAAC,cAAA,eAAiI,eACvG;IACpBD,EAAA,CAAAE,SAAA,eAAmG;IACrGF,EAAA,CAAAG,YAAA,EAAM;IAE6BH,EADnC,CAAAC,cAAA,eAAuB,gBACY,QAAG;IAAAD,EAAA,CAAAS,MAAA,GAAqB;IAAIT,EAAJ,CAAAG,YAAA,EAAI,EAAO;IAClEH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAS,MAAA,GAAqB;IAAAT,EAAA,CAAAG,YAAA,EAAI;IAErDH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAS,MAAA,IAA2B;IAE1DT,EAF0D,CAAAG,YAAA,EAAI,EACpD,EACJ;;;;IARqBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAiB,qBAAA,QAAAG,OAAA,CAAAC,SAAA,EAAArB,EAAA,CAAAQ,aAAA,CAAwB;IAGTR,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAe,iBAAA,CAAAK,OAAA,CAAAE,YAAA,CAAqB;IAC3BtB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAe,iBAAA,CAAAK,OAAA,CAAAG,WAAA,CAAqB;IAE1BvB,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAe,iBAAA,CAAAK,OAAA,CAAAI,kBAAA,CAA2B;;;;;IAoC3DxB,EAAA,CAAAC,cAAA,eAAiF;IAC7ED,EAAA,CAAAE,SAAA,eAAkH;IAClHF,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAS,MAAA,GAAiB;IAC7CT,EAD6C,CAAAG,YAAA,EAAI,EAC3C;;;;IAFEH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAiB,qBAAA,QAAAQ,OAAA,CAAAJ,SAAA,EAAArB,EAAA,CAAAQ,aAAA,CAAwB;IACJR,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAe,iBAAA,CAAAU,OAAA,CAAAC,QAAA,CAAiB;;;;;IAGuC1B,EADpF,CAAAC,cAAA,eAA0E,aACU,QAAG;IAAAD,EAAA,CAAAS,MAAA,oCAA6B;IACtHT,EADsH,CAAAG,YAAA,EAAI,EAAI,EACxH;;;;;IAPRH,EAAA,CAAA2B,uBAAA,GAAsD;IAKlD3B,EAJD,CAAA4B,UAAA,IAAAC,4DAAA,mBAAiF,IAAAC,4DAAA,mBAIN;;;;;IAJL9B,EAAA,CAAAI,SAAA,EAAS;IAATJ,EAAA,CAAAK,UAAA,YAAA0B,SAAA,CAAS;IAI/B/B,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,UAAA,SAAA0B,SAAA,CAAAC,MAAA,OAAyB;;;ADhJxF,OAAM,MAAOC,4BAA4B;EAgBvCC,YACUC,QAA8B,EAC9BC,MAAsB,EACtBC,YAA4B,EAC5BC,OAAe,EACfC,SAAmB,EACnBC,aAA0B;IAL1B,KAAAL,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,aAAa,GAAbA,aAAa;IAlBvB,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,mBAAmB,GAAU,EAAE;IAE/B,KAAAC,WAAW,GAAG,CAAC;IAEf,KAAAC,OAAO,GAAQ,WAAW;IAC1B,KAAAC,kBAAkB,GAAU,EAAE;IAC9B,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,OAAO,GAAG,uBAAuB;IACjC,KAAAC,QAAQ,GAAG,wBAAwB;IAC3B,KAAAC,WAAW,GAAmB,EAAE;IAUtC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACb,YAAY,CAACc,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;EACvE;EAEAC,QAAQA,CAAA;IACN,MAAMC,oBAAoB,GAAG,IAAI,CAACf,aAAa,CAACgB,cAAc,EAAE,CAACC,SAAS,CAAEC,IAAS,IAAI;MACvF,MAAMC,eAAe,GAAG,IAAI,CAACnB,aAAa,CAACoB,aAAa,EAAE;MAC1DF,IAAI,IAAI,IAAI,GACP,IAAI,CAACf,WAAW,GAAGgB,eAAe,CAACE,MAAM,GACzC,IAAI,CAAClB,WAAW,GAAGe,IAAI,CAACG,MAAO;MACpCH,IAAI,IAAI,IAAI,GACP,IAAI,CAACI,aAAa,GAAGH,eAAe,CAACI,QAAQ,GAC7C,IAAI,CAACD,aAAa,GAAGJ,IAAI,CAACK,QAAS;IAC1C,CAAC,CAAC;IACF,IAAI,IAAI,CAACb,SAAS,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACc,kBAAkB,CAAC,IAAI,CAACd,SAAS,CAAC;IACzC;IAEA,IAAI,CAACe,UAAU,GAAG,IAAIC,MAAM,CAACC,SAAS,CAACC,KAAK,CAC1CC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAC7C;IACD,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACtB,WAAW,CAACuB,IAAI,CAACjB,oBAAoB,CAAC;EAC7C;EAEAkB,WAAWA,CAAA;IACT,IAAI,CAACxB,WAAW,CAACyB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAAC1B,WAAW,EAAE,CAAC;EACpD;EAEAe,kBAAkBA,CAACd,SAAc;IAC/B,MAAM0B,KAAK,GAAG;MACZ1B,SAAS,EAAEA,SAAS;MACpBW,MAAM,EAAE,IAAI,CAAClB;KACd;IACD,MAAMkC,sBAAsB,GAAG,IAAI,CAAC1C,QAAQ,CAAC2C,wBAAwB,CAACF,KAAK,CAAC,CAACnB,SAAS,CACnFC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACqB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACnE,aAAa,GAAG8C,IAAI,CAACA,IAAI;QAE9B,IAAI,CAACjB,SAAS,GAAG,IAAI,CAAC7B,aAAa,CAACoE,aAAa,CAACC,KAAK,CAAC,GAAG,CAAC;QAC5D;QACA,IAAI,IAAI,CAACrE,aAAa,CAACsE,gBAAgB,EAAE;UACvC,IAAI,CAAChE,UAAU,GACb,IAAI,CAACiB,QAAQ,CAACgD,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACvE,aAAa,CAACsE,gBAAgB;QACtE;QACA,IAAI,CAACtC,OAAO,GACV,IAAI,CAAChC,aAAa,CAACwE,kBAAkB,IAAI,SAAS,GAC9C,eAAe,GACf,WAAW;QACjB,IAAI,CAACC,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAACjD,MAAM,CAACkD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE9B,IAAI,CAAC+B,OAAO;UACrBC,QAAQ,EAAE/F,UAAU,CAACgG;SACtB,CAAC;MACJ;IACF,CAAC,EACAC,GAAG,IACF,IAAI,CAACxD,MAAM,CAACkD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAEI,GAAG,CAACH,OAAO;MACpBC,QAAQ,EAAE/F,UAAU,CAACgG;KACtB,CAAC,CACL;IACD,IAAI,CAAC1C,WAAW,CAACuB,IAAI,CAACK,sBAAsB,CAAC;EAC/C;EAEAgB,YAAYA,CAAA;IACV,MAAMC,SAAS,GAAa,EAAE;IAC9B,KAAK,MAAMC,KAAK,IAAI,IAAI,CAACtD,SAAS,EAAE;MAClCqD,SAAS,CAACtB,IAAI,CAAC,IAAI,CAACrC,QAAQ,CAACgD,QAAQ,GAAG,GAAG,GAAGY,KAAK,CAACC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC5E;IACA,OAAOF,SAAS;EAClB;EAEAvF,eAAeA,CAAA;IACb,IAAI,IAAI,CAACkC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACT,MAAM,GAAG,CAAC,EAAE;MAC/C,OAAO,IAAI,CAACG,QAAQ,CAACgD,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC1C,SAAS,CAAC,CAAC,CAAC,CAACuD,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;IAC/E;IACA,OAAO,EAAE;EACX;EAEAC,qBAAqBA,CAACC,EAAO;IAC3B,IAAI,CAACjC,UAAU,CAACkC,IAAI,EAAE;IACtB,IAAI,CAACjD,SAAS,GAAGgD,EAAE;EACrB;EAEAE,sBAAsBA,CAAA;IACpB,IAAI,CAACnC,UAAU,CAACoC,IAAI,EAAE;EACxB;EAEAC,YAAYA,CAACJ,EAAO;IAClB,MAAMK,WAAW,GAAG,IAAI,CAAC/D,aAAa,CAACgE,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAACnE,OAAO,CAACoE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAAClF,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACe,MAAM,CAACuE,OAAO,CAAC;QAClBpB,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAE/F,UAAU,CAACgG;OACtB,CAAC;MACF,IAAI,CAACrD,OAAO,CAACoE,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAAC1C,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,MAAMe,KAAK,GAAG;QACZ1B,SAAS,EAAE,IAAI,CAACtC,aAAa,CAACsF,EAAE;QAChCrC,MAAM,EAAE,IAAI,CAAClB,WAAW;QACxBiE,WAAW,EAAElH,MAAM,EAAE,CAACmH,MAAM,CAAC,sBAAsB,CAAC;QACpDC,MAAM,EAAE,KAAK;QACbC,KAAK,EAAE;OACR;MACD,MAAMC,gBAAgB,GAAG,IAAI,CAAC7E,QAAQ,CAACmE,YAAY,CAAC1B,KAAK,CAAC,CAACnB,SAAS,CACjEC,IAAS,IAAI;QACZ,IAAIA,IAAI,CAACqB,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAAC3C,MAAM,CAAC6E,OAAO,CAAC;YAAE1B,MAAM,EAAE,SAAS;YAAEC,OAAO,EAAE9B,IAAI,CAACA;UAAI,CAAE,CAAC;UAC9DwD,UAAU,CAAC,MAAK;YACd,IAAI,CAACd,sBAAsB,EAAE;YAC7B,IAAI,CAAC9D,OAAO,CAACoE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;UAClC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAI,CAACtE,MAAM,CAACkD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAE9B,IAAI,CAAC+B,OAAO;YACrBC,QAAQ,EAAE/F,UAAU,CAACgG;WACtB,CAAC;QACJ;MACF,CAAC,EACAC,GAAG,IACF,IAAI,CAACxD,MAAM,CAACkD,KAAK,CAAC;QAChBC,MAAM,EAAE,OAAO;QACfC,OAAO,EAAEI,GAAG,CAACH,OAAO;QACpBC,QAAQ,EAAE/F,UAAU,CAACgG;OACtB,CAAC,CACL;MACD,IAAI,CAAC1C,WAAW,CAACuB,IAAI,CAACwC,gBAAgB,CAAC;IACzC;EACF;EAEAG,WAAWA,CAACC,WAAgB;IAC1B,MAAMb,WAAW,GAAG,IAAI,CAAC/D,aAAa,CAACgE,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAACnE,OAAO,CAACoE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAAClF,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACe,MAAM,CAACuE,OAAO,CAAC;QAClBpB,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAE/F,UAAU,CAACgG;OACtB,CAAC;MACF,IAAI,CAACrD,OAAO,CAACoE,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAAC1C,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,MAAMe,KAAK,GAAG;QACZ1B,SAAS,EAAE,IAAI,CAACtC,aAAa,CAACsF,EAAE;QAChCrC,MAAM,EAAE,IAAI,CAAClB,WAAW;QACxB0E,kBAAkB,EAAED,WAAW;QAC/B7F,WAAW,EAAE7B,MAAM,EAAE,CAACmH,MAAM,CAAC,sBAAsB;OACpD;MACD,MAAMS,uBAAuB,GAAG,IAAI,CAACnF,QAAQ,CAACoF,iBAAiB,CAAC3C,KAAK,CAAC,CAACnB,SAAS,CAC7EC,IAAS,IAAI;QACZ,IAAIA,IAAI,CAACqB,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAAC3C,MAAM,CAAC6E,OAAO,CAAC;YAClB1B,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAE9B,IAAI,CAACA,IAAI;YAClBgC,QAAQ,EAAE/F,UAAU,CAACgG;WACtB,CAAC;UACFzB,MAAM,CAACsD,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,MAAM;UACL,IAAI,CAACrF,MAAM,CAACkD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAE9B,IAAI,CAAC+B,OAAO;YACrBC,QAAQ,EAAE/F,UAAU,CAACgG;WACtB,CAAC;QACJ;MACF,CAAC,EACAC,GAAG,IACF,IAAI,CAACxD,MAAM,CAACkD,KAAK,CAAC;QAChBC,MAAM,EAAE,OAAO;QACfC,OAAO,EAAEI,GAAG,CAACH,OAAO;QACpBC,QAAQ,EAAE/F,UAAU,CAACgG;OACtB,CAAC,CACL;MACD,IAAI,CAAC1C,WAAW,CAACuB,IAAI,CAAC8C,uBAAuB,CAAC;IAChD;EACF;EAEAjC,qBAAqBA,CAAA;IACnB,MAAMiC,uBAAuB,GAAG,IAAI,CAACnF,QAAQ,CAACuF,6BAA6B,CAAC,IAAI,CAAC9G,aAAa,CAACsF,EAAE,CAAC,CAACzC,SAAS,CACzGC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACqB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAAClC,kBAAkB,GAAGa,IAAI,CAACA,IAAI;QAEnC,IAAI,CAACb,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC8E,GAAG,CAAEC,CAAC,IAAI;UAC1D,OAAO;YACL1B,EAAE,EAAE0B,CAAC,CAAC1B,EAAE;YACR1E,kBAAkB,EAAEoG,CAAC,CAACpG,kBAAkB;YACxCD,WAAW,EAAEqG,CAAC,CAACrG,WAAW,GACtB,IAAI,CAACgB,SAAS,CAACsF,SAAS,CACtBD,CAAC,CAACrG,WAAW,EACb,yBAAyB,CAC1B,GACD,EAAE;YACN2B,SAAS,EAAE0E,CAAC,CAAC1E,SAAS;YACtBW,MAAM,EAAE+D,CAAC,CAAC/D,MAAM;YAChBvC,YAAY,EAAEsG,CAAC,CAACtG,YAAY;YAC5BD,SAAS,EAAEuG,CAAC,CAACvG,SAAS,GAClB,IAAI,CAACc,QAAQ,CAACgD,QAAQ,GAAG,GAAG,GAAGyC,CAAC,CAACvG,SAAS,GAC1C;WACL;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACe,MAAM,CAACkD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE9B,IAAI,CAAC+B,OAAO;UACrBC,QAAQ,EAAE/F,UAAU,CAACgG;SACtB,CAAC;MACJ;IACF,CAAC,EACAC,GAAG,IACF,IAAI,CAACxD,MAAM,CAACkD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAEI,GAAG,CAACH,OAAO;MACpBC,QAAQ,EAAE/F,UAAU,CAACgG;KACtB,CAAC,CACL;IACD,IAAI,CAAC1C,WAAW,CAACuB,IAAI,CAAC8C,uBAAuB,CAAC;EAChD;EAEAQ,mBAAmBA,CAAC5E,SAAc;IAChC,MAAMqD,WAAW,GAAG,IAAI,CAAC/D,aAAa,CAACgE,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAACnE,OAAO,CAACoE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAAClF,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACe,MAAM,CAACuE,OAAO,CAAC;QAClBpB,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAE/F,UAAU,CAACgG;OACtB,CAAC;MACF,IAAI,CAACrD,OAAO,CAACoE,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAAC1C,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAACf,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;MAC9C,MAAM8B,KAAK,GAAG;QACZ1B,SAAS,EAAEA,SAAS;QACpBW,MAAM,EAAE,IAAI,CAAClB;OACd;MACD,IAAI,IAAI,CAACG,gBAAgB,EAAE;QACzB,MAAMiF,4BAA4B,GAAG,IAAI,CAAC5F,QAAQ,CAAC6F,mBAAmB,CAACpD,KAAK,CAAC,CAACnB,SAAS,CAAEC,IAAS,IAAI;UACpG,IAAIA,IAAI,CAACqB,MAAM,IAAI,CAAC,EAAE;YACpB,IAAI,CAACf,kBAAkB,CAACd,SAAS,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAACd,MAAM,CAACkD,KAAK,CAAC;cAChBC,MAAM,EAAE,OAAO;cACfC,OAAO,EAAE9B,IAAI,CAAC+B,OAAO;cACrBC,QAAQ,EAAE/F,UAAU,CAACgG;aACtB,CAAC;UACJ;QACF,CAAC,CAAC;QACF,IAAI,CAAC1C,WAAW,CAACuB,IAAI,CAACuD,4BAA4B,CAAC;MACrD,CAAC,MAAM;QACL,MAAME,+BAA+B,GAAG,IAAI,CAAC9F,QAAQ,CAAC+F,sBAAsB,CAACtD,KAAK,CAAC,CAACnB,SAAS,CAAEC,IAAS,IAAI;UAC1G,IAAIA,IAAI,CAACqB,MAAM,IAAI,CAAC,EAAE;YACpB,IAAI,CAACf,kBAAkB,CAACd,SAAS,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAACd,MAAM,CAACkD,KAAK,CAAC;cAChBC,MAAM,EAAE,OAAO;cACfC,OAAO,EAAE9B,IAAI,CAAC+B,OAAO;cACrBC,QAAQ,EAAE/F,UAAU,CAACgG;aACtB,CAAC;UACJ;QACF,CAAC,CAAC;QACF,IAAI,CAAC1C,WAAW,CAACuB,IAAI,CAACyD,+BAA+B,CAAC;MACxD;IACF;EACF;EAEA1D,sBAAsBA,CAAA;IACpB,MAAMK,KAAK,GAAG;MACZ1B,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBW,MAAM,EAAE,IAAI,CAAClB;KACd;IACD,MAAMwF,sBAAsB,GAAG,IAAI,CAAChG,QAAQ,CAACO,mBAAmB,CAACkC,KAAK,CAAC,CAACnB,SAAS,CAAEC,IAAS,IAAI;MAC9F,IAAIA,IAAI,CAACqB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACrC,mBAAmB,GAAGgB,IAAI,CAACA,IAAI;QACpC,IAAI,CAAChB,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACiF,GAAG,CAAEC,CAAC,IAAI;UAC5D,OAAO;YACL1B,EAAE,EAAE0B,CAAC,CAAC1B,EAAE;YACRkC,QAAQ,EAAER,CAAC,CAACQ,QAAQ;YACpBvE,MAAM,EAAE+D,CAAC,CAAC/D,MAAM;YAChBnC,QAAQ,EAAEkG,CAAC,CAAClG,QAAQ;YACpBL,SAAS,EAAEuG,CAAC,CAACvG,SAAS,GAClB,IAAI,CAACc,QAAQ,CAACgD,QAAQ,GAAG,GAAG,GAAGyC,CAAC,CAACvG,SAAS,GAC1C;WACL;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACe,MAAM,CAACkD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE9B,IAAI,CAAC+B,OAAO;UACrBC,QAAQ,EAAE/F,UAAU,CAACgG;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAAC1C,WAAW,CAACuB,IAAI,CAAC2D,sBAAsB,CAAC;EAC/C;;;uCApUWlG,4BAA4B,EAAAjC,EAAA,CAAAqI,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAvI,EAAA,CAAAqI,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzI,EAAA,CAAAqI,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3I,EAAA,CAAAqI,iBAAA,CAAAK,EAAA,CAAAE,MAAA,GAAA5I,EAAA,CAAAqI,iBAAA,CAAAQ,EAAA,CAAAC,QAAA,GAAA9I,EAAA,CAAAqI,iBAAA,CAAAU,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA5B/G,4BAA4B;MAAAgH,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnJ,EAAA,CAAAoJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCtBzC1J,EAAA,CAAAE,SAAA,cAAoE;UACpEF,EAAA,CAAAC,cAAA,UAAK;UAEHD,EADA,CAAAE,SAAA,iBAAyB,4BACsB;UACjDF,EAAA,CAAAG,YAAA,EAAM;UAGAH,EAFN,CAAAC,cAAA,aAAuB,aACmB,aACd;UACpBD,EAAA,CAAA4B,UAAA,IAAAgI,2CAAA,iBAAqE;UAMvE5J,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,aAA8B,WACT;UAAAD,EAAA,CAAAS,MAAA,IAA8B;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACnDH,EAAA,CAAAC,cAAA,YAAkB;UAACD,EAAA,CAAAS,MAAA,IAAoC;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAGnDH,EAFJ,CAAAC,cAAA,cAAkB,eACK,eACI;UAEnBD,EADA,CAAA4B,UAAA,KAAAiI,0CAAA,gBAAmE,KAAAC,0CAAA,gBACA;UAE3E9J,EADI,CAAAG,YAAA,EAAM,EACJ;UAEFH,EADF,CAAAC,cAAA,eAA+B,cACP;UACpBD,EAAA,CAAAE,SAAA,eAAqC;UAAAF,EAAA,CAAAS,MAAA,eACrC;UAAAT,EAAA,CAAAC,cAAA,gBAA0C;UAAAD,EAAA,CAAAS,MAAA,IAA6B;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,SAAA,UAAK;UAAAF,EAAA,CAAAC,cAAA,gBAA+B;UAAAD,EAAA,CAAAS,MAAA,kBAAU;UAC/HT,EAD+H,CAAAG,YAAA,EAAO,EAChI;UAUNH,EATA,CAAA4B,UAAA,KAAAmI,4CAAA,mBAAgE,KAAAC,4CAAA,mBASA;UAKpEhK,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAE,SAAA,aAA8F;UAG1DF,EAFpC,CAAAC,cAAA,eAAiB,cACW,kBACQ,gBAAsD;UAAhDD,EAAA,CAAAiK,UAAA,mBAAAC,6DAAA;YAAAlK,EAAA,CAAAmK,aAAA,CAAAC,GAAA;YAAA,OAAApK,EAAA,CAAAqK,WAAA,CAASV,GAAA,CAAA7B,mBAAA,CAAA6B,GAAA,CAAA/I,aAAA,CAAAsF,EAAA,CAAqC;UAAA,EAAC;UAAClG,EAAA,CAAAE,SAAA,eAAgH;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAS,MAAA,wBAAgB;UACjPT,EADiP,CAAAG,YAAA,EAAO,EAAS,EAC3P;UAE2BH,EADhC,CAAAC,cAAA,cAAsB,kBACU,YAAM;UAAAD,EAAA,CAAAE,SAAA,eAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAS,MAAA,iCAAyB;UAE/IT,EAF+I,CAAAG,YAAA,EAAO,EAAS,EACnJ,EACN;UACNH,EAAA,CAAAC,cAAA,eAAyB;UAKtBD,EAJA,CAAAE,SAAA,gBAAgC,gBACA,gBACA,gBACA,gBACA;UACpCF,EAAA,CAAAG,YAAA,EAAM;UAGFH,EAFJ,CAAAC,cAAA,eAAiB,eACkB,eACT;UAAAD,EAAA,CAAAE,SAAA,eAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChDH,EAAzB,CAAAC,cAAA,eAAyB,gBAAyC;UAAAD,EAAA,CAAAS,MAAA,YAAI;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,SAAA,UAAK;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAS,MAAA,IAA0B;UACpHT,EADoH,CAAAG,YAAA,EAAO,EAAM,EAC3H;UAEJH,EADF,CAAAC,cAAA,eAAiC,eACT;UAAAD,EAAA,CAAAE,SAAA,eAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC/CH,EAAzB,CAAAC,cAAA,eAAyB,gBAAyC;UAAAD,EAAA,CAAAS,MAAA,aAAK;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,SAAA,UAAK;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAS,MAAA,IAAkC;UAC7HT,EAD6H,CAAAG,YAAA,EAAO,EAAM,EACpI;UAEJH,EADF,CAAAC,cAAA,eAAiC,eACT;UAAAD,EAAA,CAAAE,SAAA,eAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChDH,EAAzB,CAAAC,cAAA,eAAyB,gBAAwC;UAAAD,EAAA,CAAAS,MAAA,YAAI;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,SAAA,UAAK;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAS,MAAA,IAAmD;;UAAAT,EAAA,CAAAE,SAAA,UAAK;UAAAF,EAAA,CAAAS,MAAA,IAAmD;;UACpMT,EADoM,CAAAG,YAAA,EAAO,EAAM,EAC3M;UAEJH,EADF,CAAAC,cAAA,eAAiC,eACT;UAAAD,EAAA,CAAAE,SAAA,eAAqD;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxDH,EAAzB,CAAAC,cAAA,eAAyB,gBAAyC;UAAAD,EAAA,CAAAS,MAAA,oBAAY;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,SAAA,UAAK;UAAAF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAS,MAAA,IAAyC;UAE7IT,EAF6I,CAAAG,YAAA,EAAO,EAAM,EAClJ,EACF;UAEJH,EADF,CAAAC,cAAA,eAAoH,kBACiC;UAAlDD,EAAA,CAAAiK,UAAA,mBAAAK,+DAAA;YAAAtK,EAAA,CAAAmK,aAAA,CAAAC,GAAA;YAAA,OAAApK,EAAA,CAAAqK,WAAA,CAASV,GAAA,CAAA1D,qBAAA,CAAA0D,GAAA,CAAA/I,aAAA,CAAAsF,EAAA,CAAuC;UAAA,EAAC;UAAClG,EAAA,CAAAC,cAAA,gBAAoB;UACrKD,EAAA,CAAAS,MAAA,IAAkB;UAAAT,EAAA,CAAAE,SAAA,aAAqE;UAKvGF,EALuG,CAAAG,YAAA,EAAO,EAAS,EACrG,EACD,EAEP,EACJ;UAMIH,EALV,CAAAC,cAAA,eAAwD,eAChC,eACO,kBACK,eACL,cACG;UAAAD,EAAA,CAAAS,MAAA,qBAAY;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACxCH,EAAA,CAAAC,cAAA,cAAyB;UACvBD,EAAA,CAAAS,MAAA,kPACA;UAAKT,EAAL,CAAAE,SAAA,WAAK,WAAK;UACVF,EAAA,CAAAS,MAAA,gOACA;UAAKT,EAAL,CAAAE,SAAA,WAAK,WAAK;UACVF,EAAA,CAAAS,MAAA,ucACF;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAS,MAAA,kBAAS;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACrCH,EAAA,CAAAC,cAAA,cAAyB;UACvBD,EAAA,CAAAS,MAAA,wcACA;UAAKT,EAAL,CAAAE,SAAA,WAAK,WAAK;UACVF,EAAA,CAAAS,MAAA,iWACF;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAEJH,EADA,CAAA4B,UAAA,MAAA2I,2CAAA,gBAAwD,MAAAC,6CAAA,kBACW;UAGrExK,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA4B;UAAAD,EAAA,CAAAS,MAAA,KAA2C;UAAAT,EAAA,CAAAG,YAAA,EAAM;UAC7EH,EAAA,CAAAC,cAAA,gBAAwB;UACtBD,EAAA,CAAAE,SAAA,wBAAkG;UAEhGF,EADF,CAAAC,cAAA,gBAAkB,mBACiE;UAAzCD,EAAA,CAAAiK,UAAA,mBAAAQ,gEAAA;YAAAzK,EAAA,CAAAmK,aAAA,CAAAC,GAAA;YAAA,MAAAM,cAAA,GAAA1K,EAAA,CAAA2K,WAAA;YAAA,OAAA3K,EAAA,CAAAqK,WAAA,CAASV,GAAA,CAAAxC,WAAA,CAAAuD,cAAA,CAAA9F,KAAA,CAA8B;UAAA,EAAC;UAAC5E,EAAA,CAAAC,cAAA,iBAAoB;UAAAD,EAAA,CAAAS,MAAA,qBAAY;UACnHT,EADmH,CAAAG,YAAA,EAAO,EAAS,EAC7H;UACNH,EAAA,CAAAC,cAAA,gBAA8G;UAC1GD,EAAA,CAAA4B,UAAA,MAAAgJ,6CAAA,mBAAiI;UAe7I5K,EAJQ,CAAAG,YAAA,EAAM,EACF,EACC,EACT,EACE;UAIAH,EAHN,CAAAC,cAAA,gBAAsB,gBACc,gBACT,cAC0D;UAAAD,EAAA,CAAAS,MAAA,oBAAW;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAC9FH,EAAA,CAAAE,SAAA,cAAyB;UACzBF,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAS,MAAA,eAAM;UAAAT,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAS,MAAA,KAAkC;UAAOT,EAAP,CAAAG,YAAA,EAAO,EAAI;UAC5GH,EAAA,CAAAE,SAAA,cAAyB;UACzBF,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAS,MAAA,aAAI;UAAAT,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAS,MAAA,2BAAkB;UAAOT,EAAP,CAAAG,YAAA,EAAO,EAAI;UAC1FH,EAAA,CAAAE,SAAA,cAAyB;UACCF,EAA1B,CAAAC,cAAA,cAA0B,aAAM;UAAAD,EAAA,CAAAS,MAAA,eAAM;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,iBAAgD;UAK9CD,EAJA,CAAAE,SAAA,iBAAwC,iBACA,iBACA,iBACA,iBACR;UAClCF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAS,MAAA,oCAET;UAEJT,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;UAIFH,EAFJ,CAAAC,cAAA,gBAAkC,gBACT,cAC0D;UAAAD,EAAA,CAAAS,MAAA,0BAAiB;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,SAAA,cAAyB;UACzBF,EAAA,CAAAC,cAAA,gBAAiB;UACfD,EAAA,CAAA4B,UAAA,MAAAiJ,sDAAA,2BAAsD;UAclE7K,EALU,CAAAG,YAAA,EAAM,EACH,EACD,EACF,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAE,SAAA,cAA8E;UAE1EF,EADJ,CAAAC,cAAA,gBAAc,cACkH;UAC1HD,EAAA,CAAAS,MAAA,0BACF;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAIEH,EAHN,CAAAC,cAAA,gBAAuE,gBAC/C,gBAC+B,gBACW;UACtDD,EAAA,CAAAE,SAAA,gBAA6C;UAC7CF,EAAA,CAAAC,cAAA,gBAA4B;UAAAD,EAAA,CAAAE,SAAA,cAAgC;UAAAF,EAAA,CAAAS,MAAA,sBAAa;UAAAT,EAAA,CAAAG,YAAA,EAAM;UAC/EH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,SAAA,gBAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnFH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,SAAA,cAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACtEH,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAS,MAAA,oBAAW;UACvCT,EADuC,CAAAG,YAAA,EAAM,EACvC;UAEFH,EADJ,CAAAC,cAAA,gBAAuB,aACA;UAAAD,EAAA,CAAAS,MAAA,oEACS;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAChCH,EAAA,CAAAC,cAAA,cAAmB;UAACD,EAAA,CAAAS,MAAA,gHAAsG;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAE5HH,EADF,CAAAC,cAAA,gBAAuC,gBACD;UAClCD,EAAA,CAAAS,MAAA,sBACF;UAAAT,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAuE;UAKrED,EAJA,CAAAE,SAAA,iBAAwC,iBACA,iBACA,iBACR,iBACA;UAEpCF,EADE,CAAAG,YAAA,EAAM,EACF;UAOAH,EANN,CAAAC,cAAA,YAAK,gBAIkB,gBACgB,cACJ;UAAAD,EAAA,CAAAS,MAAA,4BAAmB;UAGtDT,EAHsD,CAAAG,YAAA,EAAI,EAChD,EACF,EACF;UAEFH,EADJ,CAAAC,cAAA,gBAA8B,gBACH;UACrBD,EAAA,CAAAE,SAAA,gBAAqF;UAAAF,EAAA,CAAAS,MAAA,gBACrF;UAAAT,EAAA,CAAAC,cAAA,iBAAyD;UAAAD,EAAA,CAAAS,MAAA,YAAG;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,SAAA,WAAK;UAAAF,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAS,MAAA,4BAAmB;UAGrIT,EAHqI,CAAAG,YAAA,EAAO,EAC9H,EACJ,EACJ;UACNH,EAAA,CAAAE,SAAA,cAAoD;UAETF,EAD3C,CAAAC,cAAA,gBAAuD,mBACZ,iBAAqB;UAAAD,EAAA,CAAAS,MAAA,qBAAY;UAAAT,EAAA,CAAAE,SAAA,cAAqE;UAElJF,EAFkJ,CAAAG,YAAA,EAAO,EAAS,EAC3J,EACD;UAELH,EADF,CAAAC,cAAA,gBAAmD,gBACW;UACtDD,EAAA,CAAAE,SAAA,gBAA+C;UAC/CF,EAAA,CAAAC,cAAA,gBAA4B;UAAAD,EAAA,CAAAE,SAAA,cAAgC;UAAAF,EAAA,CAAAS,MAAA,wBAAe;UAAAT,EAAA,CAAAG,YAAA,EAAM;UACjFH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,SAAA,gBAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnFH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,SAAA,cAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACtEH,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAS,MAAA,oBAAW;UACvCT,EADuC,CAAAG,YAAA,EAAM,EACvC;UAEFH,EADJ,CAAAC,cAAA,gBAAuB,aACA;UAAAD,EAAA,CAAAS,MAAA,8CAEnB;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAAmB;UAACD,EAAA,CAAAS,MAAA,gHAAsG;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAE5HH,EADF,CAAAC,cAAA,gBAAuC,gBACD;UAClCD,EAAA,CAAAS,MAAA,wBACF;UAAAT,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAuE;UAKrED,EAJA,CAAAE,SAAA,iBAAwC,iBACA,iBACA,iBACR,iBACA;UAEpCF,EADE,CAAAG,YAAA,EAAM,EACF;UAOAH,EANN,CAAAC,cAAA,YAAK,gBAIkB,gBACgB,cACJ;UAAAD,EAAA,CAAAS,MAAA,2BAAkB;UAGrDT,EAHqD,CAAAG,YAAA,EAAI,EAC/C,EACF,EACF;UAEFH,EADJ,CAAAC,cAAA,gBAA8B,eACJ;UACpBD,EAAA,CAAAE,SAAA,gBAA4E;UAAAF,EAAA,CAAAS,MAAA,gBAC5E;UAAAT,EAAA,CAAAC,cAAA,iBAAyD;UAAAD,EAAA,CAAAS,MAAA,WAAE;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,SAAA,WAAK;UAAAF,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAS,MAAA,kBAAS;UAClHT,EADkH,CAAAG,YAAA,EAAO,EACnH;UACNH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAA2F;UAAAF,EAAA,CAAAS,MAAA,gBAC3F;UACET,EADF,CAAAC,cAAA,iBAA0C,iBACiD;UACvFD,EAAA,CAAAE,SAAA,iBAA0I;UAE9IF,EADE,CAAAG,YAAA,EAAM,EACD;UAACH,EAAA,CAAAE,SAAA,WAAK;UAAAF,EAAA,CAAAC,cAAA,kBAA2C;UAAAD,EAAA,CAAAS,MAAA,sBAAa;UAG/ET,EAH+E,CAAAG,YAAA,EAAO,EACxE,EACJ,EACJ;UACNH,EAAA,CAAAE,SAAA,cAAoD;UAETF,EAD3C,CAAAC,cAAA,gBAAuD,mBACZ,iBAAqB;UAAAD,EAAA,CAAAS,MAAA,qBAAY;UAAAT,EAAA,CAAAE,SAAA,cAAqE;UAElJF,EAFkJ,CAAAG,YAAA,EAAO,EAAS,EAC3J,EACD;UAELH,EADD,CAAAC,cAAA,gBAAmD,gBACU;UACtDD,EAAA,CAAAE,SAAA,iBAAmD;UACnDF,EAAA,CAAAC,cAAA,gBAA4B;UAAAD,EAAA,CAAAE,SAAA,cAAgC;UAAAF,EAAA,CAAAS,MAAA,oBAAW;UAAAT,EAAA,CAAAG,YAAA,EAAM;UAC7EH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,SAAA,gBAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnFH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,SAAA,cAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACtEH,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAS,MAAA,oBAAW;UACvCT,EADuC,CAAAG,YAAA,EAAM,EACvC;UAEFH,EADJ,CAAAC,cAAA,gBAAuB,aACA;UAAAD,EAAA,CAAAS,MAAA,gDAEnB;UAAAT,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAAmB;UAACD,EAAA,CAAAS,MAAA,gHAAsG;UAAAT,EAAA,CAAAG,YAAA,EAAI;UAE5HH,EADF,CAAAC,cAAA,gBAAuC,gBACD;UAClCD,EAAA,CAAAS,MAAA,wBACF;UAAAT,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAuE;UAKrED,EAJA,CAAAE,SAAA,iBAAwC,iBACA,iBACA,iBACR,iBACA;UAEpCF,EADE,CAAAG,YAAA,EAAM,EACF;UAOFH,EANJ,CAAAC,cAAA,YAAK,gBAIgB,gBACgB,cACJ;UAAAD,EAAA,CAAAS,MAAA,2BAAkB;UAGnDT,EAHmD,CAAAG,YAAA,EAAI,EAC/C,EACF,EACA;UAEJH,EADF,CAAAC,cAAA,gBAA8B,eACN;UACpBD,EAAA,CAAAE,SAAA,gBAA4E;UAAAF,EAAA,CAAAS,MAAA,gBAC5E;UAAAT,EAAA,CAAAC,cAAA,iBAAyD;UAAAD,EAAA,CAAAS,MAAA,WAAE;UAAAT,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,SAAA,WAAK;UAAAF,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAS,MAAA,kBAAS;UAClHT,EADkH,CAAAG,YAAA,EAAO,EACnH;UACNH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAA2F;UAAAF,EAAA,CAAAS,MAAA,gBAC3F;UACET,EADF,CAAAC,cAAA,iBAA0C,iBACiD;UACvFD,EAAA,CAAAE,SAAA,iBAA0I;UAE9IF,EADE,CAAAG,YAAA,EAAM,EACD;UAACH,EAAA,CAAAE,SAAA,WAAK;UAAAF,EAAA,CAAAC,cAAA,kBAA2C;UAAAD,EAAA,CAAAS,MAAA,sBAAa;UAE3ET,EAF2E,CAAAG,YAAA,EAAO,EACxE,EACJ;UACRH,EAAA,CAAAE,SAAA,cAAoD;UAETF,EAD3C,CAAAC,cAAA,gBAAuD,mBACZ,iBAAqB;UAAAD,EAAA,CAAAS,MAAA,qBAAY;UAAAT,EAAA,CAAAE,SAAA,cAAqE;UAM3JF,EAN2J,CAAAG,YAAA,EAAO,EAAS,EAC3J,EACD,EACP,EACE,EACJ,EACA;UACNH,EAAA,CAAAC,cAAA,eAAkB;UAChBD,EAAA,CAAAE,SAAA,mBAAyB;UAC3BF,EAAA,CAAAG,YAAA,EAAM;UAOEH,EAJR,CAAAC,cAAA,iBAA0J,iBAC9G,iBACb,iBACC,gBACuB;UAAAD,EAAA,CAAAS,MAAA,uBAAc;UAAAT,EAAA,CAAAG,YAAA,EAAK;UAClEH,EAAA,CAAAC,cAAA,oBAAmH;UAAnCD,EAAA,CAAAiK,UAAA,mBAAAa,gEAAA;YAAA9K,EAAA,CAAAmK,aAAA,CAAAC,GAAA;YAAA,OAAApK,EAAA,CAAAqK,WAAA,CAASV,GAAA,CAAAvD,sBAAA,EAAwB;UAAA,EAAC;UAEpHpG,EADE,CAAAG,YAAA,EAAS,EACL;UACNH,EAAA,CAAAC,cAAA,iBAAwB;UACtBD,EAAA,CAAAE,SAAA,mBAA8B;UAC7BF,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAS,MAAA,qDAA4C;UACnDT,EADmD,CAAAG,YAAA,EAAK,EAClD;UAEJH,EADF,CAAAC,cAAA,iBAA0B,oBACwE;UAAnCD,EAAA,CAAAiK,UAAA,mBAAAc,gEAAA;YAAA/K,EAAA,CAAAmK,aAAA,CAAAC,GAAA;YAAA,OAAApK,EAAA,CAAAqK,WAAA,CAASV,GAAA,CAAAvD,sBAAA,EAAwB;UAAA,EAAC;UAACpG,EAAA,CAAAC,cAAA,kBAAsB;UAACD,EAAA,CAAAS,MAAA,gBAAM;UAAQT,EAAR,CAAAG,YAAA,EAAO,EAAU;UAC9IH,EAAA,CAAAC,cAAA,oBAA0E;UAAlCD,EAAA,CAAAiK,UAAA,mBAAAe,gEAAA;YAAAhL,EAAA,CAAAmK,aAAA,CAAAC,GAAA;YAAA,OAAApK,EAAA,CAAAqK,WAAA,CAASV,GAAA,CAAArD,YAAA,CAAAqD,GAAA,CAAAzG,SAAA,CAAuB;UAAA,EAAC;UAAClD,EAAA,CAAAC,cAAA,kBAAsB;UAAAD,EAAA,CAAAS,MAAA,cAAK;UAI7GT,EAJ6G,CAAAG,YAAA,EAAO,EAAS,EACjH,EACF,EACF,EACF,EA9LqB;;;UAxKOH,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAK,UAAA,SAAAsJ,GAAA,CAAAlH,SAAA,IAAAkH,GAAA,CAAAlH,SAAA,CAAAT,MAAA,KAAuC;UAQhDhC,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAe,iBAAA,CAAA4I,GAAA,CAAA/I,aAAA,CAAAqK,YAAA,CAA8B;UAC5BjL,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAmB,kBAAA,MAAAwI,GAAA,CAAA/I,aAAA,CAAAsK,kBAAA,KAAoC;UAIjBlL,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAK,UAAA,SAAAsJ,GAAA,CAAA/I,aAAA,CAAAuK,WAAA,WAAuC;UACvCnL,EAAA,CAAAI,SAAA,EAAuC;UAAvCJ,EAAA,CAAAK,UAAA,SAAAsJ,GAAA,CAAA/I,aAAA,CAAAuK,WAAA,WAAuC;UAMzBnL,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAe,iBAAA,CAAA4I,GAAA,CAAA/I,aAAA,CAAAwK,WAAA,CAA6B;UAElDpL,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAK,UAAA,SAAAsJ,GAAA,CAAA/I,aAAA,CAAAuK,WAAA,WAAuC;UASvCnL,EAAA,CAAAI,SAAA,EAAuC;UAAvCJ,EAAA,CAAAK,UAAA,SAAAsJ,GAAA,CAAA/I,aAAA,CAAAuK,WAAA,WAAuC;UASkDnL,EAAA,CAAAI,SAAA,GAAuE;UAAvEJ,EAAA,CAAAiB,qBAAA,QAAA0I,GAAA,CAAA/I,aAAA,CAAAyK,sBAAA,UAAA1B,GAAA,CAAA5G,OAAA,GAAA4G,GAAA,CAAA3G,QAAA,EAAAhD,EAAA,CAAAQ,aAAA,CAAuE;UAgB/FR,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAe,iBAAA,CAAA4I,GAAA,CAAA/I,aAAA,CAAA0K,QAAA,CAA0B;UAIzBtL,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAe,iBAAA,CAAA4I,GAAA,CAAA/I,aAAA,CAAA2K,gBAAA,CAAkC;UAIpCvL,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAmB,kBAAA,SAAAnB,EAAA,CAAAW,WAAA,SAAAgJ,GAAA,CAAA/I,aAAA,CAAAC,SAAA,oBAAmD;UAAKb,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAmB,kBAAA,WAAAnB,EAAA,CAAAW,WAAA,SAAAgJ,GAAA,CAAA/I,aAAA,CAAAE,OAAA,oBAAmD;UAIlGd,EAAA,CAAAI,SAAA,IAAyC;UAAzCJ,EAAA,CAAAe,iBAAA,CAAA4I,GAAA,CAAA/I,aAAA,CAAA4K,uBAAA,CAAyC;UAInGxL,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAK,UAAA,aAAAsJ,GAAA,CAAA/I,aAAA,CAAAwE,kBAAA,cAAwD;UAC9FpF,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAmB,kBAAA,MAAAwI,GAAA,CAAA/G,OAAA,YAAkB;UAyBC5C,EAAA,CAAAI,SAAA,IAA6B;UAA7BJ,EAAA,CAAAK,UAAA,SAAAsJ,GAAA,CAAAzI,UAAA,IAAAuK,SAAA,CAA6B;UAClBzL,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAAK,UAAA,SAAAsJ,GAAA,CAAAzI,UAAA,IAAAuK,SAAA,CAA6B;UAIvCzL,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAe,iBAAA,CAAA4I,GAAA,CAAA/I,aAAA,CAAA8K,yBAAA,CAA2C;UAOyC1L,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAK,UAAA,YAAAsJ,GAAA,CAAA9G,kBAAA,CAAqB;UAqBtE7C,EAAA,CAAAI,SAAA,IAAkC;UAAlCJ,EAAA,CAAAe,iBAAA,CAAA4I,GAAA,CAAA/I,aAAA,CAAA+K,gBAAA,CAAkC;UAsBhF3L,EAAA,CAAAI,SAAA,IAA4B;UAA5BJ,EAAA,CAAAK,UAAA,SAAAsJ,GAAA,CAAAjH,mBAAA,CAA4B;;;qBD7I3C9C,eAAe,EAAEC,UAAU,EAAA+L,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,eAAA,EAAErM,YAAY,EAAAoJ,EAAA,CAAAkD,OAAA,EAAAlD,EAAA,CAAAmD,IAAA,EAAAnD,EAAA,CAAAC,QAAA,EAAEhJ,yBAAyB,EAAEC,eAAe;MAAAkM,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}