{"ast": null, "code": "import { AuthGuard } from \"./main/guards/auth.guard\";\nimport { HomeComponent } from \"./main/components/home/<USER>\";\nimport { NewMissionComponent } from \"./main/components/new-mission/new-mission.component\";\nimport { PrivacyPolicyComponent } from \"./main/components/privacy-policy/privacy-policy.component\";\nimport { VolunteeringMissionComponent } from \"./main/components/volunteering-mission/volunteering-mission.component\";\nimport { ForgotPasswordComponent } from \"./main/components/login-register/forgot-password/forgot-password.component\";\nimport { LoginComponent } from \"./main/components/login-register/login/login.component\";\nimport { RegisterComponent } from \"./main/components/login-register/register/register.component\";\nimport { ResetPasswordComponent } from \"./main/components/login-register/reset-password/reset-password.component\";\nimport { UserEditProfileComponent } from \"./main/components/user-edit-profile/user-edit-profile.component\";\nimport { VolunteeringTimesheetComponent } from \"./main/components/volunteering-timesheet/volunteering-timesheet.component\";\nexport const routes = [{\n  path: \"\",\n  component: LoginComponent\n}, {\n  path: \"register\",\n  component: RegisterComponent\n}, {\n  path: \"forgotPassword\",\n  component: ForgotPasswordComponent\n}, {\n  path: \"resetPassword\",\n  component: ResetPasswordComponent\n}, {\n  path: \"home\",\n  component: HomeComponent\n}, {\n  path: \"addNewMission\",\n  component: NewMissionComponent\n}, {\n  path: \"volunteeringMission/:missionId\",\n  component: VolunteeringMissionComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: \"userProfile/:userId\",\n  component: UserEditProfileComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: \"privacyPolicy\",\n  component: PrivacyPolicyComponent\n}, {\n  path: \"volunteeringTimesheet\",\n  component: VolunteeringTimesheetComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: \"admin\",\n  loadChildren: () => import(\"./main/components/admin-side/admin-side.route\")\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "HomeComponent", "NewMissionComponent", "PrivacyPolicyComponent", "VolunteeringMissionComponent", "ForgotPasswordComponent", "LoginComponent", "RegisterComponent", "ResetPasswordComponent", "UserEditProfileComponent", "VolunteeringTimesheetComponent", "routes", "path", "component", "canActivate", "loadChildren"], "sources": ["C:\\Users\\<USER>\\Downloads\\ci_platfrom_app-develop\\ci_platfrom_app-develop\\src\\app\\app.route.ts"], "sourcesContent": ["import { type Routes } from \"@angular/router\"\nimport { AuthGuard } from \"./main/guards/auth.guard\"\nimport { HomeComponent } from \"./main/components/home/<USER>\"\nimport { NewMissionComponent } from \"./main/components/new-mission/new-mission.component\"\nimport { PrivacyPolicyComponent } from \"./main/components/privacy-policy/privacy-policy.component\"\nimport { VolunteeringMissionComponent } from \"./main/components/volunteering-mission/volunteering-mission.component\"\nimport { ForgotPasswordComponent } from \"./main/components/login-register/forgot-password/forgot-password.component\"\nimport { LoginComponent } from \"./main/components/login-register/login/login.component\"\nimport { RegisterComponent } from \"./main/components/login-register/register/register.component\"\nimport { ResetPasswordComponent } from \"./main/components/login-register/reset-password/reset-password.component\"\nimport { UserEditProfileComponent } from \"./main/components/user-edit-profile/user-edit-profile.component\"\nimport { VolunteeringTimesheetComponent } from \"./main/components/volunteering-timesheet/volunteering-timesheet.component\"\n\nexport const routes: Routes = [\n  { path: \"\", component: LoginComponent },\n  { path: \"register\", component: RegisterComponent },\n  { path: \"forgotPassword\", component: ForgotPasswordComponent },\n  { path: \"resetPassword\", component: ResetPasswordComponent },\n  { path: \"home\", component: HomeComponent },\n  { path: \"addNewMission\", component: NewMissionComponent },\n  { path: \"volunteeringMission/:missionId\", component: VolunteeringMissionComponent, canActivate: [AuthGuard] },\n  { path: \"userProfile/:userId\", component: UserEditProfileComponent, canActivate: [AuthGuard] },\n  { path: \"privacyPolicy\", component: PrivacyPolicyComponent },\n  { path: \"volunteeringTimesheet\", component: VolunteeringTimesheetComponent, canActivate: [AuthGuard] },\n  { path: \"admin\", loadChildren: () => import(\"./main/components/admin-side/admin-side.route\") },\n]"], "mappings": "AACA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,sBAAsB,QAAQ,2DAA2D;AAClG,SAASC,4BAA4B,QAAQ,uEAAuE;AACpH,SAASC,uBAAuB,QAAQ,4EAA4E;AACpH,SAASC,cAAc,QAAQ,wDAAwD;AACvF,SAASC,iBAAiB,QAAQ,8DAA8D;AAChG,SAASC,sBAAsB,QAAQ,0EAA0E;AACjH,SAASC,wBAAwB,QAAQ,iEAAiE;AAC1G,SAASC,8BAA8B,QAAQ,2EAA2E;AAE1H,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEP;AAAc,CAAE,EACvC;EAAEM,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEN;AAAiB,CAAE,EAClD;EAAEK,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAER;AAAuB,CAAE,EAC9D;EAAEO,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEL;AAAsB,CAAE,EAC5D;EAAEI,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEZ;AAAa,CAAE,EAC1C;EAAEW,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEX;AAAmB,CAAE,EACzD;EAAEU,IAAI,EAAE,gCAAgC;EAAEC,SAAS,EAAET,4BAA4B;EAAEU,WAAW,EAAE,CAACd,SAAS;AAAC,CAAE,EAC7G;EAAEY,IAAI,EAAE,qBAAqB;EAAEC,SAAS,EAAEJ,wBAAwB;EAAEK,WAAW,EAAE,CAACd,SAAS;AAAC,CAAE,EAC9F;EAAEY,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEV;AAAsB,CAAE,EAC5D;EAAES,IAAI,EAAE,uBAAuB;EAAEC,SAAS,EAAEH,8BAA8B;EAAEI,WAAW,EAAE,CAACd,SAAS;AAAC,CAAE,EACtG;EAAEY,IAAI,EAAE,OAAO;EAAEG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C;AAAC,CAAE,CAC/F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}