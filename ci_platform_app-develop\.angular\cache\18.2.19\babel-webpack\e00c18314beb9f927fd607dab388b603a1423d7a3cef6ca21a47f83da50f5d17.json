{"ast": null, "code": "/* eslint-disable arrow-body-style */\n\nimport utils from './utils';\nimport domUtils from \"./dom-utils\";\nconst absoluteRect = (anchor, element, offset, scale) => {\n  const scrollPos = elementScrollPosition(anchor, element);\n  const rect = utils.eitherRect(domUtils.offset(anchor), offset);\n  const stackScale = 2 * scale;\n  const stackScroll = domUtils.stackingElementScroll(element);\n  if (scale !== 1 && stackScroll) {\n    stackScroll.x /= stackScale;\n    stackScroll.y /= stackScale;\n  }\n  const stackOffset = domUtils.stackingElementOffset(element);\n  if (scale !== 1 && stackOffset) {\n    stackOffset.left /= stackScale;\n    stackOffset.top /= stackScale;\n  }\n  return domUtils.removeScroll(domUtils.addScroll(utils.removeStackingOffset(utils.scaleRect(rect, scale), stackOffset), stackScroll), scrollPos);\n};\nconst relativeRect = (anchor, element, offset, scale) => {\n  const rect = utils.eitherRect(domUtils.position(anchor, element, scale), offset);\n  return utils.scaleRect(rect, scale);\n};\nconst elementScrollPosition = (anchor, element) => {\n  return anchor ? {\n    x: 0,\n    y: 0\n  } : domUtils.scrollPosition(element);\n};\nconst alignElement = settings => {\n  const {\n    anchor,\n    element,\n    anchorAlign,\n    elementAlign,\n    margin,\n    offset,\n    positionMode,\n    scale\n  } = settings;\n  const currentScale = scale || 1;\n  const fixedMode = positionMode === 'fixed' || !domUtils.hasOffsetParent(element);\n  const anchorRect = fixedMode ? absoluteRect(anchor, element, offset, currentScale) : relativeRect(anchor, element, offset, currentScale);\n  const elementRect = utils.scaleRect(domUtils.offset(element), currentScale);\n  const result = domUtils.align({\n    anchorAlign: anchorAlign,\n    anchorRect: anchorRect,\n    elementAlign: elementAlign,\n    elementRect: elementRect,\n    margin\n  });\n  return result;\n};\nexport default alignElement;", "map": {"version": 3, "names": ["utils", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "absoluteRect", "anchor", "element", "offset", "scale", "scrollPos", "elementScrollPosition", "rect", "eitherRect", "stackScale", "stackScroll", "stackingElementScroll", "x", "y", "stackOffset", "stackingElementOffset", "left", "top", "removeScroll", "addScroll", "removeStackingOffset", "scaleRect", "relativeRect", "position", "scrollPosition", "alignElement", "settings", "anchorAlign", "elementAlign", "margin", "positionMode", "currentScale", "fixedMode", "hasOffsetParent", "anchorRect", "elementRect", "result", "align"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/align-element.js"], "sourcesContent": ["/* eslint-disable arrow-body-style */\n\nimport utils from './utils';\nimport domUtils from \"./dom-utils\";\n\nconst absoluteRect = (anchor, element, offset, scale) => {\n    const scrollPos = elementScrollPosition(anchor, element);\n    const rect = utils.eitherRect(domUtils.offset(anchor), offset);\n    const stackScale = 2 * scale;\n\n    const stackScroll = domUtils.stackingElementScroll(element);\n    if (scale !== 1 && stackScroll) {\n        stackScroll.x /= stackScale;\n        stackScroll.y /= stackScale;\n    }\n\n    const stackOffset = domUtils.stackingElementOffset(element);\n    if (scale !== 1 && stackOffset) {\n        stackOffset.left /= stackScale;\n        stackOffset.top /= stackScale;\n    }\n\n    return domUtils.removeScroll(\n        domUtils.addScroll(\n            utils.removeStackingOffset(\n                utils.scaleRect(rect, scale),\n                stackOffset\n            ),\n            stackScroll\n        ),\n        scrollPos\n    );\n};\n\nconst relativeRect = (anchor, element, offset, scale) => {\n    const rect = utils.eitherRect(domUtils.position(anchor, element, scale), offset);\n    return utils.scaleRect(rect, scale);\n};\n\nconst elementScrollPosition = (anchor, element) => {\n    return anchor ? { x: 0, y: 0 } : domUtils.scrollPosition(element);\n};\n\nconst alignElement = (settings) => {\n    const { anchor, element, anchorAlign, elementAlign, margin, offset, positionMode, scale } = settings;\n\n    const currentScale = scale || 1;\n    const fixedMode = positionMode === 'fixed' || !domUtils.hasOffsetParent(element);\n    const anchorRect = fixedMode ? absoluteRect(anchor, element, offset, currentScale) : relativeRect(anchor, element, offset, currentScale);\n    const elementRect = utils.scaleRect(domUtils.offset(element), currentScale);\n\n    const result = domUtils.align({\n        anchorAlign: anchorAlign,\n        anchorRect: anchorRect,\n        elementAlign: elementAlign,\n        elementRect: elementRect,\n        margin\n    });\n\n    return result;\n};\n\nexport default alignElement;\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,QAAQ,MAAM,aAAa;AAElC,MAAMC,YAAY,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,KAAK;EACrD,MAAMC,SAAS,GAAGC,qBAAqB,CAACL,MAAM,EAAEC,OAAO,CAAC;EACxD,MAAMK,IAAI,GAAGT,KAAK,CAACU,UAAU,CAACT,QAAQ,CAACI,MAAM,CAACF,MAAM,CAAC,EAAEE,MAAM,CAAC;EAC9D,MAAMM,UAAU,GAAG,CAAC,GAAGL,KAAK;EAE5B,MAAMM,WAAW,GAAGX,QAAQ,CAACY,qBAAqB,CAACT,OAAO,CAAC;EAC3D,IAAIE,KAAK,KAAK,CAAC,IAAIM,WAAW,EAAE;IAC5BA,WAAW,CAACE,CAAC,IAAIH,UAAU;IAC3BC,WAAW,CAACG,CAAC,IAAIJ,UAAU;EAC/B;EAEA,MAAMK,WAAW,GAAGf,QAAQ,CAACgB,qBAAqB,CAACb,OAAO,CAAC;EAC3D,IAAIE,KAAK,KAAK,CAAC,IAAIU,WAAW,EAAE;IAC5BA,WAAW,CAACE,IAAI,IAAIP,UAAU;IAC9BK,WAAW,CAACG,GAAG,IAAIR,UAAU;EACjC;EAEA,OAAOV,QAAQ,CAACmB,YAAY,CACxBnB,QAAQ,CAACoB,SAAS,CACdrB,KAAK,CAACsB,oBAAoB,CACtBtB,KAAK,CAACuB,SAAS,CAACd,IAAI,EAAEH,KAAK,CAAC,EAC5BU,WACJ,CAAC,EACDJ,WACJ,CAAC,EACDL,SACJ,CAAC;AACL,CAAC;AAED,MAAMiB,YAAY,GAAGA,CAACrB,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,KAAK;EACrD,MAAMG,IAAI,GAAGT,KAAK,CAACU,UAAU,CAACT,QAAQ,CAACwB,QAAQ,CAACtB,MAAM,EAAEC,OAAO,EAAEE,KAAK,CAAC,EAAED,MAAM,CAAC;EAChF,OAAOL,KAAK,CAACuB,SAAS,CAACd,IAAI,EAAEH,KAAK,CAAC;AACvC,CAAC;AAED,MAAME,qBAAqB,GAAGA,CAACL,MAAM,EAAEC,OAAO,KAAK;EAC/C,OAAOD,MAAM,GAAG;IAAEW,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,GAAGd,QAAQ,CAACyB,cAAc,CAACtB,OAAO,CAAC;AACrE,CAAC;AAED,MAAMuB,YAAY,GAAIC,QAAQ,IAAK;EAC/B,MAAM;IAAEzB,MAAM;IAAEC,OAAO;IAAEyB,WAAW;IAAEC,YAAY;IAAEC,MAAM;IAAE1B,MAAM;IAAE2B,YAAY;IAAE1B;EAAM,CAAC,GAAGsB,QAAQ;EAEpG,MAAMK,YAAY,GAAG3B,KAAK,IAAI,CAAC;EAC/B,MAAM4B,SAAS,GAAGF,YAAY,KAAK,OAAO,IAAI,CAAC/B,QAAQ,CAACkC,eAAe,CAAC/B,OAAO,CAAC;EAChF,MAAMgC,UAAU,GAAGF,SAAS,GAAGhC,YAAY,CAACC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAE4B,YAAY,CAAC,GAAGT,YAAY,CAACrB,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAE4B,YAAY,CAAC;EACxI,MAAMI,WAAW,GAAGrC,KAAK,CAACuB,SAAS,CAACtB,QAAQ,CAACI,MAAM,CAACD,OAAO,CAAC,EAAE6B,YAAY,CAAC;EAE3E,MAAMK,MAAM,GAAGrC,QAAQ,CAACsC,KAAK,CAAC;IAC1BV,WAAW,EAAEA,WAAW;IACxBO,UAAU,EAAEA,UAAU;IACtBN,YAAY,EAAEA,YAAY;IAC1BO,WAAW,EAAEA,WAAW;IACxBN;EACJ,CAAC,CAAC;EAEF,OAAOO,MAAM;AACjB,CAAC;AAED,eAAeX,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}