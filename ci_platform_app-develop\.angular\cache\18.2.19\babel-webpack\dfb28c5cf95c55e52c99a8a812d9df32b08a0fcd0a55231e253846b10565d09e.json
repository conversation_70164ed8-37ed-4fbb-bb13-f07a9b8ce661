{"ast": null, "code": "const FIELD_REGEX = /\\[(?:(\\d+)|['\"](.*?)['\"])\\]|((?:(?!\\[.*?\\]|\\.).)+)/g;\n/**\n * @hidden\n */\nexport function fieldList(field) {\n  const fields = [];\n  field.replace(FIELD_REGEX, function (_match, index, indexAccessor, fieldName) {\n    fields.push(index !== undefined ? index : indexAccessor || fieldName);\n  });\n  return fields;\n}", "map": {"version": 3, "names": ["FIELD_REGEX", "fieldList", "field", "fields", "replace", "_match", "index", "indexAccessor", "fieldName", "push", "undefined"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-common/dist/es2015/accessors/field-list.js"], "sourcesContent": ["const FIELD_REGEX = /\\[(?:(\\d+)|['\"](.*?)['\"])\\]|((?:(?!\\[.*?\\]|\\.).)+)/g;\n/**\n * @hidden\n */\nexport function fieldList(field) {\n    const fields = [];\n    field.replace(FIELD_REGEX, function (_match, index, indexAccessor, fieldName) {\n        fields.push(index !== undefined ? index : (indexAccessor || fieldName));\n    });\n    return fields;\n}\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG,qDAAqD;AACzE;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC7B,MAAMC,MAAM,GAAG,EAAE;EACjBD,KAAK,CAACE,OAAO,CAACJ,WAAW,EAAE,UAAUK,MAAM,EAAEC,KAAK,EAAEC,aAAa,EAAEC,SAAS,EAAE;IAC1EL,MAAM,CAACM,IAAI,CAACH,KAAK,KAAKI,SAAS,GAAGJ,KAAK,GAAIC,aAAa,IAAIC,SAAU,CAAC;EAC3E,CAAC,CAAC;EACF,OAAOL,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}