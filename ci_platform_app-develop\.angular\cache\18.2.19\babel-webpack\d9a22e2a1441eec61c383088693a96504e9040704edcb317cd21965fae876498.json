{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ViewChild, HostListener, ViewEncapsulation, HostBinding, NgModule } from '@angular/core';\nimport * as i1 from '@angular/platform-browser';\nimport { HammerGestureConfig, HAMMER_GESTURE_CONFIG } from '@angular/platform-browser';\nimport * as i6 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nconst _c0 = a0 => ({\n  \"ngx-gallery-active\": a0\n});\nfunction NgxGalleryBulletsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function NgxGalleryBulletsComponent_div_0_Template_div_click_0_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleChange($event, i_r2));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, i_r2 === ctx_r2.active));\n  }\n}\nconst _c1 = [\"previewImage\"];\nfunction NgxGalleryPreviewComponent_ngx_gallery_arrows_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-arrows\", 17);\n    i0.ɵɵlistener(\"prevClick\", function NgxGalleryPreviewComponent_ngx_gallery_arrows_0_Template_ngx_gallery_arrows_prevClick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showPrev());\n    })(\"nextClick\", function NgxGalleryPreviewComponent_ngx_gallery_arrows_0_Template_ngx_gallery_arrows_nextClick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showNext());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"prevDisabled\", !ctx_r1.canShowPrev())(\"nextDisabled\", !ctx_r1.canShowNext())(\"arrowPrevIcon\", ctx_r1.arrowPrevIcon)(\"arrowNextIcon\", ctx_r1.arrowNextIcon);\n  }\n}\nfunction NgxGalleryPreviewComponent_ngx_gallery_action_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-action\", 18);\n    i0.ɵɵlistener(\"closeClick\", function NgxGalleryPreviewComponent_ngx_gallery_action_3_Template_ngx_gallery_action_closeClick_0_listener($event) {\n      const action_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(action_r4.onClick($event, ctx_r1.index));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"icon\", action_r4.icon)(\"disabled\", action_r4.disabled)(\"titleText\", action_r4.titleText);\n  }\n}\nfunction NgxGalleryPreviewComponent_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 19);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"href\", ctx_r1.src, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate1(\"ngx-gallery-icon-content \", ctx_r1.downloadIcon, \"\");\n  }\n}\nfunction NgxGalleryPreviewComponent_ngx_gallery_action_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-action\", 20);\n    i0.ɵɵlistener(\"closeClick\", function NgxGalleryPreviewComponent_ngx_gallery_action_5_Template_ngx_gallery_action_closeClick_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.zoomOut());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", ctx_r1.zoomOutIcon)(\"disabled\", !ctx_r1.canZoomOut());\n  }\n}\nfunction NgxGalleryPreviewComponent_ngx_gallery_action_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-action\", 20);\n    i0.ɵɵlistener(\"closeClick\", function NgxGalleryPreviewComponent_ngx_gallery_action_6_Template_ngx_gallery_action_closeClick_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.zoomIn());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", ctx_r1.zoomInIcon)(\"disabled\", !ctx_r1.canZoomIn());\n  }\n}\nfunction NgxGalleryPreviewComponent_ngx_gallery_action_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-action\", 8);\n    i0.ɵɵlistener(\"closeClick\", function NgxGalleryPreviewComponent_ngx_gallery_action_7_Template_ngx_gallery_action_closeClick_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.rotateLeft());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", ctx_r1.rotateLeftIcon);\n  }\n}\nfunction NgxGalleryPreviewComponent_ngx_gallery_action_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-action\", 8);\n    i0.ɵɵlistener(\"closeClick\", function NgxGalleryPreviewComponent_ngx_gallery_action_8_Template_ngx_gallery_action_closeClick_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.rotateRight());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", ctx_r1.rotateRightIcon);\n  }\n}\nfunction NgxGalleryPreviewComponent_ngx_gallery_action_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-action\", 8);\n    i0.ɵɵlistener(\"closeClick\", function NgxGalleryPreviewComponent_ngx_gallery_action_9_Template_ngx_gallery_action_closeClick_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.manageFullscreen());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", \"ngx-gallery-fullscreen \" + ctx_r1.fullscreenIcon);\n  }\n}\nfunction NgxGalleryPreviewComponent_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 21, 0);\n    i0.ɵɵlistener(\"click\", function NgxGalleryPreviewComponent_img_15_Template_img_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    })(\"mouseenter\", function NgxGalleryPreviewComponent_img_15_Template_img_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.imageMouseEnter());\n    })(\"mouseleave\", function NgxGalleryPreviewComponent_img_15_Template_img_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.imageMouseLeave());\n    })(\"mousedown\", function NgxGalleryPreviewComponent_img_15_Template_img_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.mouseDownHandler($event));\n    })(\"touchstart\", function NgxGalleryPreviewComponent_img_15_Template_img_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.mouseDownHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.getTransform())(\"left\", ctx_r1.positionLeft + \"px\")(\"top\", ctx_r1.positionTop + \"px\");\n    i0.ɵɵclassProp(\"ngx-gallery-active\", !ctx_r1.loading)(\"animation\", ctx_r1.animation)(\"ngx-gallery-grab\", ctx_r1.canDragOnZoom());\n    i0.ɵɵproperty(\"src\", ctx_r1.src, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NgxGalleryPreviewComponent_video_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 22, 0);\n    i0.ɵɵlistener(\"click\", function NgxGalleryPreviewComponent_video_16_Template_video_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    })(\"mouseenter\", function NgxGalleryPreviewComponent_video_16_Template_video_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.imageMouseEnter());\n    })(\"mouseleave\", function NgxGalleryPreviewComponent_video_16_Template_video_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.imageMouseLeave());\n    })(\"mousedown\", function NgxGalleryPreviewComponent_video_16_Template_video_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.mouseDownHandler($event));\n    })(\"touchstart\", function NgxGalleryPreviewComponent_video_16_Template_video_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.mouseDownHandler($event));\n    });\n    i0.ɵɵelement(2, \"source\", 23);\n    i0.ɵɵtext(3, \" Your browser does not support the video tag. \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.getTransform())(\"left\", ctx_r1.positionLeft + \"px\")(\"top\", ctx_r1.positionTop + \"px\");\n    i0.ɵɵclassProp(\"ngx-gallery-active\", !ctx_r1.loading)(\"animation\", ctx_r1.animation)(\"ngx-gallery-grab\", ctx_r1.canDragOnZoom());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.src, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NgxGalleryPreviewComponent_ngx_gallery_bullets_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-bullets\", 24);\n    i0.ɵɵlistener(\"bulletChange\", function NgxGalleryPreviewComponent_ngx_gallery_bullets_17_Template_ngx_gallery_bullets_bulletChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAtIndex($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"count\", ctx_r1.images.length)(\"active\", ctx_r1.index);\n  }\n}\nfunction NgxGalleryPreviewComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function NgxGalleryPreviewComponent_div_18_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.description, i0.ɵɵsanitizeHtml);\n  }\n}\nconst _c2 = a0 => ({\n  \"ngx-gallery-clickable\": a0\n});\nfunction NgxGalleryImageComponent_ng_container_1_div_1_ngx_gallery_action_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-action\", 8);\n    i0.ɵɵlistener(\"closeClick\", function NgxGalleryImageComponent_ng_container_1_div_1_ngx_gallery_action_2_Template_ngx_gallery_action_closeClick_0_listener($event) {\n      const action_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const image_r2 = i0.ɵɵnextContext(2).$implicit;\n      return i0.ɵɵresetView(action_r5.onClick($event, image_r2.index));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"icon\", action_r5.icon)(\"disabled\", action_r5.disabled)(\"titleText\", action_r5.titleText);\n  }\n}\nfunction NgxGalleryImageComponent_ng_container_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function NgxGalleryImageComponent_ng_container_1_div_1_div_3_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.descriptions[image_r2.index], i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NgxGalleryImageComponent_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function NgxGalleryImageComponent_ng_container_1_div_1_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const image_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleClick($event, image_r2.index));\n    })(\"@animation.start\", function NgxGalleryImageComponent_ng_container_1_div_1_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onStart($event));\n    })(\"@animation.done\", function NgxGalleryImageComponent_ng_container_1_div_1_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDone($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵtemplate(2, NgxGalleryImageComponent_ng_container_1_div_1_ngx_gallery_action_2_Template, 1, 3, \"ngx-gallery-action\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NgxGalleryImageComponent_ng_container_1_div_1_div_3_Template, 1, 1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background-image\", ctx_r2.getSafeUrl(image_r2.src));\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c2, ctx_r2.clickable))(\"@animation\", ctx_r2.action);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.actions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showDescription && ctx_r2.descriptions[image_r2.index]);\n  }\n}\nfunction NgxGalleryImageComponent_ng_container_1_div_2_ngx_gallery_action_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-action\", 8);\n    i0.ɵɵlistener(\"closeClick\", function NgxGalleryImageComponent_ng_container_1_div_2_ngx_gallery_action_5_Template_ngx_gallery_action_closeClick_0_listener($event) {\n      const action_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const image_r2 = i0.ɵɵnextContext(2).$implicit;\n      return i0.ɵɵresetView(action_r9.onClick($event, image_r2.index));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"icon\", action_r9.icon)(\"disabled\", action_r9.disabled)(\"titleText\", action_r9.titleText);\n  }\n}\nfunction NgxGalleryImageComponent_ng_container_1_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function NgxGalleryImageComponent_ng_container_1_div_2_div_6_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.descriptions[image_r2.index], i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NgxGalleryImageComponent_ng_container_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function NgxGalleryImageComponent_ng_container_1_div_2_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const image_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleClick($event, image_r2.index));\n    })(\"@animation.start\", function NgxGalleryImageComponent_ng_container_1_div_2_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onStart($event));\n    })(\"@animation.done\", function NgxGalleryImageComponent_ng_container_1_div_2_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDone($event));\n    });\n    i0.ɵɵelementStart(1, \"video\", 10);\n    i0.ɵɵelement(2, \"source\", 11);\n    i0.ɵɵtext(3, \" Your browser does not support the video tag. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵtemplate(5, NgxGalleryImageComponent_ng_container_1_div_2_ngx_gallery_action_5_Template, 1, 3, \"ngx-gallery-action\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, NgxGalleryImageComponent_ng_container_1_div_2_div_6_Template, 1, 1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background-image\", ctx_r2.getSafeUrl(image_r2.src));\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c2, ctx_r2.clickable))(\"@animation\", ctx_r2.action);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", image_r2.src, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.actions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showDescription && ctx_r2.descriptions[image_r2.index]);\n  }\n}\nfunction NgxGalleryImageComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgxGalleryImageComponent_ng_container_1_div_1_Template, 4, 8, \"div\", 3)(2, NgxGalleryImageComponent_ng_container_1_div_2_Template, 7, 9, \"div\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const image_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", image_r2.type === \"image\" && image_r2.index === ctx_r2._selectedIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", image_r2.type === \"video\" && image_r2.index === ctx_r2._selectedIndex);\n  }\n}\nfunction NgxGalleryImageComponent_ngx_gallery_bullets_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-bullets\", 12);\n    i0.ɵɵlistener(\"bulletChange\", function NgxGalleryImageComponent_ngx_gallery_bullets_2_Template_ngx_gallery_bullets_bulletChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.show($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"count\", ctx_r2.images.length)(\"active\", ctx_r2._selectedIndex);\n  }\n}\nfunction NgxGalleryImageComponent_ngx_gallery_arrows_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-arrows\", 13);\n    i0.ɵɵlistener(\"prevClick\", function NgxGalleryImageComponent_ngx_gallery_arrows_3_Template_ngx_gallery_arrows_prevClick_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showPrev());\n    })(\"nextClick\", function NgxGalleryImageComponent_ngx_gallery_arrows_3_Template_ngx_gallery_arrows_nextClick_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showNext());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ngx-gallery-image-size-\", ctx_r2.size, \"\");\n    i0.ɵɵproperty(\"prevDisabled\", !ctx_r2.canShowPrev())(\"nextDisabled\", !ctx_r2.canShowNext())(\"arrowPrevIcon\", ctx_r2.arrowPrevIcon)(\"arrowNextIcon\", ctx_r2.arrowNextIcon);\n  }\n}\nconst _c3 = (a0, a1) => ({\n  \"ngx-gallery-active\": a0,\n  \"ngx-gallery-clickable\": a1\n});\nfunction NgxGalleryThumbnailsComponent_a_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const image_r5 = ctx_r3.$implicit;\n    const i_r2 = ctx_r3.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background-image\", ctx_r2.getSafeUrl(image_r5));\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c3, i_r2 === ctx_r2.selectedIndex, ctx_r2.clickable));\n  }\n}\nfunction NgxGalleryThumbnailsComponent_a_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"video\", 11);\n    i0.ɵɵelement(2, \"source\", 12);\n    i0.ɵɵtext(3, \" Your browser does not support the video tag. \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const image_r5 = ctx_r3.$implicit;\n    const i_r2 = ctx_r3.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c3, i_r2 === ctx_r2.selectedIndex, ctx_r2.clickable));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", image_r5, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NgxGalleryThumbnailsComponent_a_2_ngx_gallery_action_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-action\", 13);\n    i0.ɵɵlistener(\"closeClick\", function NgxGalleryThumbnailsComponent_a_2_ngx_gallery_action_4_Template_ngx_gallery_action_closeClick_0_listener($event) {\n      const action_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const i_r2 = i0.ɵɵnextContext().index;\n      return i0.ɵɵresetView(action_r7.onClick($event, i_r2));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"icon\", action_r7.icon)(\"disabled\", action_r7.disabled)(\"titleText\", action_r7.titleText);\n  }\n}\nfunction NgxGalleryThumbnailsComponent_a_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"span\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"+\", ctx_r2.remainingCountValue, \"\");\n  }\n}\nfunction NgxGalleryThumbnailsComponent_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 3);\n    i0.ɵɵlistener(\"click\", function NgxGalleryThumbnailsComponent_a_2_Template_a_click_0_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleClick($event, i_r2));\n    });\n    i0.ɵɵtemplate(1, NgxGalleryThumbnailsComponent_a_2_div_1_Template, 1, 6, \"div\", 4)(2, NgxGalleryThumbnailsComponent_a_2_div_2_Template, 4, 5, \"div\", 5);\n    i0.ɵɵelementStart(3, \"div\", 6);\n    i0.ɵɵtemplate(4, NgxGalleryThumbnailsComponent_a_2_ngx_gallery_action_4_Template, 1, 3, \"ngx-gallery-action\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, NgxGalleryThumbnailsComponent_a_2_div_5_Template, 3, 1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r5 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"width\", ctx_r2.getThumbnailWidth())(\"height\", ctx_r2.getThumbnailHeight())(\"left\", ctx_r2.getThumbnailLeft(i_r2))(\"top\", ctx_r2.getThumbnailTop(i_r2));\n    i0.ɵɵproperty(\"href\", ctx_r2.hasLink(i_r2) ? ctx_r2.links[i_r2] : \"#\", i0.ɵɵsanitizeUrl)(\"target\", ctx_r2.linkTarget)(\"ngClass\", i0.ɵɵpureFunction2(16, _c3, i_r2 === ctx_r2.selectedIndex, ctx_r2.clickable));\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.labels[i_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getFileType(image_r5) === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getFileType(image_r5) === \"video\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.actions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.remainingCount && ctx_r2.remainingCountValue && i_r2 === ctx_r2.rows * ctx_r2.columns - 1);\n  }\n}\nfunction NgxGalleryThumbnailsComponent_ngx_gallery_arrows_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-arrows\", 16);\n    i0.ɵɵlistener(\"prevClick\", function NgxGalleryThumbnailsComponent_ngx_gallery_arrows_3_Template_ngx_gallery_arrows_prevClick_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.moveLeft());\n    })(\"nextClick\", function NgxGalleryThumbnailsComponent_ngx_gallery_arrows_3_Template_ngx_gallery_arrows_nextClick_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.moveRight());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"prevDisabled\", !ctx_r2.canMoveLeft())(\"nextDisabled\", !ctx_r2.canMoveRight())(\"arrowPrevIcon\", ctx_r2.arrowPrevIcon)(\"arrowNextIcon\", ctx_r2.arrowNextIcon);\n  }\n}\nconst _c4 = () => [];\nfunction NgxGalleryComponent_ngx_gallery_image_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-image\", 3);\n    i0.ɵɵlistener(\"imageClick\", function NgxGalleryComponent_ngx_gallery_image_1_Template_ngx_gallery_image_imageClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openPreview($event));\n    })(\"activeChange\", function NgxGalleryComponent_ngx_gallery_image_1_Template_ngx_gallery_image_activeChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectFromImage($event));\n    })(\"animating\", function NgxGalleryComponent_ngx_gallery_image_1_Template_ngx_gallery_image_animating_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setAnimating($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"height\", ctx_r1.getImageHeight());\n    i0.ɵɵproperty(\"images\", ctx_r1.mediumImages)(\"clickable\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.preview)(\"selectedIndex\", ctx_r1.selectedIndex)(\"arrows\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageArrows)(\"arrowsAutoHide\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageArrowsAutoHide)(\"arrowPrevIcon\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.arrowPrevIcon)(\"arrowNextIcon\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.arrowNextIcon)(\"swipe\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageSwipe)(\"animation\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageAnimation)(\"size\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageSize)(\"autoPlay\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageAutoPlay)(\"autoPlayInterval\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageAutoPlayInterval)(\"autoPlayPauseOnHover\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageAutoPlayPauseOnHover)(\"infinityMove\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageInfinityMove)(\"lazyLoading\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.lazyLoading)(\"actions\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageActions)(\"descriptions\", ctx_r1.descriptions)(\"showDescription\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageDescription)(\"bullets\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.imageBullets);\n  }\n}\nfunction NgxGalleryComponent_ngx_gallery_thumbnails_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-gallery-thumbnails\", 4);\n    i0.ɵɵlistener(\"activeChange\", function NgxGalleryComponent_ngx_gallery_thumbnails_2_Template_ngx_gallery_thumbnails_activeChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectFromThumbnails($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"margin-top\", ctx_r1.getThumbnailsMarginTop())(\"margin-bottom\", ctx_r1.getThumbnailsMarginBottom())(\"height\", ctx_r1.getThumbnailsHeight());\n    i0.ɵɵproperty(\"images\", ctx_r1.smallImages)(\"isAnimating\", ctx_r1.isAnimating)(\"links\", (ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailsAsLinks) ? ctx_r1.links : i0.ɵɵpureFunction0(28, _c4))(\"labels\", ctx_r1.labels)(\"linkTarget\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.linkTarget)(\"selectedIndex\", ctx_r1.selectedIndex)(\"columns\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailsColumns)(\"rows\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailsRows)(\"margin\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailMargin)(\"arrows\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailsArrows)(\"arrowsAutoHide\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailsArrowsAutoHide)(\"arrowPrevIcon\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.arrowPrevIcon)(\"arrowNextIcon\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.arrowNextIcon)(\"clickable\", (ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.image) || (ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.preview))(\"swipe\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailsSwipe)(\"size\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailSize)(\"moveSize\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailsMoveSize)(\"order\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailsOrder)(\"remainingCount\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailsRemainingCount)(\"lazyLoading\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.lazyLoading)(\"actions\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailActions)(\"ngClass\", ctx_r1.currentOptions == null ? null : ctx_r1.currentOptions.thumbnailClasses);\n  }\n}\nclass NgxGalleryService {\n  constructor(renderer) {\n    this.renderer = renderer;\n    this.swipeHandlers = new Map();\n  }\n  manageSwipe(status, element, id, nextHandler, prevHandler) {\n    const handlers = this.getSwipeHandlers(id);\n    // swipeleft and swiperight are available only if hammerjs is included\n    try {\n      if (status && !handlers) {\n        this.swipeHandlers.set(id, [this.renderer.listen(element.nativeElement, 'swipeleft', () => nextHandler()), this.renderer.listen(element.nativeElement, 'swiperight', () => prevHandler())]);\n      } else if (!status && handlers) {\n        handlers.map(handler => handler());\n        this.removeSwipeHandlers(id);\n      }\n    } catch (e) {}\n  }\n  validateUrl(url) {\n    if (url.replace) {\n      return url.replace(new RegExp(' ', 'g'), '%20').replace(new RegExp('\\'', 'g'), '%27');\n    } else {\n      return url;\n    }\n  }\n  getBackgroundUrl(image) {\n    return 'url(\\'' + this.validateUrl(image) + '\\')';\n  }\n  getFileType(fileSource) {\n    const fileExtension = fileSource.split('.').pop().toLowerCase();\n    if (fileExtension === 'avi' || fileExtension === 'flv' || fileExtension === 'wmv' || fileExtension === 'mov' || fileExtension === 'mp4') {\n      return 'video';\n    }\n    return 'image';\n  }\n  getSwipeHandlers(id) {\n    return this.swipeHandlers.get(id);\n  }\n  removeSwipeHandlers(id) {\n    this.swipeHandlers.delete(id);\n  }\n}\nNgxGalleryService.ɵfac = function NgxGalleryService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxGalleryService)(i0.ɵɵinject(i0.Renderer2));\n};\nNgxGalleryService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxGalleryService,\n  factory: NgxGalleryService.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGalleryService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }];\n  }, null);\n})();\nclass NgxGalleryArrowsComponent {\n  constructor() {\n    this.prevClick = new EventEmitter();\n    this.nextClick = new EventEmitter();\n  }\n  handlePrevClick() {\n    this.prevClick.emit();\n  }\n  handleNextClick() {\n    this.nextClick.emit();\n  }\n}\nNgxGalleryArrowsComponent.ɵfac = function NgxGalleryArrowsComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxGalleryArrowsComponent)();\n};\nNgxGalleryArrowsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxGalleryArrowsComponent,\n  selectors: [[\"ngx-gallery-arrows\"]],\n  inputs: {\n    prevDisabled: \"prevDisabled\",\n    nextDisabled: \"nextDisabled\",\n    arrowPrevIcon: \"arrowPrevIcon\",\n    arrowNextIcon: \"arrowNextIcon\"\n  },\n  outputs: {\n    prevClick: \"prevClick\",\n    nextClick: \"nextClick\"\n  },\n  decls: 6,\n  vars: 10,\n  consts: [[1, \"ngx-gallery-arrows-wrapper\", \"ngx-gallery-arrow-left\"], [\"aria-hidden\", \"true\", 1, \"ngx-gallery-icon\", \"ngx-gallery-arrow\", 3, \"click\"], [1, \"ngx-gallery-arrows-wrapper\", \"ngx-gallery-arrow-right\"]],\n  template: function NgxGalleryArrowsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵlistener(\"click\", function NgxGalleryArrowsComponent_Template_div_click_1_listener() {\n        return ctx.handlePrevClick();\n      });\n      i0.ɵɵelement(2, \"i\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 1);\n      i0.ɵɵlistener(\"click\", function NgxGalleryArrowsComponent_Template_div_click_4_listener() {\n        return ctx.handleNextClick();\n      });\n      i0.ɵɵelement(5, \"i\");\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵclassProp(\"ngx-gallery-disabled\", ctx.prevDisabled);\n      i0.ɵɵadvance();\n      i0.ɵɵclassMapInterpolate1(\"ngx-gallery-icon-content \", ctx.arrowPrevIcon, \"\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"ngx-gallery-disabled\", ctx.nextDisabled);\n      i0.ɵɵadvance();\n      i0.ɵɵclassMapInterpolate1(\"ngx-gallery-icon-content \", ctx.arrowNextIcon, \"\");\n    }\n  },\n  styles: [\".ngx-gallery-arrow-wrapper[_ngcontent-%COMP%]{position:absolute;height:100%;width:1px;display:table;table-layout:fixed}.ngx-gallery-preview-img-wrapper[_ngcontent-%COMP%]   .ngx-gallery-arrow-wrapper[_ngcontent-%COMP%]{z-index:10001}.ngx-gallery-arrow-left[_ngcontent-%COMP%]{left:0}.ngx-gallery-arrow-right[_ngcontent-%COMP%]{right:0}.ngx-gallery-arrow[_ngcontent-%COMP%]{top:50%;transform:translateY(-50%);cursor:pointer}.ngx-gallery-arrow.ngx-gallery-disabled[_ngcontent-%COMP%]{opacity:.6;cursor:default}.ngx-gallery-arrow-left[_ngcontent-%COMP%]   .ngx-gallery-arrow[_ngcontent-%COMP%]{left:10px}.ngx-gallery-arrow-right[_ngcontent-%COMP%]   .ngx-gallery-arrow[_ngcontent-%COMP%]{right:10px}\"],\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGalleryArrowsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-gallery-arrows',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"ngx-gallery-arrows-wrapper ngx-gallery-arrow-left\\\">\\n  <div class=\\\"ngx-gallery-icon ngx-gallery-arrow\\\" aria-hidden=\\\"true\\\" (click)=\\\"handlePrevClick()\\\" [class.ngx-gallery-disabled]=\\\"prevDisabled\\\">\\n    <i class=\\\"ngx-gallery-icon-content {{arrowPrevIcon}}\\\"></i>\\n  </div>\\n</div>\\n<div class=\\\"ngx-gallery-arrows-wrapper ngx-gallery-arrow-right\\\">\\n  <div class=\\\"ngx-gallery-icon ngx-gallery-arrow\\\" aria-hidden=\\\"true\\\" (click)=\\\"handleNextClick()\\\" [class.ngx-gallery-disabled]=\\\"nextDisabled\\\">\\n    <i class=\\\"ngx-gallery-icon-content {{arrowNextIcon}}\\\"></i>\\n  </div>\\n</div>\\n\",\n      styles: [\".ngx-gallery-arrow-wrapper{position:absolute;height:100%;width:1px;display:table;table-layout:fixed}.ngx-gallery-preview-img-wrapper .ngx-gallery-arrow-wrapper{z-index:10001}.ngx-gallery-arrow-left{left:0}.ngx-gallery-arrow-right{right:0}.ngx-gallery-arrow{top:50%;transform:translateY(-50%);cursor:pointer}.ngx-gallery-arrow.ngx-gallery-disabled{opacity:.6;cursor:default}.ngx-gallery-arrow-left .ngx-gallery-arrow{left:10px}.ngx-gallery-arrow-right .ngx-gallery-arrow{right:10px}\\n\"]\n    }]\n  }], function () {\n    return [];\n  }, {\n    prevDisabled: [{\n      type: Input\n    }],\n    nextDisabled: [{\n      type: Input\n    }],\n    arrowPrevIcon: [{\n      type: Input\n    }],\n    arrowNextIcon: [{\n      type: Input\n    }],\n    prevClick: [{\n      type: Output\n    }],\n    nextClick: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxGalleryActionComponent {\n  constructor() {\n    this.disabled = false;\n    this.titleText = '';\n    this.closeClick = new EventEmitter();\n  }\n  handleClick(event) {\n    if (!this.disabled) {\n      this.closeClick.emit(event);\n    }\n    event.stopPropagation();\n    event.preventDefault();\n  }\n}\nNgxGalleryActionComponent.ɵfac = function NgxGalleryActionComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxGalleryActionComponent)();\n};\nNgxGalleryActionComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxGalleryActionComponent,\n  selectors: [[\"ngx-gallery-action\"]],\n  inputs: {\n    icon: \"icon\",\n    disabled: \"disabled\",\n    titleText: \"titleText\"\n  },\n  outputs: {\n    closeClick: \"closeClick\"\n  },\n  decls: 2,\n  vars: 6,\n  consts: [[\"aria-hidden\", \"true\", 1, \"ngx-gallery-icon\", 3, \"click\", \"title\"]],\n  template: function NgxGalleryActionComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"click\", function NgxGalleryActionComponent_Template_div_click_0_listener($event) {\n        return ctx.handleClick($event);\n      });\n      i0.ɵɵelement(1, \"i\");\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"ngx-gallery-icon-disabled\", ctx.disabled);\n      i0.ɵɵpropertyInterpolate(\"title\", ctx.titleText);\n      i0.ɵɵadvance();\n      i0.ɵɵclassMapInterpolate1(\"ngx-gallery-icon-content \", ctx.icon, \"\");\n    }\n  },\n  styles: [\".ngx-gallery-icon[_ngcontent-%COMP%]{color:#fff;font-size:25px;position:absolute;z-index:2000;display:inline-block}.ngx-gallery-icon[_ngcontent-%COMP%]{position:relative;margin-right:10px;margin-top:10px;font-size:25px;cursor:pointer;text-decoration:none}.ngx-gallery-icon[_ngcontent-%COMP%]   .ngx-gallery-icon-content[_ngcontent-%COMP%]{display:block}\"],\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGalleryActionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-gallery-action',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"ngx-gallery-icon\\\" [class.ngx-gallery-icon-disabled]=\\\"disabled\\\"\\n     aria-hidden=\\\"true\\\"\\n     title=\\\"{{ titleText }}\\\"\\n     (click)=\\\"handleClick($event)\\\">\\n  <i class=\\\"ngx-gallery-icon-content {{ icon }}\\\"></i>\\n</div>\\n\",\n      styles: [\".ngx-gallery-icon{color:#fff;font-size:25px;position:absolute;z-index:2000;display:inline-block}.ngx-gallery-icon{position:relative;margin-right:10px;margin-top:10px;font-size:25px;cursor:pointer;text-decoration:none}.ngx-gallery-icon .ngx-gallery-icon-content{display:block}\\n\"]\n    }]\n  }], function () {\n    return [];\n  }, {\n    icon: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    titleText: [{\n      type: Input\n    }],\n    closeClick: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxGalleryBulletsComponent {\n  constructor() {\n    this.active = 0;\n    this.bulletChange = new EventEmitter();\n  }\n  getBullets() {\n    return Array(this.count);\n  }\n  handleChange(event, index) {\n    this.bulletChange.emit(index);\n  }\n}\nNgxGalleryBulletsComponent.ɵfac = function NgxGalleryBulletsComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxGalleryBulletsComponent)();\n};\nNgxGalleryBulletsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxGalleryBulletsComponent,\n  selectors: [[\"ngx-gallery-bullets\"]],\n  inputs: {\n    count: \"count\",\n    active: \"active\"\n  },\n  outputs: {\n    bulletChange: \"bulletChange\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[\"class\", \"ngx-gallery-bullet\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"ngx-gallery-bullet\", 3, \"click\", \"ngClass\"]],\n  template: function NgxGalleryBulletsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, NgxGalleryBulletsComponent_div_0_Template, 1, 3, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngForOf\", ctx.getBullets());\n    }\n  },\n  dependencies: [i6.NgForOf, i6.NgClass],\n  styles: [\"[_nghost-%COMP%]{position:absolute;z-index:2000;display:inline-flex;left:50%;transform:translate(-50%);bottom:0;padding:10px}.ngx-gallery-bullet[_ngcontent-%COMP%]{width:10px;height:10px;border-radius:50%;cursor:pointer;background:white}.ngx-gallery-bullet[_ngcontent-%COMP%]:not(:first-child){margin-left:5px}.ngx-gallery-bullet[_ngcontent-%COMP%]:hover, .ngx-gallery-bullet.ngx-gallery-active[_ngcontent-%COMP%]{background:black}\"],\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGalleryBulletsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-gallery-bullets',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"ngx-gallery-bullet\\\" *ngFor=\\\"let bullet of getBullets(); let i = index;\\\" (click)=\\\"handleChange($event, i)\\\"\\n     [ngClass]=\\\"{ 'ngx-gallery-active': i === active }\\\"></div>\\n\",\n      styles: [\":host{position:absolute;z-index:2000;display:inline-flex;left:50%;transform:translate(-50%);bottom:0;padding:10px}.ngx-gallery-bullet{width:10px;height:10px;border-radius:50%;cursor:pointer;background:white}.ngx-gallery-bullet:not(:first-child){margin-left:5px}.ngx-gallery-bullet:hover,.ngx-gallery-bullet.ngx-gallery-active{background:black}\\n\"]\n    }]\n  }], function () {\n    return [];\n  }, {\n    count: [{\n      type: Input\n    }],\n    active: [{\n      type: Input\n    }],\n    bulletChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxGalleryPreviewComponent {\n  constructor(sanitization, elementRef, helperService, renderer, changeDetectorRef) {\n    this.sanitization = sanitization;\n    this.elementRef = elementRef;\n    this.helperService = helperService;\n    this.renderer = renderer;\n    this.changeDetectorRef = changeDetectorRef;\n    this.showSpinner = false;\n    this.positionLeft = 0;\n    this.positionTop = 0;\n    this.zoomValue = 1;\n    this.loading = false;\n    this.rotateValue = 0;\n    this.index = 0;\n    this.previewOpen = new EventEmitter();\n    this.previewClose = new EventEmitter();\n    this.activeChange = new EventEmitter();\n    this.isOpen = false;\n    this.initialX = 0;\n    this.initialY = 0;\n    this.initialLeft = 0;\n    this.initialTop = 0;\n    this.isMove = false;\n  }\n  ngOnInit() {\n    if (this.arrows && this.arrowsAutoHide) {\n      this.arrows = false;\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['swipe']) {\n      this.helperService.manageSwipe(this.swipe, this.elementRef, 'preview', () => this.showNext(), () => this.showPrev());\n    }\n  }\n  ngOnDestroy() {\n    if (this.keyDownListener) {\n      this.keyDownListener();\n    }\n  }\n  onMouseEnter() {\n    if (this.arrowsAutoHide && !this.arrows) {\n      this.arrows = true;\n    }\n  }\n  onMouseLeave() {\n    if (this.arrowsAutoHide && this.arrows) {\n      this.arrows = false;\n    }\n  }\n  onKeyDown(e) {\n    if (this.isOpen) {\n      if (this.keyboardNavigation) {\n        if (this.isKeyboardPrev(e)) {\n          this.showPrev();\n        } else if (this.isKeyboardNext(e)) {\n          this.showNext();\n        }\n      }\n      if (this.closeOnEsc && this.isKeyboardEsc(e)) {\n        this.close();\n      }\n    }\n  }\n  open(index) {\n    this.previewOpen.emit();\n    this.index = index;\n    this.isOpen = true;\n    this.show(true);\n    if (this.forceFullscreen) {\n      this.manageFullscreen();\n    }\n    this.keyDownListener = this.renderer.listen('window', 'keydown', e => this.onKeyDown(e));\n  }\n  close() {\n    this.isOpen = false;\n    const video = this.previewImage.nativeElement;\n    if (video.currentTime > 0 && !video.paused && !video.ended && video.readyState > 2) {\n      video.pause();\n    }\n    this.closeFullscreen();\n    this.previewClose.emit();\n    this.stopAutoPlay();\n    if (this.keyDownListener) {\n      this.keyDownListener();\n    }\n  }\n  imageMouseEnter() {\n    if (this.autoPlay && this.autoPlayPauseOnHover) {\n      this.stopAutoPlay();\n    }\n  }\n  imageMouseLeave() {\n    if (this.autoPlay && this.autoPlayPauseOnHover) {\n      this.startAutoPlay();\n    }\n  }\n  startAutoPlay() {\n    if (this.autoPlay) {\n      this.stopAutoPlay();\n      this.timer = setTimeout(() => {\n        if (!this.showNext()) {\n          this.index = -1;\n          this.showNext();\n        }\n      }, this.autoPlayInterval);\n    }\n  }\n  stopAutoPlay() {\n    if (this.timer) {\n      clearTimeout(this.timer);\n    }\n  }\n  showAtIndex(index) {\n    this.index = index;\n    this.show();\n  }\n  showNext() {\n    if (this.canShowNext()) {\n      this.index++;\n      if (this.index === this.images.length) {\n        this.index = 0;\n      }\n      this.show();\n      return true;\n    } else {\n      return false;\n    }\n  }\n  showPrev() {\n    if (this.canShowPrev()) {\n      this.index--;\n      if (this.index < 0) {\n        this.index = this.images.length - 1;\n      }\n      this.show();\n    }\n  }\n  canShowNext() {\n    if (this.loading) {\n      return false;\n    } else if (this.images) {\n      return this.infinityMove || this.index < this.images.length - 1;\n    } else {\n      return false;\n    }\n  }\n  canShowPrev() {\n    if (this.loading) {\n      return false;\n    } else if (this.images) {\n      return this.infinityMove || this.index > 0;\n    } else {\n      return false;\n    }\n  }\n  manageFullscreen() {\n    if (this.fullscreen || this.forceFullscreen) {\n      const doc = document;\n      if (!doc.fullscreenElement && !doc.mozFullScreenElement && !doc.webkitFullscreenElement && !doc.msFullscreenElement) {\n        this.openFullscreen();\n      } else {\n        this.closeFullscreen();\n      }\n    }\n  }\n  getSafeUrl(image) {\n    return this.sanitization.bypassSecurityTrustUrl(image);\n  }\n  getFileType(fileSource) {\n    return this.helperService.getFileType(fileSource);\n  }\n  zoomIn() {\n    if (this.canZoomIn()) {\n      this.zoomValue += this.zoomStep;\n      if (this.zoomValue > this.zoomMax) {\n        this.zoomValue = this.zoomMax;\n      }\n    }\n  }\n  zoomOut() {\n    if (this.canZoomOut()) {\n      this.zoomValue -= this.zoomStep;\n      if (this.zoomValue < this.zoomMin) {\n        this.zoomValue = this.zoomMin;\n      }\n      if (this.zoomValue <= 1) {\n        this.resetPosition();\n      }\n    }\n  }\n  rotateLeft() {\n    this.rotateValue -= 90;\n  }\n  rotateRight() {\n    this.rotateValue += 90;\n  }\n  getTransform() {\n    return this.sanitization.bypassSecurityTrustStyle('scale(' + this.zoomValue + ') rotate(' + this.rotateValue + 'deg)');\n  }\n  canZoomIn() {\n    return this.zoomValue < this.zoomMax;\n  }\n  canZoomOut() {\n    return this.zoomValue > this.zoomMin;\n  }\n  canDragOnZoom() {\n    return this.zoom && this.zoomValue > 1;\n  }\n  mouseDownHandler(e) {\n    if (this.canDragOnZoom()) {\n      this.initialX = this.getClientX(e);\n      this.initialY = this.getClientY(e);\n      this.initialLeft = this.positionLeft;\n      this.initialTop = this.positionTop;\n      this.isMove = true;\n      e.preventDefault();\n    }\n  }\n  mouseUpHandler(e) {\n    this.isMove = false;\n  }\n  mouseMoveHandler(e) {\n    if (this.isMove) {\n      this.positionLeft = this.initialLeft + (this.getClientX(e) - this.initialX);\n      this.positionTop = this.initialTop + (this.getClientY(e) - this.initialY);\n    }\n  }\n  getClientX(e) {\n    return e.touches && e.touches.length ? e.touches[0].clientX : e.clientX;\n  }\n  getClientY(e) {\n    return e.touches && e.touches.length ? e.touches[0].clientY : e.clientY;\n  }\n  resetPosition() {\n    if (this.zoom) {\n      this.positionLeft = 0;\n      this.positionTop = 0;\n    }\n  }\n  isKeyboardNext(e) {\n    return e.keyCode === 39;\n  }\n  isKeyboardPrev(e) {\n    return e.keyCode === 37;\n  }\n  isKeyboardEsc(e) {\n    return e.keyCode === 27;\n  }\n  openFullscreen() {\n    const element = document.documentElement;\n    if (element.requestFullscreen) {\n      element.requestFullscreen();\n    } else if (element.msRequestFullscreen) {\n      element.msRequestFullscreen();\n    } else if (element.mozRequestFullScreen) {\n      element.mozRequestFullScreen();\n    } else if (element.webkitRequestFullscreen) {\n      element.webkitRequestFullscreen();\n    }\n  }\n  closeFullscreen() {\n    if (this.isFullscreen()) {\n      const doc = document;\n      if (doc.exitFullscreen) {\n        doc.exitFullscreen();\n      } else if (doc.msExitFullscreen) {\n        doc.msExitFullscreen();\n      } else if (doc.mozCancelFullScreen) {\n        doc.mozCancelFullScreen();\n      } else if (doc.webkitExitFullscreen) {\n        doc.webkitExitFullscreen();\n      }\n    }\n  }\n  isFullscreen() {\n    const doc = document;\n    return doc.fullscreenElement || doc.webkitFullscreenElement || doc.mozFullScreenElement || doc.msFullscreenElement;\n  }\n  show(first = false) {\n    this.loading = true;\n    this.stopAutoPlay();\n    this.activeChange.emit(this.index);\n    if (first || !this.animation) {\n      this._show();\n    } else {\n      setTimeout(() => this._show(), 600);\n    }\n  }\n  _show() {\n    this.zoomValue = 1;\n    this.rotateValue = 0;\n    this.resetPosition();\n    this.src = this.getSafeUrl(this.images[this.index]);\n    this.type = this.getFileType(this.images[this.index]);\n    this.srcIndex = this.index;\n    this.description = this.descriptions[this.index];\n    this.changeDetectorRef.markForCheck();\n    setTimeout(() => {\n      if (this.isLoaded(this.previewImage.nativeElement) || this.type === 'video') {\n        this.loading = false;\n        this.startAutoPlay();\n        this.changeDetectorRef.markForCheck();\n      } else if (this.type === 'video') {} else {\n        setTimeout(() => {\n          if (this.loading) {\n            this.showSpinner = true;\n            this.changeDetectorRef.markForCheck();\n          }\n        });\n        this.previewImage.nativeElement.onload = () => {\n          this.loading = false;\n          this.showSpinner = false;\n          this.previewImage.nativeElement.onload = null;\n          this.startAutoPlay();\n          this.changeDetectorRef.markForCheck();\n        };\n      }\n    });\n  }\n  isLoaded(img) {\n    if (!img.complete) {\n      return false;\n    }\n    return !(typeof img.naturalWidth !== 'undefined' && img.naturalWidth === 0);\n  }\n}\nNgxGalleryPreviewComponent.ɵfac = function NgxGalleryPreviewComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxGalleryPreviewComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NgxGalleryService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nNgxGalleryPreviewComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxGalleryPreviewComponent,\n  selectors: [[\"ngx-gallery-preview\"]],\n  viewQuery: function NgxGalleryPreviewComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.previewImage = _t.first);\n    }\n  },\n  hostBindings: function NgxGalleryPreviewComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mouseenter\", function NgxGalleryPreviewComponent_mouseenter_HostBindingHandler() {\n        return ctx.onMouseEnter();\n      })(\"mouseleave\", function NgxGalleryPreviewComponent_mouseleave_HostBindingHandler() {\n        return ctx.onMouseLeave();\n      });\n    }\n  },\n  inputs: {\n    images: \"images\",\n    descriptions: \"descriptions\",\n    showDescription: \"showDescription\",\n    arrows: \"arrows\",\n    arrowsAutoHide: \"arrowsAutoHide\",\n    swipe: \"swipe\",\n    fullscreen: \"fullscreen\",\n    forceFullscreen: \"forceFullscreen\",\n    closeOnClick: \"closeOnClick\",\n    closeOnEsc: \"closeOnEsc\",\n    keyboardNavigation: \"keyboardNavigation\",\n    arrowPrevIcon: \"arrowPrevIcon\",\n    arrowNextIcon: \"arrowNextIcon\",\n    closeIcon: \"closeIcon\",\n    fullscreenIcon: \"fullscreenIcon\",\n    spinnerIcon: \"spinnerIcon\",\n    autoPlay: \"autoPlay\",\n    autoPlayInterval: \"autoPlayInterval\",\n    autoPlayPauseOnHover: \"autoPlayPauseOnHover\",\n    infinityMove: \"infinityMove\",\n    zoom: \"zoom\",\n    zoomStep: \"zoomStep\",\n    zoomMax: \"zoomMax\",\n    zoomMin: \"zoomMin\",\n    zoomInIcon: \"zoomInIcon\",\n    zoomOutIcon: \"zoomOutIcon\",\n    animation: \"animation\",\n    actions: \"actions\",\n    rotate: \"rotate\",\n    rotateLeftIcon: \"rotateLeftIcon\",\n    rotateRightIcon: \"rotateRightIcon\",\n    download: \"download\",\n    downloadIcon: \"downloadIcon\",\n    bullets: \"bullets\"\n  },\n  outputs: {\n    previewOpen: \"previewOpen\",\n    previewClose: \"previewClose\",\n    activeChange: \"activeChange\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 19,\n  vars: 18,\n  consts: [[\"previewImage\", \"\"], [3, \"prevDisabled\", \"nextDisabled\", \"arrowPrevIcon\", \"arrowNextIcon\", \"prevClick\", \"nextClick\", 4, \"ngIf\"], [1, \"ngx-gallery-preview-top\"], [1, \"ngx-gallery-preview-icons\"], [3, \"icon\", \"disabled\", \"titleText\", \"closeClick\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"ngx-gallery-icon\", \"aria-hidden\", \"true\", \"download\", \"\", 3, \"href\", 4, \"ngIf\"], [3, \"icon\", \"disabled\", \"closeClick\", 4, \"ngIf\"], [3, \"icon\", \"closeClick\", 4, \"ngIf\"], [3, \"closeClick\", \"icon\"], [1, \"ngx-spinner-wrapper\", \"ngx-gallery-center\"], [\"aria-hidden\", \"true\"], [1, \"ngx-gallery-preview-wrapper\", 3, \"click\", \"mouseup\", \"mousemove\", \"touchend\", \"touchmove\"], [1, \"ngx-gallery-preview-img-wrapper\"], [\"class\", \"ngx-gallery-preview-img ngx-gallery-center\", 3, \"src\", \"ngx-gallery-active\", \"animation\", \"ngx-gallery-grab\", \"transform\", \"left\", \"top\", \"click\", \"mouseenter\", \"mouseleave\", \"mousedown\", \"touchstart\", 4, \"ngIf\"], [\"controls\", \"\", \"style\", \"width: 100%; height: 100%;\", \"class\", \"ngx-gallery-preview-img ngx-gallery-center\", 3, \"ngx-gallery-active\", \"animation\", \"ngx-gallery-grab\", \"transform\", \"left\", \"top\", \"click\", \"mouseenter\", \"mouseleave\", \"mousedown\", \"touchstart\", 4, \"ngIf\"], [3, \"count\", \"active\", \"bulletChange\", 4, \"ngIf\"], [\"class\", \"ngx-gallery-preview-text\", 3, \"innerHTML\", \"click\", 4, \"ngIf\"], [3, \"prevClick\", \"nextClick\", \"prevDisabled\", \"nextDisabled\", \"arrowPrevIcon\", \"arrowNextIcon\"], [3, \"closeClick\", \"icon\", \"disabled\", \"titleText\"], [\"aria-hidden\", \"true\", \"download\", \"\", 1, \"ngx-gallery-icon\", 3, \"href\"], [3, \"closeClick\", \"icon\", \"disabled\"], [1, \"ngx-gallery-preview-img\", \"ngx-gallery-center\", 3, \"click\", \"mouseenter\", \"mouseleave\", \"mousedown\", \"touchstart\", \"src\"], [\"controls\", \"\", 1, \"ngx-gallery-preview-img\", \"ngx-gallery-center\", 2, \"width\", \"100%\", \"height\", \"100%\", 3, \"click\", \"mouseenter\", \"mouseleave\", \"mousedown\", \"touchstart\"], [3, \"src\"], [3, \"bulletChange\", \"count\", \"active\"], [1, \"ngx-gallery-preview-text\", 3, \"click\", \"innerHTML\"]],\n  template: function NgxGalleryPreviewComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, NgxGalleryPreviewComponent_ngx_gallery_arrows_0_Template, 1, 4, \"ngx-gallery-arrows\", 1);\n      i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3);\n      i0.ɵɵtemplate(3, NgxGalleryPreviewComponent_ngx_gallery_action_3_Template, 1, 3, \"ngx-gallery-action\", 4)(4, NgxGalleryPreviewComponent_a_4_Template, 2, 4, \"a\", 5)(5, NgxGalleryPreviewComponent_ngx_gallery_action_5_Template, 1, 2, \"ngx-gallery-action\", 6)(6, NgxGalleryPreviewComponent_ngx_gallery_action_6_Template, 1, 2, \"ngx-gallery-action\", 6)(7, NgxGalleryPreviewComponent_ngx_gallery_action_7_Template, 1, 1, \"ngx-gallery-action\", 7)(8, NgxGalleryPreviewComponent_ngx_gallery_action_8_Template, 1, 1, \"ngx-gallery-action\", 7)(9, NgxGalleryPreviewComponent_ngx_gallery_action_9_Template, 1, 1, \"ngx-gallery-action\", 7);\n      i0.ɵɵelementStart(10, \"ngx-gallery-action\", 8);\n      i0.ɵɵlistener(\"closeClick\", function NgxGalleryPreviewComponent_Template_ngx_gallery_action_closeClick_10_listener() {\n        return ctx.close();\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"div\", 9);\n      i0.ɵɵelement(12, \"i\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"div\", 11);\n      i0.ɵɵlistener(\"click\", function NgxGalleryPreviewComponent_Template_div_click_13_listener() {\n        return ctx.closeOnClick && ctx.close();\n      })(\"mouseup\", function NgxGalleryPreviewComponent_Template_div_mouseup_13_listener($event) {\n        return ctx.mouseUpHandler($event);\n      })(\"mousemove\", function NgxGalleryPreviewComponent_Template_div_mousemove_13_listener($event) {\n        return ctx.mouseMoveHandler($event);\n      })(\"touchend\", function NgxGalleryPreviewComponent_Template_div_touchend_13_listener($event) {\n        return ctx.mouseUpHandler($event);\n      })(\"touchmove\", function NgxGalleryPreviewComponent_Template_div_touchmove_13_listener($event) {\n        return ctx.mouseMoveHandler($event);\n      });\n      i0.ɵɵelementStart(14, \"div\", 12);\n      i0.ɵɵtemplate(15, NgxGalleryPreviewComponent_img_15_Template, 2, 13, \"img\", 13)(16, NgxGalleryPreviewComponent_video_16_Template, 4, 13, \"video\", 14)(17, NgxGalleryPreviewComponent_ngx_gallery_bullets_17_Template, 1, 2, \"ngx-gallery-bullets\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(18, NgxGalleryPreviewComponent_div_18_Template, 1, 1, \"div\", 16);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.arrows);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngForOf\", ctx.actions);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.download && ctx.src);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.zoom);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.zoom);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.rotate);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.rotate);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.fullscreen);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"icon\", \"ngx-gallery-close \" + ctx.closeIcon);\n      i0.ɵɵadvance();\n      i0.ɵɵclassProp(\"ngx-gallery-active\", ctx.showSpinner);\n      i0.ɵɵadvance();\n      i0.ɵɵclassMapInterpolate1(\"ngx-gallery-icon ngx-gallery-spinner \", ctx.spinnerIcon, \"\");\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.src && ctx.type === \"image\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.src && ctx.type === \"video\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.bullets);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.showDescription && ctx.description);\n    }\n  },\n  dependencies: [NgxGalleryArrowsComponent, NgxGalleryActionComponent, NgxGalleryBulletsComponent, i6.NgIf, i6.NgForOf],\n  styles: [\".ngx-gallery-active[_nghost-%COMP%]{width:100%;height:100%;position:fixed;left:0;top:0;background:rgba(0,0,0,.7);z-index:10000;display:inline-block;font-size:50px!important}[_nghost-%COMP%]{display:none;font-size:50px!important}[_nghost-%COMP%]   .ngx-gallery-arrow[_ngcontent-%COMP%]{font-size:50px!important}ngx-gallery-bullets[_ngcontent-%COMP%]{height:5%;align-items:center;padding:0}.ngx-gallery-preview-img[_ngcontent-%COMP%]{opacity:0;max-width:90%;max-height:90%;-webkit-user-select:none;user-select:none;transition:transform .5s}.ngx-gallery-preview-img.animation[_ngcontent-%COMP%]{transition:opacity .5s linear,transform .5s}.ngx-gallery-preview-img.ngx-gallery-active[_ngcontent-%COMP%]{opacity:1}.ngx-gallery-preview-img.ngx-gallery-grab[_ngcontent-%COMP%]{cursor:grab}.ngx-gallery-icon.ngx-gallery-spinner[_ngcontent-%COMP%]{font-size:50px;left:0;display:inline-block}[_nghost-%COMP%]   .ngx-gallery-preview-top[_ngcontent-%COMP%]{position:absolute;width:100%;-webkit-user-select:none;user-select:none;font-size:25px}.ngx-gallery-preview-icons[_ngcontent-%COMP%]{float:right}.ngx-gallery-preview-icons[_ngcontent-%COMP%]   .ngx-gallery-icon[_ngcontent-%COMP%]{position:relative;margin-right:10px;margin-top:10px;font-size:25px;cursor:pointer;text-decoration:none}.ngx-gallery-preview-icons[_ngcontent-%COMP%]   .ngx-gallery-icon.ngx-gallery-icon-disabled[_ngcontent-%COMP%]{cursor:default;opacity:.4}.ngx-spinner-wrapper[_ngcontent-%COMP%]{width:50px;height:50px;display:none}.ngx-spinner-wrapper.ngx-gallery-active[_ngcontent-%COMP%]{display:inline-block}.ngx-gallery-center[_ngcontent-%COMP%]{position:absolute;left:0;right:0;bottom:0;margin:auto;top:0}.ngx-gallery-preview-text[_ngcontent-%COMP%]{width:100%;background:rgba(0,0,0,.7);padding:10px;text-align:center;color:#fff;font-size:16px;flex:0 1 auto;z-index:10}.ngx-gallery-preview-wrapper[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-flow:column}.ngx-gallery-preview-img-wrapper[_ngcontent-%COMP%]{flex:1 1 auto;position:relative}\"],\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGalleryPreviewComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-gallery-preview',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ngx-gallery-arrows *ngIf=\\\"arrows\\\" (prevClick)=\\\"showPrev()\\\" (nextClick)=\\\"showNext()\\\" [prevDisabled]=\\\"!canShowPrev()\\\"\\n                    [nextDisabled]=\\\"!canShowNext()\\\" [arrowPrevIcon]=\\\"arrowPrevIcon\\\"\\n                    [arrowNextIcon]=\\\"arrowNextIcon\\\"></ngx-gallery-arrows>\\n<div class=\\\"ngx-gallery-preview-top\\\">\\n  <div class=\\\"ngx-gallery-preview-icons\\\">\\n    <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                        [titleText]=\\\"action.titleText\\\" (closeClick)=\\\"action.onClick($event, index)\\\"></ngx-gallery-action>\\n    <a *ngIf=\\\"download && src\\\" [href]=\\\"src\\\" class=\\\"ngx-gallery-icon\\\" aria-hidden=\\\"true\\\" download>\\n      <i class=\\\"ngx-gallery-icon-content {{ downloadIcon }}\\\"></i>\\n    </a>\\n    <ngx-gallery-action *ngIf=\\\"zoom\\\" [icon]=\\\"zoomOutIcon\\\" [disabled]=\\\"!canZoomOut()\\\"\\n                        (closeClick)=\\\"zoomOut()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"zoom\\\" [icon]=\\\"zoomInIcon\\\" [disabled]=\\\"!canZoomIn()\\\"\\n                        (closeClick)=\\\"zoomIn()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"rotate\\\" [icon]=\\\"rotateLeftIcon\\\" (closeClick)=\\\"rotateLeft()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"rotate\\\" [icon]=\\\"rotateRightIcon\\\" (closeClick)=\\\"rotateRight()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"fullscreen\\\" [icon]=\\\"'ngx-gallery-fullscreen ' + fullscreenIcon\\\"\\n                        (closeClick)=\\\"manageFullscreen()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action [icon]=\\\"'ngx-gallery-close ' + closeIcon\\\" (closeClick)=\\\"close()\\\"></ngx-gallery-action>\\n  </div>\\n</div>\\n<div class=\\\"ngx-spinner-wrapper ngx-gallery-center\\\" [class.ngx-gallery-active]=\\\"showSpinner\\\">\\n  <i class=\\\"ngx-gallery-icon ngx-gallery-spinner {{spinnerIcon}}\\\" aria-hidden=\\\"true\\\"></i>\\n</div>\\n<div class=\\\"ngx-gallery-preview-wrapper\\\" (click)=\\\"closeOnClick && close()\\\" (mouseup)=\\\"mouseUpHandler($event)\\\"\\n     (mousemove)=\\\"mouseMoveHandler($event)\\\" (touchend)=\\\"mouseUpHandler($event)\\\" (touchmove)=\\\"mouseMoveHandler($event)\\\">\\n  <div class=\\\"ngx-gallery-preview-img-wrapper\\\">\\n    <img *ngIf=\\\"src && type === 'image'\\\" #previewImage class=\\\"ngx-gallery-preview-img ngx-gallery-center\\\" [src]=\\\"src\\\"\\n         (click)=\\\"$event.stopPropagation()\\\" (mouseenter)=\\\"imageMouseEnter()\\\" (mouseleave)=\\\"imageMouseLeave()\\\"\\n         (mousedown)=\\\"mouseDownHandler($event)\\\" (touchstart)=\\\"mouseDownHandler($event)\\\"\\n         [class.ngx-gallery-active]=\\\"!loading\\\" [class.animation]=\\\"animation\\\" [class.ngx-gallery-grab]=\\\"canDragOnZoom()\\\"\\n         [style.transform]=\\\"getTransform()\\\" [style.left]=\\\"positionLeft + 'px'\\\" [style.top]=\\\"positionTop + 'px'\\\"/>\\n    <video *ngIf=\\\"src && type === 'video'\\\"  #previewImage controls style=\\\"width: 100%; height: 100%;\\\"\\n    class=\\\"ngx-gallery-preview-img ngx-gallery-center\\\"\\n    (click)=\\\"$event.stopPropagation()\\\" (mouseenter)=\\\"imageMouseEnter()\\\" (mouseleave)=\\\"imageMouseLeave()\\\" (mousedown)=\\\"mouseDownHandler($event)\\\" (touchstart)=\\\"mouseDownHandler($event)\\\"\\n    [class.ngx-gallery-active]=\\\"!loading\\\" [class.animation]=\\\"animation\\\" [class.ngx-gallery-grab]=\\\"canDragOnZoom()\\\" [style.transform]=\\\"getTransform()\\\" [style.left]=\\\"positionLeft + 'px'\\\" [style.top]=\\\"positionTop + 'px'\\\">\\n      <source [src]=\\\"src\\\">\\n      Your browser does not support the video tag.\\n    </video>\\n    <ngx-gallery-bullets *ngIf=\\\"bullets\\\" [count]=\\\"images.length\\\" [active]=\\\"index\\\"\\n                         (bulletChange)=\\\"showAtIndex($event)\\\"></ngx-gallery-bullets>\\n  </div>\\n  <div class=\\\"ngx-gallery-preview-text\\\" *ngIf=\\\"showDescription && description\\\" [innerHTML]=\\\"description\\\"\\n       (click)=\\\"$event.stopPropagation()\\\"></div>\\n</div>\\n\",\n      styles: [\":host.ngx-gallery-active{width:100%;height:100%;position:fixed;left:0;top:0;background:rgba(0,0,0,.7);z-index:10000;display:inline-block;font-size:50px!important}:host{display:none;font-size:50px!important}:host .ngx-gallery-arrow{font-size:50px!important}ngx-gallery-bullets{height:5%;align-items:center;padding:0}.ngx-gallery-preview-img{opacity:0;max-width:90%;max-height:90%;-webkit-user-select:none;user-select:none;transition:transform .5s}.ngx-gallery-preview-img.animation{transition:opacity .5s linear,transform .5s}.ngx-gallery-preview-img.ngx-gallery-active{opacity:1}.ngx-gallery-preview-img.ngx-gallery-grab{cursor:grab}.ngx-gallery-icon.ngx-gallery-spinner{font-size:50px;left:0;display:inline-block}:host .ngx-gallery-preview-top{position:absolute;width:100%;-webkit-user-select:none;user-select:none;font-size:25px}.ngx-gallery-preview-icons{float:right}.ngx-gallery-preview-icons .ngx-gallery-icon{position:relative;margin-right:10px;margin-top:10px;font-size:25px;cursor:pointer;text-decoration:none}.ngx-gallery-preview-icons .ngx-gallery-icon.ngx-gallery-icon-disabled{cursor:default;opacity:.4}.ngx-spinner-wrapper{width:50px;height:50px;display:none}.ngx-spinner-wrapper.ngx-gallery-active{display:inline-block}.ngx-gallery-center{position:absolute;left:0;right:0;bottom:0;margin:auto;top:0}.ngx-gallery-preview-text{width:100%;background:rgba(0,0,0,.7);padding:10px;text-align:center;color:#fff;font-size:16px;flex:0 1 auto;z-index:10}.ngx-gallery-preview-wrapper{width:100%;height:100%;display:flex;flex-flow:column}.ngx-gallery-preview-img-wrapper{flex:1 1 auto;position:relative}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i1.DomSanitizer\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: NgxGalleryService\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    images: [{\n      type: Input\n    }],\n    descriptions: [{\n      type: Input\n    }],\n    showDescription: [{\n      type: Input\n    }],\n    arrows: [{\n      type: Input\n    }],\n    arrowsAutoHide: [{\n      type: Input\n    }],\n    swipe: [{\n      type: Input\n    }],\n    fullscreen: [{\n      type: Input\n    }],\n    forceFullscreen: [{\n      type: Input\n    }],\n    closeOnClick: [{\n      type: Input\n    }],\n    closeOnEsc: [{\n      type: Input\n    }],\n    keyboardNavigation: [{\n      type: Input\n    }],\n    arrowPrevIcon: [{\n      type: Input\n    }],\n    arrowNextIcon: [{\n      type: Input\n    }],\n    closeIcon: [{\n      type: Input\n    }],\n    fullscreenIcon: [{\n      type: Input\n    }],\n    spinnerIcon: [{\n      type: Input\n    }],\n    autoPlay: [{\n      type: Input\n    }],\n    autoPlayInterval: [{\n      type: Input\n    }],\n    autoPlayPauseOnHover: [{\n      type: Input\n    }],\n    infinityMove: [{\n      type: Input\n    }],\n    zoom: [{\n      type: Input\n    }],\n    zoomStep: [{\n      type: Input\n    }],\n    zoomMax: [{\n      type: Input\n    }],\n    zoomMin: [{\n      type: Input\n    }],\n    zoomInIcon: [{\n      type: Input\n    }],\n    zoomOutIcon: [{\n      type: Input\n    }],\n    animation: [{\n      type: Input\n    }],\n    actions: [{\n      type: Input\n    }],\n    rotate: [{\n      type: Input\n    }],\n    rotateLeftIcon: [{\n      type: Input\n    }],\n    rotateRightIcon: [{\n      type: Input\n    }],\n    download: [{\n      type: Input\n    }],\n    downloadIcon: [{\n      type: Input\n    }],\n    bullets: [{\n      type: Input\n    }],\n    previewOpen: [{\n      type: Output\n    }],\n    previewClose: [{\n      type: Output\n    }],\n    activeChange: [{\n      type: Output\n    }],\n    previewImage: [{\n      type: ViewChild,\n      args: ['previewImage']\n    }],\n    onMouseEnter: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    onMouseLeave: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\nclass NgxGalleryAnimation {}\nNgxGalleryAnimation.Fade = 'fade';\nNgxGalleryAnimation.Slide = 'slide';\nNgxGalleryAnimation.Rotate = 'rotate';\nNgxGalleryAnimation.Zoom = 'zoom';\nclass NgxGalleryImageComponent {\n  constructor(sanitization, changeDetectorRef, elementRef, helperService) {\n    this.sanitization = sanitization;\n    this.changeDetectorRef = changeDetectorRef;\n    this.elementRef = elementRef;\n    this.helperService = helperService;\n    this.imageClick = new EventEmitter();\n    this.activeChange = new EventEmitter();\n    this.animating = new EventEmitter();\n    this.canChangeImage = true;\n    this.isAnimating = false;\n    this.changeDetectorRef = changeDetectorRef;\n    this.action = 'none';\n  }\n  set selectedIndex(index) {\n    if (index > this._selectedIndex) {\n      let action;\n      if (this.animation === NgxGalleryAnimation.Slide) {\n        action = 'slideRight';\n      } else if (this.animation === NgxGalleryAnimation.Fade) {\n        action = 'fade';\n      } else if (this.animation === NgxGalleryAnimation.Rotate) {\n        action = 'rotateRight';\n      } else if (this.animation === NgxGalleryAnimation.Zoom) {\n        action = 'zoom';\n      }\n      this.setAction(action);\n    } else if (index < this._selectedIndex) {\n      let action;\n      if (this.animation === NgxGalleryAnimation.Slide) {\n        action = 'slideLeft';\n      } else if (this.animation === NgxGalleryAnimation.Fade) {\n        action = 'fade';\n      } else if (this.animation === NgxGalleryAnimation.Rotate) {\n        action = 'rotateLeft';\n      } else if (this.animation === NgxGalleryAnimation.Zoom) {\n        action = 'zoom';\n      }\n      this.setAction(action);\n    }\n    this._selectedIndex = index;\n  }\n  // @HostBinding('style.display') public display = 'inline-block';\n  // @HostBinding('style.background-color') public color = 'lime';\n  ngOnInit() {\n    if (this.arrows && this.arrowsAutoHide) {\n      this.arrows = false;\n    }\n    if (this.autoPlay) {\n      this.startAutoPlay();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['swipe']) {\n      this.helperService.manageSwipe(this.swipe, this.elementRef, 'image', () => this.showNext(), () => this.showPrev());\n    }\n  }\n  onMouseEnter() {\n    if (this.arrowsAutoHide && !this.arrows) {\n      this.arrows = true;\n    }\n    if (this.autoPlay && this.autoPlayPauseOnHover) {\n      this.stopAutoPlay();\n    }\n  }\n  onMouseLeave() {\n    if (this.arrowsAutoHide && this.arrows) {\n      this.arrows = false;\n    }\n    if (this.autoPlay && this.autoPlayPauseOnHover) {\n      this.startAutoPlay();\n    }\n  }\n  reset(index) {\n    this._selectedIndex = index;\n    this.action = 'none';\n  }\n  getImages() {\n    if (!this.images) {\n      return [];\n    }\n    if (this.lazyLoading) {\n      const indexes = [this._selectedIndex];\n      const prevIndex = this._selectedIndex - 1;\n      if (prevIndex === -1 && this.infinityMove) {\n        indexes.push(this.images.length - 1);\n      } else if (prevIndex >= 0) {\n        indexes.push(prevIndex);\n      }\n      const nextIndex = this._selectedIndex + 1;\n      if (nextIndex === this.images.length && this.infinityMove) {\n        indexes.push(0);\n      } else if (nextIndex < this.images.length) {\n        indexes.push(nextIndex);\n      }\n      return this.images.filter((img, i) => indexes.indexOf(i) !== -1);\n    } else {\n      return this.images;\n    }\n  }\n  startAutoPlay() {\n    this.stopAutoPlay();\n    this.timer = setInterval(() => {\n      if (!this.showNext()) {\n        this._selectedIndex = -1;\n        this.showNext();\n      }\n    }, this.autoPlayInterval);\n  }\n  stopAutoPlay() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  }\n  handleClick(event, index) {\n    if (this.clickable) {\n      this.imageClick.emit(index);\n      event.stopPropagation();\n      event.preventDefault();\n    }\n  }\n  show(index) {\n    if (this.isAnimating) {\n      return;\n    }\n    if (index > this._selectedIndex) {\n      let action;\n      if (this.animation === NgxGalleryAnimation.Slide) {\n        action = 'slideRight';\n      } else if (this.animation === NgxGalleryAnimation.Fade) {\n        action = 'fade';\n      } else if (this.animation === NgxGalleryAnimation.Rotate) {\n        action = 'rotateRight';\n      } else if (this.animation === NgxGalleryAnimation.Zoom) {\n        action = 'zoom';\n      }\n      this.setAction(action);\n    } else {\n      let action;\n      if (this.animation === NgxGalleryAnimation.Slide) {\n        action = 'slideLeft';\n      } else if (this.animation === NgxGalleryAnimation.Fade) {\n        action = 'fade';\n      } else if (this.animation === NgxGalleryAnimation.Rotate) {\n        action = 'rotateLeft';\n      } else if (this.animation === NgxGalleryAnimation.Zoom) {\n        action = 'zoom';\n      }\n      this.setAction(action);\n    }\n    this._selectedIndex = index;\n    this.activeChange.emit(this._selectedIndex);\n    this.setChangeTimeout();\n  }\n  setAction(action) {\n    this.action = action;\n    this.changeDetectorRef.detectChanges();\n  }\n  showNext() {\n    if (this.isAnimating) {\n      return false;\n    }\n    if (this.canShowNext() && this.canChangeImage) {\n      let action;\n      if (this.animation === NgxGalleryAnimation.Slide) {\n        action = 'slideRight';\n      } else if (this.animation === NgxGalleryAnimation.Fade) {\n        action = 'fade';\n      } else if (this.animation === NgxGalleryAnimation.Rotate) {\n        action = 'rotateRight';\n      } else if (this.animation === NgxGalleryAnimation.Zoom) {\n        action = 'zoom';\n      }\n      this.setAction(action);\n      this._selectedIndex++;\n      if (this._selectedIndex === this.images.length) {\n        this._selectedIndex = 0;\n      }\n      this.activeChange.emit(this._selectedIndex);\n      this.setChangeTimeout();\n      return true;\n    } else {\n      return false;\n    }\n  }\n  showPrev() {\n    if (this.isAnimating) {\n      return;\n    }\n    if (this.canShowPrev() && this.canChangeImage) {\n      let action;\n      if (this.animation === NgxGalleryAnimation.Slide) {\n        action = 'slideLeft';\n      } else if (this.animation === NgxGalleryAnimation.Fade) {\n        action = 'fade';\n      } else if (this.animation === NgxGalleryAnimation.Rotate) {\n        action = 'rotateLeft';\n      } else if (this.animation === NgxGalleryAnimation.Zoom) {\n        action = 'zoom';\n      }\n      this.setAction(action);\n      this._selectedIndex--;\n      if (this._selectedIndex < 0) {\n        this._selectedIndex = this.images.length - 1;\n      }\n      this.activeChange.emit(this._selectedIndex);\n      this.setChangeTimeout();\n    }\n  }\n  setChangeTimeout() {\n    this.canChangeImage = false;\n    let timeout = 1000;\n    if (this.animation === NgxGalleryAnimation.Slide || this.animation === NgxGalleryAnimation.Fade) {\n      timeout = 500;\n    }\n    setTimeout(() => {\n      this.canChangeImage = true;\n    }, timeout);\n  }\n  canShowNext() {\n    if (this.images) {\n      return this.infinityMove || this._selectedIndex < this.images.length - 1;\n    } else {\n      return false;\n    }\n  }\n  canShowPrev() {\n    if (this.images) {\n      return this.infinityMove || this._selectedIndex > 0;\n    } else {\n      return false;\n    }\n  }\n  getSafeUrl(image) {\n    return this.sanitization.bypassSecurityTrustStyle(this.helperService.getBackgroundUrl(image.toString()));\n  }\n  getFileType(fileSource) {\n    return this.helperService.getFileType(fileSource);\n  }\n  onStart(event) {\n    this.isAnimating = true;\n    this.animating.emit(true);\n  }\n  onDone(event) {\n    this.isAnimating = false;\n    this.animating.emit(false);\n  }\n}\nNgxGalleryImageComponent.ɵfac = function NgxGalleryImageComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxGalleryImageComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NgxGalleryService));\n};\nNgxGalleryImageComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxGalleryImageComponent,\n  selectors: [[\"ngx-gallery-image\"]],\n  hostBindings: function NgxGalleryImageComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mouseenter\", function NgxGalleryImageComponent_mouseenter_HostBindingHandler() {\n        return ctx.onMouseEnter();\n      })(\"mouseleave\", function NgxGalleryImageComponent_mouseleave_HostBindingHandler() {\n        return ctx.onMouseLeave();\n      });\n    }\n  },\n  inputs: {\n    images: \"images\",\n    clickable: \"clickable\",\n    selectedIndex: \"selectedIndex\",\n    arrows: \"arrows\",\n    arrowsAutoHide: \"arrowsAutoHide\",\n    swipe: \"swipe\",\n    animation: \"animation\",\n    size: \"size\",\n    arrowPrevIcon: \"arrowPrevIcon\",\n    arrowNextIcon: \"arrowNextIcon\",\n    autoPlay: \"autoPlay\",\n    autoPlayInterval: \"autoPlayInterval\",\n    autoPlayPauseOnHover: \"autoPlayPauseOnHover\",\n    infinityMove: \"infinityMove\",\n    lazyLoading: \"lazyLoading\",\n    actions: \"actions\",\n    descriptions: \"descriptions\",\n    showDescription: \"showDescription\",\n    bullets: \"bullets\"\n  },\n  outputs: {\n    imageClick: \"imageClick\",\n    activeChange: \"activeChange\",\n    animating: \"animating\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 4,\n  vars: 7,\n  consts: [[4, \"ngFor\", \"ngForOf\"], [3, \"count\", \"active\", \"bulletChange\", 4, \"ngIf\"], [3, \"class\", \"prevDisabled\", \"nextDisabled\", \"arrowPrevIcon\", \"arrowNextIcon\", \"prevClick\", \"nextClick\", 4, \"ngIf\"], [\"class\", \"ngx-gallery-image\", 3, \"ngClass\", \"background-image\", \"click\", 4, \"ngIf\"], [1, \"ngx-gallery-image\", 3, \"click\", \"ngClass\"], [1, \"ngx-gallery-icons-wrapper\"], [3, \"icon\", \"disabled\", \"titleText\", \"closeClick\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"ngx-gallery-image-text\", 3, \"innerHTML\", \"click\", 4, \"ngIf\"], [3, \"closeClick\", \"icon\", \"disabled\", \"titleText\"], [1, \"ngx-gallery-image-text\", 3, \"click\", \"innerHTML\"], [\"controls\", \"\", 2, \"width\", \"100%\", \"height\", \"100%\", \"background\", \"#000\"], [3, \"src\"], [3, \"bulletChange\", \"count\", \"active\"], [3, \"prevClick\", \"nextClick\", \"prevDisabled\", \"nextDisabled\", \"arrowPrevIcon\", \"arrowNextIcon\"]],\n  template: function NgxGalleryImageComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\");\n      i0.ɵɵtemplate(1, NgxGalleryImageComponent_ng_container_1_Template, 3, 2, \"ng-container\", 0)(2, NgxGalleryImageComponent_ngx_gallery_bullets_2_Template, 1, 2, \"ngx-gallery-bullets\", 1)(3, NgxGalleryImageComponent_ngx_gallery_arrows_3_Template, 1, 7, \"ngx-gallery-arrows\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassMapInterpolate2(\"ngx-gallery-image-wrapper ngx-gallery-animation-\", ctx.animation, \" ngx-gallery-image-size-\", ctx.size, \"\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngForOf\", ctx.getImages());\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.bullets);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.arrows);\n    }\n  },\n  dependencies: [NgxGalleryActionComponent, NgxGalleryBulletsComponent, NgxGalleryArrowsComponent, i6.NgForOf, i6.NgIf, i6.NgClass],\n  styles: [\"[_nghost-%COMP%]{width:100%;display:inline-block;position:relative;font-size:25px}.ngx-gallery-image-wrapper[_ngcontent-%COMP%]{width:100%;height:100%;position:absolute;left:0;top:0;overflow:hidden}.ngx-gallery-image[_ngcontent-%COMP%]{background-position:center;background-repeat:no-repeat;height:100%;width:100%;position:absolute;top:0}.ngx-gallery-image-size-cover[_ngcontent-%COMP%]   .ngx-gallery-image[_ngcontent-%COMP%]{background-size:cover}.ngx-gallery-image-size-contain[_ngcontent-%COMP%]   .ngx-gallery-image[_ngcontent-%COMP%]{background-size:contain}.ngx-gallery-animation-fade[_ngcontent-%COMP%]   .ngx-gallery-image[_ngcontent-%COMP%]{left:0;opacity:1;transition:.5s ease-in-out}.ngx-gallery-animation-fade[_ngcontent-%COMP%]   .ngx-gallery-image.ngx-gallery-active[_ngcontent-%COMP%]{opacity:1}.ngx-gallery-animation-rotate[_ngcontent-%COMP%]   .ngx-gallery-image[_ngcontent-%COMP%]{transition:1s ease;transform:scale(1) rotate(0);left:0;opacity:1}.ngx-gallery-animation-zoom[_ngcontent-%COMP%]   .ngx-gallery-image[_ngcontent-%COMP%]{transition:1s ease;transform:scale(1);left:0;opacity:1}.ngx-gallery-image-text[_ngcontent-%COMP%]{width:100%;background:rgba(0,0,0,.7);padding:10px;text-align:center;color:#fff;font-size:16px;position:absolute;bottom:0;z-index:10}\"],\n  data: {\n    animation: [trigger('animation', [\n    // ...\n    state('slideRight', style({})), state('slideLeft', style({})), state('fade', style({})), state('rotateLeft', style({})), state('rotateRight', style({})), state('zoom', style({})), transition('slideRight => void', [animate('500ms ease-in-out', style({\n      transform: 'translateX(-100%)'\n    }))]), transition('void => slideRight', [style({\n      transform: 'translateX(100%)'\n    }), animate('500ms ease-in-out', style({\n      transform: 'translateX(0)'\n    }))]), transition('slideLeft => void', [animate('500ms ease-in-out', style({\n      transform: 'translateX(100%)'\n    }))]), transition('void => slideLeft', [style({\n      transform: 'translateX(-100%)'\n    }), animate('500ms ease-in-out', style({\n      transform: 'translateX(0)'\n    }))]), transition('fade => void', [animate('500ms ease-in-out', style({\n      opacity: '0'\n    }))]), transition('void => fade', [style({\n      opacity: '0'\n    }), animate('500ms ease-in-out', style({\n      opacity: '1'\n    }))]), transition('rotateLeft => void', [animate('500ms ease-in-out', style({\n      transform: 'scale(1, 1) rotate(-90deg)',\n      opacity: '0'\n    }))]), transition('void => rotateLeft', [style({\n      transform: 'scale(1, 1) rotate(-90deg)',\n      opacity: '0'\n    }), animate('500ms ease-in-out', style({\n      transform: 'scale(1, 1) rotate(0deg)',\n      opacity: '1'\n    }))]), transition('rotateRight => void', [animate('500ms ease-in-out', style({\n      transform: 'scale(1, 1) rotate(90deg)',\n      opacity: '0'\n    }))]), transition('void => rotateRight', [style({\n      transform: 'scale(1, 1) rotate(90deg)',\n      opacity: '0'\n    }), animate('500ms ease-in-out', style({\n      transform: 'scale(1, 1) rotate(0deg)',\n      opacity: '1'\n    }))]), transition('zoom => void', [animate('500ms ease-in-out', style({\n      transform: 'scale(2.5,2.5)',\n      opacity: '0'\n    }))]), transition('void => zoom', [style({\n      transform: 'scale(2.5,2.5)',\n      opacity: '0'\n    }), animate('500ms ease-in-out', style({\n      transform: 'scale(1, 1)',\n      opacity: '1'\n    }))])])]\n  },\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGalleryImageComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-gallery-image',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [trigger('animation', [\n      // ...\n      state('slideRight', style({})), state('slideLeft', style({})), state('fade', style({})), state('rotateLeft', style({})), state('rotateRight', style({})), state('zoom', style({})), transition('slideRight => void', [animate('500ms ease-in-out', style({\n        transform: 'translateX(-100%)'\n      }))]), transition('void => slideRight', [style({\n        transform: 'translateX(100%)'\n      }), animate('500ms ease-in-out', style({\n        transform: 'translateX(0)'\n      }))]), transition('slideLeft => void', [animate('500ms ease-in-out', style({\n        transform: 'translateX(100%)'\n      }))]), transition('void => slideLeft', [style({\n        transform: 'translateX(-100%)'\n      }), animate('500ms ease-in-out', style({\n        transform: 'translateX(0)'\n      }))]), transition('fade => void', [animate('500ms ease-in-out', style({\n        opacity: '0'\n      }))]), transition('void => fade', [style({\n        opacity: '0'\n      }), animate('500ms ease-in-out', style({\n        opacity: '1'\n      }))]), transition('rotateLeft => void', [animate('500ms ease-in-out', style({\n        transform: 'scale(1, 1) rotate(-90deg)',\n        opacity: '0'\n      }))]), transition('void => rotateLeft', [style({\n        transform: 'scale(1, 1) rotate(-90deg)',\n        opacity: '0'\n      }), animate('500ms ease-in-out', style({\n        transform: 'scale(1, 1) rotate(0deg)',\n        opacity: '1'\n      }))]), transition('rotateRight => void', [animate('500ms ease-in-out', style({\n        transform: 'scale(1, 1) rotate(90deg)',\n        opacity: '0'\n      }))]), transition('void => rotateRight', [style({\n        transform: 'scale(1, 1) rotate(90deg)',\n        opacity: '0'\n      }), animate('500ms ease-in-out', style({\n        transform: 'scale(1, 1) rotate(0deg)',\n        opacity: '1'\n      }))]), transition('zoom => void', [animate('500ms ease-in-out', style({\n        transform: 'scale(2.5,2.5)',\n        opacity: '0'\n      }))]), transition('void => zoom', [style({\n        transform: 'scale(2.5,2.5)',\n        opacity: '0'\n      }), animate('500ms ease-in-out', style({\n        transform: 'scale(1, 1)',\n        opacity: '1'\n      }))])])],\n      template: \"<div class=\\\"ngx-gallery-image-wrapper ngx-gallery-animation-{{animation}} ngx-gallery-image-size-{{size}}\\\">\\n  <ng-container *ngFor=\\\"let image of getImages(); let i = index;\\\">\\n\\n    <div *ngIf=\\\"image.type === 'image' && image.index === _selectedIndex\\\" class=\\\"ngx-gallery-image\\\"\\n         [ngClass]=\\\"{'ngx-gallery-clickable': clickable }\\\"\\n         [style.background-image]=\\\"getSafeUrl(image.src)\\\"\\n         (click)=\\\"handleClick($event, image.index)\\\"\\n         [@animation]=\\\"action\\\"\\n         (@animation.start)=\\\"onStart($event)\\\"\\n         (@animation.done)=\\\"onDone($event)\\\">\\n      <div class=\\\"ngx-gallery-icons-wrapper\\\">\\n        <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                            [titleText]=\\\"action.titleText\\\"\\n                            (closeClick)=\\\"action.onClick($event, image.index)\\\"></ngx-gallery-action>\\n      </div>\\n      <div class=\\\"ngx-gallery-image-text\\\" *ngIf=\\\"showDescription && descriptions[image.index]\\\"\\n          [innerHTML]=\\\"descriptions[image.index]\\\" (click)=\\\"$event.stopPropagation()\\\"></div>\\n    </div>\\n\\n    <div *ngIf=\\\"image.type === 'video' && image.index === _selectedIndex\\\" class=\\\"ngx-gallery-image\\\"\\n         [ngClass]=\\\"{'ngx-gallery-clickable': clickable }\\\"\\n         [style.background-image]=\\\"getSafeUrl(image.src)\\\"\\n         (click)=\\\"handleClick($event, image.index)\\\"\\n         [@animation]=\\\"action\\\"\\n         (@animation.start)=\\\"onStart($event)\\\"\\n         (@animation.done)=\\\"onDone($event)\\\">\\n      <video controls style=\\\"width:100%; height:100%; background: #000;\\\">\\n        <source [src]=\\\"image.src\\\">\\n          Your browser does not support the video tag.\\n      </video>\\n      <div class=\\\"ngx-gallery-icons-wrapper\\\">\\n      <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                          [titleText]=\\\"action.titleText\\\"\\n                          (closeClick)=\\\"action.onClick($event, image.index)\\\"></ngx-gallery-action>\\n      </div>\\n      <div class=\\\"ngx-gallery-image-text\\\" *ngIf=\\\"showDescription && descriptions[image.index]\\\"\\n          [innerHTML]=\\\"descriptions[image.index]\\\" (click)=\\\"$event.stopPropagation()\\\"></div>\\n      </div>\\n\\n\\n  </ng-container>\\n  <ngx-gallery-bullets *ngIf=\\\"bullets\\\" [count]=\\\"images.length\\\" [active]=\\\"_selectedIndex\\\"\\n                       (bulletChange)=\\\"show($event)\\\"></ngx-gallery-bullets>\\n  <ngx-gallery-arrows class=\\\"ngx-gallery-image-size-{{size}}\\\" *ngIf=\\\"arrows\\\" (prevClick)=\\\"showPrev()\\\"\\n                      (nextClick)=\\\"showNext()\\\" [prevDisabled]=\\\"!canShowPrev()\\\" [nextDisabled]=\\\"!canShowNext()\\\"\\n                      [arrowPrevIcon]=\\\"arrowPrevIcon\\\" [arrowNextIcon]=\\\"arrowNextIcon\\\"></ngx-gallery-arrows>\\n</div>\\n\",\n      styles: [\":host{width:100%;display:inline-block;position:relative;font-size:25px}.ngx-gallery-image-wrapper{width:100%;height:100%;position:absolute;left:0;top:0;overflow:hidden}.ngx-gallery-image{background-position:center;background-repeat:no-repeat;height:100%;width:100%;position:absolute;top:0}.ngx-gallery-image-size-cover .ngx-gallery-image{background-size:cover}.ngx-gallery-image-size-contain .ngx-gallery-image{background-size:contain}.ngx-gallery-animation-fade .ngx-gallery-image{left:0;opacity:1;transition:.5s ease-in-out}.ngx-gallery-animation-fade .ngx-gallery-image.ngx-gallery-active{opacity:1}.ngx-gallery-animation-rotate .ngx-gallery-image{transition:1s ease;transform:scale(1) rotate(0);left:0;opacity:1}.ngx-gallery-animation-zoom .ngx-gallery-image{transition:1s ease;transform:scale(1);left:0;opacity:1}.ngx-gallery-image-text{width:100%;background:rgba(0,0,0,.7);padding:10px;text-align:center;color:#fff;font-size:16px;position:absolute;bottom:0;z-index:10}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i1.DomSanitizer\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: NgxGalleryService\n    }];\n  }, {\n    images: [{\n      type: Input\n    }],\n    clickable: [{\n      type: Input\n    }],\n    selectedIndex: [{\n      type: Input\n    }],\n    arrows: [{\n      type: Input\n    }],\n    arrowsAutoHide: [{\n      type: Input\n    }],\n    swipe: [{\n      type: Input\n    }],\n    animation: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    arrowPrevIcon: [{\n      type: Input\n    }],\n    arrowNextIcon: [{\n      type: Input\n    }],\n    autoPlay: [{\n      type: Input\n    }],\n    autoPlayInterval: [{\n      type: Input\n    }],\n    autoPlayPauseOnHover: [{\n      type: Input\n    }],\n    infinityMove: [{\n      type: Input\n    }],\n    lazyLoading: [{\n      type: Input\n    }],\n    actions: [{\n      type: Input\n    }],\n    descriptions: [{\n      type: Input\n    }],\n    showDescription: [{\n      type: Input\n    }],\n    bullets: [{\n      type: Input\n    }],\n    imageClick: [{\n      type: Output\n    }],\n    activeChange: [{\n      type: Output\n    }],\n    animating: [{\n      type: Output\n    }],\n    onMouseEnter: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    onMouseLeave: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\nclass NgxGalleryOrder {}\nNgxGalleryOrder.Column = 1;\nNgxGalleryOrder.Row = 2;\nNgxGalleryOrder.Page = 3;\nclass NgxGalleryThumbnailsComponent {\n  constructor(sanitization, elementRef, helperService) {\n    this.sanitization = sanitization;\n    this.elementRef = elementRef;\n    this.helperService = helperService;\n    this.minStopIndex = 0;\n    this.activeChange = new EventEmitter();\n    this.index = 0;\n  }\n  ngOnChanges(changes) {\n    if (changes['selectedIndex']) {\n      this.validateIndex();\n    }\n    if (changes['swipe']) {\n      this.helperService.manageSwipe(this.swipe, this.elementRef, 'thumbnails', () => this.moveRight(), () => this.moveLeft());\n    }\n    if (this.images) {\n      this.remainingCountValue = this.images.length - this.rows * this.columns;\n    }\n  }\n  onMouseEnter() {\n    this.mouseenter = true;\n  }\n  onMouseLeave() {\n    this.mouseenter = false;\n  }\n  reset(index) {\n    this.selectedIndex = index;\n    this.setDefaultPosition();\n    this.index = 0;\n    this.validateIndex();\n  }\n  getImages() {\n    if (!this.images) {\n      return [];\n    }\n    if (this.remainingCount) {\n      return this.images.slice(0, this.rows * this.columns);\n    } else if (this.lazyLoading && this.order !== NgxGalleryOrder.Row) {\n      let stopIndex = 0;\n      if (this.order === NgxGalleryOrder.Column) {\n        stopIndex = (this.index + this.columns + this.moveSize) * this.rows;\n      } else if (this.order === NgxGalleryOrder.Page) {\n        stopIndex = this.index + this.columns * this.rows * 2;\n      }\n      if (stopIndex <= this.minStopIndex) {\n        stopIndex = this.minStopIndex;\n      } else {\n        this.minStopIndex = stopIndex;\n      }\n      return this.images.slice(0, stopIndex);\n    } else {\n      return this.images;\n    }\n  }\n  handleClick(event, index) {\n    if (!this.hasLink(index) && !this.isAnimating) {\n      this.selectedIndex = index;\n      this.activeChange.emit(index);\n    }\n    event.stopPropagation();\n    event.preventDefault();\n  }\n  hasLink(index) {\n    return !!(this.links && this.links.length && this.links[index]);\n  }\n  moveRight() {\n    if (this.canMoveRight()) {\n      this.index += this.moveSize;\n      const maxIndex = this.getMaxIndex() - this.columns;\n      if (this.index > maxIndex) {\n        this.index = maxIndex;\n      }\n      this.setThumbnailsPosition();\n    }\n  }\n  moveLeft() {\n    if (this.canMoveLeft()) {\n      this.index -= this.moveSize;\n      if (this.index < 0) {\n        this.index = 0;\n      }\n      this.setThumbnailsPosition();\n    }\n  }\n  canMoveRight() {\n    return this.index + this.columns < this.getMaxIndex();\n  }\n  canMoveLeft() {\n    return this.index !== 0;\n  }\n  getThumbnailLeft(index) {\n    let calculatedIndex;\n    if (this.order === NgxGalleryOrder.Column) {\n      calculatedIndex = Math.floor(index / this.rows);\n    } else if (this.order === NgxGalleryOrder.Page) {\n      calculatedIndex = index % this.columns + Math.floor(index / (this.rows * this.columns)) * this.columns;\n    } else if (this.order === NgxGalleryOrder.Row && this.remainingCount) {\n      calculatedIndex = index % this.columns;\n    } else {\n      calculatedIndex = index % Math.ceil(this.images.length / this.rows);\n    }\n    return this.getThumbnailPosition(calculatedIndex, this.columns);\n  }\n  getThumbnailTop(index) {\n    let calculatedIndex;\n    if (this.order === NgxGalleryOrder.Column) {\n      calculatedIndex = index % this.rows;\n    } else if (this.order === NgxGalleryOrder.Page) {\n      calculatedIndex = Math.floor(index / this.columns) - Math.floor(index / (this.rows * this.columns)) * this.rows;\n    } else if (this.order === NgxGalleryOrder.Row && this.remainingCount) {\n      calculatedIndex = Math.floor(index / this.columns);\n    } else {\n      calculatedIndex = Math.floor(index / Math.ceil(this.images.length / this.rows));\n    }\n    return this.getThumbnailPosition(calculatedIndex, this.rows);\n  }\n  getThumbnailWidth() {\n    return this.getThumbnailDimension(this.columns);\n  }\n  getThumbnailHeight() {\n    return this.getThumbnailDimension(this.rows);\n  }\n  setThumbnailsPosition() {\n    this.thumbnailsLeft = -(100 / this.columns * this.index) + '%';\n    this.thumbnailsMarginLeft = -((this.margin - (this.columns - 1) * this.margin / this.columns) * this.index) + 'px';\n  }\n  setDefaultPosition() {\n    this.thumbnailsLeft = '0px';\n    this.thumbnailsMarginLeft = '0px';\n  }\n  canShowArrows() {\n    if (this.remainingCount) {\n      return false;\n    } else {\n      return this.arrows && this.images && this.images.length > this.getVisibleCount() && (!this.arrowsAutoHide || this.mouseenter);\n    }\n  }\n  validateIndex() {\n    if (this.images) {\n      let newIndex;\n      if (this.order === NgxGalleryOrder.Column) {\n        newIndex = Math.floor(this.selectedIndex / this.rows);\n      } else {\n        newIndex = this.selectedIndex % Math.ceil(this.images.length / this.rows);\n      }\n      if (this.remainingCount) {\n        newIndex = 0;\n      }\n      if (newIndex < this.index || newIndex >= this.index + this.columns) {\n        const maxIndex = this.getMaxIndex() - this.columns;\n        this.index = newIndex > maxIndex ? maxIndex : newIndex;\n        this.setThumbnailsPosition();\n      }\n    }\n  }\n  getSafeUrl(image) {\n    return this.sanitization.bypassSecurityTrustStyle(this.helperService.getBackgroundUrl(image.toString()));\n  }\n  getFileType(fileSource) {\n    return this.helperService.getFileType(fileSource.toString());\n  }\n  getThumbnailPosition(index, count) {\n    return this.getSafeStyle('calc(' + 100 / count * index + '% + ' + (this.margin - (count - 1) * this.margin / count) * index + 'px)');\n  }\n  getThumbnailDimension(count) {\n    if (this.margin !== 0) {\n      return this.getSafeStyle('calc(' + 100 / count + '% - ' + (count - 1) * this.margin / count + 'px)');\n    } else {\n      return this.getSafeStyle('calc(' + 100 / count + '% + 1px)');\n    }\n  }\n  getMaxIndex() {\n    if (this.order === NgxGalleryOrder.Page) {\n      let maxIndex = Math.floor(this.images.length / this.getVisibleCount()) * this.columns;\n      if (this.images.length % this.getVisibleCount() > this.columns) {\n        maxIndex += this.columns;\n      } else {\n        maxIndex += this.images.length % this.getVisibleCount();\n      }\n      return maxIndex;\n    } else {\n      return Math.ceil(this.images.length / this.rows);\n    }\n  }\n  getVisibleCount() {\n    return this.columns * this.rows;\n  }\n  getSafeStyle(value) {\n    return this.sanitization.bypassSecurityTrustStyle(value);\n  }\n}\nNgxGalleryThumbnailsComponent.ɵfac = function NgxGalleryThumbnailsComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxGalleryThumbnailsComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NgxGalleryService));\n};\nNgxGalleryThumbnailsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxGalleryThumbnailsComponent,\n  selectors: [[\"ngx-gallery-thumbnails\"]],\n  hostBindings: function NgxGalleryThumbnailsComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mouseenter\", function NgxGalleryThumbnailsComponent_mouseenter_HostBindingHandler() {\n        return ctx.onMouseEnter();\n      })(\"mouseleave\", function NgxGalleryThumbnailsComponent_mouseleave_HostBindingHandler() {\n        return ctx.onMouseLeave();\n      });\n    }\n  },\n  inputs: {\n    images: \"images\",\n    isAnimating: \"isAnimating\",\n    links: \"links\",\n    labels: \"labels\",\n    linkTarget: \"linkTarget\",\n    columns: \"columns\",\n    rows: \"rows\",\n    arrows: \"arrows\",\n    arrowsAutoHide: \"arrowsAutoHide\",\n    margin: \"margin\",\n    selectedIndex: \"selectedIndex\",\n    clickable: \"clickable\",\n    swipe: \"swipe\",\n    size: \"size\",\n    arrowPrevIcon: \"arrowPrevIcon\",\n    arrowNextIcon: \"arrowNextIcon\",\n    moveSize: \"moveSize\",\n    order: \"order\",\n    remainingCount: \"remainingCount\",\n    lazyLoading: \"lazyLoading\",\n    actions: \"actions\"\n  },\n  outputs: {\n    activeChange: \"activeChange\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 4,\n  vars: 9,\n  consts: [[1, \"ngx-gallery-thumbnails\"], [\"class\", \"ngx-gallery-thumbnail\", 3, \"href\", \"target\", \"width\", \"height\", \"left\", \"top\", \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"prevDisabled\", \"nextDisabled\", \"arrowPrevIcon\", \"arrowNextIcon\", \"prevClick\", \"nextClick\", 4, \"ngIf\"], [1, \"ngx-gallery-thumbnail\", 3, \"click\", \"href\", \"target\", \"ngClass\"], [\"class\", \"ngx-gallery-thumbnail\", \"style\", \"width: 100%; height: 100%; position:absolute;\", 3, \"background-image\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"ngx-gallery-thumbnail-video\", 4, \"ngIf\"], [1, \"ngx-gallery-icons-wrapper\"], [3, \"icon\", \"disabled\", \"titleText\", \"closeClick\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"ngx-gallery-remaining-count-overlay\", 4, \"ngIf\"], [1, \"ngx-gallery-thumbnail\", 2, \"width\", \"100%\", \"height\", \"100%\", \"position\", \"absolute\", 3, \"ngClass\"], [1, \"ngx-gallery-thumbnail-video\"], [1, \"ngx-gallery-thumbnail\", 2, \"width\", \"100%\", \"height\", \"100%\", \"position\", \"absolute\", \"left\", \"0\", \"background\", \"#000\", 3, \"ngClass\"], [3, \"src\"], [3, \"closeClick\", \"icon\", \"disabled\", \"titleText\"], [1, \"ngx-gallery-remaining-count-overlay\"], [1, \"ngx-gallery-remaining-count\"], [3, \"prevClick\", \"nextClick\", \"prevDisabled\", \"nextDisabled\", \"arrowPrevIcon\", \"arrowNextIcon\"]],\n  template: function NgxGalleryThumbnailsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0);\n      i0.ɵɵtemplate(2, NgxGalleryThumbnailsComponent_a_2_Template, 6, 19, \"a\", 1);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(3, NgxGalleryThumbnailsComponent_ngx_gallery_arrows_3_Template, 1, 4, \"ngx-gallery-arrows\", 2);\n    }\n    if (rf & 2) {\n      i0.ɵɵclassMapInterpolate1(\"ngx-gallery-thumbnails-wrapper ngx-gallery-thumbnail-size-\", ctx.size, \"\");\n      i0.ɵɵadvance();\n      i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx.thumbnailsLeft + \")\")(\"margin-left\", ctx.thumbnailsMarginLeft);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngForOf\", ctx.getImages());\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.canShowArrows());\n    }\n  },\n  dependencies: [NgxGalleryActionComponent, NgxGalleryArrowsComponent, i6.NgForOf, i6.NgClass, i6.NgIf],\n  styles: [\"@charset \\\"UTF-8\\\";[_nghost-%COMP%]{width:100%;display:inline-block;position:relative;font-size:25px}.ngx-gallery-thumbnails-wrapper[_ngcontent-%COMP%]{width:100%;height:100%;position:absolute;overflow:hidden}.ngx-gallery-thumbnails[_ngcontent-%COMP%]{height:100%;width:100%;position:absolute;left:0;transform:translate(0);transition:transform .5s ease-in-out;will-change:transform}.ngx-gallery-thumbnails[_ngcontent-%COMP%]   .ngx-gallery-thumbnail[_ngcontent-%COMP%]{position:absolute;height:100%;background-position:center;background-repeat:no-repeat;text-decoration:none;border:1px double black}.ngx-gallery-thumbnails[_ngcontent-%COMP%]   .ngx-gallery-thumbnail[_ngcontent-%COMP%]   .ngx-gallery-thumbnail-video[_ngcontent-%COMP%]:after{content:\\\"\\\\f144\\\";display:block;position:absolute;background:#0000;height:100%;width:100%;left:0;top:calc(50% - 20px);font-size:40px;color:#fff;margin:0;padding:0;font-family:fontawesome;text-shadow:0px 4px 3px rgba(0,0,0,.4),0px 8px 13px rgba(0,0,0,.1),0px 18px 23px rgba(0,0,0,.1)}.ngx-gallery-thumbnails[_ngcontent-%COMP%]   .ngx-gallery-thumbnail[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{background-size:cover;height:100%}.ngx-gallery-thumbnails[_ngcontent-%COMP%]   .ngx-gallery-thumbnail.ngx-gallery-active[_ngcontent-%COMP%]{border:1px double #cc4548}.ngx-gallery-thumbnail-size-cover[_ngcontent-%COMP%]   .ngx-gallery-thumbnails[_ngcontent-%COMP%]   .ngx-gallery-thumbnail[_ngcontent-%COMP%]{background-size:cover}.ngx-gallery-thumbnail-size-contain[_ngcontent-%COMP%]   .ngx-gallery-thumbnails[_ngcontent-%COMP%]   .ngx-gallery-thumbnail[_ngcontent-%COMP%]{background-size:contain}.ngx-gallery-remaining-count-overlay[_ngcontent-%COMP%]{width:100%;height:100%;position:absolute;left:0;top:0;background-color:#0006}.ngx-gallery-remaining-count[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#fff;font-size:30px}\"],\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGalleryThumbnailsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-gallery-thumbnails',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"ngx-gallery-thumbnails-wrapper ngx-gallery-thumbnail-size-{{size}}\\\">\\n  <div class=\\\"ngx-gallery-thumbnails\\\" [style.transform]=\\\"'translateX(' + thumbnailsLeft + ')'\\\"\\n       [style.marginLeft]=\\\"thumbnailsMarginLeft\\\">\\n    <a [href]=\\\"hasLink(i) ? links[i] : '#'\\\" [target]=\\\"linkTarget\\\" class=\\\"ngx-gallery-thumbnail\\\"\\n       *ngFor=\\\"let image of getImages(); let i = index;\\\"\\n       (click)=\\\"handleClick($event, i)\\\" [style.width]=\\\"getThumbnailWidth()\\\" [style.height]=\\\"getThumbnailHeight()\\\"\\n       [style.left]=\\\"getThumbnailLeft(i)\\\" [style.top]=\\\"getThumbnailTop(i)\\\"\\n       [ngClass]=\\\"{ 'ngx-gallery-active': i === selectedIndex, 'ngx-gallery-clickable': clickable }\\\"\\n       [attr.aria-label]=\\\"labels[i]\\\">\\n       <div *ngIf=\\\"getFileType(image) === 'image'\\\" [style.background-image]=\\\"getSafeUrl(image)\\\" class=\\\"ngx-gallery-thumbnail\\\"\\n       [ngClass]=\\\"{ 'ngx-gallery-active': i === selectedIndex, 'ngx-gallery-clickable': clickable }\\\"\\n       style=\\\"width: 100%; height: 100%; position:absolute;\\\"></div>\\n       <div *ngIf=\\\"getFileType(image) === 'video'\\\" class=\\\"ngx-gallery-thumbnail-video\\\">\\n        <video  class=\\\"ngx-gallery-thumbnail\\\" [ngClass]=\\\"{ 'ngx-gallery-active': i === selectedIndex, 'ngx-gallery-clickable': clickable }\\\"\\n        style=\\\"width: 100%; height: 100%; position:absolute; left:0; background:#000;\\\">\\n        <source [src]=\\\"image\\\">\\n          Your browser does not support the video tag.\\n       </video>\\n      </div>\\n       <div class=\\\"ngx-gallery-icons-wrapper\\\">\\n        <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                            [titleText]=\\\"action.titleText\\\" (closeClick)=\\\"action.onClick($event, i)\\\"></ngx-gallery-action>\\n      </div>\\n      <div class=\\\"ngx-gallery-remaining-count-overlay\\\"\\n           *ngIf=\\\"remainingCount && remainingCountValue && (i === (rows * columns) - 1)\\\">\\n        <span class=\\\"ngx-gallery-remaining-count\\\">+{{remainingCountValue}}</span>\\n      </div>\\n    </a>\\n  </div>\\n</div>\\n<ngx-gallery-arrows *ngIf=\\\"canShowArrows()\\\" (prevClick)=\\\"moveLeft()\\\" (nextClick)=\\\"moveRight()\\\"\\n                    [prevDisabled]=\\\"!canMoveLeft()\\\" [nextDisabled]=\\\"!canMoveRight()\\\" [arrowPrevIcon]=\\\"arrowPrevIcon\\\"\\n                    [arrowNextIcon]=\\\"arrowNextIcon\\\"></ngx-gallery-arrows>\\n\",\n      styles: [\"@charset \\\"UTF-8\\\";:host{width:100%;display:inline-block;position:relative;font-size:25px}.ngx-gallery-thumbnails-wrapper{width:100%;height:100%;position:absolute;overflow:hidden}.ngx-gallery-thumbnails{height:100%;width:100%;position:absolute;left:0;transform:translate(0);transition:transform .5s ease-in-out;will-change:transform}.ngx-gallery-thumbnails .ngx-gallery-thumbnail{position:absolute;height:100%;background-position:center;background-repeat:no-repeat;text-decoration:none;border:1px double black}.ngx-gallery-thumbnails .ngx-gallery-thumbnail .ngx-gallery-thumbnail-video:after{content:\\\"\\\\f144\\\";display:block;position:absolute;background:#0000;height:100%;width:100%;left:0;top:calc(50% - 20px);font-size:40px;color:#fff;margin:0;padding:0;font-family:fontawesome;text-shadow:0px 4px 3px rgba(0,0,0,.4),0px 8px 13px rgba(0,0,0,.1),0px 18px 23px rgba(0,0,0,.1)}.ngx-gallery-thumbnails .ngx-gallery-thumbnail .img{background-size:cover;height:100%}.ngx-gallery-thumbnails .ngx-gallery-thumbnail.ngx-gallery-active{border:1px double #cc4548}.ngx-gallery-thumbnail-size-cover .ngx-gallery-thumbnails .ngx-gallery-thumbnail{background-size:cover}.ngx-gallery-thumbnail-size-contain .ngx-gallery-thumbnails .ngx-gallery-thumbnail{background-size:contain}.ngx-gallery-remaining-count-overlay{width:100%;height:100%;position:absolute;left:0;top:0;background-color:#0006}.ngx-gallery-remaining-count{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#fff;font-size:30px}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i1.DomSanitizer\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: NgxGalleryService\n    }];\n  }, {\n    images: [{\n      type: Input\n    }],\n    isAnimating: [{\n      type: Input\n    }],\n    links: [{\n      type: Input\n    }],\n    labels: [{\n      type: Input\n    }],\n    linkTarget: [{\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    rows: [{\n      type: Input\n    }],\n    arrows: [{\n      type: Input\n    }],\n    arrowsAutoHide: [{\n      type: Input\n    }],\n    margin: [{\n      type: Input\n    }],\n    selectedIndex: [{\n      type: Input\n    }],\n    clickable: [{\n      type: Input\n    }],\n    swipe: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    arrowPrevIcon: [{\n      type: Input\n    }],\n    arrowNextIcon: [{\n      type: Input\n    }],\n    moveSize: [{\n      type: Input\n    }],\n    order: [{\n      type: Input\n    }],\n    remainingCount: [{\n      type: Input\n    }],\n    lazyLoading: [{\n      type: Input\n    }],\n    actions: [{\n      type: Input\n    }],\n    activeChange: [{\n      type: Output\n    }],\n    onMouseEnter: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    onMouseLeave: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\nclass NgxGalleryAction {\n  constructor(action) {\n    this.icon = action.icon;\n    this.disabled = action.disabled ? action.disabled : false;\n    this.titleText = action.titleText ? action.titleText : '';\n    this.onClick = action.onClick;\n  }\n}\nclass NgxGalleryLayout {}\nNgxGalleryLayout.ThumbnailsTop = 'thumbnails-top';\nNgxGalleryLayout.ThumbnailsBottom = 'thumbnails-bottom';\nclass NgxGalleryImageSize {}\nNgxGalleryImageSize.Cover = 'cover';\nNgxGalleryImageSize.Contain = 'contain';\nclass NgxGalleryOptions {\n  constructor(obj) {\n    const preventDefaults = obj.breakpoint === undefined ? false : true;\n    function use(source, defaultValue) {\n      return obj && (source !== undefined || preventDefaults) ? source : defaultValue;\n    }\n    this.breakpoint = use(obj.breakpoint, undefined);\n    this.width = use(obj.width, '500px');\n    this.height = use(obj.height, '400px');\n    this.fullWidth = use(obj.fullWidth, false);\n    this.layout = use(obj.layout, NgxGalleryLayout.ThumbnailsBottom);\n    this.startIndex = use(obj.startIndex, 0);\n    this.linkTarget = use(obj.linkTarget, '_blank');\n    this.lazyLoading = use(obj.lazyLoading, true);\n    this.image = use(obj.image, true);\n    this.imagePercent = use(obj.imagePercent, 75);\n    this.imageArrows = use(obj.imageArrows, true);\n    this.imageArrowsAutoHide = use(obj.imageArrowsAutoHide, false);\n    this.imageSwipe = use(obj.imageSwipe, false);\n    this.imageAnimation = use(obj.imageAnimation, NgxGalleryAnimation.Fade);\n    this.imageSize = use(obj.imageSize, NgxGalleryImageSize.Cover);\n    this.imageAutoPlay = use(obj.imageAutoPlay, false);\n    this.imageAutoPlayInterval = use(obj.imageAutoPlayInterval, 2000);\n    this.imageAutoPlayPauseOnHover = use(obj.imageAutoPlayPauseOnHover, false);\n    this.imageInfinityMove = use(obj.imageInfinityMove, false);\n    if (obj && obj.imageActions && obj.imageActions.length) {\n      obj.imageActions = obj.imageActions.map(action => new NgxGalleryAction(action));\n    }\n    this.imageActions = use(obj.imageActions, []);\n    this.imageDescription = use(obj.imageDescription, false);\n    this.imageBullets = use(obj.imageBullets, false);\n    this.thumbnails = use(obj.thumbnails, true);\n    this.thumbnailsColumns = use(obj.thumbnailsColumns, 4);\n    this.thumbnailsRows = use(obj.thumbnailsRows, 1);\n    this.thumbnailsPercent = use(obj.thumbnailsPercent, 25);\n    this.thumbnailsMargin = use(obj.thumbnailsMargin, 10);\n    this.thumbnailsArrows = use(obj.thumbnailsArrows, true);\n    this.thumbnailsArrowsAutoHide = use(obj.thumbnailsArrowsAutoHide, false);\n    this.thumbnailsSwipe = use(obj.thumbnailsSwipe, false);\n    this.thumbnailsMoveSize = use(obj.thumbnailsMoveSize, 1);\n    this.thumbnailsOrder = use(obj.thumbnailsOrder, NgxGalleryOrder.Column);\n    this.thumbnailsRemainingCount = use(obj.thumbnailsRemainingCount, false);\n    this.thumbnailsAsLinks = use(obj.thumbnailsAsLinks, false);\n    this.thumbnailsAutoHide = use(obj.thumbnailsAutoHide, false);\n    this.thumbnailMargin = use(obj.thumbnailMargin, 10);\n    this.thumbnailSize = use(obj.thumbnailSize, NgxGalleryImageSize.Cover);\n    if (obj && obj.thumbnailActions && obj.thumbnailActions.length) {\n      obj.thumbnailActions = obj.thumbnailActions.map(action => new NgxGalleryAction(action));\n    }\n    this.thumbnailActions = use(obj.thumbnailActions, []);\n    this.thumbnailClasses = use(obj.thumbnailClasses, []);\n    this.preview = use(obj.preview, true);\n    this.previewDescription = use(obj.previewDescription, true);\n    this.previewArrows = use(obj.previewArrows, true);\n    this.previewArrowsAutoHide = use(obj.previewArrowsAutoHide, false);\n    this.previewSwipe = use(obj.previewSwipe, false);\n    this.previewFullscreen = use(obj.previewFullscreen, false);\n    this.previewForceFullscreen = use(obj.previewForceFullscreen, false);\n    this.previewCloseOnClick = use(obj.previewCloseOnClick, false);\n    this.previewCloseOnEsc = use(obj.previewCloseOnEsc, false);\n    this.previewKeyboardNavigation = use(obj.previewKeyboardNavigation, false);\n    this.previewAnimation = use(obj.previewAnimation, true);\n    this.previewAutoPlay = use(obj.previewAutoPlay, false);\n    this.previewAutoPlayInterval = use(obj.previewAutoPlayInterval, 2000);\n    this.previewAutoPlayPauseOnHover = use(obj.previewAutoPlayPauseOnHover, false);\n    this.previewInfinityMove = use(obj.previewInfinityMove, false);\n    this.previewZoom = use(obj.previewZoom, false);\n    this.previewZoomStep = use(obj.previewZoomStep, 0.1);\n    this.previewZoomMax = use(obj.previewZoomMax, 2);\n    this.previewZoomMin = use(obj.previewZoomMin, 0.5);\n    this.previewRotate = use(obj.previewRotate, false);\n    this.previewDownload = use(obj.previewDownload, false);\n    this.previewCustom = use(obj.previewCustom, undefined);\n    this.previewBullets = use(obj.previewBullets, false);\n    this.arrowPrevIcon = use(obj.arrowPrevIcon, 'fa fa-arrow-circle-left');\n    this.arrowNextIcon = use(obj.arrowNextIcon, 'fa fa-arrow-circle-right');\n    this.closeIcon = use(obj.closeIcon, 'fa fa-times-circle');\n    this.fullscreenIcon = use(obj.fullscreenIcon, 'fa fa-arrows-alt');\n    this.spinnerIcon = use(obj.spinnerIcon, 'fa fa-spinner fa-pulse fa-3x fa-fw');\n    this.zoomInIcon = use(obj.zoomInIcon, 'fa fa-search-plus');\n    this.zoomOutIcon = use(obj.zoomOutIcon, 'fa fa-search-minus');\n    this.rotateLeftIcon = use(obj.rotateLeftIcon, 'fa fa-undo');\n    this.rotateRightIcon = use(obj.rotateRightIcon, 'fa fa-repeat');\n    this.downloadIcon = use(obj.downloadIcon, 'fa fa-arrow-circle-down');\n    if (obj && obj.actions && obj.actions.length) {\n      obj.actions = obj.actions.map(action => new NgxGalleryAction(action));\n    }\n    this.actions = use(obj.actions, []);\n  }\n}\nclass NgxGalleryOrderedImage {\n  constructor(obj) {\n    this.src = obj.src;\n    this.type = obj.type;\n    this.index = obj.index;\n  }\n}\nclass NgxGalleryComponent {\n  constructor(myElement, helperService) {\n    this.myElement = myElement;\n    this.helperService = helperService;\n    this.options = [{}];\n    this.imagesReady = new EventEmitter();\n    // eslint-disable-next-line @angular-eslint/no-output-native\n    this.change = new EventEmitter();\n    this.previewOpen = new EventEmitter();\n    this.previewClose = new EventEmitter();\n    this.previewChange = new EventEmitter();\n    this.oldImagesLength = 0;\n    this.selectedIndex = 0;\n    this.breakpoint = undefined;\n    this.prevBreakpoint = undefined;\n  }\n  ngOnInit() {\n    this.options = this.options.map(opt => new NgxGalleryOptions(opt));\n    this.sortOptions();\n    this.setBreakpoint();\n    this.setOptions();\n    this.checkFullWidth();\n    if (this.currentOptions) {\n      this.selectedIndex = this.currentOptions.startIndex;\n    }\n  }\n  ngDoCheck() {\n    if (this.images !== undefined && this.images.length !== this.oldImagesLength || this.images !== this.oldImages) {\n      this.oldImagesLength = this.images.length;\n      this.oldImages = this.images;\n      this.setOptions();\n      this.setImages();\n      if (this.images && this.images.length) {\n        this.imagesReady.emit();\n      }\n      if (this.image) {\n        this.image.reset(this.currentOptions.startIndex);\n      }\n      if (this.currentOptions.thumbnailsAutoHide && this.currentOptions.thumbnails && this.images.length <= 1) {\n        this.currentOptions.thumbnails = false;\n        this.currentOptions.imageArrows = false;\n      }\n      this.resetThumbnails();\n    }\n  }\n  ngAfterViewInit() {\n    this.checkFullWidth();\n  }\n  onResize() {\n    this.setBreakpoint();\n    if (this.prevBreakpoint !== this.breakpoint) {\n      this.setOptions();\n      this.resetThumbnails();\n    }\n    if (this.currentOptions && this.currentOptions.fullWidth) {\n      if (this.fullWidthTimeout) {\n        clearTimeout(this.fullWidthTimeout);\n      }\n      this.fullWidthTimeout = setTimeout(() => {\n        this.checkFullWidth();\n      }, 200);\n    }\n  }\n  getImageHeight() {\n    return this.currentOptions && this.currentOptions.thumbnails ? this.currentOptions.imagePercent + '%' : '100%';\n  }\n  getThumbnailsHeight() {\n    if (this.currentOptions && this.currentOptions.image) {\n      return 'calc(' + this.currentOptions.thumbnailsPercent + '% - ' + this.currentOptions.thumbnailsMargin + 'px)';\n    } else {\n      return '100%';\n    }\n  }\n  getThumbnailsMarginTop() {\n    if (this.currentOptions && this.currentOptions.layout === NgxGalleryLayout.ThumbnailsBottom) {\n      return this.currentOptions.thumbnailsMargin + 'px';\n    } else {\n      return '0px';\n    }\n  }\n  getThumbnailsMarginBottom() {\n    if (this.currentOptions && this.currentOptions.layout === NgxGalleryLayout.ThumbnailsTop) {\n      return this.currentOptions.thumbnailsMargin + 'px';\n    } else {\n      return '0px';\n    }\n  }\n  openPreview(index) {\n    if (this.currentOptions.previewCustom) {\n      this.currentOptions.previewCustom(index);\n    } else {\n      this.previewEnabled = true;\n      this.preview.open(index);\n    }\n  }\n  onPreviewOpen() {\n    this.previewOpen.emit();\n    if (this.image && this.image.autoPlay) {\n      this.image.stopAutoPlay();\n    }\n  }\n  onPreviewClose() {\n    this.previewEnabled = false;\n    this.previewClose.emit();\n    if (this.image && this.image.autoPlay) {\n      this.image.startAutoPlay();\n    }\n  }\n  selectFromImage(index) {\n    this.select(index);\n  }\n  selectFromThumbnails(index) {\n    this.select(index);\n    if (this.currentOptions && this.currentOptions.thumbnails && this.currentOptions.preview && (!this.currentOptions.image || this.currentOptions.thumbnailsRemainingCount)) {\n      this.openPreview(this.selectedIndex);\n    }\n  }\n  show(index) {\n    this.select(index);\n  }\n  showNext() {\n    this.image.showNext();\n  }\n  showPrev() {\n    this.image.showPrev();\n  }\n  canShowNext() {\n    if (this.images && this.currentOptions) {\n      return !!(this.currentOptions.imageInfinityMove || this.selectedIndex < this.images.length - 1);\n    } else {\n      return false;\n    }\n  }\n  canShowPrev() {\n    if (this.images && this.currentOptions) {\n      return !!(this.currentOptions.imageInfinityMove || this.selectedIndex > 0);\n    } else {\n      return false;\n    }\n  }\n  previewSelect(index) {\n    this.previewChange.emit({\n      index,\n      image: this.images[index]\n    });\n  }\n  moveThumbnailsRight() {\n    this.thumbnails.moveRight();\n  }\n  moveThumbnailsLeft() {\n    this.thumbnails.moveLeft();\n  }\n  canMoveThumbnailsRight() {\n    return this.thumbnails.canMoveRight();\n  }\n  canMoveThumbnailsLeft() {\n    return this.thumbnails.canMoveLeft();\n  }\n  resetThumbnails() {\n    if (this.thumbnails) {\n      this.thumbnails.reset(this.currentOptions.startIndex);\n    }\n  }\n  select(index) {\n    this.selectedIndex = index;\n    this.change.emit({\n      index,\n      image: this.images[index]\n    });\n  }\n  checkFullWidth() {\n    if (this.currentOptions && this.currentOptions.fullWidth) {\n      this.width = document.body.clientWidth + 'px';\n      this.left = 'translateX(' + -(document.body.clientWidth - this.myElement.nativeElement.parentNode.innerWidth) / 2 + 'px)';\n    }\n  }\n  setImages() {\n    this.images.forEach(img => img.type = this.helperService.getFileType(img.url || img.big || img.medium || img.small || ''));\n    this.smallImages = this.images.map(img => img.small);\n    this.mediumImages = this.images.map((img, i) => new NgxGalleryOrderedImage({\n      src: img.medium,\n      type: img.type,\n      index: i\n    }));\n    this.bigImages = this.images.map(img => img.big);\n    this.descriptions = this.images.map(img => img.description);\n    this.links = this.images.map(img => img.url);\n    this.labels = this.images.map(img => img.label);\n  }\n  setBreakpoint() {\n    this.prevBreakpoint = this.breakpoint;\n    let breakpoints;\n    if (typeof window !== 'undefined') {\n      breakpoints = this.options.filter(opt => opt.breakpoint >= window.innerWidth).map(opt => opt.breakpoint);\n    }\n    if (breakpoints && breakpoints.length) {\n      this.breakpoint = breakpoints.pop();\n    } else {\n      this.breakpoint = undefined;\n    }\n  }\n  sortOptions() {\n    this.options = [...this.options.filter(a => a.breakpoint === undefined), ...this.options.filter(a => a.breakpoint !== undefined).sort((a, b) => b.breakpoint - a.breakpoint)];\n  }\n  setOptions() {\n    this.currentOptions = new NgxGalleryOptions({});\n    this.options.filter(opt => opt.breakpoint === undefined || opt.breakpoint >= this.breakpoint).map(opt => this.combineOptions(this.currentOptions, opt));\n    this.width = this.currentOptions.width;\n    this.height = this.currentOptions.height;\n  }\n  combineOptions(first, second) {\n    Object.keys(second).map(val => first[val] = second[val] !== undefined ? second[val] : first[val]);\n  }\n  setAnimating(event) {\n    this.isAnimating = event;\n  }\n}\nNgxGalleryComponent.ɵfac = function NgxGalleryComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxGalleryComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NgxGalleryService));\n};\nNgxGalleryComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxGalleryComponent,\n  selectors: [[\"ngx-gallery\"]],\n  viewQuery: function NgxGalleryComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(NgxGalleryPreviewComponent, 5);\n      i0.ɵɵviewQuery(NgxGalleryImageComponent, 5);\n      i0.ɵɵviewQuery(NgxGalleryThumbnailsComponent, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.preview = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.image = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.thumbnails = _t.first);\n    }\n  },\n  hostVars: 6,\n  hostBindings: function NgxGalleryComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"resize\", function NgxGalleryComponent_resize_HostBindingHandler() {\n        return ctx.onResize();\n      }, false, i0.ɵɵresolveWindow);\n    }\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"width\", ctx.width)(\"height\", ctx.height)(\"transform\", ctx.left);\n    }\n  },\n  inputs: {\n    options: \"options\",\n    images: \"images\"\n  },\n  outputs: {\n    imagesReady: \"imagesReady\",\n    change: \"change\",\n    previewOpen: \"previewOpen\",\n    previewClose: \"previewClose\",\n    previewChange: \"previewChange\"\n  },\n  features: [i0.ɵɵProvidersFeature([NgxGalleryService])],\n  decls: 4,\n  vars: 41,\n  consts: [[3, \"height\", \"images\", \"clickable\", \"selectedIndex\", \"arrows\", \"arrowsAutoHide\", \"arrowPrevIcon\", \"arrowNextIcon\", \"swipe\", \"animation\", \"size\", \"autoPlay\", \"autoPlayInterval\", \"autoPlayPauseOnHover\", \"infinityMove\", \"lazyLoading\", \"actions\", \"descriptions\", \"showDescription\", \"bullets\", \"imageClick\", \"activeChange\", \"animating\", 4, \"ngIf\"], [3, \"marginTop\", \"marginBottom\", \"height\", \"images\", \"isAnimating\", \"links\", \"labels\", \"linkTarget\", \"selectedIndex\", \"columns\", \"rows\", \"margin\", \"arrows\", \"arrowsAutoHide\", \"arrowPrevIcon\", \"arrowNextIcon\", \"clickable\", \"swipe\", \"size\", \"moveSize\", \"order\", \"remainingCount\", \"lazyLoading\", \"actions\", \"ngClass\", \"activeChange\", 4, \"ngIf\"], [3, \"previewClose\", \"previewOpen\", \"activeChange\", \"images\", \"descriptions\", \"showDescription\", \"arrowPrevIcon\", \"arrowNextIcon\", \"closeIcon\", \"fullscreenIcon\", \"spinnerIcon\", \"arrows\", \"arrowsAutoHide\", \"swipe\", \"fullscreen\", \"forceFullscreen\", \"closeOnClick\", \"closeOnEsc\", \"keyboardNavigation\", \"animation\", \"autoPlay\", \"autoPlayInterval\", \"autoPlayPauseOnHover\", \"infinityMove\", \"zoom\", \"zoomStep\", \"zoomMax\", \"zoomMin\", \"zoomInIcon\", \"zoomOutIcon\", \"actions\", \"rotate\", \"rotateLeftIcon\", \"rotateRightIcon\", \"download\", \"downloadIcon\", \"bullets\"], [3, \"imageClick\", \"activeChange\", \"animating\", \"images\", \"clickable\", \"selectedIndex\", \"arrows\", \"arrowsAutoHide\", \"arrowPrevIcon\", \"arrowNextIcon\", \"swipe\", \"animation\", \"size\", \"autoPlay\", \"autoPlayInterval\", \"autoPlayPauseOnHover\", \"infinityMove\", \"lazyLoading\", \"actions\", \"descriptions\", \"showDescription\", \"bullets\"], [3, \"activeChange\", \"images\", \"isAnimating\", \"links\", \"labels\", \"linkTarget\", \"selectedIndex\", \"columns\", \"rows\", \"margin\", \"arrows\", \"arrowsAutoHide\", \"arrowPrevIcon\", \"arrowNextIcon\", \"clickable\", \"swipe\", \"size\", \"moveSize\", \"order\", \"remainingCount\", \"lazyLoading\", \"actions\", \"ngClass\"]],\n  template: function NgxGalleryComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\");\n      i0.ɵɵtemplate(1, NgxGalleryComponent_ngx_gallery_image_1_Template, 1, 21, \"ngx-gallery-image\", 0)(2, NgxGalleryComponent_ngx_gallery_thumbnails_2_Template, 1, 29, \"ngx-gallery-thumbnails\", 1);\n      i0.ɵɵelementStart(3, \"ngx-gallery-preview\", 2);\n      i0.ɵɵlistener(\"previewClose\", function NgxGalleryComponent_Template_ngx_gallery_preview_previewClose_3_listener() {\n        return ctx.onPreviewClose();\n      })(\"previewOpen\", function NgxGalleryComponent_Template_ngx_gallery_preview_previewOpen_3_listener() {\n        return ctx.onPreviewOpen();\n      })(\"activeChange\", function NgxGalleryComponent_Template_ngx_gallery_preview_activeChange_3_listener($event) {\n        return ctx.previewSelect($event);\n      });\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassMapInterpolate1(\"ngx-gallery-layout \", ctx.currentOptions == null ? null : ctx.currentOptions.layout, \"\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.currentOptions == null ? null : ctx.currentOptions.image);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.currentOptions == null ? null : ctx.currentOptions.thumbnails);\n      i0.ɵɵadvance();\n      i0.ɵɵclassProp(\"ngx-gallery-active\", ctx.previewEnabled);\n      i0.ɵɵproperty(\"images\", ctx.bigImages)(\"descriptions\", ctx.descriptions)(\"showDescription\", ctx.currentOptions == null ? null : ctx.currentOptions.previewDescription)(\"arrowPrevIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.arrowPrevIcon)(\"arrowNextIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.arrowNextIcon)(\"closeIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.closeIcon)(\"fullscreenIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.fullscreenIcon)(\"spinnerIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.spinnerIcon)(\"arrows\", ctx.currentOptions == null ? null : ctx.currentOptions.previewArrows)(\"arrowsAutoHide\", ctx.currentOptions == null ? null : ctx.currentOptions.previewArrowsAutoHide)(\"swipe\", ctx.currentOptions == null ? null : ctx.currentOptions.previewSwipe)(\"fullscreen\", ctx.currentOptions == null ? null : ctx.currentOptions.previewFullscreen)(\"forceFullscreen\", ctx.currentOptions == null ? null : ctx.currentOptions.previewForceFullscreen)(\"closeOnClick\", ctx.currentOptions == null ? null : ctx.currentOptions.previewCloseOnClick)(\"closeOnEsc\", ctx.currentOptions == null ? null : ctx.currentOptions.previewCloseOnEsc)(\"keyboardNavigation\", ctx.currentOptions == null ? null : ctx.currentOptions.previewKeyboardNavigation)(\"animation\", ctx.currentOptions == null ? null : ctx.currentOptions.previewAnimation)(\"autoPlay\", ctx.currentOptions == null ? null : ctx.currentOptions.previewAutoPlay)(\"autoPlayInterval\", ctx.currentOptions == null ? null : ctx.currentOptions.previewAutoPlayInterval)(\"autoPlayPauseOnHover\", ctx.currentOptions == null ? null : ctx.currentOptions.previewAutoPlayPauseOnHover)(\"infinityMove\", ctx.currentOptions == null ? null : ctx.currentOptions.previewInfinityMove)(\"zoom\", ctx.currentOptions == null ? null : ctx.currentOptions.previewZoom)(\"zoomStep\", ctx.currentOptions == null ? null : ctx.currentOptions.previewZoomStep)(\"zoomMax\", ctx.currentOptions == null ? null : ctx.currentOptions.previewZoomMax)(\"zoomMin\", ctx.currentOptions == null ? null : ctx.currentOptions.previewZoomMin)(\"zoomInIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.zoomInIcon)(\"zoomOutIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.zoomOutIcon)(\"actions\", ctx.currentOptions == null ? null : ctx.currentOptions.actions)(\"rotate\", ctx.currentOptions == null ? null : ctx.currentOptions.previewRotate)(\"rotateLeftIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.rotateLeftIcon)(\"rotateRightIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.rotateRightIcon)(\"download\", ctx.currentOptions == null ? null : ctx.currentOptions.previewDownload)(\"downloadIcon\", ctx.currentOptions == null ? null : ctx.currentOptions.downloadIcon)(\"bullets\", ctx.currentOptions == null ? null : ctx.currentOptions.previewBullets);\n    }\n  },\n  dependencies: [NgxGalleryImageComponent, NgxGalleryThumbnailsComponent, NgxGalleryPreviewComponent, i6.NgIf, i6.NgClass],\n  styles: [\":host{display:inline-block}:host>*{float:left}.ngx-gallery-layout{width:100%;height:100%;display:flex;flex-direction:column}.ngx-gallery-layout.thumbnails-top ngx-gallery-image{order:2}.ngx-gallery-layout.thumbnails-top ngx-gallery-thumbnails{order:1}.ngx-gallery-layout.thumbnails-bottom ngx-gallery-image{order:1}.ngx-gallery-layout.thumbnails-bottom ngx-gallery-thumbnails{order:2}*{box-sizing:border-box}.ngx-gallery-icon{color:#fff;position:absolute;display:inline-block}.ngx-gallery-icon .ngx-gallery-icon-content{display:block}ngx-gallery-preview{font-size:25px}ngx-gallery-preview .ngx-gallery-icon{z-index:2000}.ngx-gallery-clickable{cursor:pointer}.ngx-gallery-icons-wrapper .ngx-gallery-icon{position:relative;margin-right:5px;margin-top:5px;font-size:20px;cursor:pointer}.ngx-gallery-icons-wrapper{float:right}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGalleryComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-gallery',\n      encapsulation: ViewEncapsulation.None,\n      providers: [NgxGalleryService],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"ngx-gallery-layout {{currentOptions?.layout}}\\\">\\n  <ngx-gallery-image *ngIf=\\\"currentOptions?.image\\\" [style.height]=\\\"getImageHeight()\\\" [images]=\\\"mediumImages\\\"\\n                     [clickable]=\\\"currentOptions?.preview\\\" [selectedIndex]=\\\"selectedIndex\\\"\\n                     [arrows]=\\\"currentOptions?.imageArrows\\\" [arrowsAutoHide]=\\\"currentOptions?.imageArrowsAutoHide\\\"\\n                     [arrowPrevIcon]=\\\"currentOptions?.arrowPrevIcon\\\" [arrowNextIcon]=\\\"currentOptions?.arrowNextIcon\\\"\\n                     [swipe]=\\\"currentOptions?.imageSwipe\\\" [animation]=\\\"currentOptions?.imageAnimation\\\"\\n                     [size]=\\\"currentOptions?.imageSize\\\" [autoPlay]=\\\"currentOptions?.imageAutoPlay\\\"\\n                     [autoPlayInterval]=\\\"currentOptions?.imageAutoPlayInterval\\\"\\n                     [autoPlayPauseOnHover]=\\\"currentOptions?.imageAutoPlayPauseOnHover\\\"\\n                     [infinityMove]=\\\"currentOptions?.imageInfinityMove\\\" [lazyLoading]=\\\"currentOptions?.lazyLoading\\\"\\n                     [actions]=\\\"currentOptions?.imageActions\\\" [descriptions]=\\\"descriptions\\\"\\n                     [showDescription]=\\\"currentOptions?.imageDescription\\\" [bullets]=\\\"currentOptions?.imageBullets\\\"\\n                     (imageClick)=\\\"openPreview($event)\\\" (activeChange)=\\\"selectFromImage($event)\\\" (animating)=\\\"setAnimating($event)\\\"></ngx-gallery-image>\\n\\n  <ngx-gallery-thumbnails *ngIf=\\\"currentOptions?.thumbnails\\\" [style.marginTop]=\\\"getThumbnailsMarginTop()\\\"\\n                          [style.marginBottom]=\\\"getThumbnailsMarginBottom()\\\" [style.height]=\\\"getThumbnailsHeight()\\\"\\n                          [images]=\\\"smallImages\\\" [isAnimating]=\\\"isAnimating\\\" [links]=\\\"currentOptions?.thumbnailsAsLinks ? links : []\\\"\\n                          [labels]=\\\"labels\\\" [linkTarget]=\\\"currentOptions?.linkTarget\\\" [selectedIndex]=\\\"selectedIndex\\\"\\n                          [columns]=\\\"currentOptions?.thumbnailsColumns\\\" [rows]=\\\"currentOptions?.thumbnailsRows\\\"\\n                          [margin]=\\\"currentOptions?.thumbnailMargin\\\" [arrows]=\\\"currentOptions?.thumbnailsArrows\\\"\\n                          [arrowsAutoHide]=\\\"currentOptions?.thumbnailsArrowsAutoHide\\\"\\n                          [arrowPrevIcon]=\\\"currentOptions?.arrowPrevIcon\\\"\\n                          [arrowNextIcon]=\\\"currentOptions?.arrowNextIcon\\\"\\n                          [clickable]=\\\"currentOptions?.image || currentOptions?.preview\\\"\\n                          [swipe]=\\\"currentOptions?.thumbnailsSwipe\\\" [size]=\\\"currentOptions?.thumbnailSize\\\"\\n                          [moveSize]=\\\"currentOptions?.thumbnailsMoveSize\\\" [order]=\\\"currentOptions?.thumbnailsOrder\\\"\\n                          [remainingCount]=\\\"currentOptions?.thumbnailsRemainingCount\\\"\\n                          [lazyLoading]=\\\"currentOptions?.lazyLoading\\\" [actions]=\\\"currentOptions?.thumbnailActions\\\"\\n                          (activeChange)=\\\"selectFromThumbnails($event)\\\" [ngClass]=\\\"currentOptions?.thumbnailClasses\\\"></ngx-gallery-thumbnails>\\n\\n  <ngx-gallery-preview [images]=\\\"bigImages\\\" [descriptions]=\\\"descriptions\\\"\\n                       [showDescription]=\\\"currentOptions?.previewDescription\\\"\\n                       [arrowPrevIcon]=\\\"currentOptions?.arrowPrevIcon\\\" [arrowNextIcon]=\\\"currentOptions?.arrowNextIcon\\\"\\n                       [closeIcon]=\\\"currentOptions?.closeIcon\\\" [fullscreenIcon]=\\\"currentOptions?.fullscreenIcon\\\"\\n                       [spinnerIcon]=\\\"currentOptions?.spinnerIcon\\\" [arrows]=\\\"currentOptions?.previewArrows\\\"\\n                       [arrowsAutoHide]=\\\"currentOptions?.previewArrowsAutoHide\\\" [swipe]=\\\"currentOptions?.previewSwipe\\\"\\n                       [fullscreen]=\\\"currentOptions?.previewFullscreen\\\"\\n                       [forceFullscreen]=\\\"currentOptions?.previewForceFullscreen\\\"\\n                       [closeOnClick]=\\\"currentOptions?.previewCloseOnClick\\\"\\n                       [closeOnEsc]=\\\"currentOptions?.previewCloseOnEsc\\\"\\n                       [keyboardNavigation]=\\\"currentOptions?.previewKeyboardNavigation\\\"\\n                       [animation]=\\\"currentOptions?.previewAnimation\\\" [autoPlay]=\\\"currentOptions?.previewAutoPlay\\\"\\n                       [autoPlayInterval]=\\\"currentOptions?.previewAutoPlayInterval\\\"\\n                       [autoPlayPauseOnHover]=\\\"currentOptions?.previewAutoPlayPauseOnHover\\\"\\n                       [infinityMove]=\\\"currentOptions?.previewInfinityMove\\\" [zoom]=\\\"currentOptions?.previewZoom\\\"\\n                       [zoomStep]=\\\"currentOptions?.previewZoomStep\\\" [zoomMax]=\\\"currentOptions?.previewZoomMax\\\"\\n                       [zoomMin]=\\\"currentOptions?.previewZoomMin\\\" [zoomInIcon]=\\\"currentOptions?.zoomInIcon\\\"\\n                       [zoomOutIcon]=\\\"currentOptions?.zoomOutIcon\\\" [actions]=\\\"currentOptions?.actions\\\"\\n                       [rotate]=\\\"currentOptions?.previewRotate\\\" [rotateLeftIcon]=\\\"currentOptions?.rotateLeftIcon\\\"\\n                       [rotateRightIcon]=\\\"currentOptions?.rotateRightIcon\\\" [download]=\\\"currentOptions?.previewDownload\\\"\\n                       [downloadIcon]=\\\"currentOptions?.downloadIcon\\\" [bullets]=\\\"currentOptions?.previewBullets\\\"\\n                       (previewClose)=\\\"onPreviewClose()\\\" (previewOpen)=\\\"onPreviewOpen()\\\"\\n                       (activeChange)=\\\"previewSelect($event)\\\"\\n                       [class.ngx-gallery-active]=\\\"previewEnabled\\\"></ngx-gallery-preview>\\n</div>\\n\",\n      styles: [\":host{display:inline-block}:host>*{float:left}.ngx-gallery-layout{width:100%;height:100%;display:flex;flex-direction:column}.ngx-gallery-layout.thumbnails-top ngx-gallery-image{order:2}.ngx-gallery-layout.thumbnails-top ngx-gallery-thumbnails{order:1}.ngx-gallery-layout.thumbnails-bottom ngx-gallery-image{order:1}.ngx-gallery-layout.thumbnails-bottom ngx-gallery-thumbnails{order:2}*{box-sizing:border-box}.ngx-gallery-icon{color:#fff;position:absolute;display:inline-block}.ngx-gallery-icon .ngx-gallery-icon-content{display:block}ngx-gallery-preview{font-size:25px}ngx-gallery-preview .ngx-gallery-icon{z-index:2000}.ngx-gallery-clickable{cursor:pointer}.ngx-gallery-icons-wrapper .ngx-gallery-icon{position:relative;margin-right:5px;margin-top:5px;font-size:20px;cursor:pointer}.ngx-gallery-icons-wrapper{float:right}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: NgxGalleryService\n    }];\n  }, {\n    options: [{\n      type: Input\n    }],\n    images: [{\n      type: Input\n    }],\n    imagesReady: [{\n      type: Output\n    }],\n    change: [{\n      type: Output\n    }],\n    previewOpen: [{\n      type: Output\n    }],\n    previewClose: [{\n      type: Output\n    }],\n    previewChange: [{\n      type: Output\n    }],\n    preview: [{\n      type: ViewChild,\n      args: [NgxGalleryPreviewComponent]\n    }],\n    image: [{\n      type: ViewChild,\n      args: [NgxGalleryImageComponent]\n    }],\n    thumbnails: [{\n      type: ViewChild,\n      args: [NgxGalleryThumbnailsComponent]\n    }],\n    width: [{\n      type: HostBinding,\n      args: ['style.width']\n    }],\n    height: [{\n      type: HostBinding,\n      args: ['style.height']\n    }],\n    left: [{\n      type: HostBinding,\n      args: ['style.transform']\n    }],\n    onResize: [{\n      type: HostListener,\n      args: ['window:resize']\n    }]\n  });\n})();\nclass CustomHammerConfig extends HammerGestureConfig {\n  constructor() {\n    super(...arguments);\n    this.overrides = {\n      pinch: {\n        enable: false\n      },\n      rotate: {\n        enable: false\n      }\n    };\n  }\n}\nCustomHammerConfig.ɵfac = /* @__PURE__ */(() => {\n  let ɵCustomHammerConfig_BaseFactory;\n  return function CustomHammerConfig_Factory(__ngFactoryType__) {\n    return (ɵCustomHammerConfig_BaseFactory || (ɵCustomHammerConfig_BaseFactory = i0.ɵɵgetInheritedFactory(CustomHammerConfig)))(__ngFactoryType__ || CustomHammerConfig);\n  };\n})();\nCustomHammerConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CustomHammerConfig,\n  factory: CustomHammerConfig.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomHammerConfig, [{\n    type: Injectable\n  }], null, null);\n})();\nclass NgxGalleryModule {}\nNgxGalleryModule.ɵfac = function NgxGalleryModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxGalleryModule)();\n};\nNgxGalleryModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxGalleryModule\n});\nNgxGalleryModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: HAMMER_GESTURE_CONFIG,\n    useClass: CustomHammerConfig\n  }],\n  imports: [[CommonModule]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGalleryModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [NgxGalleryComponent, NgxGalleryImageComponent, NgxGalleryArrowsComponent, NgxGalleryThumbnailsComponent, NgxGalleryPreviewComponent, NgxGalleryActionComponent, NgxGalleryBulletsComponent],\n      imports: [CommonModule],\n      exports: [NgxGalleryComponent],\n      providers: [{\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: CustomHammerConfig\n      }]\n    }]\n  }], null, null);\n})();\nclass NgxGalleryImage {\n  constructor(obj) {\n    this.small = obj.small;\n    this.medium = obj.medium;\n    this.big = obj.big;\n    this.description = obj.description;\n    this.url = obj.url;\n    this.type = obj.type;\n    this.label = obj.label;\n  }\n}\n\n/*\n * Public API Surface of ngx-gallery\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CustomHammerConfig, NgxGalleryAction, NgxGalleryAnimation, NgxGalleryArrowsComponent, NgxGalleryBulletsComponent, NgxGalleryComponent, NgxGalleryImage, NgxGalleryImageSize, NgxGalleryLayout, NgxGalleryModule, NgxGalleryOptions, NgxGalleryOrder, NgxGalleryOrderedImage, NgxGalleryPreviewComponent, NgxGalleryService, NgxGalleryThumbnailsComponent };", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "Component", "ChangeDetectionStrategy", "Input", "Output", "ViewChild", "HostListener", "ViewEncapsulation", "HostBinding", "NgModule", "i1", "HammerGestureConfig", "HAMMER_GESTURE_CONFIG", "i6", "CommonModule", "trigger", "state", "style", "transition", "animate", "_c0", "a0", "NgxGalleryBulletsComponent_div_0_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "NgxGalleryBulletsComponent_div_0_Template_div_click_0_listener", "$event", "i_r2", "ɵɵrestoreView", "index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "handleChange", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "active", "_c1", "NgxGalleryPreviewComponent_ngx_gallery_arrows_0_Template", "NgxGalleryPreviewComponent_ngx_gallery_arrows_0_Template_ngx_gallery_arrows_prevClick_0_listener", "ctx_r1", "showPrev", "NgxGalleryPreviewComponent_ngx_gallery_arrows_0_Template_ngx_gallery_arrows_nextClick_0_listener", "showNext", "canShowPrev", "canShowNext", "arrowPrevIcon", "arrowNextIcon", "NgxGalleryPreviewComponent_ngx_gallery_action_3_Template", "_r3", "NgxGalleryPreviewComponent_ngx_gallery_action_3_Template_ngx_gallery_action_closeClick_0_listener", "action_r4", "$implicit", "onClick", "icon", "disabled", "titleText", "NgxGalleryPreviewComponent_a_4_Template", "ɵɵelement", "src", "ɵɵsanitizeUrl", "ɵɵadvance", "ɵɵclassMapInterpolate1", "downloadIcon", "NgxGalleryPreviewComponent_ngx_gallery_action_5_Template", "_r5", "NgxGalleryPreviewComponent_ngx_gallery_action_5_Template_ngx_gallery_action_closeClick_0_listener", "zoomOut", "zoomOutIcon", "canZoomOut", "NgxGalleryPreviewComponent_ngx_gallery_action_6_Template", "_r6", "NgxGalleryPreviewComponent_ngx_gallery_action_6_Template_ngx_gallery_action_closeClick_0_listener", "zoomIn", "zoomInIcon", "canZoomIn", "NgxGalleryPreviewComponent_ngx_gallery_action_7_Template", "_r7", "NgxGalleryPreviewComponent_ngx_gallery_action_7_Template_ngx_gallery_action_closeClick_0_listener", "rotateLeft", "rotateLeftIcon", "NgxGalleryPreviewComponent_ngx_gallery_action_8_Template", "_r8", "NgxGalleryPreviewComponent_ngx_gallery_action_8_Template_ngx_gallery_action_closeClick_0_listener", "rotateRight", "rotateRightIcon", "NgxGalleryPreviewComponent_ngx_gallery_action_9_Template", "_r9", "NgxGalleryPreviewComponent_ngx_gallery_action_9_Template_ngx_gallery_action_closeClick_0_listener", "manageFullscreen", "fullscreenIcon", "NgxGalleryPreviewComponent_img_15_Template", "_r10", "NgxGalleryPreviewComponent_img_15_Template_img_click_0_listener", "stopPropagation", "NgxGalleryPreviewComponent_img_15_Template_img_mouseenter_0_listener", "imageMouseEnter", "NgxGalleryPreviewComponent_img_15_Template_img_mouseleave_0_listener", "imageMouseLeave", "NgxGalleryPreviewComponent_img_15_Template_img_mousedown_0_listener", "mouseDownHandler", "NgxGalleryPreviewComponent_img_15_Template_img_touchstart_0_listener", "ɵɵstyleProp", "getTransform", "positionLeft", "positionTop", "ɵɵclassProp", "loading", "animation", "canDragOnZoom", "NgxGalleryPreviewComponent_video_16_Template", "_r11", "NgxGalleryPreviewComponent_video_16_Template_video_click_0_listener", "NgxGalleryPreviewComponent_video_16_Template_video_mouseenter_0_listener", "NgxGalleryPreviewComponent_video_16_Template_video_mouseleave_0_listener", "NgxGalleryPreviewComponent_video_16_Template_video_mousedown_0_listener", "NgxGalleryPreviewComponent_video_16_Template_video_touchstart_0_listener", "ɵɵtext", "NgxGalleryPreviewComponent_ngx_gallery_bullets_17_Template", "_r12", "NgxGalleryPreviewComponent_ngx_gallery_bullets_17_Template_ngx_gallery_bullets_bulletChange_0_listener", "showAtIndex", "images", "length", "NgxGalleryPreviewComponent_div_18_Template", "_r13", "NgxGalleryPreviewComponent_div_18_Template_div_click_0_listener", "description", "ɵɵsanitizeHtml", "_c2", "NgxGalleryImageComponent_ng_container_1_div_1_ngx_gallery_action_2_Template", "_r4", "NgxGalleryImageComponent_ng_container_1_div_1_ngx_gallery_action_2_Template_ngx_gallery_action_closeClick_0_listener", "action_r5", "image_r2", "NgxGalleryImageComponent_ng_container_1_div_1_div_3_Template", "NgxGalleryImageComponent_ng_container_1_div_1_div_3_Template_div_click_0_listener", "descriptions", "NgxGalleryImageComponent_ng_container_1_div_1_Template", "NgxGalleryImageComponent_ng_container_1_div_1_Template_div_click_0_listener", "handleClick", "NgxGalleryImageComponent_ng_container_1_div_1_Template_div_animation_animation_start_0_listener", "onStart", "NgxGalleryImageComponent_ng_container_1_div_1_Template_div_animation_animation_done_0_listener", "onDone", "ɵɵtemplate", "getSafeUrl", "clickable", "action", "actions", "showDescription", "NgxGalleryImageComponent_ng_container_1_div_2_ngx_gallery_action_5_Template", "NgxGalleryImageComponent_ng_container_1_div_2_ngx_gallery_action_5_Template_ngx_gallery_action_closeClick_0_listener", "action_r9", "NgxGalleryImageComponent_ng_container_1_div_2_div_6_Template", "NgxGalleryImageComponent_ng_container_1_div_2_div_6_Template_div_click_0_listener", "NgxGalleryImageComponent_ng_container_1_div_2_Template", "NgxGalleryImageComponent_ng_container_1_div_2_Template_div_click_0_listener", "NgxGalleryImageComponent_ng_container_1_div_2_Template_div_animation_animation_start_0_listener", "NgxGalleryImageComponent_ng_container_1_div_2_Template_div_animation_animation_done_0_listener", "NgxGalleryImageComponent_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "type", "_selectedIndex", "NgxGalleryImageComponent_ngx_gallery_bullets_2_Template", "NgxGalleryImageComponent_ngx_gallery_bullets_2_Template_ngx_gallery_bullets_bulletChange_0_listener", "show", "NgxGalleryImageComponent_ngx_gallery_arrows_3_Template", "NgxGalleryImageComponent_ngx_gallery_arrows_3_Template_ngx_gallery_arrows_prevClick_0_listener", "NgxGalleryImageComponent_ngx_gallery_arrows_3_Template_ngx_gallery_arrows_nextClick_0_listener", "size", "_c3", "a1", "NgxGalleryThumbnailsComponent_a_2_div_1_Template", "ctx_r3", "image_r5", "ɵɵpureFunction2", "selectedIndex", "NgxGalleryThumbnailsComponent_a_2_div_2_Template", "NgxGalleryThumbnailsComponent_a_2_ngx_gallery_action_4_Template", "NgxGalleryThumbnailsComponent_a_2_ngx_gallery_action_4_Template_ngx_gallery_action_closeClick_0_listener", "action_r7", "NgxGalleryThumbnailsComponent_a_2_div_5_Template", "ɵɵtextInterpolate1", "remainingCountValue", "NgxGalleryThumbnailsComponent_a_2_Template", "NgxGalleryThumbnailsComponent_a_2_Template_a_click_0_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getThumbnailHeight", "getThumbnailLeft", "getThumbnailTop", "hasLink", "links", "linkTarget", "ɵɵattribute", "labels", "getFileType", "remainingCount", "rows", "columns", "NgxGalleryThumbnailsComponent_ngx_gallery_arrows_3_Template", "NgxGalleryThumbnailsComponent_ngx_gallery_arrows_3_Template_ngx_gallery_arrows_prevClick_0_listener", "moveLeft", "NgxGalleryThumbnailsComponent_ngx_gallery_arrows_3_Template_ngx_gallery_arrows_nextClick_0_listener", "moveRight", "canMoveLeft", "canMoveRight", "_c4", "NgxGalleryComponent_ngx_gallery_image_1_Template", "NgxGalleryComponent_ngx_gallery_image_1_Template_ngx_gallery_image_imageClick_0_listener", "openPreview", "NgxGalleryComponent_ngx_gallery_image_1_Template_ngx_gallery_image_activeChange_0_listener", "selectFromImage", "NgxGalleryComponent_ngx_gallery_image_1_Template_ngx_gallery_image_animating_0_listener", "setAnimating", "getImageHeight", "mediumImages", "currentOptions", "preview", "imageArrows", "imageArrowsAutoHide", "imageSwipe", "imageAnimation", "imageSize", "imageAutoPlay", "imageAutoPlayInterval", "imageAutoPlayPauseOnHover", "imageInfinityMove", "lazyLoading", "imageActions", "imageDescription", "imageBullets", "NgxGalleryComponent_ngx_gallery_thumbnails_2_Template", "NgxGalleryComponent_ngx_gallery_thumbnails_2_Template_ngx_gallery_thumbnails_activeChange_0_listener", "selectFromThumbnails", "getThumbnailsMarginTop", "getThumbnailsMarginBottom", "getThumbnailsHeight", "smallImages", "isAnimating", "thumbnailsAsLinks", "ɵɵpureFunction0", "thumbnailsColumns", "thumbnailsRows", "thumbnail<PERSON>argin", "thumbnailsArrows", "thumbnailsArrowsAutoHide", "image", "thumbnailsSwipe", "thumbnailSize", "thumbnailsMoveSize", "thumbnailsOrder", "thumbnailsR<PERSON>iningCount", "thumbnailActions", "thumbnailClasses", "NgxGalleryService", "constructor", "renderer", "swipeHandlers", "Map", "manageSwipe", "status", "element", "id", "<PERSON><PERSON><PERSON><PERSON>", "prev<PERSON><PERSON><PERSON>", "handlers", "getSwipeHandlers", "set", "listen", "nativeElement", "map", "handler", "removeSwipeHandlers", "e", "validateUrl", "url", "replace", "RegExp", "getBackgroundUrl", "fileSource", "fileExtension", "split", "pop", "toLowerCase", "get", "delete", "ɵfac", "NgxGalleryService_Factory", "__ngFactoryType__", "ɵɵinject", "Renderer2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "NgxGalleryArrowsComponent", "prevClick", "nextClick", "handlePrevClick", "emit", "handleNextClick", "NgxGalleryArrowsComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "inputs", "prevDisabled", "nextDisabled", "outputs", "decls", "vars", "consts", "template", "NgxGalleryArrowsComponent_Template", "NgxGalleryArrowsComponent_Template_div_click_1_listener", "NgxGalleryArrowsComponent_Template_div_click_4_listener", "styles", "changeDetection", "selector", "OnPush", "NgxGalleryActionComponent", "closeClick", "event", "preventDefault", "NgxGalleryActionComponent_Factory", "NgxGalleryActionComponent_Template", "NgxGalleryActionComponent_Template_div_click_0_listener", "ɵɵpropertyInterpolate", "NgxGalleryBulletsComponent", "bulletChange", "getBullets", "Array", "count", "NgxGalleryBulletsComponent_Factory", "NgxGalleryBulletsComponent_Template", "dependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ng<PERSON><PERSON>", "NgxGalleryPreviewComponent", "sanitization", "elementRef", "helperService", "changeDetectorRef", "showSpinner", "zoomValue", "rotateValue", "previewOpen", "previewClose", "activeChange", "isOpen", "initialX", "initialY", "initialLeft", "initialTop", "isMove", "ngOnInit", "arrows", "arrowsAutoHide", "ngOnChanges", "changes", "swipe", "ngOnDestroy", "keyDownListener", "onMouseEnter", "onMouseLeave", "onKeyDown", "keyboardNavigation", "isKeyboardPrev", "isKeyboardNext", "closeOnEsc", "isKeyboardEsc", "close", "open", "forceFullscreen", "video", "previewImage", "currentTime", "paused", "ended", "readyState", "pause", "closeFullscreen", "stopAutoPlay", "autoPlay", "autoPlayPauseOnHover", "startAutoPlay", "timer", "setTimeout", "autoPlayInterval", "clearTimeout", "infinityMove", "fullscreen", "doc", "document", "fullscreenElement", "mozFullScreenElement", "webkitFullscreenElement", "msFullscreenElement", "openFullscreen", "bypassSecurityTrustUrl", "zoomStep", "zoomMax", "zoomMin", "resetPosition", "bypassSecurityTrustStyle", "zoom", "getClientX", "getClientY", "mouseUpHandler", "mouseMoveHandler", "touches", "clientX", "clientY", "keyCode", "documentElement", "requestFullscreen", "msRequestFullscreen", "mozRequestFullScreen", "webkitRequestFullscreen", "isFullscreen", "exitFullscreen", "msExitFullscreen", "mozCancelFullScreen", "webkitExitFullscreen", "first", "_show", "srcIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isLoaded", "onload", "img", "complete", "naturalWidth", "NgxGalleryPreviewComponent_Factory", "ɵɵdirectiveInject", "Dom<PERSON><PERSON><PERSON>zer", "ElementRef", "ChangeDetectorRef", "viewQuery", "NgxGalleryPreviewComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostBindings", "NgxGalleryPreviewComponent_HostBindings", "NgxGalleryPreviewComponent_mouseenter_HostBindingHandler", "NgxGalleryPreviewComponent_mouseleave_HostBindingHandler", "closeOnClick", "closeIcon", "spinnerIcon", "rotate", "download", "bullets", "features", "ɵɵNgOnChangesFeature", "NgxGalleryPreviewComponent_Template", "NgxGalleryPreviewComponent_Template_ngx_gallery_action_closeClick_10_listener", "NgxGalleryPreviewComponent_Template_div_click_13_listener", "NgxGalleryPreviewComponent_Template_div_mouseup_13_listener", "NgxGalleryPreviewComponent_Template_div_mousemove_13_listener", "NgxGalleryPreviewComponent_Template_div_touchend_13_listener", "NgxGalleryPreviewComponent_Template_div_touchmove_13_listener", "NgIf", "NgxGalleryAnimation", "Fade", "Slide", "Rotate", "Zoom", "NgxGalleryImageComponent", "imageClick", "animating", "canChangeImage", "setAction", "reset", "getImages", "indexes", "prevIndex", "push", "nextIndex", "filter", "i", "indexOf", "setInterval", "clearInterval", "setChangeTimeout", "detectChanges", "timeout", "toString", "NgxGalleryImageComponent_Factory", "NgxGalleryImageComponent_HostBindings", "NgxGalleryImageComponent_mouseenter_HostBindingHandler", "NgxGalleryImageComponent_mouseleave_HostBindingHandler", "NgxGalleryImageComponent_Template", "ɵɵclassMapInterpolate2", "data", "transform", "opacity", "animations", "NgxGalleryOrder", "Column", "Row", "Page", "NgxGalleryThumbnailsComponent", "minStopIndex", "validateIndex", "mouseenter", "setDefaultPosition", "slice", "order", "stopIndex", "moveSize", "maxIndex", "getMaxIndex", "setThumbnailsPosition", "calculatedIndex", "Math", "floor", "ceil", "getThumbnailPosition", "getThumbnailDimension", "thumbnailsLeft", "thumbnailsMarginLeft", "margin", "canShowArrows", "getVisibleCount", "newIndex", "getSafeStyle", "value", "NgxGalleryThumbnailsComponent_Factory", "NgxGalleryThumbnailsComponent_HostBindings", "NgxGalleryThumbnailsComponent_mouseenter_HostBindingHandler", "NgxGalleryThumbnailsComponent_mouseleave_HostBindingHandler", "NgxGalleryThumbnailsComponent_Template", "NgxGalleryAction", "NgxGalleryLayout", "ThumbnailsTop", "ThumbnailsBottom", "NgxGalleryImageSize", "Cover", "Contain", "NgxGalleryOptions", "obj", "preventDefaults", "breakpoint", "undefined", "use", "source", "defaultValue", "width", "height", "fullWidth", "layout", "startIndex", "imagePercent", "thumbnails", "thumbnailsPercent", "thumbnails<PERSON>argin", "thumbnailsAutoHide", "previewDescription", "previewArrows", "previewArrowsAutoHide", "previewSwipe", "previewFullscreen", "previewForceFullscreen", "previewCloseOnClick", "previewCloseOnEsc", "previewKeyboardNavigation", "previewAnimation", "previewAutoPlay", "previewAutoPlayInterval", "previewAutoPlayPauseOnHover", "previewInfinityMove", "previewZoom", "previewZoomStep", "previewZoomMax", "previewZoomMin", "previewRotate", "previewDownload", "previewCustom", "previewBullets", "NgxGalleryOrderedImage", "NgxGalleryComponent", "myElement", "options", "imagesReady", "change", "previewChange", "oldImages<PERSON><PERSON><PERSON>", "prevBreakpoint", "opt", "sortOptions", "setBreakpoint", "setOptions", "checkFull<PERSON><PERSON>th", "ngDoCheck", "oldImages", "setImages", "resetThumbnails", "ngAfterViewInit", "onResize", "fullWidthTimeout", "previewEnabled", "onPreviewOpen", "onPreviewClose", "select", "previewSelect", "moveThumbnailsRight", "moveThumbnailsLeft", "canMoveThumbnailsRight", "canMoveThumbnailsLeft", "body", "clientWidth", "left", "parentNode", "innerWidth", "for<PERSON>ach", "big", "medium", "small", "bigImages", "label", "breakpoints", "window", "a", "sort", "b", "combineOptions", "second", "Object", "keys", "val", "NgxGalleryComponent_Factory", "NgxGalleryComponent_Query", "hostVars", "NgxGalleryComponent_HostBindings", "NgxGalleryComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "ɵɵProvidersFeature", "NgxGalleryComponent_Template", "NgxGalleryComponent_Template_ngx_gallery_preview_previewClose_3_listener", "NgxGalleryComponent_Template_ngx_gallery_preview_previewOpen_3_listener", "NgxGalleryComponent_Template_ngx_gallery_preview_activeChange_3_listener", "encapsulation", "None", "providers", "CustomHammerConfig", "arguments", "overrides", "pinch", "enable", "ɵCustomHammerConfig_BaseFactory", "CustomHammerConfig_Factory", "ɵɵgetInheritedFactory", "NgxGalleryModule", "NgxGalleryModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "provide", "useClass", "imports", "declarations", "exports", "NgxGalleryImage"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@kolkov/ngx-gallery/fesm2020/kolkov-ngx-gallery.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ViewChild, HostListener, ViewEncapsulation, HostBinding, NgModule } from '@angular/core';\nimport * as i1 from '@angular/platform-browser';\nimport { HammerGestureConfig, HAMMER_GESTURE_CONFIG } from '@angular/platform-browser';\nimport * as i6 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\n\nclass NgxGalleryService {\n    constructor(renderer) {\n        this.renderer = renderer;\n        this.swipeHandlers = new Map();\n    }\n    manageSwipe(status, element, id, nextHandler, prevHandler) {\n        const handlers = this.getSwipeHandlers(id);\n        // swipeleft and swiperight are available only if hammerjs is included\n        try {\n            if (status && !handlers) {\n                this.swipeHandlers.set(id, [\n                    this.renderer.listen(element.nativeElement, 'swipeleft', () => nextHandler()),\n                    this.renderer.listen(element.nativeElement, 'swiperight', () => prevHandler())\n                ]);\n            }\n            else if (!status && handlers) {\n                handlers.map((handler) => handler());\n                this.removeSwipeHandlers(id);\n            }\n        }\n        catch (e) {\n        }\n    }\n    validateUrl(url) {\n        if (url.replace) {\n            return url.replace(new RegExp(' ', 'g'), '%20')\n                .replace(new RegExp('\\'', 'g'), '%27');\n        }\n        else {\n            return url;\n        }\n    }\n    getBackgroundUrl(image) {\n        return 'url(\\'' + this.validateUrl(image) + '\\')';\n    }\n    getFileType(fileSource) {\n        const fileExtension = fileSource.split('.').pop().toLowerCase();\n        if (fileExtension === 'avi' || fileExtension === 'flv'\n            || fileExtension === 'wmv' || fileExtension === 'mov'\n            || fileExtension === 'mp4') {\n            return 'video';\n        }\n        return 'image';\n    }\n    getSwipeHandlers(id) {\n        return this.swipeHandlers.get(id);\n    }\n    removeSwipeHandlers(id) {\n        this.swipeHandlers.delete(id);\n    }\n}\nNgxGalleryService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryService, deps: [{ token: i0.Renderer2 }], target: i0.ɵɵFactoryTarget.Injectable });\nNgxGalleryService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }]; } });\n\nclass NgxGalleryArrowsComponent {\n    constructor() {\n        this.prevClick = new EventEmitter();\n        this.nextClick = new EventEmitter();\n    }\n    handlePrevClick() {\n        this.prevClick.emit();\n    }\n    handleNextClick() {\n        this.nextClick.emit();\n    }\n}\nNgxGalleryArrowsComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryArrowsComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxGalleryArrowsComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.3\", type: NgxGalleryArrowsComponent, selector: \"ngx-gallery-arrows\", inputs: { prevDisabled: \"prevDisabled\", nextDisabled: \"nextDisabled\", arrowPrevIcon: \"arrowPrevIcon\", arrowNextIcon: \"arrowNextIcon\" }, outputs: { prevClick: \"prevClick\", nextClick: \"nextClick\" }, ngImport: i0, template: \"<div class=\\\"ngx-gallery-arrows-wrapper ngx-gallery-arrow-left\\\">\\n  <div class=\\\"ngx-gallery-icon ngx-gallery-arrow\\\" aria-hidden=\\\"true\\\" (click)=\\\"handlePrevClick()\\\" [class.ngx-gallery-disabled]=\\\"prevDisabled\\\">\\n    <i class=\\\"ngx-gallery-icon-content {{arrowPrevIcon}}\\\"></i>\\n  </div>\\n</div>\\n<div class=\\\"ngx-gallery-arrows-wrapper ngx-gallery-arrow-right\\\">\\n  <div class=\\\"ngx-gallery-icon ngx-gallery-arrow\\\" aria-hidden=\\\"true\\\" (click)=\\\"handleNextClick()\\\" [class.ngx-gallery-disabled]=\\\"nextDisabled\\\">\\n    <i class=\\\"ngx-gallery-icon-content {{arrowNextIcon}}\\\"></i>\\n  </div>\\n</div>\\n\", styles: [\".ngx-gallery-arrow-wrapper{position:absolute;height:100%;width:1px;display:table;table-layout:fixed}.ngx-gallery-preview-img-wrapper .ngx-gallery-arrow-wrapper{z-index:10001}.ngx-gallery-arrow-left{left:0}.ngx-gallery-arrow-right{right:0}.ngx-gallery-arrow{top:50%;transform:translateY(-50%);cursor:pointer}.ngx-gallery-arrow.ngx-gallery-disabled{opacity:.6;cursor:default}.ngx-gallery-arrow-left .ngx-gallery-arrow{left:10px}.ngx-gallery-arrow-right .ngx-gallery-arrow{right:10px}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryArrowsComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-gallery-arrows', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"ngx-gallery-arrows-wrapper ngx-gallery-arrow-left\\\">\\n  <div class=\\\"ngx-gallery-icon ngx-gallery-arrow\\\" aria-hidden=\\\"true\\\" (click)=\\\"handlePrevClick()\\\" [class.ngx-gallery-disabled]=\\\"prevDisabled\\\">\\n    <i class=\\\"ngx-gallery-icon-content {{arrowPrevIcon}}\\\"></i>\\n  </div>\\n</div>\\n<div class=\\\"ngx-gallery-arrows-wrapper ngx-gallery-arrow-right\\\">\\n  <div class=\\\"ngx-gallery-icon ngx-gallery-arrow\\\" aria-hidden=\\\"true\\\" (click)=\\\"handleNextClick()\\\" [class.ngx-gallery-disabled]=\\\"nextDisabled\\\">\\n    <i class=\\\"ngx-gallery-icon-content {{arrowNextIcon}}\\\"></i>\\n  </div>\\n</div>\\n\", styles: [\".ngx-gallery-arrow-wrapper{position:absolute;height:100%;width:1px;display:table;table-layout:fixed}.ngx-gallery-preview-img-wrapper .ngx-gallery-arrow-wrapper{z-index:10001}.ngx-gallery-arrow-left{left:0}.ngx-gallery-arrow-right{right:0}.ngx-gallery-arrow{top:50%;transform:translateY(-50%);cursor:pointer}.ngx-gallery-arrow.ngx-gallery-disabled{opacity:.6;cursor:default}.ngx-gallery-arrow-left .ngx-gallery-arrow{left:10px}.ngx-gallery-arrow-right .ngx-gallery-arrow{right:10px}\\n\"] }]\n        }], ctorParameters: function () { return []; }, propDecorators: { prevDisabled: [{\n                type: Input\n            }], nextDisabled: [{\n                type: Input\n            }], arrowPrevIcon: [{\n                type: Input\n            }], arrowNextIcon: [{\n                type: Input\n            }], prevClick: [{\n                type: Output\n            }], nextClick: [{\n                type: Output\n            }] } });\n\nclass NgxGalleryActionComponent {\n    constructor() {\n        this.disabled = false;\n        this.titleText = '';\n        this.closeClick = new EventEmitter();\n    }\n    handleClick(event) {\n        if (!this.disabled) {\n            this.closeClick.emit(event);\n        }\n        event.stopPropagation();\n        event.preventDefault();\n    }\n}\nNgxGalleryActionComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryActionComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxGalleryActionComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.3\", type: NgxGalleryActionComponent, selector: \"ngx-gallery-action\", inputs: { icon: \"icon\", disabled: \"disabled\", titleText: \"titleText\" }, outputs: { closeClick: \"closeClick\" }, ngImport: i0, template: \"<div class=\\\"ngx-gallery-icon\\\" [class.ngx-gallery-icon-disabled]=\\\"disabled\\\"\\n     aria-hidden=\\\"true\\\"\\n     title=\\\"{{ titleText }}\\\"\\n     (click)=\\\"handleClick($event)\\\">\\n  <i class=\\\"ngx-gallery-icon-content {{ icon }}\\\"></i>\\n</div>\\n\", styles: [\".ngx-gallery-icon{color:#fff;font-size:25px;position:absolute;z-index:2000;display:inline-block}.ngx-gallery-icon{position:relative;margin-right:10px;margin-top:10px;font-size:25px;cursor:pointer;text-decoration:none}.ngx-gallery-icon .ngx-gallery-icon-content{display:block}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryActionComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-gallery-action', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"ngx-gallery-icon\\\" [class.ngx-gallery-icon-disabled]=\\\"disabled\\\"\\n     aria-hidden=\\\"true\\\"\\n     title=\\\"{{ titleText }}\\\"\\n     (click)=\\\"handleClick($event)\\\">\\n  <i class=\\\"ngx-gallery-icon-content {{ icon }}\\\"></i>\\n</div>\\n\", styles: [\".ngx-gallery-icon{color:#fff;font-size:25px;position:absolute;z-index:2000;display:inline-block}.ngx-gallery-icon{position:relative;margin-right:10px;margin-top:10px;font-size:25px;cursor:pointer;text-decoration:none}.ngx-gallery-icon .ngx-gallery-icon-content{display:block}\\n\"] }]\n        }], ctorParameters: function () { return []; }, propDecorators: { icon: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], titleText: [{\n                type: Input\n            }], closeClick: [{\n                type: Output\n            }] } });\n\nclass NgxGalleryBulletsComponent {\n    constructor() {\n        this.active = 0;\n        this.bulletChange = new EventEmitter();\n    }\n    getBullets() {\n        return Array(this.count);\n    }\n    handleChange(event, index) {\n        this.bulletChange.emit(index);\n    }\n}\nNgxGalleryBulletsComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryBulletsComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nNgxGalleryBulletsComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.3\", type: NgxGalleryBulletsComponent, selector: \"ngx-gallery-bullets\", inputs: { count: \"count\", active: \"active\" }, outputs: { bulletChange: \"bulletChange\" }, ngImport: i0, template: \"<div class=\\\"ngx-gallery-bullet\\\" *ngFor=\\\"let bullet of getBullets(); let i = index;\\\" (click)=\\\"handleChange($event, i)\\\"\\n     [ngClass]=\\\"{ 'ngx-gallery-active': i === active }\\\"></div>\\n\", styles: [\":host{position:absolute;z-index:2000;display:inline-flex;left:50%;transform:translate(-50%);bottom:0;padding:10px}.ngx-gallery-bullet{width:10px;height:10px;border-radius:50%;cursor:pointer;background:white}.ngx-gallery-bullet:not(:first-child){margin-left:5px}.ngx-gallery-bullet:hover,.ngx-gallery-bullet.ngx-gallery-active{background:black}\\n\"], directives: [{ type: i6.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { type: i6.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryBulletsComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-gallery-bullets', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"ngx-gallery-bullet\\\" *ngFor=\\\"let bullet of getBullets(); let i = index;\\\" (click)=\\\"handleChange($event, i)\\\"\\n     [ngClass]=\\\"{ 'ngx-gallery-active': i === active }\\\"></div>\\n\", styles: [\":host{position:absolute;z-index:2000;display:inline-flex;left:50%;transform:translate(-50%);bottom:0;padding:10px}.ngx-gallery-bullet{width:10px;height:10px;border-radius:50%;cursor:pointer;background:white}.ngx-gallery-bullet:not(:first-child){margin-left:5px}.ngx-gallery-bullet:hover,.ngx-gallery-bullet.ngx-gallery-active{background:black}\\n\"] }]\n        }], ctorParameters: function () { return []; }, propDecorators: { count: [{\n                type: Input\n            }], active: [{\n                type: Input\n            }], bulletChange: [{\n                type: Output\n            }] } });\n\nclass NgxGalleryPreviewComponent {\n    constructor(sanitization, elementRef, helperService, renderer, changeDetectorRef) {\n        this.sanitization = sanitization;\n        this.elementRef = elementRef;\n        this.helperService = helperService;\n        this.renderer = renderer;\n        this.changeDetectorRef = changeDetectorRef;\n        this.showSpinner = false;\n        this.positionLeft = 0;\n        this.positionTop = 0;\n        this.zoomValue = 1;\n        this.loading = false;\n        this.rotateValue = 0;\n        this.index = 0;\n        this.previewOpen = new EventEmitter();\n        this.previewClose = new EventEmitter();\n        this.activeChange = new EventEmitter();\n        this.isOpen = false;\n        this.initialX = 0;\n        this.initialY = 0;\n        this.initialLeft = 0;\n        this.initialTop = 0;\n        this.isMove = false;\n    }\n    ngOnInit() {\n        if (this.arrows && this.arrowsAutoHide) {\n            this.arrows = false;\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes['swipe']) {\n            this.helperService.manageSwipe(this.swipe, this.elementRef, 'preview', () => this.showNext(), () => this.showPrev());\n        }\n    }\n    ngOnDestroy() {\n        if (this.keyDownListener) {\n            this.keyDownListener();\n        }\n    }\n    onMouseEnter() {\n        if (this.arrowsAutoHide && !this.arrows) {\n            this.arrows = true;\n        }\n    }\n    onMouseLeave() {\n        if (this.arrowsAutoHide && this.arrows) {\n            this.arrows = false;\n        }\n    }\n    onKeyDown(e) {\n        if (this.isOpen) {\n            if (this.keyboardNavigation) {\n                if (this.isKeyboardPrev(e)) {\n                    this.showPrev();\n                }\n                else if (this.isKeyboardNext(e)) {\n                    this.showNext();\n                }\n            }\n            if (this.closeOnEsc && this.isKeyboardEsc(e)) {\n                this.close();\n            }\n        }\n    }\n    open(index) {\n        this.previewOpen.emit();\n        this.index = index;\n        this.isOpen = true;\n        this.show(true);\n        if (this.forceFullscreen) {\n            this.manageFullscreen();\n        }\n        this.keyDownListener = this.renderer.listen('window', 'keydown', (e) => this.onKeyDown(e));\n    }\n    close() {\n        this.isOpen = false;\n        const video = this.previewImage.nativeElement;\n        if (video.currentTime > 0 &&\n            !video.paused &&\n            !video.ended &&\n            video.readyState > 2) {\n            video.pause();\n        }\n        this.closeFullscreen();\n        this.previewClose.emit();\n        this.stopAutoPlay();\n        if (this.keyDownListener) {\n            this.keyDownListener();\n        }\n    }\n    imageMouseEnter() {\n        if (this.autoPlay && this.autoPlayPauseOnHover) {\n            this.stopAutoPlay();\n        }\n    }\n    imageMouseLeave() {\n        if (this.autoPlay && this.autoPlayPauseOnHover) {\n            this.startAutoPlay();\n        }\n    }\n    startAutoPlay() {\n        if (this.autoPlay) {\n            this.stopAutoPlay();\n            this.timer = setTimeout(() => {\n                if (!this.showNext()) {\n                    this.index = -1;\n                    this.showNext();\n                }\n            }, this.autoPlayInterval);\n        }\n    }\n    stopAutoPlay() {\n        if (this.timer) {\n            clearTimeout(this.timer);\n        }\n    }\n    showAtIndex(index) {\n        this.index = index;\n        this.show();\n    }\n    showNext() {\n        if (this.canShowNext()) {\n            this.index++;\n            if (this.index === this.images.length) {\n                this.index = 0;\n            }\n            this.show();\n            return true;\n        }\n        else {\n            return false;\n        }\n    }\n    showPrev() {\n        if (this.canShowPrev()) {\n            this.index--;\n            if (this.index < 0) {\n                this.index = this.images.length - 1;\n            }\n            this.show();\n        }\n    }\n    canShowNext() {\n        if (this.loading) {\n            return false;\n        }\n        else if (this.images) {\n            return this.infinityMove || this.index < this.images.length - 1;\n        }\n        else {\n            return false;\n        }\n    }\n    canShowPrev() {\n        if (this.loading) {\n            return false;\n        }\n        else if (this.images) {\n            return this.infinityMove || this.index > 0;\n        }\n        else {\n            return false;\n        }\n    }\n    manageFullscreen() {\n        if (this.fullscreen || this.forceFullscreen) {\n            const doc = document;\n            if (!doc.fullscreenElement && !doc.mozFullScreenElement\n                && !doc.webkitFullscreenElement && !doc.msFullscreenElement) {\n                this.openFullscreen();\n            }\n            else {\n                this.closeFullscreen();\n            }\n        }\n    }\n    getSafeUrl(image) {\n        return this.sanitization.bypassSecurityTrustUrl(image);\n    }\n    getFileType(fileSource) {\n        return this.helperService.getFileType(fileSource);\n    }\n    zoomIn() {\n        if (this.canZoomIn()) {\n            this.zoomValue += this.zoomStep;\n            if (this.zoomValue > this.zoomMax) {\n                this.zoomValue = this.zoomMax;\n            }\n        }\n    }\n    zoomOut() {\n        if (this.canZoomOut()) {\n            this.zoomValue -= this.zoomStep;\n            if (this.zoomValue < this.zoomMin) {\n                this.zoomValue = this.zoomMin;\n            }\n            if (this.zoomValue <= 1) {\n                this.resetPosition();\n            }\n        }\n    }\n    rotateLeft() {\n        this.rotateValue -= 90;\n    }\n    rotateRight() {\n        this.rotateValue += 90;\n    }\n    getTransform() {\n        return this.sanitization.bypassSecurityTrustStyle('scale(' + this.zoomValue + ') rotate(' + this.rotateValue + 'deg)');\n    }\n    canZoomIn() {\n        return this.zoomValue < this.zoomMax;\n    }\n    canZoomOut() {\n        return this.zoomValue > this.zoomMin;\n    }\n    canDragOnZoom() {\n        return this.zoom && this.zoomValue > 1;\n    }\n    mouseDownHandler(e) {\n        if (this.canDragOnZoom()) {\n            this.initialX = this.getClientX(e);\n            this.initialY = this.getClientY(e);\n            this.initialLeft = this.positionLeft;\n            this.initialTop = this.positionTop;\n            this.isMove = true;\n            e.preventDefault();\n        }\n    }\n    mouseUpHandler(e) {\n        this.isMove = false;\n    }\n    mouseMoveHandler(e) {\n        if (this.isMove) {\n            this.positionLeft = this.initialLeft + (this.getClientX(e) - this.initialX);\n            this.positionTop = this.initialTop + (this.getClientY(e) - this.initialY);\n        }\n    }\n    getClientX(e) {\n        return e.touches && e.touches.length ? e.touches[0].clientX : e.clientX;\n    }\n    getClientY(e) {\n        return e.touches && e.touches.length ? e.touches[0].clientY : e.clientY;\n    }\n    resetPosition() {\n        if (this.zoom) {\n            this.positionLeft = 0;\n            this.positionTop = 0;\n        }\n    }\n    isKeyboardNext(e) {\n        return e.keyCode === 39;\n    }\n    isKeyboardPrev(e) {\n        return e.keyCode === 37;\n    }\n    isKeyboardEsc(e) {\n        return e.keyCode === 27;\n    }\n    openFullscreen() {\n        const element = document.documentElement;\n        if (element.requestFullscreen) {\n            element.requestFullscreen();\n        }\n        else if (element.msRequestFullscreen) {\n            element.msRequestFullscreen();\n        }\n        else if (element.mozRequestFullScreen) {\n            element.mozRequestFullScreen();\n        }\n        else if (element.webkitRequestFullscreen) {\n            element.webkitRequestFullscreen();\n        }\n    }\n    closeFullscreen() {\n        if (this.isFullscreen()) {\n            const doc = document;\n            if (doc.exitFullscreen) {\n                doc.exitFullscreen();\n            }\n            else if (doc.msExitFullscreen) {\n                doc.msExitFullscreen();\n            }\n            else if (doc.mozCancelFullScreen) {\n                doc.mozCancelFullScreen();\n            }\n            else if (doc.webkitExitFullscreen) {\n                doc.webkitExitFullscreen();\n            }\n        }\n    }\n    isFullscreen() {\n        const doc = document;\n        return doc.fullscreenElement || doc.webkitFullscreenElement\n            || doc.mozFullScreenElement || doc.msFullscreenElement;\n    }\n    show(first = false) {\n        this.loading = true;\n        this.stopAutoPlay();\n        this.activeChange.emit(this.index);\n        if (first || !this.animation) {\n            this._show();\n        }\n        else {\n            setTimeout(() => this._show(), 600);\n        }\n    }\n    _show() {\n        this.zoomValue = 1;\n        this.rotateValue = 0;\n        this.resetPosition();\n        this.src = this.getSafeUrl(this.images[this.index]);\n        this.type = this.getFileType(this.images[this.index]);\n        this.srcIndex = this.index;\n        this.description = this.descriptions[this.index];\n        this.changeDetectorRef.markForCheck();\n        setTimeout(() => {\n            if (this.isLoaded(this.previewImage.nativeElement) || this.type === 'video') {\n                this.loading = false;\n                this.startAutoPlay();\n                this.changeDetectorRef.markForCheck();\n            }\n            else if (this.type === 'video') {\n            }\n            else {\n                setTimeout(() => {\n                    if (this.loading) {\n                        this.showSpinner = true;\n                        this.changeDetectorRef.markForCheck();\n                    }\n                });\n                this.previewImage.nativeElement.onload = () => {\n                    this.loading = false;\n                    this.showSpinner = false;\n                    this.previewImage.nativeElement.onload = null;\n                    this.startAutoPlay();\n                    this.changeDetectorRef.markForCheck();\n                };\n            }\n        });\n    }\n    isLoaded(img) {\n        if (!img.complete) {\n            return false;\n        }\n        return !(typeof img.naturalWidth !== 'undefined' && img.naturalWidth === 0);\n    }\n}\nNgxGalleryPreviewComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryPreviewComponent, deps: [{ token: i1.DomSanitizer }, { token: i0.ElementRef }, { token: NgxGalleryService }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nNgxGalleryPreviewComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.3\", type: NgxGalleryPreviewComponent, selector: \"ngx-gallery-preview\", inputs: { images: \"images\", descriptions: \"descriptions\", showDescription: \"showDescription\", arrows: \"arrows\", arrowsAutoHide: \"arrowsAutoHide\", swipe: \"swipe\", fullscreen: \"fullscreen\", forceFullscreen: \"forceFullscreen\", closeOnClick: \"closeOnClick\", closeOnEsc: \"closeOnEsc\", keyboardNavigation: \"keyboardNavigation\", arrowPrevIcon: \"arrowPrevIcon\", arrowNextIcon: \"arrowNextIcon\", closeIcon: \"closeIcon\", fullscreenIcon: \"fullscreenIcon\", spinnerIcon: \"spinnerIcon\", autoPlay: \"autoPlay\", autoPlayInterval: \"autoPlayInterval\", autoPlayPauseOnHover: \"autoPlayPauseOnHover\", infinityMove: \"infinityMove\", zoom: \"zoom\", zoomStep: \"zoomStep\", zoomMax: \"zoomMax\", zoomMin: \"zoomMin\", zoomInIcon: \"zoomInIcon\", zoomOutIcon: \"zoomOutIcon\", animation: \"animation\", actions: \"actions\", rotate: \"rotate\", rotateLeftIcon: \"rotateLeftIcon\", rotateRightIcon: \"rotateRightIcon\", download: \"download\", downloadIcon: \"downloadIcon\", bullets: \"bullets\" }, outputs: { previewOpen: \"previewOpen\", previewClose: \"previewClose\", activeChange: \"activeChange\" }, host: { listeners: { \"mouseenter\": \"onMouseEnter()\", \"mouseleave\": \"onMouseLeave()\" } }, viewQueries: [{ propertyName: \"previewImage\", first: true, predicate: [\"previewImage\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<ngx-gallery-arrows *ngIf=\\\"arrows\\\" (prevClick)=\\\"showPrev()\\\" (nextClick)=\\\"showNext()\\\" [prevDisabled]=\\\"!canShowPrev()\\\"\\n                    [nextDisabled]=\\\"!canShowNext()\\\" [arrowPrevIcon]=\\\"arrowPrevIcon\\\"\\n                    [arrowNextIcon]=\\\"arrowNextIcon\\\"></ngx-gallery-arrows>\\n<div class=\\\"ngx-gallery-preview-top\\\">\\n  <div class=\\\"ngx-gallery-preview-icons\\\">\\n    <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                        [titleText]=\\\"action.titleText\\\" (closeClick)=\\\"action.onClick($event, index)\\\"></ngx-gallery-action>\\n    <a *ngIf=\\\"download && src\\\" [href]=\\\"src\\\" class=\\\"ngx-gallery-icon\\\" aria-hidden=\\\"true\\\" download>\\n      <i class=\\\"ngx-gallery-icon-content {{ downloadIcon }}\\\"></i>\\n    </a>\\n    <ngx-gallery-action *ngIf=\\\"zoom\\\" [icon]=\\\"zoomOutIcon\\\" [disabled]=\\\"!canZoomOut()\\\"\\n                        (closeClick)=\\\"zoomOut()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"zoom\\\" [icon]=\\\"zoomInIcon\\\" [disabled]=\\\"!canZoomIn()\\\"\\n                        (closeClick)=\\\"zoomIn()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"rotate\\\" [icon]=\\\"rotateLeftIcon\\\" (closeClick)=\\\"rotateLeft()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"rotate\\\" [icon]=\\\"rotateRightIcon\\\" (closeClick)=\\\"rotateRight()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"fullscreen\\\" [icon]=\\\"'ngx-gallery-fullscreen ' + fullscreenIcon\\\"\\n                        (closeClick)=\\\"manageFullscreen()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action [icon]=\\\"'ngx-gallery-close ' + closeIcon\\\" (closeClick)=\\\"close()\\\"></ngx-gallery-action>\\n  </div>\\n</div>\\n<div class=\\\"ngx-spinner-wrapper ngx-gallery-center\\\" [class.ngx-gallery-active]=\\\"showSpinner\\\">\\n  <i class=\\\"ngx-gallery-icon ngx-gallery-spinner {{spinnerIcon}}\\\" aria-hidden=\\\"true\\\"></i>\\n</div>\\n<div class=\\\"ngx-gallery-preview-wrapper\\\" (click)=\\\"closeOnClick && close()\\\" (mouseup)=\\\"mouseUpHandler($event)\\\"\\n     (mousemove)=\\\"mouseMoveHandler($event)\\\" (touchend)=\\\"mouseUpHandler($event)\\\" (touchmove)=\\\"mouseMoveHandler($event)\\\">\\n  <div class=\\\"ngx-gallery-preview-img-wrapper\\\">\\n    <img *ngIf=\\\"src && type === 'image'\\\" #previewImage class=\\\"ngx-gallery-preview-img ngx-gallery-center\\\" [src]=\\\"src\\\"\\n         (click)=\\\"$event.stopPropagation()\\\" (mouseenter)=\\\"imageMouseEnter()\\\" (mouseleave)=\\\"imageMouseLeave()\\\"\\n         (mousedown)=\\\"mouseDownHandler($event)\\\" (touchstart)=\\\"mouseDownHandler($event)\\\"\\n         [class.ngx-gallery-active]=\\\"!loading\\\" [class.animation]=\\\"animation\\\" [class.ngx-gallery-grab]=\\\"canDragOnZoom()\\\"\\n         [style.transform]=\\\"getTransform()\\\" [style.left]=\\\"positionLeft + 'px'\\\" [style.top]=\\\"positionTop + 'px'\\\"/>\\n    <video *ngIf=\\\"src && type === 'video'\\\"  #previewImage controls style=\\\"width: 100%; height: 100%;\\\"\\n    class=\\\"ngx-gallery-preview-img ngx-gallery-center\\\"\\n    (click)=\\\"$event.stopPropagation()\\\" (mouseenter)=\\\"imageMouseEnter()\\\" (mouseleave)=\\\"imageMouseLeave()\\\" (mousedown)=\\\"mouseDownHandler($event)\\\" (touchstart)=\\\"mouseDownHandler($event)\\\"\\n    [class.ngx-gallery-active]=\\\"!loading\\\" [class.animation]=\\\"animation\\\" [class.ngx-gallery-grab]=\\\"canDragOnZoom()\\\" [style.transform]=\\\"getTransform()\\\" [style.left]=\\\"positionLeft + 'px'\\\" [style.top]=\\\"positionTop + 'px'\\\">\\n      <source [src]=\\\"src\\\">\\n      Your browser does not support the video tag.\\n    </video>\\n    <ngx-gallery-bullets *ngIf=\\\"bullets\\\" [count]=\\\"images.length\\\" [active]=\\\"index\\\"\\n                         (bulletChange)=\\\"showAtIndex($event)\\\"></ngx-gallery-bullets>\\n  </div>\\n  <div class=\\\"ngx-gallery-preview-text\\\" *ngIf=\\\"showDescription && description\\\" [innerHTML]=\\\"description\\\"\\n       (click)=\\\"$event.stopPropagation()\\\"></div>\\n</div>\\n\", styles: [\":host.ngx-gallery-active{width:100%;height:100%;position:fixed;left:0;top:0;background:rgba(0,0,0,.7);z-index:10000;display:inline-block;font-size:50px!important}:host{display:none;font-size:50px!important}:host .ngx-gallery-arrow{font-size:50px!important}ngx-gallery-bullets{height:5%;align-items:center;padding:0}.ngx-gallery-preview-img{opacity:0;max-width:90%;max-height:90%;-webkit-user-select:none;user-select:none;transition:transform .5s}.ngx-gallery-preview-img.animation{transition:opacity .5s linear,transform .5s}.ngx-gallery-preview-img.ngx-gallery-active{opacity:1}.ngx-gallery-preview-img.ngx-gallery-grab{cursor:grab}.ngx-gallery-icon.ngx-gallery-spinner{font-size:50px;left:0;display:inline-block}:host .ngx-gallery-preview-top{position:absolute;width:100%;-webkit-user-select:none;user-select:none;font-size:25px}.ngx-gallery-preview-icons{float:right}.ngx-gallery-preview-icons .ngx-gallery-icon{position:relative;margin-right:10px;margin-top:10px;font-size:25px;cursor:pointer;text-decoration:none}.ngx-gallery-preview-icons .ngx-gallery-icon.ngx-gallery-icon-disabled{cursor:default;opacity:.4}.ngx-spinner-wrapper{width:50px;height:50px;display:none}.ngx-spinner-wrapper.ngx-gallery-active{display:inline-block}.ngx-gallery-center{position:absolute;left:0;right:0;bottom:0;margin:auto;top:0}.ngx-gallery-preview-text{width:100%;background:rgba(0,0,0,.7);padding:10px;text-align:center;color:#fff;font-size:16px;flex:0 1 auto;z-index:10}.ngx-gallery-preview-wrapper{width:100%;height:100%;display:flex;flex-flow:column}.ngx-gallery-preview-img-wrapper{flex:1 1 auto;position:relative}\\n\"], components: [{ type: NgxGalleryArrowsComponent, selector: \"ngx-gallery-arrows\", inputs: [\"prevDisabled\", \"nextDisabled\", \"arrowPrevIcon\", \"arrowNextIcon\"], outputs: [\"prevClick\", \"nextClick\"] }, { type: NgxGalleryActionComponent, selector: \"ngx-gallery-action\", inputs: [\"icon\", \"disabled\", \"titleText\"], outputs: [\"closeClick\"] }, { type: NgxGalleryBulletsComponent, selector: \"ngx-gallery-bullets\", inputs: [\"count\", \"active\"], outputs: [\"bulletChange\"] }], directives: [{ type: i6.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i6.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryPreviewComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-gallery-preview', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ngx-gallery-arrows *ngIf=\\\"arrows\\\" (prevClick)=\\\"showPrev()\\\" (nextClick)=\\\"showNext()\\\" [prevDisabled]=\\\"!canShowPrev()\\\"\\n                    [nextDisabled]=\\\"!canShowNext()\\\" [arrowPrevIcon]=\\\"arrowPrevIcon\\\"\\n                    [arrowNextIcon]=\\\"arrowNextIcon\\\"></ngx-gallery-arrows>\\n<div class=\\\"ngx-gallery-preview-top\\\">\\n  <div class=\\\"ngx-gallery-preview-icons\\\">\\n    <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                        [titleText]=\\\"action.titleText\\\" (closeClick)=\\\"action.onClick($event, index)\\\"></ngx-gallery-action>\\n    <a *ngIf=\\\"download && src\\\" [href]=\\\"src\\\" class=\\\"ngx-gallery-icon\\\" aria-hidden=\\\"true\\\" download>\\n      <i class=\\\"ngx-gallery-icon-content {{ downloadIcon }}\\\"></i>\\n    </a>\\n    <ngx-gallery-action *ngIf=\\\"zoom\\\" [icon]=\\\"zoomOutIcon\\\" [disabled]=\\\"!canZoomOut()\\\"\\n                        (closeClick)=\\\"zoomOut()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"zoom\\\" [icon]=\\\"zoomInIcon\\\" [disabled]=\\\"!canZoomIn()\\\"\\n                        (closeClick)=\\\"zoomIn()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"rotate\\\" [icon]=\\\"rotateLeftIcon\\\" (closeClick)=\\\"rotateLeft()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"rotate\\\" [icon]=\\\"rotateRightIcon\\\" (closeClick)=\\\"rotateRight()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action *ngIf=\\\"fullscreen\\\" [icon]=\\\"'ngx-gallery-fullscreen ' + fullscreenIcon\\\"\\n                        (closeClick)=\\\"manageFullscreen()\\\"></ngx-gallery-action>\\n    <ngx-gallery-action [icon]=\\\"'ngx-gallery-close ' + closeIcon\\\" (closeClick)=\\\"close()\\\"></ngx-gallery-action>\\n  </div>\\n</div>\\n<div class=\\\"ngx-spinner-wrapper ngx-gallery-center\\\" [class.ngx-gallery-active]=\\\"showSpinner\\\">\\n  <i class=\\\"ngx-gallery-icon ngx-gallery-spinner {{spinnerIcon}}\\\" aria-hidden=\\\"true\\\"></i>\\n</div>\\n<div class=\\\"ngx-gallery-preview-wrapper\\\" (click)=\\\"closeOnClick && close()\\\" (mouseup)=\\\"mouseUpHandler($event)\\\"\\n     (mousemove)=\\\"mouseMoveHandler($event)\\\" (touchend)=\\\"mouseUpHandler($event)\\\" (touchmove)=\\\"mouseMoveHandler($event)\\\">\\n  <div class=\\\"ngx-gallery-preview-img-wrapper\\\">\\n    <img *ngIf=\\\"src && type === 'image'\\\" #previewImage class=\\\"ngx-gallery-preview-img ngx-gallery-center\\\" [src]=\\\"src\\\"\\n         (click)=\\\"$event.stopPropagation()\\\" (mouseenter)=\\\"imageMouseEnter()\\\" (mouseleave)=\\\"imageMouseLeave()\\\"\\n         (mousedown)=\\\"mouseDownHandler($event)\\\" (touchstart)=\\\"mouseDownHandler($event)\\\"\\n         [class.ngx-gallery-active]=\\\"!loading\\\" [class.animation]=\\\"animation\\\" [class.ngx-gallery-grab]=\\\"canDragOnZoom()\\\"\\n         [style.transform]=\\\"getTransform()\\\" [style.left]=\\\"positionLeft + 'px'\\\" [style.top]=\\\"positionTop + 'px'\\\"/>\\n    <video *ngIf=\\\"src && type === 'video'\\\"  #previewImage controls style=\\\"width: 100%; height: 100%;\\\"\\n    class=\\\"ngx-gallery-preview-img ngx-gallery-center\\\"\\n    (click)=\\\"$event.stopPropagation()\\\" (mouseenter)=\\\"imageMouseEnter()\\\" (mouseleave)=\\\"imageMouseLeave()\\\" (mousedown)=\\\"mouseDownHandler($event)\\\" (touchstart)=\\\"mouseDownHandler($event)\\\"\\n    [class.ngx-gallery-active]=\\\"!loading\\\" [class.animation]=\\\"animation\\\" [class.ngx-gallery-grab]=\\\"canDragOnZoom()\\\" [style.transform]=\\\"getTransform()\\\" [style.left]=\\\"positionLeft + 'px'\\\" [style.top]=\\\"positionTop + 'px'\\\">\\n      <source [src]=\\\"src\\\">\\n      Your browser does not support the video tag.\\n    </video>\\n    <ngx-gallery-bullets *ngIf=\\\"bullets\\\" [count]=\\\"images.length\\\" [active]=\\\"index\\\"\\n                         (bulletChange)=\\\"showAtIndex($event)\\\"></ngx-gallery-bullets>\\n  </div>\\n  <div class=\\\"ngx-gallery-preview-text\\\" *ngIf=\\\"showDescription && description\\\" [innerHTML]=\\\"description\\\"\\n       (click)=\\\"$event.stopPropagation()\\\"></div>\\n</div>\\n\", styles: [\":host.ngx-gallery-active{width:100%;height:100%;position:fixed;left:0;top:0;background:rgba(0,0,0,.7);z-index:10000;display:inline-block;font-size:50px!important}:host{display:none;font-size:50px!important}:host .ngx-gallery-arrow{font-size:50px!important}ngx-gallery-bullets{height:5%;align-items:center;padding:0}.ngx-gallery-preview-img{opacity:0;max-width:90%;max-height:90%;-webkit-user-select:none;user-select:none;transition:transform .5s}.ngx-gallery-preview-img.animation{transition:opacity .5s linear,transform .5s}.ngx-gallery-preview-img.ngx-gallery-active{opacity:1}.ngx-gallery-preview-img.ngx-gallery-grab{cursor:grab}.ngx-gallery-icon.ngx-gallery-spinner{font-size:50px;left:0;display:inline-block}:host .ngx-gallery-preview-top{position:absolute;width:100%;-webkit-user-select:none;user-select:none;font-size:25px}.ngx-gallery-preview-icons{float:right}.ngx-gallery-preview-icons .ngx-gallery-icon{position:relative;margin-right:10px;margin-top:10px;font-size:25px;cursor:pointer;text-decoration:none}.ngx-gallery-preview-icons .ngx-gallery-icon.ngx-gallery-icon-disabled{cursor:default;opacity:.4}.ngx-spinner-wrapper{width:50px;height:50px;display:none}.ngx-spinner-wrapper.ngx-gallery-active{display:inline-block}.ngx-gallery-center{position:absolute;left:0;right:0;bottom:0;margin:auto;top:0}.ngx-gallery-preview-text{width:100%;background:rgba(0,0,0,.7);padding:10px;text-align:center;color:#fff;font-size:16px;flex:0 1 auto;z-index:10}.ngx-gallery-preview-wrapper{width:100%;height:100%;display:flex;flex-flow:column}.ngx-gallery-preview-img-wrapper{flex:1 1 auto;position:relative}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i1.DomSanitizer }, { type: i0.ElementRef }, { type: NgxGalleryService }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { images: [{\n                type: Input\n            }], descriptions: [{\n                type: Input\n            }], showDescription: [{\n                type: Input\n            }], arrows: [{\n                type: Input\n            }], arrowsAutoHide: [{\n                type: Input\n            }], swipe: [{\n                type: Input\n            }], fullscreen: [{\n                type: Input\n            }], forceFullscreen: [{\n                type: Input\n            }], closeOnClick: [{\n                type: Input\n            }], closeOnEsc: [{\n                type: Input\n            }], keyboardNavigation: [{\n                type: Input\n            }], arrowPrevIcon: [{\n                type: Input\n            }], arrowNextIcon: [{\n                type: Input\n            }], closeIcon: [{\n                type: Input\n            }], fullscreenIcon: [{\n                type: Input\n            }], spinnerIcon: [{\n                type: Input\n            }], autoPlay: [{\n                type: Input\n            }], autoPlayInterval: [{\n                type: Input\n            }], autoPlayPauseOnHover: [{\n                type: Input\n            }], infinityMove: [{\n                type: Input\n            }], zoom: [{\n                type: Input\n            }], zoomStep: [{\n                type: Input\n            }], zoomMax: [{\n                type: Input\n            }], zoomMin: [{\n                type: Input\n            }], zoomInIcon: [{\n                type: Input\n            }], zoomOutIcon: [{\n                type: Input\n            }], animation: [{\n                type: Input\n            }], actions: [{\n                type: Input\n            }], rotate: [{\n                type: Input\n            }], rotateLeftIcon: [{\n                type: Input\n            }], rotateRightIcon: [{\n                type: Input\n            }], download: [{\n                type: Input\n            }], downloadIcon: [{\n                type: Input\n            }], bullets: [{\n                type: Input\n            }], previewOpen: [{\n                type: Output\n            }], previewClose: [{\n                type: Output\n            }], activeChange: [{\n                type: Output\n            }], previewImage: [{\n                type: ViewChild,\n                args: ['previewImage']\n            }], onMouseEnter: [{\n                type: HostListener,\n                args: ['mouseenter']\n            }], onMouseLeave: [{\n                type: HostListener,\n                args: ['mouseleave']\n            }] } });\n\nclass NgxGalleryAnimation {\n}\nNgxGalleryAnimation.Fade = 'fade';\nNgxGalleryAnimation.Slide = 'slide';\nNgxGalleryAnimation.Rotate = 'rotate';\nNgxGalleryAnimation.Zoom = 'zoom';\n\nclass NgxGalleryImageComponent {\n    constructor(sanitization, changeDetectorRef, elementRef, helperService) {\n        this.sanitization = sanitization;\n        this.changeDetectorRef = changeDetectorRef;\n        this.elementRef = elementRef;\n        this.helperService = helperService;\n        this.imageClick = new EventEmitter();\n        this.activeChange = new EventEmitter();\n        this.animating = new EventEmitter();\n        this.canChangeImage = true;\n        this.isAnimating = false;\n        this.changeDetectorRef = changeDetectorRef;\n        this.action = 'none';\n    }\n    set selectedIndex(index) {\n        if (index > this._selectedIndex) {\n            let action;\n            if (this.animation === NgxGalleryAnimation.Slide) {\n                action = 'slideRight';\n            }\n            else if (this.animation === NgxGalleryAnimation.Fade) {\n                action = 'fade';\n            }\n            else if (this.animation === NgxGalleryAnimation.Rotate) {\n                action = 'rotateRight';\n            }\n            else if (this.animation === NgxGalleryAnimation.Zoom) {\n                action = 'zoom';\n            }\n            this.setAction(action);\n        }\n        else if (index < this._selectedIndex) {\n            let action;\n            if (this.animation === NgxGalleryAnimation.Slide) {\n                action = 'slideLeft';\n            }\n            else if (this.animation === NgxGalleryAnimation.Fade) {\n                action = 'fade';\n            }\n            else if (this.animation === NgxGalleryAnimation.Rotate) {\n                action = 'rotateLeft';\n            }\n            else if (this.animation === NgxGalleryAnimation.Zoom) {\n                action = 'zoom';\n            }\n            this.setAction(action);\n        }\n        this._selectedIndex = index;\n    }\n    // @HostBinding('style.display') public display = 'inline-block';\n    // @HostBinding('style.background-color') public color = 'lime';\n    ngOnInit() {\n        if (this.arrows && this.arrowsAutoHide) {\n            this.arrows = false;\n        }\n        if (this.autoPlay) {\n            this.startAutoPlay();\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes['swipe']) {\n            this.helperService.manageSwipe(this.swipe, this.elementRef, 'image', () => this.showNext(), () => this.showPrev());\n        }\n    }\n    onMouseEnter() {\n        if (this.arrowsAutoHide && !this.arrows) {\n            this.arrows = true;\n        }\n        if (this.autoPlay && this.autoPlayPauseOnHover) {\n            this.stopAutoPlay();\n        }\n    }\n    onMouseLeave() {\n        if (this.arrowsAutoHide && this.arrows) {\n            this.arrows = false;\n        }\n        if (this.autoPlay && this.autoPlayPauseOnHover) {\n            this.startAutoPlay();\n        }\n    }\n    reset(index) {\n        this._selectedIndex = index;\n        this.action = 'none';\n    }\n    getImages() {\n        if (!this.images) {\n            return [];\n        }\n        if (this.lazyLoading) {\n            const indexes = [this._selectedIndex];\n            const prevIndex = this._selectedIndex - 1;\n            if (prevIndex === -1 && this.infinityMove) {\n                indexes.push(this.images.length - 1);\n            }\n            else if (prevIndex >= 0) {\n                indexes.push(prevIndex);\n            }\n            const nextIndex = this._selectedIndex + 1;\n            if (nextIndex === this.images.length && this.infinityMove) {\n                indexes.push(0);\n            }\n            else if (nextIndex < this.images.length) {\n                indexes.push(nextIndex);\n            }\n            return this.images.filter((img, i) => indexes.indexOf(i) !== -1);\n        }\n        else {\n            return this.images;\n        }\n    }\n    startAutoPlay() {\n        this.stopAutoPlay();\n        this.timer = setInterval(() => {\n            if (!this.showNext()) {\n                this._selectedIndex = -1;\n                this.showNext();\n            }\n        }, this.autoPlayInterval);\n    }\n    stopAutoPlay() {\n        if (this.timer) {\n            clearInterval(this.timer);\n        }\n    }\n    handleClick(event, index) {\n        if (this.clickable) {\n            this.imageClick.emit(index);\n            event.stopPropagation();\n            event.preventDefault();\n        }\n    }\n    show(index) {\n        if (this.isAnimating) {\n            return;\n        }\n        if (index > this._selectedIndex) {\n            let action;\n            if (this.animation === NgxGalleryAnimation.Slide) {\n                action = 'slideRight';\n            }\n            else if (this.animation === NgxGalleryAnimation.Fade) {\n                action = 'fade';\n            }\n            else if (this.animation === NgxGalleryAnimation.Rotate) {\n                action = 'rotateRight';\n            }\n            else if (this.animation === NgxGalleryAnimation.Zoom) {\n                action = 'zoom';\n            }\n            this.setAction(action);\n        }\n        else {\n            let action;\n            if (this.animation === NgxGalleryAnimation.Slide) {\n                action = 'slideLeft';\n            }\n            else if (this.animation === NgxGalleryAnimation.Fade) {\n                action = 'fade';\n            }\n            else if (this.animation === NgxGalleryAnimation.Rotate) {\n                action = 'rotateLeft';\n            }\n            else if (this.animation === NgxGalleryAnimation.Zoom) {\n                action = 'zoom';\n            }\n            this.setAction(action);\n        }\n        this._selectedIndex = index;\n        this.activeChange.emit(this._selectedIndex);\n        this.setChangeTimeout();\n    }\n    setAction(action) {\n        this.action = action;\n        this.changeDetectorRef.detectChanges();\n    }\n    showNext() {\n        if (this.isAnimating) {\n            return false;\n        }\n        if (this.canShowNext() && this.canChangeImage) {\n            let action;\n            if (this.animation === NgxGalleryAnimation.Slide) {\n                action = 'slideRight';\n            }\n            else if (this.animation === NgxGalleryAnimation.Fade) {\n                action = 'fade';\n            }\n            else if (this.animation === NgxGalleryAnimation.Rotate) {\n                action = 'rotateRight';\n            }\n            else if (this.animation === NgxGalleryAnimation.Zoom) {\n                action = 'zoom';\n            }\n            this.setAction(action);\n            this._selectedIndex++;\n            if (this._selectedIndex === this.images.length) {\n                this._selectedIndex = 0;\n            }\n            this.activeChange.emit(this._selectedIndex);\n            this.setChangeTimeout();\n            return true;\n        }\n        else {\n            return false;\n        }\n    }\n    showPrev() {\n        if (this.isAnimating) {\n            return;\n        }\n        if (this.canShowPrev() && this.canChangeImage) {\n            let action;\n            if (this.animation === NgxGalleryAnimation.Slide) {\n                action = 'slideLeft';\n            }\n            else if (this.animation === NgxGalleryAnimation.Fade) {\n                action = 'fade';\n            }\n            else if (this.animation === NgxGalleryAnimation.Rotate) {\n                action = 'rotateLeft';\n            }\n            else if (this.animation === NgxGalleryAnimation.Zoom) {\n                action = 'zoom';\n            }\n            this.setAction(action);\n            this._selectedIndex--;\n            if (this._selectedIndex < 0) {\n                this._selectedIndex = this.images.length - 1;\n            }\n            this.activeChange.emit(this._selectedIndex);\n            this.setChangeTimeout();\n        }\n    }\n    setChangeTimeout() {\n        this.canChangeImage = false;\n        let timeout = 1000;\n        if (this.animation === NgxGalleryAnimation.Slide\n            || this.animation === NgxGalleryAnimation.Fade) {\n            timeout = 500;\n        }\n        setTimeout(() => {\n            this.canChangeImage = true;\n        }, timeout);\n    }\n    canShowNext() {\n        if (this.images) {\n            return this.infinityMove || this._selectedIndex < this.images.length - 1;\n        }\n        else {\n            return false;\n        }\n    }\n    canShowPrev() {\n        if (this.images) {\n            return this.infinityMove || this._selectedIndex > 0;\n        }\n        else {\n            return false;\n        }\n    }\n    getSafeUrl(image) {\n        return this.sanitization.bypassSecurityTrustStyle(this.helperService.getBackgroundUrl(image.toString()));\n    }\n    getFileType(fileSource) {\n        return this.helperService.getFileType(fileSource);\n    }\n    onStart(event) {\n        this.isAnimating = true;\n        this.animating.emit(true);\n    }\n    onDone(event) {\n        this.isAnimating = false;\n        this.animating.emit(false);\n    }\n}\nNgxGalleryImageComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryImageComponent, deps: [{ token: i1.DomSanitizer }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: NgxGalleryService }], target: i0.ɵɵFactoryTarget.Component });\nNgxGalleryImageComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.3\", type: NgxGalleryImageComponent, selector: \"ngx-gallery-image\", inputs: { images: \"images\", clickable: \"clickable\", selectedIndex: \"selectedIndex\", arrows: \"arrows\", arrowsAutoHide: \"arrowsAutoHide\", swipe: \"swipe\", animation: \"animation\", size: \"size\", arrowPrevIcon: \"arrowPrevIcon\", arrowNextIcon: \"arrowNextIcon\", autoPlay: \"autoPlay\", autoPlayInterval: \"autoPlayInterval\", autoPlayPauseOnHover: \"autoPlayPauseOnHover\", infinityMove: \"infinityMove\", lazyLoading: \"lazyLoading\", actions: \"actions\", descriptions: \"descriptions\", showDescription: \"showDescription\", bullets: \"bullets\" }, outputs: { imageClick: \"imageClick\", activeChange: \"activeChange\", animating: \"animating\" }, host: { listeners: { \"mouseenter\": \"onMouseEnter()\", \"mouseleave\": \"onMouseLeave()\" } }, usesOnChanges: true, ngImport: i0, template: \"<div class=\\\"ngx-gallery-image-wrapper ngx-gallery-animation-{{animation}} ngx-gallery-image-size-{{size}}\\\">\\n  <ng-container *ngFor=\\\"let image of getImages(); let i = index;\\\">\\n\\n    <div *ngIf=\\\"image.type === 'image' && image.index === _selectedIndex\\\" class=\\\"ngx-gallery-image\\\"\\n         [ngClass]=\\\"{'ngx-gallery-clickable': clickable }\\\"\\n         [style.background-image]=\\\"getSafeUrl(image.src)\\\"\\n         (click)=\\\"handleClick($event, image.index)\\\"\\n         [@animation]=\\\"action\\\"\\n         (@animation.start)=\\\"onStart($event)\\\"\\n         (@animation.done)=\\\"onDone($event)\\\">\\n      <div class=\\\"ngx-gallery-icons-wrapper\\\">\\n        <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                            [titleText]=\\\"action.titleText\\\"\\n                            (closeClick)=\\\"action.onClick($event, image.index)\\\"></ngx-gallery-action>\\n      </div>\\n      <div class=\\\"ngx-gallery-image-text\\\" *ngIf=\\\"showDescription && descriptions[image.index]\\\"\\n          [innerHTML]=\\\"descriptions[image.index]\\\" (click)=\\\"$event.stopPropagation()\\\"></div>\\n    </div>\\n\\n    <div *ngIf=\\\"image.type === 'video' && image.index === _selectedIndex\\\" class=\\\"ngx-gallery-image\\\"\\n         [ngClass]=\\\"{'ngx-gallery-clickable': clickable }\\\"\\n         [style.background-image]=\\\"getSafeUrl(image.src)\\\"\\n         (click)=\\\"handleClick($event, image.index)\\\"\\n         [@animation]=\\\"action\\\"\\n         (@animation.start)=\\\"onStart($event)\\\"\\n         (@animation.done)=\\\"onDone($event)\\\">\\n      <video controls style=\\\"width:100%; height:100%; background: #000;\\\">\\n        <source [src]=\\\"image.src\\\">\\n          Your browser does not support the video tag.\\n      </video>\\n      <div class=\\\"ngx-gallery-icons-wrapper\\\">\\n      <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                          [titleText]=\\\"action.titleText\\\"\\n                          (closeClick)=\\\"action.onClick($event, image.index)\\\"></ngx-gallery-action>\\n      </div>\\n      <div class=\\\"ngx-gallery-image-text\\\" *ngIf=\\\"showDescription && descriptions[image.index]\\\"\\n          [innerHTML]=\\\"descriptions[image.index]\\\" (click)=\\\"$event.stopPropagation()\\\"></div>\\n      </div>\\n\\n\\n  </ng-container>\\n  <ngx-gallery-bullets *ngIf=\\\"bullets\\\" [count]=\\\"images.length\\\" [active]=\\\"_selectedIndex\\\"\\n                       (bulletChange)=\\\"show($event)\\\"></ngx-gallery-bullets>\\n  <ngx-gallery-arrows class=\\\"ngx-gallery-image-size-{{size}}\\\" *ngIf=\\\"arrows\\\" (prevClick)=\\\"showPrev()\\\"\\n                      (nextClick)=\\\"showNext()\\\" [prevDisabled]=\\\"!canShowPrev()\\\" [nextDisabled]=\\\"!canShowNext()\\\"\\n                      [arrowPrevIcon]=\\\"arrowPrevIcon\\\" [arrowNextIcon]=\\\"arrowNextIcon\\\"></ngx-gallery-arrows>\\n</div>\\n\", styles: [\":host{width:100%;display:inline-block;position:relative;font-size:25px}.ngx-gallery-image-wrapper{width:100%;height:100%;position:absolute;left:0;top:0;overflow:hidden}.ngx-gallery-image{background-position:center;background-repeat:no-repeat;height:100%;width:100%;position:absolute;top:0}.ngx-gallery-image-size-cover .ngx-gallery-image{background-size:cover}.ngx-gallery-image-size-contain .ngx-gallery-image{background-size:contain}.ngx-gallery-animation-fade .ngx-gallery-image{left:0;opacity:1;transition:.5s ease-in-out}.ngx-gallery-animation-fade .ngx-gallery-image.ngx-gallery-active{opacity:1}.ngx-gallery-animation-rotate .ngx-gallery-image{transition:1s ease;transform:scale(1) rotate(0);left:0;opacity:1}.ngx-gallery-animation-zoom .ngx-gallery-image{transition:1s ease;transform:scale(1);left:0;opacity:1}.ngx-gallery-image-text{width:100%;background:rgba(0,0,0,.7);padding:10px;text-align:center;color:#fff;font-size:16px;position:absolute;bottom:0;z-index:10}\\n\"], components: [{ type: NgxGalleryActionComponent, selector: \"ngx-gallery-action\", inputs: [\"icon\", \"disabled\", \"titleText\"], outputs: [\"closeClick\"] }, { type: NgxGalleryBulletsComponent, selector: \"ngx-gallery-bullets\", inputs: [\"count\", \"active\"], outputs: [\"bulletChange\"] }, { type: NgxGalleryArrowsComponent, selector: \"ngx-gallery-arrows\", inputs: [\"prevDisabled\", \"nextDisabled\", \"arrowPrevIcon\", \"arrowNextIcon\"], outputs: [\"prevClick\", \"nextClick\"] }], directives: [{ type: i6.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { type: i6.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i6.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], animations: [\n        trigger('animation', [\n            // ...\n            state('slideRight', style({})),\n            state('slideLeft', style({})),\n            state('fade', style({})),\n            state('rotateLeft', style({})),\n            state('rotateRight', style({})),\n            state('zoom', style({})),\n            transition('slideRight => void', [\n                animate('500ms ease-in-out', style({ transform: 'translateX(-100%)' }))\n            ]),\n            transition('void => slideRight', [\n                style({ transform: 'translateX(100%)' }),\n                animate('500ms ease-in-out', style({ transform: 'translateX(0)' }))\n            ]),\n            transition('slideLeft => void', [\n                animate('500ms ease-in-out', style({ transform: 'translateX(100%)' }))\n            ]),\n            transition('void => slideLeft', [\n                style({ transform: 'translateX(-100%)' }),\n                animate('500ms ease-in-out', style({ transform: 'translateX(0)' }))\n            ]),\n            transition('fade => void', [\n                animate('500ms ease-in-out', style({ opacity: '0' }))\n            ]),\n            transition('void => fade', [\n                style({ opacity: '0' }),\n                animate('500ms ease-in-out', style({ opacity: '1' }))\n            ]),\n            transition('rotateLeft => void', [\n                animate('500ms ease-in-out', style({ transform: 'scale(1, 1) rotate(-90deg)', opacity: '0' }))\n            ]),\n            transition('void => rotateLeft', [\n                style({ transform: 'scale(1, 1) rotate(-90deg)', opacity: '0' }),\n                animate('500ms ease-in-out', style({ transform: 'scale(1, 1) rotate(0deg)', opacity: '1' }))\n            ]),\n            transition('rotateRight => void', [\n                animate('500ms ease-in-out', style({ transform: 'scale(1, 1) rotate(90deg)', opacity: '0' }))\n            ]),\n            transition('void => rotateRight', [\n                style({ transform: 'scale(1, 1) rotate(90deg)', opacity: '0' }),\n                animate('500ms ease-in-out', style({ transform: 'scale(1, 1) rotate(0deg)', opacity: '1' }))\n            ]),\n            transition('zoom => void', [\n                animate('500ms ease-in-out', style({ transform: 'scale(2.5,2.5)', opacity: '0' }))\n            ]),\n            transition('void => zoom', [\n                style({ transform: 'scale(2.5,2.5)', opacity: '0' }),\n                animate('500ms ease-in-out', style({ transform: 'scale(1, 1)', opacity: '1' }))\n            ]),\n        ]),\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryImageComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-gallery-image', changeDetection: ChangeDetectionStrategy.OnPush, animations: [\n                        trigger('animation', [\n                            // ...\n                            state('slideRight', style({})),\n                            state('slideLeft', style({})),\n                            state('fade', style({})),\n                            state('rotateLeft', style({})),\n                            state('rotateRight', style({})),\n                            state('zoom', style({})),\n                            transition('slideRight => void', [\n                                animate('500ms ease-in-out', style({ transform: 'translateX(-100%)' }))\n                            ]),\n                            transition('void => slideRight', [\n                                style({ transform: 'translateX(100%)' }),\n                                animate('500ms ease-in-out', style({ transform: 'translateX(0)' }))\n                            ]),\n                            transition('slideLeft => void', [\n                                animate('500ms ease-in-out', style({ transform: 'translateX(100%)' }))\n                            ]),\n                            transition('void => slideLeft', [\n                                style({ transform: 'translateX(-100%)' }),\n                                animate('500ms ease-in-out', style({ transform: 'translateX(0)' }))\n                            ]),\n                            transition('fade => void', [\n                                animate('500ms ease-in-out', style({ opacity: '0' }))\n                            ]),\n                            transition('void => fade', [\n                                style({ opacity: '0' }),\n                                animate('500ms ease-in-out', style({ opacity: '1' }))\n                            ]),\n                            transition('rotateLeft => void', [\n                                animate('500ms ease-in-out', style({ transform: 'scale(1, 1) rotate(-90deg)', opacity: '0' }))\n                            ]),\n                            transition('void => rotateLeft', [\n                                style({ transform: 'scale(1, 1) rotate(-90deg)', opacity: '0' }),\n                                animate('500ms ease-in-out', style({ transform: 'scale(1, 1) rotate(0deg)', opacity: '1' }))\n                            ]),\n                            transition('rotateRight => void', [\n                                animate('500ms ease-in-out', style({ transform: 'scale(1, 1) rotate(90deg)', opacity: '0' }))\n                            ]),\n                            transition('void => rotateRight', [\n                                style({ transform: 'scale(1, 1) rotate(90deg)', opacity: '0' }),\n                                animate('500ms ease-in-out', style({ transform: 'scale(1, 1) rotate(0deg)', opacity: '1' }))\n                            ]),\n                            transition('zoom => void', [\n                                animate('500ms ease-in-out', style({ transform: 'scale(2.5,2.5)', opacity: '0' }))\n                            ]),\n                            transition('void => zoom', [\n                                style({ transform: 'scale(2.5,2.5)', opacity: '0' }),\n                                animate('500ms ease-in-out', style({ transform: 'scale(1, 1)', opacity: '1' }))\n                            ]),\n                        ]),\n                    ], template: \"<div class=\\\"ngx-gallery-image-wrapper ngx-gallery-animation-{{animation}} ngx-gallery-image-size-{{size}}\\\">\\n  <ng-container *ngFor=\\\"let image of getImages(); let i = index;\\\">\\n\\n    <div *ngIf=\\\"image.type === 'image' && image.index === _selectedIndex\\\" class=\\\"ngx-gallery-image\\\"\\n         [ngClass]=\\\"{'ngx-gallery-clickable': clickable }\\\"\\n         [style.background-image]=\\\"getSafeUrl(image.src)\\\"\\n         (click)=\\\"handleClick($event, image.index)\\\"\\n         [@animation]=\\\"action\\\"\\n         (@animation.start)=\\\"onStart($event)\\\"\\n         (@animation.done)=\\\"onDone($event)\\\">\\n      <div class=\\\"ngx-gallery-icons-wrapper\\\">\\n        <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                            [titleText]=\\\"action.titleText\\\"\\n                            (closeClick)=\\\"action.onClick($event, image.index)\\\"></ngx-gallery-action>\\n      </div>\\n      <div class=\\\"ngx-gallery-image-text\\\" *ngIf=\\\"showDescription && descriptions[image.index]\\\"\\n          [innerHTML]=\\\"descriptions[image.index]\\\" (click)=\\\"$event.stopPropagation()\\\"></div>\\n    </div>\\n\\n    <div *ngIf=\\\"image.type === 'video' && image.index === _selectedIndex\\\" class=\\\"ngx-gallery-image\\\"\\n         [ngClass]=\\\"{'ngx-gallery-clickable': clickable }\\\"\\n         [style.background-image]=\\\"getSafeUrl(image.src)\\\"\\n         (click)=\\\"handleClick($event, image.index)\\\"\\n         [@animation]=\\\"action\\\"\\n         (@animation.start)=\\\"onStart($event)\\\"\\n         (@animation.done)=\\\"onDone($event)\\\">\\n      <video controls style=\\\"width:100%; height:100%; background: #000;\\\">\\n        <source [src]=\\\"image.src\\\">\\n          Your browser does not support the video tag.\\n      </video>\\n      <div class=\\\"ngx-gallery-icons-wrapper\\\">\\n      <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                          [titleText]=\\\"action.titleText\\\"\\n                          (closeClick)=\\\"action.onClick($event, image.index)\\\"></ngx-gallery-action>\\n      </div>\\n      <div class=\\\"ngx-gallery-image-text\\\" *ngIf=\\\"showDescription && descriptions[image.index]\\\"\\n          [innerHTML]=\\\"descriptions[image.index]\\\" (click)=\\\"$event.stopPropagation()\\\"></div>\\n      </div>\\n\\n\\n  </ng-container>\\n  <ngx-gallery-bullets *ngIf=\\\"bullets\\\" [count]=\\\"images.length\\\" [active]=\\\"_selectedIndex\\\"\\n                       (bulletChange)=\\\"show($event)\\\"></ngx-gallery-bullets>\\n  <ngx-gallery-arrows class=\\\"ngx-gallery-image-size-{{size}}\\\" *ngIf=\\\"arrows\\\" (prevClick)=\\\"showPrev()\\\"\\n                      (nextClick)=\\\"showNext()\\\" [prevDisabled]=\\\"!canShowPrev()\\\" [nextDisabled]=\\\"!canShowNext()\\\"\\n                      [arrowPrevIcon]=\\\"arrowPrevIcon\\\" [arrowNextIcon]=\\\"arrowNextIcon\\\"></ngx-gallery-arrows>\\n</div>\\n\", styles: [\":host{width:100%;display:inline-block;position:relative;font-size:25px}.ngx-gallery-image-wrapper{width:100%;height:100%;position:absolute;left:0;top:0;overflow:hidden}.ngx-gallery-image{background-position:center;background-repeat:no-repeat;height:100%;width:100%;position:absolute;top:0}.ngx-gallery-image-size-cover .ngx-gallery-image{background-size:cover}.ngx-gallery-image-size-contain .ngx-gallery-image{background-size:contain}.ngx-gallery-animation-fade .ngx-gallery-image{left:0;opacity:1;transition:.5s ease-in-out}.ngx-gallery-animation-fade .ngx-gallery-image.ngx-gallery-active{opacity:1}.ngx-gallery-animation-rotate .ngx-gallery-image{transition:1s ease;transform:scale(1) rotate(0);left:0;opacity:1}.ngx-gallery-animation-zoom .ngx-gallery-image{transition:1s ease;transform:scale(1);left:0;opacity:1}.ngx-gallery-image-text{width:100%;background:rgba(0,0,0,.7);padding:10px;text-align:center;color:#fff;font-size:16px;position:absolute;bottom:0;z-index:10}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i1.DomSanitizer }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: NgxGalleryService }]; }, propDecorators: { images: [{\n                type: Input\n            }], clickable: [{\n                type: Input\n            }], selectedIndex: [{\n                type: Input\n            }], arrows: [{\n                type: Input\n            }], arrowsAutoHide: [{\n                type: Input\n            }], swipe: [{\n                type: Input\n            }], animation: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], arrowPrevIcon: [{\n                type: Input\n            }], arrowNextIcon: [{\n                type: Input\n            }], autoPlay: [{\n                type: Input\n            }], autoPlayInterval: [{\n                type: Input\n            }], autoPlayPauseOnHover: [{\n                type: Input\n            }], infinityMove: [{\n                type: Input\n            }], lazyLoading: [{\n                type: Input\n            }], actions: [{\n                type: Input\n            }], descriptions: [{\n                type: Input\n            }], showDescription: [{\n                type: Input\n            }], bullets: [{\n                type: Input\n            }], imageClick: [{\n                type: Output\n            }], activeChange: [{\n                type: Output\n            }], animating: [{\n                type: Output\n            }], onMouseEnter: [{\n                type: HostListener,\n                args: ['mouseenter']\n            }], onMouseLeave: [{\n                type: HostListener,\n                args: ['mouseleave']\n            }] } });\n\nclass NgxGalleryOrder {\n}\nNgxGalleryOrder.Column = 1;\nNgxGalleryOrder.Row = 2;\nNgxGalleryOrder.Page = 3;\n\nclass NgxGalleryThumbnailsComponent {\n    constructor(sanitization, elementRef, helperService) {\n        this.sanitization = sanitization;\n        this.elementRef = elementRef;\n        this.helperService = helperService;\n        this.minStopIndex = 0;\n        this.activeChange = new EventEmitter();\n        this.index = 0;\n    }\n    ngOnChanges(changes) {\n        if (changes['selectedIndex']) {\n            this.validateIndex();\n        }\n        if (changes['swipe']) {\n            this.helperService.manageSwipe(this.swipe, this.elementRef, 'thumbnails', () => this.moveRight(), () => this.moveLeft());\n        }\n        if (this.images) {\n            this.remainingCountValue = this.images.length - (this.rows * this.columns);\n        }\n    }\n    onMouseEnter() {\n        this.mouseenter = true;\n    }\n    onMouseLeave() {\n        this.mouseenter = false;\n    }\n    reset(index) {\n        this.selectedIndex = index;\n        this.setDefaultPosition();\n        this.index = 0;\n        this.validateIndex();\n    }\n    getImages() {\n        if (!this.images) {\n            return [];\n        }\n        if (this.remainingCount) {\n            return this.images.slice(0, this.rows * this.columns);\n        }\n        else if (this.lazyLoading && this.order !== NgxGalleryOrder.Row) {\n            let stopIndex = 0;\n            if (this.order === NgxGalleryOrder.Column) {\n                stopIndex = (this.index + this.columns + this.moveSize) * this.rows;\n            }\n            else if (this.order === NgxGalleryOrder.Page) {\n                stopIndex = this.index + ((this.columns * this.rows) * 2);\n            }\n            if (stopIndex <= this.minStopIndex) {\n                stopIndex = this.minStopIndex;\n            }\n            else {\n                this.minStopIndex = stopIndex;\n            }\n            return this.images.slice(0, stopIndex);\n        }\n        else {\n            return this.images;\n        }\n    }\n    handleClick(event, index) {\n        if (!this.hasLink(index) && !this.isAnimating) {\n            this.selectedIndex = index;\n            this.activeChange.emit(index);\n        }\n        event.stopPropagation();\n        event.preventDefault();\n    }\n    hasLink(index) {\n        return !!(this.links && this.links.length && this.links[index]);\n    }\n    moveRight() {\n        if (this.canMoveRight()) {\n            this.index += this.moveSize;\n            const maxIndex = this.getMaxIndex() - this.columns;\n            if (this.index > maxIndex) {\n                this.index = maxIndex;\n            }\n            this.setThumbnailsPosition();\n        }\n    }\n    moveLeft() {\n        if (this.canMoveLeft()) {\n            this.index -= this.moveSize;\n            if (this.index < 0) {\n                this.index = 0;\n            }\n            this.setThumbnailsPosition();\n        }\n    }\n    canMoveRight() {\n        return this.index + this.columns < this.getMaxIndex();\n    }\n    canMoveLeft() {\n        return this.index !== 0;\n    }\n    getThumbnailLeft(index) {\n        let calculatedIndex;\n        if (this.order === NgxGalleryOrder.Column) {\n            calculatedIndex = Math.floor(index / this.rows);\n        }\n        else if (this.order === NgxGalleryOrder.Page) {\n            calculatedIndex = (index % this.columns) + (Math.floor(index / (this.rows * this.columns)) * this.columns);\n        }\n        else if (this.order === NgxGalleryOrder.Row && this.remainingCount) {\n            calculatedIndex = index % this.columns;\n        }\n        else {\n            calculatedIndex = index % Math.ceil(this.images.length / this.rows);\n        }\n        return this.getThumbnailPosition(calculatedIndex, this.columns);\n    }\n    getThumbnailTop(index) {\n        let calculatedIndex;\n        if (this.order === NgxGalleryOrder.Column) {\n            calculatedIndex = index % this.rows;\n        }\n        else if (this.order === NgxGalleryOrder.Page) {\n            calculatedIndex = Math.floor(index / this.columns) - (Math.floor(index / (this.rows * this.columns)) * this.rows);\n        }\n        else if (this.order === NgxGalleryOrder.Row && this.remainingCount) {\n            calculatedIndex = Math.floor(index / this.columns);\n        }\n        else {\n            calculatedIndex = Math.floor(index / Math.ceil(this.images.length / this.rows));\n        }\n        return this.getThumbnailPosition(calculatedIndex, this.rows);\n    }\n    getThumbnailWidth() {\n        return this.getThumbnailDimension(this.columns);\n    }\n    getThumbnailHeight() {\n        return this.getThumbnailDimension(this.rows);\n    }\n    setThumbnailsPosition() {\n        this.thumbnailsLeft = -((100 / this.columns) * this.index) + '%';\n        this.thumbnailsMarginLeft = -((this.margin - (((this.columns - 1)\n            * this.margin) / this.columns)) * this.index) + 'px';\n    }\n    setDefaultPosition() {\n        this.thumbnailsLeft = '0px';\n        this.thumbnailsMarginLeft = '0px';\n    }\n    canShowArrows() {\n        if (this.remainingCount) {\n            return false;\n        }\n        else {\n            return this.arrows && this.images && this.images.length > this.getVisibleCount()\n                && (!this.arrowsAutoHide || this.mouseenter);\n        }\n    }\n    validateIndex() {\n        if (this.images) {\n            let newIndex;\n            if (this.order === NgxGalleryOrder.Column) {\n                newIndex = Math.floor(this.selectedIndex / this.rows);\n            }\n            else {\n                newIndex = this.selectedIndex % Math.ceil(this.images.length / this.rows);\n            }\n            if (this.remainingCount) {\n                newIndex = 0;\n            }\n            if (newIndex < this.index || newIndex >= this.index + this.columns) {\n                const maxIndex = this.getMaxIndex() - this.columns;\n                this.index = newIndex > maxIndex ? maxIndex : newIndex;\n                this.setThumbnailsPosition();\n            }\n        }\n    }\n    getSafeUrl(image) {\n        return this.sanitization.bypassSecurityTrustStyle(this.helperService.getBackgroundUrl(image.toString()));\n    }\n    getFileType(fileSource) {\n        return this.helperService.getFileType(fileSource.toString());\n    }\n    getThumbnailPosition(index, count) {\n        return this.getSafeStyle('calc(' + ((100 / count) * index) + '% + '\n            + ((this.margin - (((count - 1) * this.margin) / count)) * index) + 'px)');\n    }\n    getThumbnailDimension(count) {\n        if (this.margin !== 0) {\n            return this.getSafeStyle('calc(' + (100 / count) + '% - '\n                + (((count - 1) * this.margin) / count) + 'px)');\n        }\n        else {\n            return this.getSafeStyle('calc(' + (100 / count) + '% + 1px)');\n        }\n    }\n    getMaxIndex() {\n        if (this.order === NgxGalleryOrder.Page) {\n            let maxIndex = (Math.floor(this.images.length / this.getVisibleCount()) * this.columns);\n            if (this.images.length % this.getVisibleCount() > this.columns) {\n                maxIndex += this.columns;\n            }\n            else {\n                maxIndex += this.images.length % this.getVisibleCount();\n            }\n            return maxIndex;\n        }\n        else {\n            return Math.ceil(this.images.length / this.rows);\n        }\n    }\n    getVisibleCount() {\n        return this.columns * this.rows;\n    }\n    getSafeStyle(value) {\n        return this.sanitization.bypassSecurityTrustStyle(value);\n    }\n}\nNgxGalleryThumbnailsComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryThumbnailsComponent, deps: [{ token: i1.DomSanitizer }, { token: i0.ElementRef }, { token: NgxGalleryService }], target: i0.ɵɵFactoryTarget.Component });\nNgxGalleryThumbnailsComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.3\", type: NgxGalleryThumbnailsComponent, selector: \"ngx-gallery-thumbnails\", inputs: { images: \"images\", isAnimating: \"isAnimating\", links: \"links\", labels: \"labels\", linkTarget: \"linkTarget\", columns: \"columns\", rows: \"rows\", arrows: \"arrows\", arrowsAutoHide: \"arrowsAutoHide\", margin: \"margin\", selectedIndex: \"selectedIndex\", clickable: \"clickable\", swipe: \"swipe\", size: \"size\", arrowPrevIcon: \"arrowPrevIcon\", arrowNextIcon: \"arrowNextIcon\", moveSize: \"moveSize\", order: \"order\", remainingCount: \"remainingCount\", lazyLoading: \"lazyLoading\", actions: \"actions\" }, outputs: { activeChange: \"activeChange\" }, host: { listeners: { \"mouseenter\": \"onMouseEnter()\", \"mouseleave\": \"onMouseLeave()\" } }, usesOnChanges: true, ngImport: i0, template: \"<div class=\\\"ngx-gallery-thumbnails-wrapper ngx-gallery-thumbnail-size-{{size}}\\\">\\n  <div class=\\\"ngx-gallery-thumbnails\\\" [style.transform]=\\\"'translateX(' + thumbnailsLeft + ')'\\\"\\n       [style.marginLeft]=\\\"thumbnailsMarginLeft\\\">\\n    <a [href]=\\\"hasLink(i) ? links[i] : '#'\\\" [target]=\\\"linkTarget\\\" class=\\\"ngx-gallery-thumbnail\\\"\\n       *ngFor=\\\"let image of getImages(); let i = index;\\\"\\n       (click)=\\\"handleClick($event, i)\\\" [style.width]=\\\"getThumbnailWidth()\\\" [style.height]=\\\"getThumbnailHeight()\\\"\\n       [style.left]=\\\"getThumbnailLeft(i)\\\" [style.top]=\\\"getThumbnailTop(i)\\\"\\n       [ngClass]=\\\"{ 'ngx-gallery-active': i === selectedIndex, 'ngx-gallery-clickable': clickable }\\\"\\n       [attr.aria-label]=\\\"labels[i]\\\">\\n       <div *ngIf=\\\"getFileType(image) === 'image'\\\" [style.background-image]=\\\"getSafeUrl(image)\\\" class=\\\"ngx-gallery-thumbnail\\\"\\n       [ngClass]=\\\"{ 'ngx-gallery-active': i === selectedIndex, 'ngx-gallery-clickable': clickable }\\\"\\n       style=\\\"width: 100%; height: 100%; position:absolute;\\\"></div>\\n       <div *ngIf=\\\"getFileType(image) === 'video'\\\" class=\\\"ngx-gallery-thumbnail-video\\\">\\n        <video  class=\\\"ngx-gallery-thumbnail\\\" [ngClass]=\\\"{ 'ngx-gallery-active': i === selectedIndex, 'ngx-gallery-clickable': clickable }\\\"\\n        style=\\\"width: 100%; height: 100%; position:absolute; left:0; background:#000;\\\">\\n        <source [src]=\\\"image\\\">\\n          Your browser does not support the video tag.\\n       </video>\\n      </div>\\n       <div class=\\\"ngx-gallery-icons-wrapper\\\">\\n        <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                            [titleText]=\\\"action.titleText\\\" (closeClick)=\\\"action.onClick($event, i)\\\"></ngx-gallery-action>\\n      </div>\\n      <div class=\\\"ngx-gallery-remaining-count-overlay\\\"\\n           *ngIf=\\\"remainingCount && remainingCountValue && (i === (rows * columns) - 1)\\\">\\n        <span class=\\\"ngx-gallery-remaining-count\\\">+{{remainingCountValue}}</span>\\n      </div>\\n    </a>\\n  </div>\\n</div>\\n<ngx-gallery-arrows *ngIf=\\\"canShowArrows()\\\" (prevClick)=\\\"moveLeft()\\\" (nextClick)=\\\"moveRight()\\\"\\n                    [prevDisabled]=\\\"!canMoveLeft()\\\" [nextDisabled]=\\\"!canMoveRight()\\\" [arrowPrevIcon]=\\\"arrowPrevIcon\\\"\\n                    [arrowNextIcon]=\\\"arrowNextIcon\\\"></ngx-gallery-arrows>\\n\", styles: [\"@charset \\\"UTF-8\\\";:host{width:100%;display:inline-block;position:relative;font-size:25px}.ngx-gallery-thumbnails-wrapper{width:100%;height:100%;position:absolute;overflow:hidden}.ngx-gallery-thumbnails{height:100%;width:100%;position:absolute;left:0;transform:translate(0);transition:transform .5s ease-in-out;will-change:transform}.ngx-gallery-thumbnails .ngx-gallery-thumbnail{position:absolute;height:100%;background-position:center;background-repeat:no-repeat;text-decoration:none;border:1px double black}.ngx-gallery-thumbnails .ngx-gallery-thumbnail .ngx-gallery-thumbnail-video:after{content:\\\"\\\\f144\\\";display:block;position:absolute;background:#0000;height:100%;width:100%;left:0;top:calc(50% - 20px);font-size:40px;color:#fff;margin:0;padding:0;font-family:fontawesome;text-shadow:0px 4px 3px rgba(0,0,0,.4),0px 8px 13px rgba(0,0,0,.1),0px 18px 23px rgba(0,0,0,.1)}.ngx-gallery-thumbnails .ngx-gallery-thumbnail .img{background-size:cover;height:100%}.ngx-gallery-thumbnails .ngx-gallery-thumbnail.ngx-gallery-active{border:1px double #cc4548}.ngx-gallery-thumbnail-size-cover .ngx-gallery-thumbnails .ngx-gallery-thumbnail{background-size:cover}.ngx-gallery-thumbnail-size-contain .ngx-gallery-thumbnails .ngx-gallery-thumbnail{background-size:contain}.ngx-gallery-remaining-count-overlay{width:100%;height:100%;position:absolute;left:0;top:0;background-color:#0006}.ngx-gallery-remaining-count{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#fff;font-size:30px}\\n\"], components: [{ type: NgxGalleryActionComponent, selector: \"ngx-gallery-action\", inputs: [\"icon\", \"disabled\", \"titleText\"], outputs: [\"closeClick\"] }, { type: NgxGalleryArrowsComponent, selector: \"ngx-gallery-arrows\", inputs: [\"prevDisabled\", \"nextDisabled\", \"arrowPrevIcon\", \"arrowNextIcon\"], outputs: [\"prevClick\", \"nextClick\"] }], directives: [{ type: i6.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { type: i6.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i6.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryThumbnailsComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-gallery-thumbnails', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"ngx-gallery-thumbnails-wrapper ngx-gallery-thumbnail-size-{{size}}\\\">\\n  <div class=\\\"ngx-gallery-thumbnails\\\" [style.transform]=\\\"'translateX(' + thumbnailsLeft + ')'\\\"\\n       [style.marginLeft]=\\\"thumbnailsMarginLeft\\\">\\n    <a [href]=\\\"hasLink(i) ? links[i] : '#'\\\" [target]=\\\"linkTarget\\\" class=\\\"ngx-gallery-thumbnail\\\"\\n       *ngFor=\\\"let image of getImages(); let i = index;\\\"\\n       (click)=\\\"handleClick($event, i)\\\" [style.width]=\\\"getThumbnailWidth()\\\" [style.height]=\\\"getThumbnailHeight()\\\"\\n       [style.left]=\\\"getThumbnailLeft(i)\\\" [style.top]=\\\"getThumbnailTop(i)\\\"\\n       [ngClass]=\\\"{ 'ngx-gallery-active': i === selectedIndex, 'ngx-gallery-clickable': clickable }\\\"\\n       [attr.aria-label]=\\\"labels[i]\\\">\\n       <div *ngIf=\\\"getFileType(image) === 'image'\\\" [style.background-image]=\\\"getSafeUrl(image)\\\" class=\\\"ngx-gallery-thumbnail\\\"\\n       [ngClass]=\\\"{ 'ngx-gallery-active': i === selectedIndex, 'ngx-gallery-clickable': clickable }\\\"\\n       style=\\\"width: 100%; height: 100%; position:absolute;\\\"></div>\\n       <div *ngIf=\\\"getFileType(image) === 'video'\\\" class=\\\"ngx-gallery-thumbnail-video\\\">\\n        <video  class=\\\"ngx-gallery-thumbnail\\\" [ngClass]=\\\"{ 'ngx-gallery-active': i === selectedIndex, 'ngx-gallery-clickable': clickable }\\\"\\n        style=\\\"width: 100%; height: 100%; position:absolute; left:0; background:#000;\\\">\\n        <source [src]=\\\"image\\\">\\n          Your browser does not support the video tag.\\n       </video>\\n      </div>\\n       <div class=\\\"ngx-gallery-icons-wrapper\\\">\\n        <ngx-gallery-action *ngFor=\\\"let action of actions\\\" [icon]=\\\"action.icon\\\" [disabled]=\\\"action.disabled\\\"\\n                            [titleText]=\\\"action.titleText\\\" (closeClick)=\\\"action.onClick($event, i)\\\"></ngx-gallery-action>\\n      </div>\\n      <div class=\\\"ngx-gallery-remaining-count-overlay\\\"\\n           *ngIf=\\\"remainingCount && remainingCountValue && (i === (rows * columns) - 1)\\\">\\n        <span class=\\\"ngx-gallery-remaining-count\\\">+{{remainingCountValue}}</span>\\n      </div>\\n    </a>\\n  </div>\\n</div>\\n<ngx-gallery-arrows *ngIf=\\\"canShowArrows()\\\" (prevClick)=\\\"moveLeft()\\\" (nextClick)=\\\"moveRight()\\\"\\n                    [prevDisabled]=\\\"!canMoveLeft()\\\" [nextDisabled]=\\\"!canMoveRight()\\\" [arrowPrevIcon]=\\\"arrowPrevIcon\\\"\\n                    [arrowNextIcon]=\\\"arrowNextIcon\\\"></ngx-gallery-arrows>\\n\", styles: [\"@charset \\\"UTF-8\\\";:host{width:100%;display:inline-block;position:relative;font-size:25px}.ngx-gallery-thumbnails-wrapper{width:100%;height:100%;position:absolute;overflow:hidden}.ngx-gallery-thumbnails{height:100%;width:100%;position:absolute;left:0;transform:translate(0);transition:transform .5s ease-in-out;will-change:transform}.ngx-gallery-thumbnails .ngx-gallery-thumbnail{position:absolute;height:100%;background-position:center;background-repeat:no-repeat;text-decoration:none;border:1px double black}.ngx-gallery-thumbnails .ngx-gallery-thumbnail .ngx-gallery-thumbnail-video:after{content:\\\"\\\\f144\\\";display:block;position:absolute;background:#0000;height:100%;width:100%;left:0;top:calc(50% - 20px);font-size:40px;color:#fff;margin:0;padding:0;font-family:fontawesome;text-shadow:0px 4px 3px rgba(0,0,0,.4),0px 8px 13px rgba(0,0,0,.1),0px 18px 23px rgba(0,0,0,.1)}.ngx-gallery-thumbnails .ngx-gallery-thumbnail .img{background-size:cover;height:100%}.ngx-gallery-thumbnails .ngx-gallery-thumbnail.ngx-gallery-active{border:1px double #cc4548}.ngx-gallery-thumbnail-size-cover .ngx-gallery-thumbnails .ngx-gallery-thumbnail{background-size:cover}.ngx-gallery-thumbnail-size-contain .ngx-gallery-thumbnails .ngx-gallery-thumbnail{background-size:contain}.ngx-gallery-remaining-count-overlay{width:100%;height:100%;position:absolute;left:0;top:0;background-color:#0006}.ngx-gallery-remaining-count{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#fff;font-size:30px}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i1.DomSanitizer }, { type: i0.ElementRef }, { type: NgxGalleryService }]; }, propDecorators: { images: [{\n                type: Input\n            }], isAnimating: [{\n                type: Input\n            }], links: [{\n                type: Input\n            }], labels: [{\n                type: Input\n            }], linkTarget: [{\n                type: Input\n            }], columns: [{\n                type: Input\n            }], rows: [{\n                type: Input\n            }], arrows: [{\n                type: Input\n            }], arrowsAutoHide: [{\n                type: Input\n            }], margin: [{\n                type: Input\n            }], selectedIndex: [{\n                type: Input\n            }], clickable: [{\n                type: Input\n            }], swipe: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], arrowPrevIcon: [{\n                type: Input\n            }], arrowNextIcon: [{\n                type: Input\n            }], moveSize: [{\n                type: Input\n            }], order: [{\n                type: Input\n            }], remainingCount: [{\n                type: Input\n            }], lazyLoading: [{\n                type: Input\n            }], actions: [{\n                type: Input\n            }], activeChange: [{\n                type: Output\n            }], onMouseEnter: [{\n                type: HostListener,\n                args: ['mouseenter']\n            }], onMouseLeave: [{\n                type: HostListener,\n                args: ['mouseleave']\n            }] } });\n\nclass NgxGalleryAction {\n    constructor(action) {\n        this.icon = action.icon;\n        this.disabled = action.disabled ? action.disabled : false;\n        this.titleText = action.titleText ? action.titleText : '';\n        this.onClick = action.onClick;\n    }\n}\n\nclass NgxGalleryLayout {\n}\nNgxGalleryLayout.ThumbnailsTop = 'thumbnails-top';\nNgxGalleryLayout.ThumbnailsBottom = 'thumbnails-bottom';\n\nclass NgxGalleryImageSize {\n}\nNgxGalleryImageSize.Cover = 'cover';\nNgxGalleryImageSize.Contain = 'contain';\n\nclass NgxGalleryOptions {\n    constructor(obj) {\n        const preventDefaults = obj.breakpoint === undefined ? false : true;\n        function use(source, defaultValue) {\n            return obj && (source !== undefined || preventDefaults) ? source : defaultValue;\n        }\n        this.breakpoint = use(obj.breakpoint, undefined);\n        this.width = use(obj.width, '500px');\n        this.height = use(obj.height, '400px');\n        this.fullWidth = use(obj.fullWidth, false);\n        this.layout = use(obj.layout, NgxGalleryLayout.ThumbnailsBottom);\n        this.startIndex = use(obj.startIndex, 0);\n        this.linkTarget = use(obj.linkTarget, '_blank');\n        this.lazyLoading = use(obj.lazyLoading, true);\n        this.image = use(obj.image, true);\n        this.imagePercent = use(obj.imagePercent, 75);\n        this.imageArrows = use(obj.imageArrows, true);\n        this.imageArrowsAutoHide = use(obj.imageArrowsAutoHide, false);\n        this.imageSwipe = use(obj.imageSwipe, false);\n        this.imageAnimation = use(obj.imageAnimation, NgxGalleryAnimation.Fade);\n        this.imageSize = use(obj.imageSize, NgxGalleryImageSize.Cover);\n        this.imageAutoPlay = use(obj.imageAutoPlay, false);\n        this.imageAutoPlayInterval = use(obj.imageAutoPlayInterval, 2000);\n        this.imageAutoPlayPauseOnHover = use(obj.imageAutoPlayPauseOnHover, false);\n        this.imageInfinityMove = use(obj.imageInfinityMove, false);\n        if (obj && obj.imageActions && obj.imageActions.length) {\n            obj.imageActions = obj.imageActions.map(action => new NgxGalleryAction(action));\n        }\n        this.imageActions = use(obj.imageActions, []);\n        this.imageDescription = use(obj.imageDescription, false);\n        this.imageBullets = use(obj.imageBullets, false);\n        this.thumbnails = use(obj.thumbnails, true);\n        this.thumbnailsColumns = use(obj.thumbnailsColumns, 4);\n        this.thumbnailsRows = use(obj.thumbnailsRows, 1);\n        this.thumbnailsPercent = use(obj.thumbnailsPercent, 25);\n        this.thumbnailsMargin = use(obj.thumbnailsMargin, 10);\n        this.thumbnailsArrows = use(obj.thumbnailsArrows, true);\n        this.thumbnailsArrowsAutoHide = use(obj.thumbnailsArrowsAutoHide, false);\n        this.thumbnailsSwipe = use(obj.thumbnailsSwipe, false);\n        this.thumbnailsMoveSize = use(obj.thumbnailsMoveSize, 1);\n        this.thumbnailsOrder = use(obj.thumbnailsOrder, NgxGalleryOrder.Column);\n        this.thumbnailsRemainingCount = use(obj.thumbnailsRemainingCount, false);\n        this.thumbnailsAsLinks = use(obj.thumbnailsAsLinks, false);\n        this.thumbnailsAutoHide = use(obj.thumbnailsAutoHide, false);\n        this.thumbnailMargin = use(obj.thumbnailMargin, 10);\n        this.thumbnailSize = use(obj.thumbnailSize, NgxGalleryImageSize.Cover);\n        if (obj && obj.thumbnailActions && obj.thumbnailActions.length) {\n            obj.thumbnailActions = obj.thumbnailActions.map(action => new NgxGalleryAction(action));\n        }\n        this.thumbnailActions = use(obj.thumbnailActions, []);\n        this.thumbnailClasses = use(obj.thumbnailClasses, []);\n        this.preview = use(obj.preview, true);\n        this.previewDescription = use(obj.previewDescription, true);\n        this.previewArrows = use(obj.previewArrows, true);\n        this.previewArrowsAutoHide = use(obj.previewArrowsAutoHide, false);\n        this.previewSwipe = use(obj.previewSwipe, false);\n        this.previewFullscreen = use(obj.previewFullscreen, false);\n        this.previewForceFullscreen = use(obj.previewForceFullscreen, false);\n        this.previewCloseOnClick = use(obj.previewCloseOnClick, false);\n        this.previewCloseOnEsc = use(obj.previewCloseOnEsc, false);\n        this.previewKeyboardNavigation = use(obj.previewKeyboardNavigation, false);\n        this.previewAnimation = use(obj.previewAnimation, true);\n        this.previewAutoPlay = use(obj.previewAutoPlay, false);\n        this.previewAutoPlayInterval = use(obj.previewAutoPlayInterval, 2000);\n        this.previewAutoPlayPauseOnHover = use(obj.previewAutoPlayPauseOnHover, false);\n        this.previewInfinityMove = use(obj.previewInfinityMove, false);\n        this.previewZoom = use(obj.previewZoom, false);\n        this.previewZoomStep = use(obj.previewZoomStep, 0.1);\n        this.previewZoomMax = use(obj.previewZoomMax, 2);\n        this.previewZoomMin = use(obj.previewZoomMin, 0.5);\n        this.previewRotate = use(obj.previewRotate, false);\n        this.previewDownload = use(obj.previewDownload, false);\n        this.previewCustom = use(obj.previewCustom, undefined);\n        this.previewBullets = use(obj.previewBullets, false);\n        this.arrowPrevIcon = use(obj.arrowPrevIcon, 'fa fa-arrow-circle-left');\n        this.arrowNextIcon = use(obj.arrowNextIcon, 'fa fa-arrow-circle-right');\n        this.closeIcon = use(obj.closeIcon, 'fa fa-times-circle');\n        this.fullscreenIcon = use(obj.fullscreenIcon, 'fa fa-arrows-alt');\n        this.spinnerIcon = use(obj.spinnerIcon, 'fa fa-spinner fa-pulse fa-3x fa-fw');\n        this.zoomInIcon = use(obj.zoomInIcon, 'fa fa-search-plus');\n        this.zoomOutIcon = use(obj.zoomOutIcon, 'fa fa-search-minus');\n        this.rotateLeftIcon = use(obj.rotateLeftIcon, 'fa fa-undo');\n        this.rotateRightIcon = use(obj.rotateRightIcon, 'fa fa-repeat');\n        this.downloadIcon = use(obj.downloadIcon, 'fa fa-arrow-circle-down');\n        if (obj && obj.actions && obj.actions.length) {\n            obj.actions = obj.actions.map(action => new NgxGalleryAction(action));\n        }\n        this.actions = use(obj.actions, []);\n    }\n}\n\nclass NgxGalleryOrderedImage {\n    constructor(obj) {\n        this.src = obj.src;\n        this.type = obj.type;\n        this.index = obj.index;\n    }\n}\n\nclass NgxGalleryComponent {\n    constructor(myElement, helperService) {\n        this.myElement = myElement;\n        this.helperService = helperService;\n        this.options = [{}];\n        this.imagesReady = new EventEmitter();\n        // eslint-disable-next-line @angular-eslint/no-output-native\n        this.change = new EventEmitter();\n        this.previewOpen = new EventEmitter();\n        this.previewClose = new EventEmitter();\n        this.previewChange = new EventEmitter();\n        this.oldImagesLength = 0;\n        this.selectedIndex = 0;\n        this.breakpoint = undefined;\n        this.prevBreakpoint = undefined;\n    }\n    ngOnInit() {\n        this.options = this.options.map((opt) => new NgxGalleryOptions(opt));\n        this.sortOptions();\n        this.setBreakpoint();\n        this.setOptions();\n        this.checkFullWidth();\n        if (this.currentOptions) {\n            this.selectedIndex = this.currentOptions.startIndex;\n        }\n    }\n    ngDoCheck() {\n        if (this.images !== undefined && (this.images.length !== this.oldImagesLength)\n            || (this.images !== this.oldImages)) {\n            this.oldImagesLength = this.images.length;\n            this.oldImages = this.images;\n            this.setOptions();\n            this.setImages();\n            if (this.images && this.images.length) {\n                this.imagesReady.emit();\n            }\n            if (this.image) {\n                this.image.reset(this.currentOptions.startIndex);\n            }\n            if (this.currentOptions.thumbnailsAutoHide && this.currentOptions.thumbnails\n                && this.images.length <= 1) {\n                this.currentOptions.thumbnails = false;\n                this.currentOptions.imageArrows = false;\n            }\n            this.resetThumbnails();\n        }\n    }\n    ngAfterViewInit() {\n        this.checkFullWidth();\n    }\n    onResize() {\n        this.setBreakpoint();\n        if (this.prevBreakpoint !== this.breakpoint) {\n            this.setOptions();\n            this.resetThumbnails();\n        }\n        if (this.currentOptions && this.currentOptions.fullWidth) {\n            if (this.fullWidthTimeout) {\n                clearTimeout(this.fullWidthTimeout);\n            }\n            this.fullWidthTimeout = setTimeout(() => {\n                this.checkFullWidth();\n            }, 200);\n        }\n    }\n    getImageHeight() {\n        return (this.currentOptions && this.currentOptions.thumbnails) ?\n            this.currentOptions.imagePercent + '%' : '100%';\n    }\n    getThumbnailsHeight() {\n        if (this.currentOptions && this.currentOptions.image) {\n            return 'calc(' + this.currentOptions.thumbnailsPercent + '% - '\n                + this.currentOptions.thumbnailsMargin + 'px)';\n        }\n        else {\n            return '100%';\n        }\n    }\n    getThumbnailsMarginTop() {\n        if (this.currentOptions && this.currentOptions.layout === NgxGalleryLayout.ThumbnailsBottom) {\n            return this.currentOptions.thumbnailsMargin + 'px';\n        }\n        else {\n            return '0px';\n        }\n    }\n    getThumbnailsMarginBottom() {\n        if (this.currentOptions && this.currentOptions.layout === NgxGalleryLayout.ThumbnailsTop) {\n            return this.currentOptions.thumbnailsMargin + 'px';\n        }\n        else {\n            return '0px';\n        }\n    }\n    openPreview(index) {\n        if (this.currentOptions.previewCustom) {\n            this.currentOptions.previewCustom(index);\n        }\n        else {\n            this.previewEnabled = true;\n            this.preview.open(index);\n        }\n    }\n    onPreviewOpen() {\n        this.previewOpen.emit();\n        if (this.image && this.image.autoPlay) {\n            this.image.stopAutoPlay();\n        }\n    }\n    onPreviewClose() {\n        this.previewEnabled = false;\n        this.previewClose.emit();\n        if (this.image && this.image.autoPlay) {\n            this.image.startAutoPlay();\n        }\n    }\n    selectFromImage(index) {\n        this.select(index);\n    }\n    selectFromThumbnails(index) {\n        this.select(index);\n        if (this.currentOptions && this.currentOptions.thumbnails && this.currentOptions.preview\n            && (!this.currentOptions.image || this.currentOptions.thumbnailsRemainingCount)) {\n            this.openPreview(this.selectedIndex);\n        }\n    }\n    show(index) {\n        this.select(index);\n    }\n    showNext() {\n        this.image.showNext();\n    }\n    showPrev() {\n        this.image.showPrev();\n    }\n    canShowNext() {\n        if (this.images && this.currentOptions) {\n            return !!(this.currentOptions.imageInfinityMove || this.selectedIndex < this.images.length - 1);\n        }\n        else {\n            return false;\n        }\n    }\n    canShowPrev() {\n        if (this.images && this.currentOptions) {\n            return !!(this.currentOptions.imageInfinityMove || this.selectedIndex > 0);\n        }\n        else {\n            return false;\n        }\n    }\n    previewSelect(index) {\n        this.previewChange.emit({ index, image: this.images[index] });\n    }\n    moveThumbnailsRight() {\n        this.thumbnails.moveRight();\n    }\n    moveThumbnailsLeft() {\n        this.thumbnails.moveLeft();\n    }\n    canMoveThumbnailsRight() {\n        return this.thumbnails.canMoveRight();\n    }\n    canMoveThumbnailsLeft() {\n        return this.thumbnails.canMoveLeft();\n    }\n    resetThumbnails() {\n        if (this.thumbnails) {\n            this.thumbnails.reset(this.currentOptions.startIndex);\n        }\n    }\n    select(index) {\n        this.selectedIndex = index;\n        this.change.emit({\n            index,\n            image: this.images[index]\n        });\n    }\n    checkFullWidth() {\n        if (this.currentOptions && this.currentOptions.fullWidth) {\n            this.width = document.body.clientWidth + 'px';\n            this.left = 'translateX(' + (-(document.body.clientWidth -\n                this.myElement.nativeElement.parentNode.innerWidth) / 2) + 'px)';\n        }\n    }\n    setImages() {\n        this.images.forEach((img) => img.type = this.helperService.getFileType(img.url || img.big || img.medium || img.small || ''));\n        this.smallImages = this.images.map((img) => img.small);\n        this.mediumImages = this.images.map((img, i) => new NgxGalleryOrderedImage({\n            src: img.medium,\n            type: img.type,\n            index: i\n        }));\n        this.bigImages = this.images.map((img) => img.big);\n        this.descriptions = this.images.map((img) => img.description);\n        this.links = this.images.map((img) => img.url);\n        this.labels = this.images.map((img) => img.label);\n    }\n    setBreakpoint() {\n        this.prevBreakpoint = this.breakpoint;\n        let breakpoints;\n        if (typeof window !== 'undefined') {\n            breakpoints = this.options.filter((opt) => opt.breakpoint >= window.innerWidth)\n                .map((opt) => opt.breakpoint);\n        }\n        if (breakpoints && breakpoints.length) {\n            this.breakpoint = breakpoints.pop();\n        }\n        else {\n            this.breakpoint = undefined;\n        }\n    }\n    sortOptions() {\n        this.options = [\n            ...this.options.filter((a) => a.breakpoint === undefined),\n            ...this.options\n                .filter((a) => a.breakpoint !== undefined)\n                .sort((a, b) => b.breakpoint - a.breakpoint)\n        ];\n    }\n    setOptions() {\n        this.currentOptions = new NgxGalleryOptions({});\n        this.options\n            .filter((opt) => opt.breakpoint === undefined || opt.breakpoint >= this.breakpoint)\n            .map((opt) => this.combineOptions(this.currentOptions, opt));\n        this.width = this.currentOptions.width;\n        this.height = this.currentOptions.height;\n    }\n    combineOptions(first, second) {\n        Object.keys(second).map((val) => first[val] = second[val] !== undefined ? second[val] : first[val]);\n    }\n    setAnimating(event) {\n        this.isAnimating = event;\n    }\n}\nNgxGalleryComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryComponent, deps: [{ token: i0.ElementRef }, { token: NgxGalleryService }], target: i0.ɵɵFactoryTarget.Component });\nNgxGalleryComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.3\", type: NgxGalleryComponent, selector: \"ngx-gallery\", inputs: { options: \"options\", images: \"images\" }, outputs: { imagesReady: \"imagesReady\", change: \"change\", previewOpen: \"previewOpen\", previewClose: \"previewClose\", previewChange: \"previewChange\" }, host: { listeners: { \"window:resize\": \"onResize()\" }, properties: { \"style.width\": \"this.width\", \"style.height\": \"this.height\", \"style.transform\": \"this.left\" } }, providers: [NgxGalleryService], viewQueries: [{ propertyName: \"preview\", first: true, predicate: NgxGalleryPreviewComponent, descendants: true }, { propertyName: \"image\", first: true, predicate: NgxGalleryImageComponent, descendants: true }, { propertyName: \"thumbnails\", first: true, predicate: NgxGalleryThumbnailsComponent, descendants: true }], ngImport: i0, template: \"<div class=\\\"ngx-gallery-layout {{currentOptions?.layout}}\\\">\\n  <ngx-gallery-image *ngIf=\\\"currentOptions?.image\\\" [style.height]=\\\"getImageHeight()\\\" [images]=\\\"mediumImages\\\"\\n                     [clickable]=\\\"currentOptions?.preview\\\" [selectedIndex]=\\\"selectedIndex\\\"\\n                     [arrows]=\\\"currentOptions?.imageArrows\\\" [arrowsAutoHide]=\\\"currentOptions?.imageArrowsAutoHide\\\"\\n                     [arrowPrevIcon]=\\\"currentOptions?.arrowPrevIcon\\\" [arrowNextIcon]=\\\"currentOptions?.arrowNextIcon\\\"\\n                     [swipe]=\\\"currentOptions?.imageSwipe\\\" [animation]=\\\"currentOptions?.imageAnimation\\\"\\n                     [size]=\\\"currentOptions?.imageSize\\\" [autoPlay]=\\\"currentOptions?.imageAutoPlay\\\"\\n                     [autoPlayInterval]=\\\"currentOptions?.imageAutoPlayInterval\\\"\\n                     [autoPlayPauseOnHover]=\\\"currentOptions?.imageAutoPlayPauseOnHover\\\"\\n                     [infinityMove]=\\\"currentOptions?.imageInfinityMove\\\" [lazyLoading]=\\\"currentOptions?.lazyLoading\\\"\\n                     [actions]=\\\"currentOptions?.imageActions\\\" [descriptions]=\\\"descriptions\\\"\\n                     [showDescription]=\\\"currentOptions?.imageDescription\\\" [bullets]=\\\"currentOptions?.imageBullets\\\"\\n                     (imageClick)=\\\"openPreview($event)\\\" (activeChange)=\\\"selectFromImage($event)\\\" (animating)=\\\"setAnimating($event)\\\"></ngx-gallery-image>\\n\\n  <ngx-gallery-thumbnails *ngIf=\\\"currentOptions?.thumbnails\\\" [style.marginTop]=\\\"getThumbnailsMarginTop()\\\"\\n                          [style.marginBottom]=\\\"getThumbnailsMarginBottom()\\\" [style.height]=\\\"getThumbnailsHeight()\\\"\\n                          [images]=\\\"smallImages\\\" [isAnimating]=\\\"isAnimating\\\" [links]=\\\"currentOptions?.thumbnailsAsLinks ? links : []\\\"\\n                          [labels]=\\\"labels\\\" [linkTarget]=\\\"currentOptions?.linkTarget\\\" [selectedIndex]=\\\"selectedIndex\\\"\\n                          [columns]=\\\"currentOptions?.thumbnailsColumns\\\" [rows]=\\\"currentOptions?.thumbnailsRows\\\"\\n                          [margin]=\\\"currentOptions?.thumbnailMargin\\\" [arrows]=\\\"currentOptions?.thumbnailsArrows\\\"\\n                          [arrowsAutoHide]=\\\"currentOptions?.thumbnailsArrowsAutoHide\\\"\\n                          [arrowPrevIcon]=\\\"currentOptions?.arrowPrevIcon\\\"\\n                          [arrowNextIcon]=\\\"currentOptions?.arrowNextIcon\\\"\\n                          [clickable]=\\\"currentOptions?.image || currentOptions?.preview\\\"\\n                          [swipe]=\\\"currentOptions?.thumbnailsSwipe\\\" [size]=\\\"currentOptions?.thumbnailSize\\\"\\n                          [moveSize]=\\\"currentOptions?.thumbnailsMoveSize\\\" [order]=\\\"currentOptions?.thumbnailsOrder\\\"\\n                          [remainingCount]=\\\"currentOptions?.thumbnailsRemainingCount\\\"\\n                          [lazyLoading]=\\\"currentOptions?.lazyLoading\\\" [actions]=\\\"currentOptions?.thumbnailActions\\\"\\n                          (activeChange)=\\\"selectFromThumbnails($event)\\\" [ngClass]=\\\"currentOptions?.thumbnailClasses\\\"></ngx-gallery-thumbnails>\\n\\n  <ngx-gallery-preview [images]=\\\"bigImages\\\" [descriptions]=\\\"descriptions\\\"\\n                       [showDescription]=\\\"currentOptions?.previewDescription\\\"\\n                       [arrowPrevIcon]=\\\"currentOptions?.arrowPrevIcon\\\" [arrowNextIcon]=\\\"currentOptions?.arrowNextIcon\\\"\\n                       [closeIcon]=\\\"currentOptions?.closeIcon\\\" [fullscreenIcon]=\\\"currentOptions?.fullscreenIcon\\\"\\n                       [spinnerIcon]=\\\"currentOptions?.spinnerIcon\\\" [arrows]=\\\"currentOptions?.previewArrows\\\"\\n                       [arrowsAutoHide]=\\\"currentOptions?.previewArrowsAutoHide\\\" [swipe]=\\\"currentOptions?.previewSwipe\\\"\\n                       [fullscreen]=\\\"currentOptions?.previewFullscreen\\\"\\n                       [forceFullscreen]=\\\"currentOptions?.previewForceFullscreen\\\"\\n                       [closeOnClick]=\\\"currentOptions?.previewCloseOnClick\\\"\\n                       [closeOnEsc]=\\\"currentOptions?.previewCloseOnEsc\\\"\\n                       [keyboardNavigation]=\\\"currentOptions?.previewKeyboardNavigation\\\"\\n                       [animation]=\\\"currentOptions?.previewAnimation\\\" [autoPlay]=\\\"currentOptions?.previewAutoPlay\\\"\\n                       [autoPlayInterval]=\\\"currentOptions?.previewAutoPlayInterval\\\"\\n                       [autoPlayPauseOnHover]=\\\"currentOptions?.previewAutoPlayPauseOnHover\\\"\\n                       [infinityMove]=\\\"currentOptions?.previewInfinityMove\\\" [zoom]=\\\"currentOptions?.previewZoom\\\"\\n                       [zoomStep]=\\\"currentOptions?.previewZoomStep\\\" [zoomMax]=\\\"currentOptions?.previewZoomMax\\\"\\n                       [zoomMin]=\\\"currentOptions?.previewZoomMin\\\" [zoomInIcon]=\\\"currentOptions?.zoomInIcon\\\"\\n                       [zoomOutIcon]=\\\"currentOptions?.zoomOutIcon\\\" [actions]=\\\"currentOptions?.actions\\\"\\n                       [rotate]=\\\"currentOptions?.previewRotate\\\" [rotateLeftIcon]=\\\"currentOptions?.rotateLeftIcon\\\"\\n                       [rotateRightIcon]=\\\"currentOptions?.rotateRightIcon\\\" [download]=\\\"currentOptions?.previewDownload\\\"\\n                       [downloadIcon]=\\\"currentOptions?.downloadIcon\\\" [bullets]=\\\"currentOptions?.previewBullets\\\"\\n                       (previewClose)=\\\"onPreviewClose()\\\" (previewOpen)=\\\"onPreviewOpen()\\\"\\n                       (activeChange)=\\\"previewSelect($event)\\\"\\n                       [class.ngx-gallery-active]=\\\"previewEnabled\\\"></ngx-gallery-preview>\\n</div>\\n\", styles: [\":host{display:inline-block}:host>*{float:left}.ngx-gallery-layout{width:100%;height:100%;display:flex;flex-direction:column}.ngx-gallery-layout.thumbnails-top ngx-gallery-image{order:2}.ngx-gallery-layout.thumbnails-top ngx-gallery-thumbnails{order:1}.ngx-gallery-layout.thumbnails-bottom ngx-gallery-image{order:1}.ngx-gallery-layout.thumbnails-bottom ngx-gallery-thumbnails{order:2}*{box-sizing:border-box}.ngx-gallery-icon{color:#fff;position:absolute;display:inline-block}.ngx-gallery-icon .ngx-gallery-icon-content{display:block}ngx-gallery-preview{font-size:25px}ngx-gallery-preview .ngx-gallery-icon{z-index:2000}.ngx-gallery-clickable{cursor:pointer}.ngx-gallery-icons-wrapper .ngx-gallery-icon{position:relative;margin-right:5px;margin-top:5px;font-size:20px;cursor:pointer}.ngx-gallery-icons-wrapper{float:right}\\n\"], components: [{ type: NgxGalleryImageComponent, selector: \"ngx-gallery-image\", inputs: [\"images\", \"clickable\", \"selectedIndex\", \"arrows\", \"arrowsAutoHide\", \"swipe\", \"animation\", \"size\", \"arrowPrevIcon\", \"arrowNextIcon\", \"autoPlay\", \"autoPlayInterval\", \"autoPlayPauseOnHover\", \"infinityMove\", \"lazyLoading\", \"actions\", \"descriptions\", \"showDescription\", \"bullets\"], outputs: [\"imageClick\", \"activeChange\", \"animating\"] }, { type: NgxGalleryThumbnailsComponent, selector: \"ngx-gallery-thumbnails\", inputs: [\"images\", \"isAnimating\", \"links\", \"labels\", \"linkTarget\", \"columns\", \"rows\", \"arrows\", \"arrowsAutoHide\", \"margin\", \"selectedIndex\", \"clickable\", \"swipe\", \"size\", \"arrowPrevIcon\", \"arrowNextIcon\", \"moveSize\", \"order\", \"remainingCount\", \"lazyLoading\", \"actions\"], outputs: [\"activeChange\"] }, { type: NgxGalleryPreviewComponent, selector: \"ngx-gallery-preview\", inputs: [\"images\", \"descriptions\", \"showDescription\", \"arrows\", \"arrowsAutoHide\", \"swipe\", \"fullscreen\", \"forceFullscreen\", \"closeOnClick\", \"closeOnEsc\", \"keyboardNavigation\", \"arrowPrevIcon\", \"arrowNextIcon\", \"closeIcon\", \"fullscreenIcon\", \"spinnerIcon\", \"autoPlay\", \"autoPlayInterval\", \"autoPlayPauseOnHover\", \"infinityMove\", \"zoom\", \"zoomStep\", \"zoomMax\", \"zoomMin\", \"zoomInIcon\", \"zoomOutIcon\", \"animation\", \"actions\", \"rotate\", \"rotateLeftIcon\", \"rotateRightIcon\", \"download\", \"downloadIcon\", \"bullets\"], outputs: [\"previewOpen\", \"previewClose\", \"activeChange\"] }], directives: [{ type: i6.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i6.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-gallery', encapsulation: ViewEncapsulation.None, providers: [NgxGalleryService], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"ngx-gallery-layout {{currentOptions?.layout}}\\\">\\n  <ngx-gallery-image *ngIf=\\\"currentOptions?.image\\\" [style.height]=\\\"getImageHeight()\\\" [images]=\\\"mediumImages\\\"\\n                     [clickable]=\\\"currentOptions?.preview\\\" [selectedIndex]=\\\"selectedIndex\\\"\\n                     [arrows]=\\\"currentOptions?.imageArrows\\\" [arrowsAutoHide]=\\\"currentOptions?.imageArrowsAutoHide\\\"\\n                     [arrowPrevIcon]=\\\"currentOptions?.arrowPrevIcon\\\" [arrowNextIcon]=\\\"currentOptions?.arrowNextIcon\\\"\\n                     [swipe]=\\\"currentOptions?.imageSwipe\\\" [animation]=\\\"currentOptions?.imageAnimation\\\"\\n                     [size]=\\\"currentOptions?.imageSize\\\" [autoPlay]=\\\"currentOptions?.imageAutoPlay\\\"\\n                     [autoPlayInterval]=\\\"currentOptions?.imageAutoPlayInterval\\\"\\n                     [autoPlayPauseOnHover]=\\\"currentOptions?.imageAutoPlayPauseOnHover\\\"\\n                     [infinityMove]=\\\"currentOptions?.imageInfinityMove\\\" [lazyLoading]=\\\"currentOptions?.lazyLoading\\\"\\n                     [actions]=\\\"currentOptions?.imageActions\\\" [descriptions]=\\\"descriptions\\\"\\n                     [showDescription]=\\\"currentOptions?.imageDescription\\\" [bullets]=\\\"currentOptions?.imageBullets\\\"\\n                     (imageClick)=\\\"openPreview($event)\\\" (activeChange)=\\\"selectFromImage($event)\\\" (animating)=\\\"setAnimating($event)\\\"></ngx-gallery-image>\\n\\n  <ngx-gallery-thumbnails *ngIf=\\\"currentOptions?.thumbnails\\\" [style.marginTop]=\\\"getThumbnailsMarginTop()\\\"\\n                          [style.marginBottom]=\\\"getThumbnailsMarginBottom()\\\" [style.height]=\\\"getThumbnailsHeight()\\\"\\n                          [images]=\\\"smallImages\\\" [isAnimating]=\\\"isAnimating\\\" [links]=\\\"currentOptions?.thumbnailsAsLinks ? links : []\\\"\\n                          [labels]=\\\"labels\\\" [linkTarget]=\\\"currentOptions?.linkTarget\\\" [selectedIndex]=\\\"selectedIndex\\\"\\n                          [columns]=\\\"currentOptions?.thumbnailsColumns\\\" [rows]=\\\"currentOptions?.thumbnailsRows\\\"\\n                          [margin]=\\\"currentOptions?.thumbnailMargin\\\" [arrows]=\\\"currentOptions?.thumbnailsArrows\\\"\\n                          [arrowsAutoHide]=\\\"currentOptions?.thumbnailsArrowsAutoHide\\\"\\n                          [arrowPrevIcon]=\\\"currentOptions?.arrowPrevIcon\\\"\\n                          [arrowNextIcon]=\\\"currentOptions?.arrowNextIcon\\\"\\n                          [clickable]=\\\"currentOptions?.image || currentOptions?.preview\\\"\\n                          [swipe]=\\\"currentOptions?.thumbnailsSwipe\\\" [size]=\\\"currentOptions?.thumbnailSize\\\"\\n                          [moveSize]=\\\"currentOptions?.thumbnailsMoveSize\\\" [order]=\\\"currentOptions?.thumbnailsOrder\\\"\\n                          [remainingCount]=\\\"currentOptions?.thumbnailsRemainingCount\\\"\\n                          [lazyLoading]=\\\"currentOptions?.lazyLoading\\\" [actions]=\\\"currentOptions?.thumbnailActions\\\"\\n                          (activeChange)=\\\"selectFromThumbnails($event)\\\" [ngClass]=\\\"currentOptions?.thumbnailClasses\\\"></ngx-gallery-thumbnails>\\n\\n  <ngx-gallery-preview [images]=\\\"bigImages\\\" [descriptions]=\\\"descriptions\\\"\\n                       [showDescription]=\\\"currentOptions?.previewDescription\\\"\\n                       [arrowPrevIcon]=\\\"currentOptions?.arrowPrevIcon\\\" [arrowNextIcon]=\\\"currentOptions?.arrowNextIcon\\\"\\n                       [closeIcon]=\\\"currentOptions?.closeIcon\\\" [fullscreenIcon]=\\\"currentOptions?.fullscreenIcon\\\"\\n                       [spinnerIcon]=\\\"currentOptions?.spinnerIcon\\\" [arrows]=\\\"currentOptions?.previewArrows\\\"\\n                       [arrowsAutoHide]=\\\"currentOptions?.previewArrowsAutoHide\\\" [swipe]=\\\"currentOptions?.previewSwipe\\\"\\n                       [fullscreen]=\\\"currentOptions?.previewFullscreen\\\"\\n                       [forceFullscreen]=\\\"currentOptions?.previewForceFullscreen\\\"\\n                       [closeOnClick]=\\\"currentOptions?.previewCloseOnClick\\\"\\n                       [closeOnEsc]=\\\"currentOptions?.previewCloseOnEsc\\\"\\n                       [keyboardNavigation]=\\\"currentOptions?.previewKeyboardNavigation\\\"\\n                       [animation]=\\\"currentOptions?.previewAnimation\\\" [autoPlay]=\\\"currentOptions?.previewAutoPlay\\\"\\n                       [autoPlayInterval]=\\\"currentOptions?.previewAutoPlayInterval\\\"\\n                       [autoPlayPauseOnHover]=\\\"currentOptions?.previewAutoPlayPauseOnHover\\\"\\n                       [infinityMove]=\\\"currentOptions?.previewInfinityMove\\\" [zoom]=\\\"currentOptions?.previewZoom\\\"\\n                       [zoomStep]=\\\"currentOptions?.previewZoomStep\\\" [zoomMax]=\\\"currentOptions?.previewZoomMax\\\"\\n                       [zoomMin]=\\\"currentOptions?.previewZoomMin\\\" [zoomInIcon]=\\\"currentOptions?.zoomInIcon\\\"\\n                       [zoomOutIcon]=\\\"currentOptions?.zoomOutIcon\\\" [actions]=\\\"currentOptions?.actions\\\"\\n                       [rotate]=\\\"currentOptions?.previewRotate\\\" [rotateLeftIcon]=\\\"currentOptions?.rotateLeftIcon\\\"\\n                       [rotateRightIcon]=\\\"currentOptions?.rotateRightIcon\\\" [download]=\\\"currentOptions?.previewDownload\\\"\\n                       [downloadIcon]=\\\"currentOptions?.downloadIcon\\\" [bullets]=\\\"currentOptions?.previewBullets\\\"\\n                       (previewClose)=\\\"onPreviewClose()\\\" (previewOpen)=\\\"onPreviewOpen()\\\"\\n                       (activeChange)=\\\"previewSelect($event)\\\"\\n                       [class.ngx-gallery-active]=\\\"previewEnabled\\\"></ngx-gallery-preview>\\n</div>\\n\", styles: [\":host{display:inline-block}:host>*{float:left}.ngx-gallery-layout{width:100%;height:100%;display:flex;flex-direction:column}.ngx-gallery-layout.thumbnails-top ngx-gallery-image{order:2}.ngx-gallery-layout.thumbnails-top ngx-gallery-thumbnails{order:1}.ngx-gallery-layout.thumbnails-bottom ngx-gallery-image{order:1}.ngx-gallery-layout.thumbnails-bottom ngx-gallery-thumbnails{order:2}*{box-sizing:border-box}.ngx-gallery-icon{color:#fff;position:absolute;display:inline-block}.ngx-gallery-icon .ngx-gallery-icon-content{display:block}ngx-gallery-preview{font-size:25px}ngx-gallery-preview .ngx-gallery-icon{z-index:2000}.ngx-gallery-clickable{cursor:pointer}.ngx-gallery-icons-wrapper .ngx-gallery-icon{position:relative;margin-right:5px;margin-top:5px;font-size:20px;cursor:pointer}.ngx-gallery-icons-wrapper{float:right}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: NgxGalleryService }]; }, propDecorators: { options: [{\n                type: Input\n            }], images: [{\n                type: Input\n            }], imagesReady: [{\n                type: Output\n            }], change: [{\n                type: Output\n            }], previewOpen: [{\n                type: Output\n            }], previewClose: [{\n                type: Output\n            }], previewChange: [{\n                type: Output\n            }], preview: [{\n                type: ViewChild,\n                args: [NgxGalleryPreviewComponent]\n            }], image: [{\n                type: ViewChild,\n                args: [NgxGalleryImageComponent]\n            }], thumbnails: [{\n                type: ViewChild,\n                args: [NgxGalleryThumbnailsComponent]\n            }], width: [{\n                type: HostBinding,\n                args: ['style.width']\n            }], height: [{\n                type: HostBinding,\n                args: ['style.height']\n            }], left: [{\n                type: HostBinding,\n                args: ['style.transform']\n            }], onResize: [{\n                type: HostListener,\n                args: ['window:resize']\n            }] } });\n\nclass CustomHammerConfig extends HammerGestureConfig {\n    constructor() {\n        super(...arguments);\n        this.overrides = {\n            pinch: { enable: false },\n            rotate: { enable: false }\n        };\n    }\n}\nCustomHammerConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: CustomHammerConfig, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\nCustomHammerConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: CustomHammerConfig });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: CustomHammerConfig, decorators: [{\n            type: Injectable\n        }] });\nclass NgxGalleryModule {\n}\nNgxGalleryModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNgxGalleryModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryModule, declarations: [NgxGalleryComponent,\n        NgxGalleryImageComponent,\n        NgxGalleryArrowsComponent,\n        NgxGalleryThumbnailsComponent,\n        NgxGalleryPreviewComponent,\n        NgxGalleryActionComponent,\n        NgxGalleryBulletsComponent], imports: [CommonModule], exports: [NgxGalleryComponent] });\nNgxGalleryModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryModule, providers: [\n        { provide: HAMMER_GESTURE_CONFIG, useClass: CustomHammerConfig }\n    ], imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.3\", ngImport: i0, type: NgxGalleryModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [\n                        NgxGalleryComponent,\n                        NgxGalleryImageComponent,\n                        NgxGalleryArrowsComponent,\n                        NgxGalleryThumbnailsComponent,\n                        NgxGalleryPreviewComponent,\n                        NgxGalleryActionComponent,\n                        NgxGalleryBulletsComponent\n                    ],\n                    imports: [CommonModule],\n                    exports: [NgxGalleryComponent],\n                    providers: [\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: CustomHammerConfig }\n                    ]\n                }]\n        }] });\n\nclass NgxGalleryImage {\n    constructor(obj) {\n        this.small = obj.small;\n        this.medium = obj.medium;\n        this.big = obj.big;\n        this.description = obj.description;\n        this.url = obj.url;\n        this.type = obj.type;\n        this.label = obj.label;\n    }\n}\n\n/*\n * Public API Surface of ngx-gallery\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CustomHammerConfig, NgxGalleryAction, NgxGalleryAnimation, NgxGalleryArrowsComponent, NgxGalleryBulletsComponent, NgxGalleryComponent, NgxGalleryImage, NgxGalleryImageSize, NgxGalleryLayout, NgxGalleryModule, NgxGalleryOptions, NgxGalleryOrder, NgxGalleryOrderedImage, NgxGalleryPreviewComponent, NgxGalleryService, NgxGalleryThumbnailsComponent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,eAAe;AAC9K,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,mBAAmB,EAAEC,qBAAqB,QAAQ,2BAA2B;AACtF,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAAC,MAAAC,GAAA,GAAAC,EAAA;EAAA,sBAAAA;AAAA;AAAA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAqDmB3B,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,YAkF0W,CAAC;IAlF7W7B,EAAE,CAAA8B,UAAA,mBAAAC,+DAAAC,MAAA;MAAA,MAAAC,IAAA,GAAFjC,EAAE,CAAAkC,aAAA,CAAAP,GAAA,EAAAQ,KAAA;MAAA,MAAAC,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAkFsRF,MAAA,CAAAG,YAAA,CAAAP,MAAA,EAAAC,IAAsB,CAAC;IAAA,CAAC,CAAC;IAlFjTjC,EAAE,CAAAwC,YAAA,CAkFgX,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAQ,IAAA,GAAAP,GAAA,CAAAS,KAAA;IAAA,MAAAC,MAAA,GAlFnXpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,YAAFzC,EAAE,CAAA0C,eAAA,IAAApB,GAAA,EAAAW,IAAA,KAAAG,MAAA,CAAAO,MAAA,CAkFyW,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,yDAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAlF5W3B,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,4BA2bwlD,CAAC;IA3b3lD7B,EAAE,CAAA8B,UAAA,uBAAAgB,iGAAA;MAAF9C,EAAE,CAAAkC,aAAA,CAAAP,GAAA;MAAA,MAAAoB,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2b+3CS,MAAA,CAAAC,QAAA,CAAS,CAAC;IAAA,CAAC,CAAC,uBAAAC,iGAAA;MA3b74CjD,EAAE,CAAAkC,aAAA,CAAAP,GAAA;MAAA,MAAAoB,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2b05CS,MAAA,CAAAG,QAAA,CAAS,CAAC;IAAA,CAAC,CAAC;IA3bx6ClD,EAAE,CAAAwC,YAAA,CA2b6mD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3bhnD/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,kBAAAM,MAAA,CAAAI,WAAA,EA2bu8C,CAAC,kBAAAJ,MAAA,CAAAK,WAAA,EAAsD,CAAC,kBAAAL,MAAA,CAAAM,aAAiC,CAAC,kBAAAN,MAAA,CAAAO,aAAsD,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GA3b1lDxD,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,4BA2b65D,CAAC;IA3bh6D7B,EAAE,CAAA8B,UAAA,wBAAA2B,kGAAAzB,MAAA;MAAA,MAAA0B,SAAA,GAAF1D,EAAE,CAAAkC,aAAA,CAAAsB,GAAA,EAAAG,SAAA;MAAA,MAAAZ,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2b83DoB,SAAA,CAAAE,OAAA,CAAA5B,MAAA,EAAAe,MAAA,CAAAZ,KAA4B,CAAC;IAAA,CAAC,CAAC;IA3b/5DnC,EAAE,CAAAwC,YAAA,CA2bk7D,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAiC,SAAA,GAAAhC,GAAA,CAAAiC,SAAA;IA3br7D3D,EAAE,CAAAyC,UAAA,SAAAiB,SAAA,CAAAG,IA2boxD,CAAC,aAAAH,SAAA,CAAAI,QAA8B,CAAC,cAAAJ,SAAA,CAAAK,SAAyD,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3bh3DzB,EAAE,CAAA6B,cAAA,WA2b6hE,CAAC;IA3bhiE7B,EAAE,CAAAiE,SAAA,OA2bkmE,CAAC;IA3brmEjE,EAAE,CAAAwC,YAAA,CA2b4mE,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3b/mE/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,SAAAM,MAAA,CAAAmB,GAAA,EAAFlE,EAAE,CAAAmE,aA2bm+D,CAAC;IA3bt+DnE,EAAE,CAAAoE,SAAA,CA2b6lE,CAAC;IA3bhmEpE,EAAE,CAAAqE,sBAAA,8BAAAtB,MAAA,CAAAuB,YAAA,IA2b6lE,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+C,GAAA,GA3bhmExE,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,4BA2b6vE,CAAC;IA3bhwE7B,EAAE,CAAA8B,UAAA,wBAAA2C,kGAAA;MAAFzE,EAAE,CAAAkC,aAAA,CAAAsC,GAAA;MAAA,MAAAzB,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2bkvES,MAAA,CAAA2B,OAAA,CAAQ,CAAC;IAAA,CAAC,CAAC;IA3b/vE1E,EAAE,CAAAwC,YAAA,CA2bkxE,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3brxE/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,SAAAM,MAAA,CAAA4B,WA2b2qE,CAAC,cAAA5B,MAAA,CAAA6B,UAAA,EAA4B,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqD,GAAA,GA3b3sE9E,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,4BA2bg6E,CAAC;IA3bn6E7B,EAAE,CAAA8B,UAAA,wBAAAiD,kGAAA;MAAF/E,EAAE,CAAAkC,aAAA,CAAA4C,GAAA;MAAA,MAAA/B,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2bs5ES,MAAA,CAAAiC,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IA3bl6EhF,EAAE,CAAAwC,YAAA,CA2bq7E,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3bx7E/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,SAAAM,MAAA,CAAAkC,UA2bg1E,CAAC,cAAAlC,MAAA,CAAAmC,SAAA,EAA2B,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAA1D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2D,GAAA,GA3b/2EpF,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,2BA2bwhF,CAAC;IA3b3hF7B,EAAE,CAAA8B,UAAA,wBAAAuD,kGAAA;MAAFrF,EAAE,CAAAkC,aAAA,CAAAkD,GAAA;MAAA,MAAArC,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2b0gFS,MAAA,CAAAuC,UAAA,CAAW,CAAC;IAAA,CAAC,CAAC;IA3b1hFtF,EAAE,CAAAwC,YAAA,CA2b6iF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3bhjF/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,SAAAM,MAAA,CAAAwC,cA2by/E,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgE,GAAA,GA3b5/EzF,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,2BA2bkpF,CAAC;IA3brpF7B,EAAE,CAAA8B,UAAA,wBAAA4D,kGAAA;MAAF1F,EAAE,CAAAkC,aAAA,CAAAuD,GAAA;MAAA,MAAA1C,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2bmoFS,MAAA,CAAA4C,WAAA,CAAY,CAAC;IAAA,CAAC,CAAC;IA3bppF3F,EAAE,CAAAwC,YAAA,CA2buqF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3b1qF/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,SAAAM,MAAA,CAAA6C,eA2bknF,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAApE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqE,GAAA,GA3brnF9F,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,2BA2by0F,CAAC;IA3b50F7B,EAAE,CAAA8B,UAAA,wBAAAiE,kGAAA;MAAF/F,EAAE,CAAAkC,aAAA,CAAA4D,GAAA;MAAA,MAAA/C,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2bqzFS,MAAA,CAAAiD,gBAAA,CAAiB,CAAC;IAAA,CAAC,CAAC;IA3b30FhG,EAAE,CAAAwC,YAAA,CA2b81F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3bj2F/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,qCAAAM,MAAA,CAAAkD,cA2b2wF,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA0E,IAAA,GA3b9wFnG,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,gBA2b4hI,CAAC;IA3b/hI7B,EAAE,CAAA8B,UAAA,mBAAAsE,gEAAApE,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAAiE,IAAA;MAAA,OAAFnG,EAAE,CAAAsC,WAAA,CA2bwmHN,MAAA,CAAAqE,eAAA,CAAuB,CAAC;IAAA,CAAC,CAAC,wBAAAC,qEAAA;MA3bpoHtG,EAAE,CAAAkC,aAAA,CAAAiE,IAAA;MAAA,MAAApD,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2bkpHS,MAAA,CAAAwD,eAAA,CAAgB,CAAC;IAAA,CAAC,CAAC,wBAAAC,qEAAA;MA3bvqHxG,EAAE,CAAAkC,aAAA,CAAAiE,IAAA;MAAA,MAAApD,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2bqrHS,MAAA,CAAA0D,eAAA,CAAgB,CAAC;IAAA,CAAC,CAAC,uBAAAC,oEAAA1E,MAAA;MA3b1sHhC,EAAE,CAAAkC,aAAA,CAAAiE,IAAA;MAAA,MAAApD,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2biuHS,MAAA,CAAA4D,gBAAA,CAAA3E,MAAuB,CAAC;IAAA,CAAC,CAAC,wBAAA4E,qEAAA5E,MAAA;MA3b7vHhC,EAAE,CAAAkC,aAAA,CAAAiE,IAAA;MAAA,MAAApD,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2b2wHS,MAAA,CAAA4D,gBAAA,CAAA3E,MAAuB,CAAC;IAAA,CAAC,CAAC;IA3bvyHhC,EAAE,CAAAwC,YAAA,CA2b4hI,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3b/hI/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6G,WAAA,cAAA9D,MAAA,CAAA+D,YAAA,EA2bk9H,CAAC,SAAA/D,MAAA,CAAAgE,YAAA,OAAoC,CAAC,QAAAhE,MAAA,CAAAiE,WAAA,OAAkC,CAAC;IA3b7hIhH,EAAE,CAAAiH,WAAA,wBAAAlE,MAAA,CAAAmE,OA2bs1H,CAAC,cAAAnE,MAAA,CAAAoE,SAA+B,CAAC,qBAAApE,MAAA,CAAAqE,aAAA,EAA4C,CAAC;IA3bt6HpH,EAAE,CAAAyC,UAAA,QAAAM,MAAA,CAAAmB,GAAA,EAAFlE,EAAE,CAAAmE,aA2bklH,CAAC;EAAA;AAAA;AAAA,SAAAkD,6CAAA5F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA6F,IAAA,GA3brlHtH,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,kBA2b4mJ,CAAC;IA3b/mJ7B,EAAE,CAAA8B,UAAA,mBAAAyF,oEAAAvF,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAAoF,IAAA;MAAA,OAAFtH,EAAE,CAAAsC,WAAA,CA2bktIN,MAAA,CAAAqE,eAAA,CAAuB,CAAC;IAAA,CAAC,CAAC,wBAAAmB,yEAAA;MA3b9uIxH,EAAE,CAAAkC,aAAA,CAAAoF,IAAA;MAAA,MAAAvE,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2b4vIS,MAAA,CAAAwD,eAAA,CAAgB,CAAC;IAAA,CAAC,CAAC,wBAAAkB,yEAAA;MA3bjxIzH,EAAE,CAAAkC,aAAA,CAAAoF,IAAA;MAAA,MAAAvE,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2b+xIS,MAAA,CAAA0D,eAAA,CAAgB,CAAC;IAAA,CAAC,CAAC,uBAAAiB,wEAAA1F,MAAA;MA3bpzIhC,EAAE,CAAAkC,aAAA,CAAAoF,IAAA;MAAA,MAAAvE,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2bi0IS,MAAA,CAAA4D,gBAAA,CAAA3E,MAAuB,CAAC;IAAA,CAAC,CAAC,wBAAA2F,yEAAA3F,MAAA;MA3b71IhC,EAAE,CAAAkC,aAAA,CAAAoF,IAAA;MAAA,MAAAvE,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2b22IS,MAAA,CAAA4D,gBAAA,CAAA3E,MAAuB,CAAC;IAAA,CAAC,CAAC;IA3bv4IhC,EAAE,CAAAiE,SAAA,gBA2b0oJ,CAAC;IA3b7oJjE,EAAE,CAAA4H,MAAA,oDA2bosJ,CAAC;IA3bvsJ5H,EAAE,CAAAwC,YAAA,CA2b4sJ,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3b/sJ/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6G,WAAA,cAAA9D,MAAA,CAAA+D,YAAA,EA2bmiJ,CAAC,SAAA/D,MAAA,CAAAgE,YAAA,OAAoC,CAAC,QAAAhE,MAAA,CAAAiE,WAAA,OAAkC,CAAC;IA3b9mJhH,EAAE,CAAAiH,WAAA,wBAAAlE,MAAA,CAAAmE,OA2bi7I,CAAC,cAAAnE,MAAA,CAAAoE,SAA+B,CAAC,qBAAApE,MAAA,CAAAqE,aAAA,EAA4C,CAAC;IA3bjgJpH,EAAE,CAAAoE,SAAA,EA2byoJ,CAAC;IA3b5oJpE,EAAE,CAAAyC,UAAA,QAAAM,MAAA,CAAAmB,GAAA,EAAFlE,EAAE,CAAAmE,aA2byoJ,CAAC;EAAA;AAAA;AAAA,SAAA0D,2DAAApG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqG,IAAA,GA3b5oJ9H,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,6BA2bu2J,CAAC;IA3b12J7B,EAAE,CAAA8B,UAAA,0BAAAiG,uGAAA/F,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAA4F,IAAA;MAAA,MAAA/E,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA2bk1JS,MAAA,CAAAiF,WAAA,CAAAhG,MAAkB,CAAC;IAAA,CAAC,CAAC;IA3bz2JhC,EAAE,CAAAwC,YAAA,CA2b63J,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3bh4J/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,UAAAM,MAAA,CAAAkF,MAAA,CAAAC,MA2bkxJ,CAAC,WAAAnF,MAAA,CAAAZ,KAAkB,CAAC;EAAA;AAAA;AAAA,SAAAgG,2CAAA1G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2G,IAAA,GA3bxyJpI,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,aA2bqiK,CAAC;IA3bxiK7B,EAAE,CAAA8B,UAAA,mBAAAuG,gEAAArG,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAAkG,IAAA;MAAA,OAAFpI,EAAE,CAAAsC,WAAA,CA2b2gKN,MAAA,CAAAqE,eAAA,CAAuB,CAAC;IAAA,CAAC,CAAC;IA3bviKrG,EAAE,CAAAwC,YAAA,CA2b2iK,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GA3b9iK/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,cAAAM,MAAA,CAAAuF,WAAA,EAAFtI,EAAE,CAAAuI,cA2bu/J,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,GAAAjH,EAAA;EAAA,yBAAAA;AAAA;AAAA,SAAAkH,4EAAAhH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiH,GAAA,GA3b1/J1I,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,2BA+yBsrD,CAAC;IA/yBzrD7B,EAAE,CAAA8B,UAAA,wBAAA6G,qHAAA3G,MAAA;MAAA,MAAA4G,SAAA,GAAF5I,EAAE,CAAAkC,aAAA,CAAAwG,GAAA,EAAA/E,SAAA;MAAA,MAAAkF,QAAA,GAAF7I,EAAE,CAAAqC,aAAA,IAAAsB,SAAA;MAAA,OAAF3D,EAAE,CAAAsC,WAAA,CA+yBipDsG,SAAA,CAAAhF,OAAA,CAAA5B,MAAA,EAAA6G,QAAA,CAAA1G,KAAkC,CAAC;IAAA,CAAC,CAAC;IA/yBxrDnC,EAAE,CAAAwC,YAAA,CA+yB2sD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAmH,SAAA,GAAAlH,GAAA,CAAAiC,SAAA;IA/yB9sD3D,EAAE,CAAAyC,UAAA,SAAAmG,SAAA,CAAA/E,IA+yBsgD,CAAC,aAAA+E,SAAA,CAAA9E,QAA8B,CAAC,cAAA8E,SAAA,CAAA7E,SAA6D,CAAC;EAAA;AAAA;AAAA,SAAA+E,6DAAArH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqD,GAAA,GA/yBtmD9E,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,YA+yBw5D,CAAC;IA/yB35D7B,EAAE,CAAA8B,UAAA,mBAAAiH,kFAAA/G,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAA4C,GAAA;MAAA,OAAF9E,EAAE,CAAAsC,WAAA,CA+yB83DN,MAAA,CAAAqE,eAAA,CAAuB,CAAC;IAAA,CAAC,CAAC;IA/yB15DrG,EAAE,CAAAwC,YAAA,CA+yB85D,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAoH,QAAA,GA/yBj6D7I,EAAE,CAAAqC,aAAA,IAAAsB,SAAA;IAAA,MAAAvB,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,cAAAL,MAAA,CAAA4G,YAAA,CAAAH,QAAA,CAAA1G,KAAA,GAAFnC,EAAE,CAAAuI,cA+yBk3D,CAAC;EAAA;AAAA;AAAA,SAAAU,uDAAAxH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA/yBr3D3B,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,YA+yBg4C,CAAC;IA/yBn4C7B,EAAE,CAAA8B,UAAA,mBAAAoH,4EAAAlH,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAAP,GAAA;MAAA,MAAAkH,QAAA,GAAF7I,EAAE,CAAAqC,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA+yB4tCF,MAAA,CAAA+G,WAAA,CAAAnH,MAAA,EAAA6G,QAAA,CAAA1G,KAA+B,CAAC;IAAA,CAAC,CAAC,8BAAAiH,gGAAApH,MAAA;MA/yBhwChC,EAAE,CAAAkC,aAAA,CAAAP,GAAA;MAAA,MAAAS,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA+yBg0CF,MAAA,CAAAiH,OAAA,CAAArH,MAAc,CAAC;IAAA,CAAC,CAAC,6BAAAsH,+FAAAtH,MAAA;MA/yBn1ChC,EAAE,CAAAkC,aAAA,CAAAP,GAAA;MAAA,MAAAS,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA+yBg3CF,MAAA,CAAAmH,MAAA,CAAAvH,MAAa,CAAC;IAAA,CAAC,CAAC;IA/yBl4ChC,EAAE,CAAA6B,cAAA,YA+yBi7C,CAAC;IA/yBp7C7B,EAAE,CAAAwJ,UAAA,IAAAf,2EAAA,+BA+yBsrD,CAAC;IA/yBzrDzI,EAAE,CAAAwC,YAAA,CA+yBytD,CAAC;IA/yB5tDxC,EAAE,CAAAwJ,UAAA,IAAAV,4DAAA,gBA+yBw5D,CAAC;IA/yB35D9I,EAAE,CAAAwC,YAAA,CA+yB06D,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAoH,QAAA,GA/yB76D7I,EAAE,CAAAqC,aAAA,GAAAsB,SAAA;IAAA,MAAAvB,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6G,WAAA,qBAAAzE,MAAA,CAAAqH,UAAA,CAAAZ,QAAA,CAAA3E,GAAA,CA+yBssC,CAAC;IA/yBzsClE,EAAE,CAAAyC,UAAA,YAAFzC,EAAE,CAAA0C,eAAA,IAAA8F,GAAA,EAAApG,MAAA,CAAAsH,SAAA,CA+yByoC,CAAC,eAAAtH,MAAA,CAAAuH,MAAqJ,CAAC;IA/yBlyC3J,EAAE,CAAAoE,SAAA,EA+yB6+C,CAAC;IA/yBh/CpE,EAAE,CAAAyC,UAAA,YAAAL,MAAA,CAAAwH,OA+yB6+C,CAAC;IA/yBh/C5J,EAAE,CAAAoE,SAAA,CA+yB2zD,CAAC;IA/yB9zDpE,EAAE,CAAAyC,UAAA,SAAAL,MAAA,CAAAyH,eAAA,IAAAzH,MAAA,CAAA4G,YAAA,CAAAH,QAAA,CAAA1G,KAAA,CA+yB2zD,CAAC;EAAA;AAAA;AAAA,SAAA2H,4EAAArI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgE,GAAA,GA/yB9zDzF,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,2BA+yBqzF,CAAC;IA/yBxzF7B,EAAE,CAAA8B,UAAA,wBAAAiI,qHAAA/H,MAAA;MAAA,MAAAgI,SAAA,GAAFhK,EAAE,CAAAkC,aAAA,CAAAuD,GAAA,EAAA9B,SAAA;MAAA,MAAAkF,QAAA,GAAF7I,EAAE,CAAAqC,aAAA,IAAAsB,SAAA;MAAA,OAAF3D,EAAE,CAAAsC,WAAA,CA+yBgxF0H,SAAA,CAAApG,OAAA,CAAA5B,MAAA,EAAA6G,QAAA,CAAA1G,KAAkC,CAAC;IAAA,CAAC,CAAC;IA/yBvzFnC,EAAE,CAAAwC,YAAA,CA+yB00F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAuI,SAAA,GAAAtI,GAAA,CAAAiC,SAAA;IA/yB70F3D,EAAE,CAAAyC,UAAA,SAAAuH,SAAA,CAAAnG,IA+yByoF,CAAC,aAAAmG,SAAA,CAAAlG,QAA8B,CAAC,cAAAkG,SAAA,CAAAjG,SAA2D,CAAC;EAAA;AAAA;AAAA,SAAAkG,6DAAAxI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA0E,IAAA,GA/yBvuFnG,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,YA+yBuhG,CAAC;IA/yB1hG7B,EAAE,CAAA8B,UAAA,mBAAAoI,kFAAAlI,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAAiE,IAAA;MAAA,OAAFnG,EAAE,CAAAsC,WAAA,CA+yB6/FN,MAAA,CAAAqE,eAAA,CAAuB,CAAC;IAAA,CAAC,CAAC;IA/yBzhGrG,EAAE,CAAAwC,YAAA,CA+yB6hG,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAoH,QAAA,GA/yBhiG7I,EAAE,CAAAqC,aAAA,IAAAsB,SAAA;IAAA,MAAAvB,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,cAAAL,MAAA,CAAA4G,YAAA,CAAAH,QAAA,CAAA1G,KAAA,GAAFnC,EAAE,CAAAuI,cA+yBi/F,CAAC;EAAA;AAAA;AAAA,SAAA4B,uDAAA1I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2D,GAAA,GA/yBp/FpF,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,YA+yB00E,CAAC;IA/yB70E7B,EAAE,CAAA8B,UAAA,mBAAAsI,4EAAApI,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAAkD,GAAA;MAAA,MAAAyD,QAAA,GAAF7I,EAAE,CAAAqC,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA+yBsqEF,MAAA,CAAA+G,WAAA,CAAAnH,MAAA,EAAA6G,QAAA,CAAA1G,KAA+B,CAAC;IAAA,CAAC,CAAC,8BAAAkI,gGAAArI,MAAA;MA/yB1sEhC,EAAE,CAAAkC,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA+yB0wEF,MAAA,CAAAiH,OAAA,CAAArH,MAAc,CAAC;IAAA,CAAC,CAAC,6BAAAsI,+FAAAtI,MAAA;MA/yB7xEhC,EAAE,CAAAkC,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA+yB0zEF,MAAA,CAAAmH,MAAA,CAAAvH,MAAa,CAAC;IAAA,CAAC,CAAC;IA/yB50EhC,EAAE,CAAA6B,cAAA,eA+yBu5E,CAAC;IA/yB15E7B,EAAE,CAAAiE,SAAA,gBA+yB67E,CAAC;IA/yBh8EjE,EAAE,CAAA4H,MAAA,oDA+yB6/E,CAAC;IA/yBhgF5H,EAAE,CAAAwC,YAAA,CA+yBqgF,CAAC;IA/yBxgFxC,EAAE,CAAA6B,cAAA,YA+yBsjF,CAAC;IA/yBzjF7B,EAAE,CAAAwJ,UAAA,IAAAM,2EAAA,+BA+yBqzF,CAAC;IA/yBxzF9J,EAAE,CAAAwC,YAAA,CA+yBw1F,CAAC;IA/yB31FxC,EAAE,CAAAwJ,UAAA,IAAAS,4DAAA,gBA+yBuhG,CAAC;IA/yB1hGjK,EAAE,CAAAwC,YAAA,CA+yB2iG,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAoH,QAAA,GA/yB9iG7I,EAAE,CAAAqC,aAAA,GAAAsB,SAAA;IAAA,MAAAvB,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6G,WAAA,qBAAAzE,MAAA,CAAAqH,UAAA,CAAAZ,QAAA,CAAA3E,GAAA,CA+yBgpE,CAAC;IA/yBnpElE,EAAE,CAAAyC,UAAA,YAAFzC,EAAE,CAAA0C,eAAA,IAAA8F,GAAA,EAAApG,MAAA,CAAAsH,SAAA,CA+yBmlE,CAAC,eAAAtH,MAAA,CAAAuH,MAAqJ,CAAC;IA/yB5uE3J,EAAE,CAAAoE,SAAA,EA+yB47E,CAAC;IA/yB/7EpE,EAAE,CAAAyC,UAAA,QAAAoG,QAAA,CAAA3E,GAAA,EAAFlE,EAAE,CAAAmE,aA+yB47E,CAAC;IA/yB/7EnE,EAAE,CAAAoE,SAAA,EA+yBgnF,CAAC;IA/yBnnFpE,EAAE,CAAAyC,UAAA,YAAAL,MAAA,CAAAwH,OA+yBgnF,CAAC;IA/yBnnF5J,EAAE,CAAAoE,SAAA,CA+yB07F,CAAC;IA/yB77FpE,EAAE,CAAAyC,UAAA,SAAAL,MAAA,CAAAyH,eAAA,IAAAzH,MAAA,CAAA4G,YAAA,CAAAH,QAAA,CAAA1G,KAAA,CA+yB07F,CAAC;EAAA;AAAA;AAAA,SAAAoI,iDAAA9I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/yB77FzB,EAAE,CAAAwK,uBAAA,EA+yBg+B,CAAC;IA/yBn+BxK,EAAE,CAAAwJ,UAAA,IAAAP,sDAAA,gBA+yBg4C,CAAC,IAAAkB,sDAAA,gBAAy8B,CAAC;IA/yB70EnK,EAAE,CAAAyK,qBAAA;EAAA;EAAA,IAAAhJ,EAAA;IAAA,MAAAoH,QAAA,GAAAnH,GAAA,CAAAiC,SAAA;IAAA,MAAAvB,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAoE,SAAA,CA+yB6iC,CAAC;IA/yBhjCpE,EAAE,CAAAyC,UAAA,SAAAoG,QAAA,CAAA6B,IAAA,gBAAA7B,QAAA,CAAA1G,KAAA,KAAAC,MAAA,CAAAuI,cA+yB6iC,CAAC;IA/yBhjC3K,EAAE,CAAAoE,SAAA,CA+yBu/D,CAAC;IA/yB1/DpE,EAAE,CAAAyC,UAAA,SAAAoG,QAAA,CAAA6B,IAAA,gBAAA7B,QAAA,CAAA1G,KAAA,KAAAC,MAAA,CAAAuI,cA+yBu/D,CAAC;EAAA;AAAA;AAAA,SAAAC,wDAAAnJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA6F,IAAA,GA/yB1/DtH,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,6BA+yB2tG,CAAC;IA/yB9tG7B,EAAE,CAAA8B,UAAA,0BAAA+I,oGAAA7I,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAAoF,IAAA;MAAA,MAAAlF,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA+yB6sGF,MAAA,CAAA0I,IAAA,CAAA9I,MAAW,CAAC;IAAA,CAAC,CAAC;IA/yB7tGhC,EAAE,CAAAwC,YAAA,CA+yBivG,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAW,MAAA,GA/yBpvGpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,UAAAL,MAAA,CAAA6F,MAAA,CAAAC,MA+yBsoG,CAAC,WAAA9F,MAAA,CAAAuI,cAA2B,CAAC;EAAA;AAAA;AAAA,SAAAI,uDAAAtJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqG,IAAA,GA/yBrqG9H,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,4BA+yBgjH,CAAC;IA/yBnjH7B,EAAE,CAAA8B,UAAA,uBAAAkJ,+FAAA;MAAFhL,EAAE,CAAAkC,aAAA,CAAA4F,IAAA;MAAA,MAAA1F,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA+yBm1GF,MAAA,CAAAY,QAAA,CAAS,CAAC;IAAA,CAAC,CAAC,uBAAAiI,+FAAA;MA/yBj2GjL,EAAE,CAAAkC,aAAA,CAAA4F,IAAA;MAAA,MAAA1F,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA+yBq4GF,MAAA,CAAAc,QAAA,CAAS,CAAC;IAAA,CAAC,CAAC;IA/yBn5GlD,EAAE,CAAAwC,YAAA,CA+yBqkH,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAW,MAAA,GA/yBxkHpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAqE,sBAAA,4BAAAjC,MAAA,CAAA8I,IAAA,IA+yBkzG,CAAC;IA/yBrzGlL,EAAE,CAAAyC,UAAA,kBAAAL,MAAA,CAAAe,WAAA,EA+yBk7G,CAAC,kBAAAf,MAAA,CAAAgB,WAAA,EAAiC,CAAC,kBAAAhB,MAAA,CAAAiB,aAAwD,CAAC,kBAAAjB,MAAA,CAAAkB,aAAiC,CAAC;EAAA;AAAA;AAAA,MAAA6H,GAAA,GAAAA,CAAA5J,EAAA,EAAA6J,EAAA;EAAA,sBAAA7J,EAAA;EAAA,yBAAA6J;AAAA;AAAA,SAAAC,iDAAA5J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/yBljHzB,EAAE,CAAAiE,SAAA,YAyqCowD,CAAC;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAA6J,MAAA,GAzqCvwDtL,EAAE,CAAAqC,aAAA;IAAA,MAAAkJ,QAAA,GAAAD,MAAA,CAAA3H,SAAA;IAAA,MAAA1B,IAAA,GAAAqJ,MAAA,CAAAnJ,KAAA;IAAA,MAAAC,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6G,WAAA,qBAAAzE,MAAA,CAAAqH,UAAA,CAAA8B,QAAA,CAyqCqjD,CAAC;IAzqCxjDvL,EAAE,CAAAyC,UAAA,YAAFzC,EAAE,CAAAwL,eAAA,IAAAL,GAAA,EAAAlJ,IAAA,KAAAG,MAAA,CAAAqJ,aAAA,EAAArJ,MAAA,CAAAsH,SAAA,CAyqC6rD,CAAC;EAAA;AAAA;AAAA,SAAAgC,iDAAAjK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzqChsDzB,EAAE,CAAA6B,cAAA,aAyqCi2D,CAAC,eAA2O,CAAC;IAzqChlE7B,EAAE,CAAAiE,SAAA,gBAyqC+mE,CAAC;IAzqClnEjE,EAAE,CAAA4H,MAAA,oDAyqCgrE,CAAC;IAzqCnrE5H,EAAE,CAAAwC,YAAA,CAyqCwrE,CAAC,CAAa,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA6J,MAAA,GAzqCzsEtL,EAAE,CAAAqC,aAAA;IAAA,MAAAkJ,QAAA,GAAAD,MAAA,CAAA3H,SAAA;IAAA,MAAA1B,IAAA,GAAAqJ,MAAA,CAAAnJ,KAAA;IAAA,MAAAC,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAoE,SAAA,CAyqCk/D,CAAC;IAzqCr/DpE,EAAE,CAAAyC,UAAA,YAAFzC,EAAE,CAAAwL,eAAA,IAAAL,GAAA,EAAAlJ,IAAA,KAAAG,MAAA,CAAAqJ,aAAA,EAAArJ,MAAA,CAAAsH,SAAA,CAyqCk/D,CAAC;IAzqCr/D1J,EAAE,CAAAoE,SAAA,CAyqC8mE,CAAC;IAzqCjnEpE,EAAE,CAAAyC,UAAA,QAAA8I,QAAA,EAAFvL,EAAE,CAAAmE,aAyqC8mE,CAAC;EAAA;AAAA;AAAA,SAAAwH,gEAAAlK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqD,GAAA,GAzqCjnE9E,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,4BAyqCs9E,CAAC;IAzqCz9E7B,EAAE,CAAA8B,UAAA,wBAAA8J,yGAAA5J,MAAA;MAAA,MAAA6J,SAAA,GAAF7L,EAAE,CAAAkC,aAAA,CAAA4C,GAAA,EAAAnB,SAAA;MAAA,MAAA1B,IAAA,GAAFjC,EAAE,CAAAqC,aAAA,GAAAF,KAAA;MAAA,OAAFnC,EAAE,CAAAsC,WAAA,CAyqC27EuJ,SAAA,CAAAjI,OAAA,CAAA5B,MAAA,EAAAC,IAAwB,CAAC;IAAA,CAAC,CAAC;IAzqCx9EjC,EAAE,CAAAwC,YAAA,CAyqC2+E,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAoK,SAAA,GAAAnK,GAAA,CAAAiC,SAAA;IAzqC9+E3D,EAAE,CAAAyC,UAAA,SAAAoJ,SAAA,CAAAhI,IAyqC60E,CAAC,aAAAgI,SAAA,CAAA/H,QAA8B,CAAC,cAAA+H,SAAA,CAAA9H,SAA6D,CAAC;EAAA;AAAA;AAAA,SAAA+H,iDAAArK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzqC76EzB,EAAE,CAAA6B,cAAA,aAyqCgpF,CAAC,cAAqD,CAAC;IAzqCzsF7B,EAAE,CAAA4H,MAAA,EAyqC8tF,CAAC;IAzqCjuF5H,EAAE,CAAAwC,YAAA,CAyqCquF,CAAC,CAAa,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAW,MAAA,GAzqCtvFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAoE,SAAA,EAyqC8tF,CAAC;IAzqCjuFpE,EAAE,CAAA+L,kBAAA,MAAA3J,MAAA,CAAA4J,mBAAA,IAyqC8tF,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAAxK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAzqCjuF3B,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,UAyqCg9C,CAAC;IAzqCn9C7B,EAAE,CAAA8B,UAAA,mBAAAoK,8DAAAlK,MAAA;MAAA,MAAAC,IAAA,GAAFjC,EAAE,CAAAkC,aAAA,CAAAP,GAAA,EAAAQ,KAAA;MAAA,MAAAC,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAyqC0oCF,MAAA,CAAA+G,WAAA,CAAAnH,MAAA,EAAAC,IAAqB,CAAC;IAAA,CAAC,CAAC;IAzqCpqCjC,EAAE,CAAAwJ,UAAA,IAAA6B,gDAAA,gBAyqC8vD,CAAC,IAAAK,gDAAA,gBAAkG,CAAC;IAzqCp2D1L,EAAE,CAAA6B,cAAA,YAyqCwvE,CAAC;IAzqC3vE7B,EAAE,CAAAwJ,UAAA,IAAAmC,+DAAA,+BAyqCs9E,CAAC;IAzqCz9E3L,EAAE,CAAAwC,YAAA,CAyqCy/E,CAAC;IAzqC5/ExC,EAAE,CAAAwJ,UAAA,IAAAsC,gDAAA,gBAyqCgpF,CAAC;IAzqCnpF9L,EAAE,CAAAwC,YAAA,CAyqC6vF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA8J,QAAA,GAAA7J,GAAA,CAAAiC,SAAA;IAAA,MAAA1B,IAAA,GAAAP,GAAA,CAAAS,KAAA;IAAA,MAAAC,MAAA,GAzqChwFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6G,WAAA,UAAAzE,MAAA,CAAA+J,iBAAA,EAyqCusC,CAAC,WAAA/J,MAAA,CAAAgK,kBAAA,EAAuC,CAAC,SAAAhK,MAAA,CAAAiK,gBAAA,CAAApK,IAAA,CAA4C,CAAC,QAAAG,MAAA,CAAAkK,eAAA,CAAArK,IAAA,CAAkC,CAAC;IAzqCl0CjC,EAAE,CAAAyC,UAAA,SAAAL,MAAA,CAAAmK,OAAA,CAAAtK,IAAA,IAAAG,MAAA,CAAAoK,KAAA,CAAAvK,IAAA,SAAFjC,EAAE,CAAAmE,aAyqCkgC,CAAC,WAAA/B,MAAA,CAAAqK,UAAuB,CAAC,YAzqC7hCzM,EAAE,CAAAwL,eAAA,KAAAL,GAAA,EAAAlJ,IAAA,KAAAG,MAAA,CAAAqJ,aAAA,EAAArJ,MAAA,CAAAsH,SAAA,CAyqCu6C,CAAC;IAzqC16C1J,EAAE,CAAA0M,WAAA,eAAAtK,MAAA,CAAAuK,MAAA,CAAA1K,IAAA;IAAFjC,EAAE,CAAAoE,SAAA,CAyqCogD,CAAC;IAzqCvgDpE,EAAE,CAAAyC,UAAA,SAAAL,MAAA,CAAAwK,WAAA,CAAArB,QAAA,aAyqCogD,CAAC;IAzqCvgDvL,EAAE,CAAAoE,SAAA,CAyqCwzD,CAAC;IAzqC3zDpE,EAAE,CAAAyC,UAAA,SAAAL,MAAA,CAAAwK,WAAA,CAAArB,QAAA,aAyqCwzD,CAAC;IAzqC3zDvL,EAAE,CAAAoE,SAAA,EAyqCozE,CAAC;IAzqCvzEpE,EAAE,CAAAyC,UAAA,YAAAL,MAAA,CAAAwH,OAyqCozE,CAAC;IAzqCvzE5J,EAAE,CAAAoE,SAAA,CAyqC6oF,CAAC;IAzqChpFpE,EAAE,CAAAyC,UAAA,SAAAL,MAAA,CAAAyK,cAAA,IAAAzK,MAAA,CAAA4J,mBAAA,IAAA/J,IAAA,KAAAG,MAAA,CAAA0K,IAAA,GAAA1K,MAAA,CAAA2K,OAAA,IAyqC6oF,CAAC;EAAA;AAAA;AAAA,SAAAC,4DAAAvL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgE,GAAA,GAzqChpFzF,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,4BAyqCyiG,CAAC;IAzqC5iG7B,EAAE,CAAA8B,UAAA,uBAAAmL,oGAAA;MAAFjN,EAAE,CAAAkC,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAyqC80FF,MAAA,CAAA8K,QAAA,CAAS,CAAC;IAAA,CAAC,CAAC,uBAAAC,oGAAA;MAzqC51FnN,EAAE,CAAAkC,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAyqCy2FF,MAAA,CAAAgL,SAAA,CAAU,CAAC;IAAA,CAAC,CAAC;IAzqCx3FpN,EAAE,CAAAwC,YAAA,CAyqC8jG,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAW,MAAA,GAzqCjkGpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,kBAAAL,MAAA,CAAAiL,WAAA,EAyqC46F,CAAC,kBAAAjL,MAAA,CAAAkL,YAAA,EAAkC,CAAC,kBAAAlL,MAAA,CAAAiB,aAAiC,CAAC,kBAAAjB,MAAA,CAAAkB,aAAsD,CAAC;EAAA;AAAA;AAAA,MAAAiK,GAAA,GAAAA,CAAA;AAAA,SAAAC,iDAAA/L,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAzqC3iG3B,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,0BAmkD+mE,CAAC;IAnkDlnE7B,EAAE,CAAA8B,UAAA,wBAAA2L,yFAAAzL,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAAP,GAAA;MAAA,MAAAoB,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAmkD0gES,MAAA,CAAA2K,WAAA,CAAA1L,MAAkB,CAAC;IAAA,CAAC,CAAC,0BAAA2L,2FAAA3L,MAAA;MAnkDjiEhC,EAAE,CAAAkC,aAAA,CAAAP,GAAA;MAAA,MAAAoB,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAmkDijES,MAAA,CAAA6K,eAAA,CAAA5L,MAAsB,CAAC;IAAA,CAAC,CAAC,uBAAA6L,wFAAA7L,MAAA;MAnkD5kEhC,EAAE,CAAAkC,aAAA,CAAAP,GAAA;MAAA,MAAAoB,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAmkDylES,MAAA,CAAA+K,YAAA,CAAA9L,MAAmB,CAAC;IAAA,CAAC,CAAC;IAnkDjnEhC,EAAE,CAAAwC,YAAA,CAmkDmoE,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GAnkDtoE/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6G,WAAA,WAAA9D,MAAA,CAAAgL,cAAA,EAmkDm6B,CAAC;IAnkDt6B/N,EAAE,CAAAyC,UAAA,WAAAM,MAAA,CAAAiL,YAmkD67B,CAAC,cAAAjL,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAC,OAA6D,CAAC,kBAAAnL,MAAA,CAAA0I,aAAiC,CAAC,WAAA1I,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAE,WAA8D,CAAC,mBAAApL,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAG,mBAAwD,CAAC,kBAAArL,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA5K,aAAuE,CAAC,kBAAAN,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA3K,aAAiD,CAAC,UAAAP,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAI,UAA4D,CAAC,cAAAtL,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAK,cAA8C,CAAC,SAAAvL,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAM,SAA0D,CAAC,aAAAxL,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAO,aAA4C,CAAC,qBAAAzL,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAQ,qBAAkF,CAAC,yBAAA1L,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAS,yBAA0F,CAAC,iBAAA3L,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAU,iBAA0E,CAAC,gBAAA5L,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAW,WAA6C,CAAC,YAAA7L,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAY,YAAgE,CAAC,iBAAA9L,MAAA,CAAAiG,YAA+B,CAAC,oBAAAjG,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAa,gBAA4E,CAAC,YAAA/L,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAc,YAA0C,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAAvN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GAnkDt+DxD,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,+BAmkDsuH,CAAC;IAnkDzuH7B,EAAE,CAAA8B,UAAA,0BAAAmN,qGAAAjN,MAAA;MAAFhC,EAAE,CAAAkC,aAAA,CAAAsB,GAAA;MAAA,MAAAT,MAAA,GAAF/C,EAAE,CAAAqC,aAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CAmkDypHS,MAAA,CAAAmM,oBAAA,CAAAlN,MAA2B,CAAC;IAAA,CAAC,CAAC;IAnkDzrHhC,EAAE,CAAAwC,YAAA,CAmkD+vH,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsB,MAAA,GAnkDlwH/C,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAA6G,WAAA,eAAA9D,MAAA,CAAAoM,sBAAA,EAmkDovE,CAAC,kBAAApM,MAAA,CAAAqM,yBAAA,EAA+E,CAAC,WAAArM,MAAA,CAAAsM,mBAAA,EAAwC,CAAC;IAnkDh3ErP,EAAE,CAAAyC,UAAA,WAAAM,MAAA,CAAAuM,WAmkDi6E,CAAC,gBAAAvM,MAAA,CAAAwM,WAA6B,CAAC,WAAAxM,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAuB,iBAAA,IAAAzM,MAAA,CAAAyJ,KAAA,GAnkDl8ExM,EAAE,CAAAyP,eAAA,KAAAlC,GAAA,CAmkD0/E,CAAC,WAAAxK,MAAA,CAAA4J,MAA8C,CAAC,eAAA5J,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAxB,UAA2C,CAAC,kBAAA1J,MAAA,CAAA0I,aAAiC,CAAC,YAAA1I,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAyB,iBAA0E,CAAC,SAAA3M,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA0B,cAAyC,CAAC,WAAA5M,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA2B,eAAuE,CAAC,WAAA7M,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA4B,gBAA6C,CAAC,mBAAA9M,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA6B,wBAAwF,CAAC,kBAAA/M,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA5K,aAA4E,CAAC,kBAAAN,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA3K,aAA4E,CAAC,eAAAP,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA8B,KAAA,MAAAhN,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAC,OAAA,CAA2F,CAAC,UAAAnL,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAA+B,eAAsE,CAAC,SAAAjN,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAgC,aAAwC,CAAC,aAAAlN,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAiC,kBAA4E,CAAC,UAAAnN,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAkC,eAA2C,CAAC,mBAAApN,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAmC,wBAAwF,CAAC,gBAAArN,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAW,WAAwE,CAAC,YAAA7L,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAoC,gBAA8C,CAAC,YAAAtN,MAAA,CAAAkL,cAAA,kBAAAlL,MAAA,CAAAkL,cAAA,CAAAqC,gBAAyH,CAAC;EAAA;AAAA;AAtnD50H,MAAMC,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;EAClC;EACAC,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAEC,EAAE,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACvD,MAAMC,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACJ,EAAE,CAAC;IAC1C;IACA,IAAI;MACA,IAAIF,MAAM,IAAI,CAACK,QAAQ,EAAE;QACrB,IAAI,CAACR,aAAa,CAACU,GAAG,CAACL,EAAE,EAAE,CACvB,IAAI,CAACN,QAAQ,CAACY,MAAM,CAACP,OAAO,CAACQ,aAAa,EAAE,WAAW,EAAE,MAAMN,WAAW,CAAC,CAAC,CAAC,EAC7E,IAAI,CAACP,QAAQ,CAACY,MAAM,CAACP,OAAO,CAACQ,aAAa,EAAE,YAAY,EAAE,MAAML,WAAW,CAAC,CAAC,CAAC,CACjF,CAAC;MACN,CAAC,MACI,IAAI,CAACJ,MAAM,IAAIK,QAAQ,EAAE;QAC1BA,QAAQ,CAACK,GAAG,CAAEC,OAAO,IAAKA,OAAO,CAAC,CAAC,CAAC;QACpC,IAAI,CAACC,mBAAmB,CAACV,EAAE,CAAC;MAChC;IACJ,CAAC,CACD,OAAOW,CAAC,EAAE,CACV;EACJ;EACAC,WAAWA,CAACC,GAAG,EAAE;IACb,IAAIA,GAAG,CAACC,OAAO,EAAE;MACb,OAAOD,GAAG,CAACC,OAAO,CAAC,IAAIC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAC1CD,OAAO,CAAC,IAAIC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC;IAC9C,CAAC,MACI;MACD,OAAOF,GAAG;IACd;EACJ;EACAG,gBAAgBA,CAAChC,KAAK,EAAE;IACpB,OAAO,QAAQ,GAAG,IAAI,CAAC4B,WAAW,CAAC5B,KAAK,CAAC,GAAG,KAAK;EACrD;EACAnD,WAAWA,CAACoF,UAAU,EAAE;IACpB,MAAMC,aAAa,GAAGD,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC/D,IAAIH,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,KAAK,IAC/CA,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,KAAK,IAClDA,aAAa,KAAK,KAAK,EAAE;MAC5B,OAAO,OAAO;IAClB;IACA,OAAO,OAAO;EAClB;EACAd,gBAAgBA,CAACJ,EAAE,EAAE;IACjB,OAAO,IAAI,CAACL,aAAa,CAAC2B,GAAG,CAACtB,EAAE,CAAC;EACrC;EACAU,mBAAmBA,CAACV,EAAE,EAAE;IACpB,IAAI,CAACL,aAAa,CAAC4B,MAAM,CAACvB,EAAE,CAAC;EACjC;AACJ;AACAR,iBAAiB,CAACgC,IAAI,YAAAC,0BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAwFlC,iBAAiB,EAA3BvQ,EAAE,CAAA0S,QAAA,CAA2C1S,EAAE,CAAC2S,SAAS;AAAA,CAA6C;AAC1MpC,iBAAiB,CAACqC,KAAK,kBAD6E5S,EAAE,CAAA6S,kBAAA;EAAAC,KAAA,EACYvC,iBAAiB;EAAAwC,OAAA,EAAjBxC,iBAAiB,CAAAgC,IAAA;EAAAS,UAAA,EAAc;AAAM,EAAG;AAC1J;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFoGjT,EAAE,CAAAkT,iBAAA,CAEX3C,iBAAiB,EAAc,CAAC;IAC/G7F,IAAI,EAAEzK,UAAU;IAChBkT,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtI,IAAI,EAAE1K,EAAE,CAAC2S;IAAU,CAAC,CAAC;EAAE,CAAC;AAAA;AAE5E,MAAMS,yBAAyB,CAAC;EAC5B5C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6C,SAAS,GAAG,IAAInT,YAAY,CAAC,CAAC;IACnC,IAAI,CAACoT,SAAS,GAAG,IAAIpT,YAAY,CAAC,CAAC;EACvC;EACAqT,eAAeA,CAAA,EAAG;IACd,IAAI,CAACF,SAAS,CAACG,IAAI,CAAC,CAAC;EACzB;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACH,SAAS,CAACE,IAAI,CAAC,CAAC;EACzB;AACJ;AACAJ,yBAAyB,CAACb,IAAI,YAAAmB,kCAAAjB,iBAAA;EAAA,YAAAA,iBAAA,IAAwFW,yBAAyB;AAAA,CAAmD;AAClMA,yBAAyB,CAACO,IAAI,kBAtBsE3T,EAAE,CAAA4T,iBAAA;EAAAlJ,IAAA,EAsBI0I,yBAAyB;EAAAS,SAAA;EAAAC,MAAA;IAAAC,YAAA;IAAAC,YAAA;IAAA3Q,aAAA;IAAAC,aAAA;EAAA;EAAA2Q,OAAA;IAAAZ,SAAA;IAAAC,SAAA;EAAA;EAAAY,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mCAAA7S,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAtB/BzB,EAAE,CAAA6B,cAAA,YAsB6V,CAAC,YAAsJ,CAAC;MAtBvf7B,EAAE,CAAA8B,UAAA,mBAAAyS,wDAAA;QAAA,OAsBmb7S,GAAA,CAAA6R,eAAA,CAAgB,CAAC;MAAA,CAAC,CAAC;MAtBxcvT,EAAE,CAAAiE,SAAA,OAsBsjB,CAAC;MAtBzjBjE,EAAE,CAAAwC,YAAA,CAsBgkB,CAAC,CAAO,CAAC;MAtB3kBxC,EAAE,CAAA6B,cAAA,YAsB4oB,CAAC,YAAsJ,CAAC;MAtBtyB7B,EAAE,CAAA8B,UAAA,mBAAA0S,wDAAA;QAAA,OAsBkuB9S,GAAA,CAAA+R,eAAA,CAAgB,CAAC;MAAA,CAAC,CAAC;MAtBvvBzT,EAAE,CAAAiE,SAAA,OAsBq2B,CAAC;MAtBx2BjE,EAAE,CAAAwC,YAAA,CAsB+2B,CAAC,CAAO,CAAC;IAAA;IAAA,IAAAf,EAAA;MAtB13BzB,EAAE,CAAAoE,SAAA,CAsBmf,CAAC;MAtBtfpE,EAAE,CAAAiH,WAAA,yBAAAvF,GAAA,CAAAqS,YAsBmf,CAAC;MAtBtf/T,EAAE,CAAAoE,SAAA,CAsBijB,CAAC;MAtBpjBpE,EAAE,CAAAqE,sBAAA,8BAAA3C,GAAA,CAAA2B,aAAA,IAsBijB,CAAC;MAtBpjBrD,EAAE,CAAAoE,SAAA,EAsBkyB,CAAC;MAtBryBpE,EAAE,CAAAiH,WAAA,yBAAAvF,GAAA,CAAAsS,YAsBkyB,CAAC;MAtBryBhU,EAAE,CAAAoE,SAAA,CAsBg2B,CAAC;MAtBn2BpE,EAAE,CAAAqE,sBAAA,8BAAA3C,GAAA,CAAA4B,aAAA,IAsBg2B,CAAC;IAAA;EAAA;EAAAmR,MAAA;EAAAC,eAAA;AAAA,EAAkkB;AACzgD;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KAvBoGjT,EAAE,CAAAkT,iBAAA,CAuBXE,yBAAyB,EAAc,CAAC;IACvH1I,IAAI,EAAEvK,SAAS;IACfgT,IAAI,EAAE,CAAC;MAAEwB,QAAQ,EAAE,oBAAoB;MAAED,eAAe,EAAEtU,uBAAuB,CAACwU,MAAM;MAAEP,QAAQ,EAAE,+lBAA+lB;MAAEI,MAAM,EAAE,CAAC,qeAAqe;IAAE,CAAC;EAC1rC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEV,YAAY,EAAE,CAAC;MACzErJ,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE2T,YAAY,EAAE,CAAC;MACftJ,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEgD,aAAa,EAAE,CAAC;MAChBqH,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEiD,aAAa,EAAE,CAAC;MAChBoH,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEgT,SAAS,EAAE,CAAC;MACZ3I,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEgT,SAAS,EAAE,CAAC;MACZ5I,IAAI,EAAEpK;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuU,yBAAyB,CAAC;EAC5BrE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC1M,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC+Q,UAAU,GAAG,IAAI5U,YAAY,CAAC,CAAC;EACxC;EACAiJ,WAAWA,CAAC4L,KAAK,EAAE;IACf,IAAI,CAAC,IAAI,CAACjR,QAAQ,EAAE;MAChB,IAAI,CAACgR,UAAU,CAACtB,IAAI,CAACuB,KAAK,CAAC;IAC/B;IACAA,KAAK,CAAC1O,eAAe,CAAC,CAAC;IACvB0O,KAAK,CAACC,cAAc,CAAC,CAAC;EAC1B;AACJ;AACAH,yBAAyB,CAACtC,IAAI,YAAA0C,kCAAAxC,iBAAA;EAAA,YAAAA,iBAAA,IAAwFoC,yBAAyB;AAAA,CAAmD;AAClMA,yBAAyB,CAAClB,IAAI,kBAvDsE3T,EAAE,CAAA4T,iBAAA;EAAAlJ,IAAA,EAuDImK,yBAAyB;EAAAhB,SAAA;EAAAC,MAAA;IAAAjQ,IAAA;IAAAC,QAAA;IAAAC,SAAA;EAAA;EAAAkQ,OAAA;IAAAa,UAAA;EAAA;EAAAZ,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAa,mCAAAzT,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAvD/BzB,EAAE,CAAA6B,cAAA,YAuDsX,CAAC;MAvDzX7B,EAAE,CAAA8B,UAAA,mBAAAqT,wDAAAnT,MAAA;QAAA,OAuDiWN,GAAA,CAAAyH,WAAA,CAAAnH,MAAkB,CAAC;MAAA,CAAC,CAAC;MAvDxXhC,EAAE,CAAAiE,SAAA,OAuD+a,CAAC;MAvDlbjE,EAAE,CAAAwC,YAAA,CAuDub,CAAC;IAAA;IAAA,IAAAf,EAAA;MAvD1bzB,EAAE,CAAAiH,WAAA,8BAAAvF,GAAA,CAAAoC,QAuDoR,CAAC;MAvDvR9D,EAAE,CAAAoV,qBAAA,UAAA1T,GAAA,CAAAqC,SAuD+U,CAAC;MAvDlV/D,EAAE,CAAAoE,SAAA,CAuD0a,CAAC;MAvD7apE,EAAE,CAAAqE,sBAAA,8BAAA3C,GAAA,CAAAmC,IAAA,IAuD0a,CAAC;IAAA;EAAA;EAAA4Q,MAAA;EAAAC,eAAA;AAAA,EAA0W;AAC33B;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KAxDoGjT,EAAE,CAAAkT,iBAAA,CAwDX2B,yBAAyB,EAAc,CAAC;IACvHnK,IAAI,EAAEvK,SAAS;IACfgT,IAAI,EAAE,CAAC;MAAEwB,QAAQ,EAAE,oBAAoB;MAAED,eAAe,EAAEtU,uBAAuB,CAACwU,MAAM;MAAEP,QAAQ,EAAE,qPAAqP;MAAEI,MAAM,EAAE,CAAC,uRAAuR;IAAE,CAAC;EACloB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAE5Q,IAAI,EAAE,CAAC;MACjE6G,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEyD,QAAQ,EAAE,CAAC;MACX4G,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE0D,SAAS,EAAE,CAAC;MACZ2G,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEyU,UAAU,EAAE,CAAC;MACbpK,IAAI,EAAEpK;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+U,0BAA0B,CAAC;EAC7B7E,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7N,MAAM,GAAG,CAAC;IACf,IAAI,CAAC2S,YAAY,GAAG,IAAIpV,YAAY,CAAC,CAAC;EAC1C;EACAqV,UAAUA,CAAA,EAAG;IACT,OAAOC,KAAK,CAAC,IAAI,CAACC,KAAK,CAAC;EAC5B;EACAlT,YAAYA,CAACwS,KAAK,EAAE5S,KAAK,EAAE;IACvB,IAAI,CAACmT,YAAY,CAAC9B,IAAI,CAACrR,KAAK,CAAC;EACjC;AACJ;AACAkT,0BAA0B,CAAC9C,IAAI,YAAAmD,mCAAAjD,iBAAA;EAAA,YAAAA,iBAAA,IAAwF4C,0BAA0B;AAAA,CAAmD;AACpMA,0BAA0B,CAAC1B,IAAI,kBAlFqE3T,EAAE,CAAA4T,iBAAA;EAAAlJ,IAAA,EAkFK2K,0BAA0B;EAAAxB,SAAA;EAAAC,MAAA;IAAA2B,KAAA;IAAA9S,MAAA;EAAA;EAAAsR,OAAA;IAAAqB,YAAA;EAAA;EAAApB,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAsB,oCAAAlU,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAlFjCzB,EAAE,CAAAwJ,UAAA,IAAAhI,yCAAA,gBAkF0W,CAAC;IAAA;IAAA,IAAAC,EAAA;MAlF7WzB,EAAE,CAAAyC,UAAA,YAAAf,GAAA,CAAA6T,UAAA,EAkF0P,CAAC;IAAA;EAAA;EAAAK,YAAA,GAAuf7U,EAAE,CAAC8U,OAAO,EAAgG9U,EAAE,CAAC+U,OAAO;EAAArB,MAAA;EAAAC,eAAA;AAAA,EAA+G;AAC3jC;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KAnFoGjT,EAAE,CAAAkT,iBAAA,CAmFXmC,0BAA0B,EAAc,CAAC;IACxH3K,IAAI,EAAEvK,SAAS;IACfgT,IAAI,EAAE,CAAC;MAAEwB,QAAQ,EAAE,qBAAqB;MAAED,eAAe,EAAEtU,uBAAuB,CAACwU,MAAM;MAAEP,QAAQ,EAAE,iMAAiM;MAAEI,MAAM,EAAE,CAAC,2VAA2V;IAAE,CAAC;EACnpB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEgB,KAAK,EAAE,CAAC;MAClE/K,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEsC,MAAM,EAAE,CAAC;MACT+H,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEiV,YAAY,EAAE,CAAC;MACf5K,IAAI,EAAEpK;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyV,0BAA0B,CAAC;EAC7BvF,WAAWA,CAACwF,YAAY,EAAEC,UAAU,EAAEC,aAAa,EAAEzF,QAAQ,EAAE0F,iBAAiB,EAAE;IAC9E,IAAI,CAACH,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC0F,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACrP,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACqP,SAAS,GAAG,CAAC;IAClB,IAAI,CAACnP,OAAO,GAAG,KAAK;IACpB,IAAI,CAACoP,WAAW,GAAG,CAAC;IACpB,IAAI,CAACnU,KAAK,GAAG,CAAC;IACd,IAAI,CAACoU,WAAW,GAAG,IAAIrW,YAAY,CAAC,CAAC;IACrC,IAAI,CAACsW,YAAY,GAAG,IAAItW,YAAY,CAAC,CAAC;IACtC,IAAI,CAACuW,YAAY,GAAG,IAAIvW,YAAY,CAAC,CAAC;IACtC,IAAI,CAACwW,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;EACvB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAACC,cAAc,EAAE;MACpC,IAAI,CAACD,MAAM,GAAG,KAAK;IACvB;EACJ;EACAE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,OAAO,CAAC,EAAE;MAClB,IAAI,CAAClB,aAAa,CAACtF,WAAW,CAAC,IAAI,CAACyG,KAAK,EAAE,IAAI,CAACpB,UAAU,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC/S,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAC;IACxH;EACJ;EACAsU,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACN,cAAc,IAAI,CAAC,IAAI,CAACD,MAAM,EAAE;MACrC,IAAI,CAACA,MAAM,GAAG,IAAI;IACtB;EACJ;EACAQ,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACP,cAAc,IAAI,IAAI,CAACD,MAAM,EAAE;MACpC,IAAI,CAACA,MAAM,GAAG,KAAK;IACvB;EACJ;EACAS,SAASA,CAAChG,CAAC,EAAE;IACT,IAAI,IAAI,CAACgF,MAAM,EAAE;MACb,IAAI,IAAI,CAACiB,kBAAkB,EAAE;QACzB,IAAI,IAAI,CAACC,cAAc,CAAClG,CAAC,CAAC,EAAE;UACxB,IAAI,CAAC1O,QAAQ,CAAC,CAAC;QACnB,CAAC,MACI,IAAI,IAAI,CAAC6U,cAAc,CAACnG,CAAC,CAAC,EAAE;UAC7B,IAAI,CAACxO,QAAQ,CAAC,CAAC;QACnB;MACJ;MACA,IAAI,IAAI,CAAC4U,UAAU,IAAI,IAAI,CAACC,aAAa,CAACrG,CAAC,CAAC,EAAE;QAC1C,IAAI,CAACsG,KAAK,CAAC,CAAC;MAChB;IACJ;EACJ;EACAC,IAAIA,CAAC9V,KAAK,EAAE;IACR,IAAI,CAACoU,WAAW,CAAC/C,IAAI,CAAC,CAAC;IACvB,IAAI,CAACrR,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuU,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC5L,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,IAAI,CAACoN,eAAe,EAAE;MACtB,IAAI,CAAClS,gBAAgB,CAAC,CAAC;IAC3B;IACA,IAAI,CAACuR,eAAe,GAAG,IAAI,CAAC9G,QAAQ,CAACY,MAAM,CAAC,QAAQ,EAAE,SAAS,EAAGK,CAAC,IAAK,IAAI,CAACgG,SAAS,CAAChG,CAAC,CAAC,CAAC;EAC9F;EACAsG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACtB,MAAM,GAAG,KAAK;IACnB,MAAMyB,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC9G,aAAa;IAC7C,IAAI6G,KAAK,CAACE,WAAW,GAAG,CAAC,IACrB,CAACF,KAAK,CAACG,MAAM,IACb,CAACH,KAAK,CAACI,KAAK,IACZJ,KAAK,CAACK,UAAU,GAAG,CAAC,EAAE;MACtBL,KAAK,CAACM,KAAK,CAAC,CAAC;IACjB;IACA,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAAClC,YAAY,CAAChD,IAAI,CAAC,CAAC;IACxB,IAAI,CAACmF,YAAY,CAAC,CAAC;IACnB,IAAI,IAAI,CAACpB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAhR,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACqS,QAAQ,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC5C,IAAI,CAACF,YAAY,CAAC,CAAC;IACvB;EACJ;EACAlS,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACmS,QAAQ,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC5C,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACF,QAAQ,EAAE;MACf,IAAI,CAACD,YAAY,CAAC,CAAC;MACnB,IAAI,CAACI,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC1B,IAAI,CAAC,IAAI,CAAC9V,QAAQ,CAAC,CAAC,EAAE;UAClB,IAAI,CAACf,KAAK,GAAG,CAAC,CAAC;UACf,IAAI,CAACe,QAAQ,CAAC,CAAC;QACnB;MACJ,CAAC,EAAE,IAAI,CAAC+V,gBAAgB,CAAC;IAC7B;EACJ;EACAN,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACI,KAAK,EAAE;MACZG,YAAY,CAAC,IAAI,CAACH,KAAK,CAAC;IAC5B;EACJ;EACA/Q,WAAWA,CAAC7F,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC2I,IAAI,CAAC,CAAC;EACf;EACA5H,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;MACpB,IAAI,CAACjB,KAAK,EAAE;MACZ,IAAI,IAAI,CAACA,KAAK,KAAK,IAAI,CAAC8F,MAAM,CAACC,MAAM,EAAE;QACnC,IAAI,CAAC/F,KAAK,GAAG,CAAC;MAClB;MACA,IAAI,CAAC2I,IAAI,CAAC,CAAC;MACX,OAAO,IAAI;IACf,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACA9H,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE;MACpB,IAAI,CAAChB,KAAK,EAAE;MACZ,IAAI,IAAI,CAACA,KAAK,GAAG,CAAC,EAAE;QAChB,IAAI,CAACA,KAAK,GAAG,IAAI,CAAC8F,MAAM,CAACC,MAAM,GAAG,CAAC;MACvC;MACA,IAAI,CAAC4C,IAAI,CAAC,CAAC;IACf;EACJ;EACA1H,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC8D,OAAO,EAAE;MACd,OAAO,KAAK;IAChB,CAAC,MACI,IAAI,IAAI,CAACe,MAAM,EAAE;MAClB,OAAO,IAAI,CAACkR,YAAY,IAAI,IAAI,CAAChX,KAAK,GAAG,IAAI,CAAC8F,MAAM,CAACC,MAAM,GAAG,CAAC;IACnE,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACA/E,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC+D,OAAO,EAAE;MACd,OAAO,KAAK;IAChB,CAAC,MACI,IAAI,IAAI,CAACe,MAAM,EAAE;MAClB,OAAO,IAAI,CAACkR,YAAY,IAAI,IAAI,CAAChX,KAAK,GAAG,CAAC;IAC9C,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACA6D,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACoT,UAAU,IAAI,IAAI,CAAClB,eAAe,EAAE;MACzC,MAAMmB,GAAG,GAAGC,QAAQ;MACpB,IAAI,CAACD,GAAG,CAACE,iBAAiB,IAAI,CAACF,GAAG,CAACG,oBAAoB,IAChD,CAACH,GAAG,CAACI,uBAAuB,IAAI,CAACJ,GAAG,CAACK,mBAAmB,EAAE;QAC7D,IAAI,CAACC,cAAc,CAAC,CAAC;MACzB,CAAC,MACI;QACD,IAAI,CAACjB,eAAe,CAAC,CAAC;MAC1B;IACJ;EACJ;EACAjP,UAAUA,CAACsG,KAAK,EAAE;IACd,OAAO,IAAI,CAACiG,YAAY,CAAC4D,sBAAsB,CAAC7J,KAAK,CAAC;EAC1D;EACAnD,WAAWA,CAACoF,UAAU,EAAE;IACpB,OAAO,IAAI,CAACkE,aAAa,CAACtJ,WAAW,CAACoF,UAAU,CAAC;EACrD;EACAhN,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE;MAClB,IAAI,CAACmR,SAAS,IAAI,IAAI,CAACwD,QAAQ;MAC/B,IAAI,IAAI,CAACxD,SAAS,GAAG,IAAI,CAACyD,OAAO,EAAE;QAC/B,IAAI,CAACzD,SAAS,GAAG,IAAI,CAACyD,OAAO;MACjC;IACJ;EACJ;EACApV,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACE,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAACyR,SAAS,IAAI,IAAI,CAACwD,QAAQ;MAC/B,IAAI,IAAI,CAACxD,SAAS,GAAG,IAAI,CAAC0D,OAAO,EAAE;QAC/B,IAAI,CAAC1D,SAAS,GAAG,IAAI,CAAC0D,OAAO;MACjC;MACA,IAAI,IAAI,CAAC1D,SAAS,IAAI,CAAC,EAAE;QACrB,IAAI,CAAC2D,aAAa,CAAC,CAAC;MACxB;IACJ;EACJ;EACA1U,UAAUA,CAAA,EAAG;IACT,IAAI,CAACgR,WAAW,IAAI,EAAE;EAC1B;EACA3Q,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2Q,WAAW,IAAI,EAAE;EAC1B;EACAxP,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACkP,YAAY,CAACiE,wBAAwB,CAAC,QAAQ,GAAG,IAAI,CAAC5D,SAAS,GAAG,WAAW,GAAG,IAAI,CAACC,WAAW,GAAG,MAAM,CAAC;EAC1H;EACApR,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACmR,SAAS,GAAG,IAAI,CAACyD,OAAO;EACxC;EACAlV,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACyR,SAAS,GAAG,IAAI,CAAC0D,OAAO;EACxC;EACA3S,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC8S,IAAI,IAAI,IAAI,CAAC7D,SAAS,GAAG,CAAC;EAC1C;EACA1P,gBAAgBA,CAAC+K,CAAC,EAAE;IAChB,IAAI,IAAI,CAACtK,aAAa,CAAC,CAAC,EAAE;MACtB,IAAI,CAACuP,QAAQ,GAAG,IAAI,CAACwD,UAAU,CAACzI,CAAC,CAAC;MAClC,IAAI,CAACkF,QAAQ,GAAG,IAAI,CAACwD,UAAU,CAAC1I,CAAC,CAAC;MAClC,IAAI,CAACmF,WAAW,GAAG,IAAI,CAAC9P,YAAY;MACpC,IAAI,CAAC+P,UAAU,GAAG,IAAI,CAAC9P,WAAW;MAClC,IAAI,CAAC+P,MAAM,GAAG,IAAI;MAClBrF,CAAC,CAACsD,cAAc,CAAC,CAAC;IACtB;EACJ;EACAqF,cAAcA,CAAC3I,CAAC,EAAE;IACd,IAAI,CAACqF,MAAM,GAAG,KAAK;EACvB;EACAuD,gBAAgBA,CAAC5I,CAAC,EAAE;IAChB,IAAI,IAAI,CAACqF,MAAM,EAAE;MACb,IAAI,CAAChQ,YAAY,GAAG,IAAI,CAAC8P,WAAW,IAAI,IAAI,CAACsD,UAAU,CAACzI,CAAC,CAAC,GAAG,IAAI,CAACiF,QAAQ,CAAC;MAC3E,IAAI,CAAC3P,WAAW,GAAG,IAAI,CAAC8P,UAAU,IAAI,IAAI,CAACsD,UAAU,CAAC1I,CAAC,CAAC,GAAG,IAAI,CAACkF,QAAQ,CAAC;IAC7E;EACJ;EACAuD,UAAUA,CAACzI,CAAC,EAAE;IACV,OAAOA,CAAC,CAAC6I,OAAO,IAAI7I,CAAC,CAAC6I,OAAO,CAACrS,MAAM,GAAGwJ,CAAC,CAAC6I,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG9I,CAAC,CAAC8I,OAAO;EAC3E;EACAJ,UAAUA,CAAC1I,CAAC,EAAE;IACV,OAAOA,CAAC,CAAC6I,OAAO,IAAI7I,CAAC,CAAC6I,OAAO,CAACrS,MAAM,GAAGwJ,CAAC,CAAC6I,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO,GAAG/I,CAAC,CAAC+I,OAAO;EAC3E;EACAT,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACE,IAAI,EAAE;MACX,IAAI,CAACnT,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,WAAW,GAAG,CAAC;IACxB;EACJ;EACA6Q,cAAcA,CAACnG,CAAC,EAAE;IACd,OAAOA,CAAC,CAACgJ,OAAO,KAAK,EAAE;EAC3B;EACA9C,cAAcA,CAAClG,CAAC,EAAE;IACd,OAAOA,CAAC,CAACgJ,OAAO,KAAK,EAAE;EAC3B;EACA3C,aAAaA,CAACrG,CAAC,EAAE;IACb,OAAOA,CAAC,CAACgJ,OAAO,KAAK,EAAE;EAC3B;EACAf,cAAcA,CAAA,EAAG;IACb,MAAM7I,OAAO,GAAGwI,QAAQ,CAACqB,eAAe;IACxC,IAAI7J,OAAO,CAAC8J,iBAAiB,EAAE;MAC3B9J,OAAO,CAAC8J,iBAAiB,CAAC,CAAC;IAC/B,CAAC,MACI,IAAI9J,OAAO,CAAC+J,mBAAmB,EAAE;MAClC/J,OAAO,CAAC+J,mBAAmB,CAAC,CAAC;IACjC,CAAC,MACI,IAAI/J,OAAO,CAACgK,oBAAoB,EAAE;MACnChK,OAAO,CAACgK,oBAAoB,CAAC,CAAC;IAClC,CAAC,MACI,IAAIhK,OAAO,CAACiK,uBAAuB,EAAE;MACtCjK,OAAO,CAACiK,uBAAuB,CAAC,CAAC;IACrC;EACJ;EACArC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACsC,YAAY,CAAC,CAAC,EAAE;MACrB,MAAM3B,GAAG,GAAGC,QAAQ;MACpB,IAAID,GAAG,CAAC4B,cAAc,EAAE;QACpB5B,GAAG,CAAC4B,cAAc,CAAC,CAAC;MACxB,CAAC,MACI,IAAI5B,GAAG,CAAC6B,gBAAgB,EAAE;QAC3B7B,GAAG,CAAC6B,gBAAgB,CAAC,CAAC;MAC1B,CAAC,MACI,IAAI7B,GAAG,CAAC8B,mBAAmB,EAAE;QAC9B9B,GAAG,CAAC8B,mBAAmB,CAAC,CAAC;MAC7B,CAAC,MACI,IAAI9B,GAAG,CAAC+B,oBAAoB,EAAE;QAC/B/B,GAAG,CAAC+B,oBAAoB,CAAC,CAAC;MAC9B;IACJ;EACJ;EACAJ,YAAYA,CAAA,EAAG;IACX,MAAM3B,GAAG,GAAGC,QAAQ;IACpB,OAAOD,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACI,uBAAuB,IACpDJ,GAAG,CAACG,oBAAoB,IAAIH,GAAG,CAACK,mBAAmB;EAC9D;EACA5O,IAAIA,CAACuQ,KAAK,GAAG,KAAK,EAAE;IAChB,IAAI,CAACnU,OAAO,GAAG,IAAI;IACnB,IAAI,CAACyR,YAAY,CAAC,CAAC;IACnB,IAAI,CAAClC,YAAY,CAACjD,IAAI,CAAC,IAAI,CAACrR,KAAK,CAAC;IAClC,IAAIkZ,KAAK,IAAI,CAAC,IAAI,CAAClU,SAAS,EAAE;MAC1B,IAAI,CAACmU,KAAK,CAAC,CAAC;IAChB,CAAC,MACI;MACDtC,UAAU,CAAC,MAAM,IAAI,CAACsC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;IACvC;EACJ;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACjF,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC0D,aAAa,CAAC,CAAC;IACpB,IAAI,CAAC9V,GAAG,GAAG,IAAI,CAACuF,UAAU,CAAC,IAAI,CAACxB,MAAM,CAAC,IAAI,CAAC9F,KAAK,CAAC,CAAC;IACnD,IAAI,CAACuI,IAAI,GAAG,IAAI,CAACkC,WAAW,CAAC,IAAI,CAAC3E,MAAM,CAAC,IAAI,CAAC9F,KAAK,CAAC,CAAC;IACrD,IAAI,CAACoZ,QAAQ,GAAG,IAAI,CAACpZ,KAAK;IAC1B,IAAI,CAACmG,WAAW,GAAG,IAAI,CAACU,YAAY,CAAC,IAAI,CAAC7G,KAAK,CAAC;IAChD,IAAI,CAACgU,iBAAiB,CAACqF,YAAY,CAAC,CAAC;IACrCxC,UAAU,CAAC,MAAM;MACb,IAAI,IAAI,CAACyC,QAAQ,CAAC,IAAI,CAACrD,YAAY,CAAC9G,aAAa,CAAC,IAAI,IAAI,CAAC5G,IAAI,KAAK,OAAO,EAAE;QACzE,IAAI,CAACxD,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC4R,aAAa,CAAC,CAAC;QACpB,IAAI,CAAC3C,iBAAiB,CAACqF,YAAY,CAAC,CAAC;MACzC,CAAC,MACI,IAAI,IAAI,CAAC9Q,IAAI,KAAK,OAAO,EAAE,CAChC,CAAC,MACI;QACDsO,UAAU,CAAC,MAAM;UACb,IAAI,IAAI,CAAC9R,OAAO,EAAE;YACd,IAAI,CAACkP,WAAW,GAAG,IAAI;YACvB,IAAI,CAACD,iBAAiB,CAACqF,YAAY,CAAC,CAAC;UACzC;QACJ,CAAC,CAAC;QACF,IAAI,CAACpD,YAAY,CAAC9G,aAAa,CAACoK,MAAM,GAAG,MAAM;UAC3C,IAAI,CAACxU,OAAO,GAAG,KAAK;UACpB,IAAI,CAACkP,WAAW,GAAG,KAAK;UACxB,IAAI,CAACgC,YAAY,CAAC9G,aAAa,CAACoK,MAAM,GAAG,IAAI;UAC7C,IAAI,CAAC5C,aAAa,CAAC,CAAC;UACpB,IAAI,CAAC3C,iBAAiB,CAACqF,YAAY,CAAC,CAAC;QACzC,CAAC;MACL;IACJ,CAAC,CAAC;EACN;EACAC,QAAQA,CAACE,GAAG,EAAE;IACV,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE;MACf,OAAO,KAAK;IAChB;IACA,OAAO,EAAE,OAAOD,GAAG,CAACE,YAAY,KAAK,WAAW,IAAIF,GAAG,CAACE,YAAY,KAAK,CAAC,CAAC;EAC/E;AACJ;AACA9F,0BAA0B,CAACxD,IAAI,YAAAuJ,mCAAArJ,iBAAA;EAAA,YAAAA,iBAAA,IAAwFsD,0BAA0B,EA1b7C/V,EAAE,CAAA+b,iBAAA,CA0b6Dnb,EAAE,CAACob,YAAY,GA1b9Ehc,EAAE,CAAA+b,iBAAA,CA0byF/b,EAAE,CAACic,UAAU,GA1bxGjc,EAAE,CAAA+b,iBAAA,CA0bmHxL,iBAAiB,GA1btIvQ,EAAE,CAAA+b,iBAAA,CA0biJ/b,EAAE,CAAC2S,SAAS,GA1b/J3S,EAAE,CAAA+b,iBAAA,CA0b0K/b,EAAE,CAACkc,iBAAiB;AAAA,CAA4C;AAChVnG,0BAA0B,CAACpC,IAAI,kBA3bqE3T,EAAE,CAAA4T,iBAAA;EAAAlJ,IAAA,EA2bKqL,0BAA0B;EAAAlC,SAAA;EAAAsI,SAAA,WAAAC,iCAAA3a,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3bjCzB,EAAE,CAAAqc,WAAA,CAAAzZ,GAAA;IAAA;IAAA,IAAAnB,EAAA;MAAA,IAAA6a,EAAA;MAAFtc,EAAE,CAAAuc,cAAA,CAAAD,EAAA,GAAFtc,EAAE,CAAAwc,WAAA,QAAA9a,GAAA,CAAA0W,YAAA,GAAAkE,EAAA,CAAAjB,KAAA;IAAA;EAAA;EAAAoB,YAAA,WAAAC,wCAAAjb,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFzB,EAAE,CAAA8B,UAAA,wBAAA6a,yDAAA;QAAA,OA2bKjb,GAAA,CAAA8V,YAAA,CAAa,CAAC;MAAA,CAAW,CAAC,wBAAAoF,yDAAA;QAAA,OAA1Blb,GAAA,CAAA+V,YAAA,CAAa,CAAC;MAAA,CAAW,CAAC;IAAA;EAAA;EAAA3D,MAAA;IAAA7L,MAAA;IAAAe,YAAA;IAAAa,eAAA;IAAAoN,MAAA;IAAAC,cAAA;IAAAG,KAAA;IAAA+B,UAAA;IAAAlB,eAAA;IAAA2E,YAAA;IAAA/E,UAAA;IAAAH,kBAAA;IAAAtU,aAAA;IAAAC,aAAA;IAAAwZ,SAAA;IAAA7W,cAAA;IAAA8W,WAAA;IAAAnE,QAAA;IAAAK,gBAAA;IAAAJ,oBAAA;IAAAM,YAAA;IAAAe,IAAA;IAAAL,QAAA;IAAAC,OAAA;IAAAC,OAAA;IAAA9U,UAAA;IAAAN,WAAA;IAAAwC,SAAA;IAAAyC,OAAA;IAAAoT,MAAA;IAAAzX,cAAA;IAAAK,eAAA;IAAAqX,QAAA;IAAA3Y,YAAA;IAAA4Y,OAAA;EAAA;EAAAjJ,OAAA;IAAAsC,WAAA;IAAAC,YAAA;IAAAC,YAAA;EAAA;EAAA0G,QAAA,GA3bjCnd,EAAE,CAAAod,oBAAA;EAAAlJ,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAgJ,oCAAA5b,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFzB,EAAE,CAAAwJ,UAAA,IAAA3G,wDAAA,+BA2bwlD,CAAC;MA3b3lD7C,EAAE,CAAA6B,cAAA,YA2bspD,CAAC,YAA4C,CAAC;MA3btsD7B,EAAE,CAAAwJ,UAAA,IAAAjG,wDAAA,+BA2b65D,CAAC,IAAAS,uCAAA,cAA+H,CAAC,IAAAO,wDAAA,+BAA+N,CAAC,IAAAM,wDAAA,+BAAkK,CAAC,IAAAM,wDAAA,+BAAuH,CAAC,IAAAK,wDAAA,+BAAyH,CAAC,IAAAK,wDAAA,+BAAsL,CAAC;MA3b50F7F,EAAE,CAAA6B,cAAA,4BA2b67F,CAAC;MA3bh8F7B,EAAE,CAAA8B,UAAA,wBAAAwb,8EAAA;QAAA,OA2bo7F5b,GAAA,CAAAsW,KAAA,CAAM,CAAC;MAAA,CAAC,CAAC;MA3b/7FhY,EAAE,CAAAwC,YAAA,CA2bk9F,CAAC,CAAS,CAAC,CAAO,CAAC;MA3bv+FxC,EAAE,CAAA6B,cAAA,aA2bukG,CAAC;MA3b1kG7B,EAAE,CAAAiE,SAAA,YA2bsqG,CAAC;MA3bzqGjE,EAAE,CAAAwC,YAAA,CA2b8qG,CAAC;MA3bjrGxC,EAAE,CAAA6B,cAAA,cA2bk6G,CAAC;MA3br6G7B,EAAE,CAAA8B,UAAA,mBAAAyb,0DAAA;QAAA,OAAA7b,GAAA,CAAAmb,YAAA,IA2bsvGnb,GAAA,CAAAsW,KAAA,CAAM,CAAC;MAAA,CAAC,CAAC,qBAAAwF,4DAAAxb,MAAA;QAAA,OAAaN,GAAA,CAAA2Y,cAAA,CAAArY,MAAqB,CAAC;MAAA,CAAC,CAAC,uBAAAyb,8DAAAzb,MAAA;QAAA,OAAqBN,GAAA,CAAA4Y,gBAAA,CAAAtY,MAAuB,CAAC;MAAA,CAAC,CAAC,sBAAA0b,6DAAA1b,MAAA;QAAA,OAAcN,GAAA,CAAA2Y,cAAA,CAAArY,MAAqB,CAAC;MAAA,CAAC,CAAC,uBAAA2b,8DAAA3b,MAAA;QAAA,OAAeN,GAAA,CAAA4Y,gBAAA,CAAAtY,MAAuB,CAAC;MAAA,CAAC,CAAC;MA3bp6GhC,EAAE,CAAA6B,cAAA,cA2bq9G,CAAC;MA3bx9G7B,EAAE,CAAAwJ,UAAA,KAAAtD,0CAAA,kBA2b4hI,CAAC,KAAAmB,4CAAA,oBAA+kB,CAAC,KAAAQ,0DAAA,iCAA0P,CAAC;MA3b12J7H,EAAE,CAAAwC,YAAA,CA2bu4J,CAAC;MA3b14JxC,EAAE,CAAAwJ,UAAA,KAAArB,0CAAA,iBA2bqiK,CAAC;MA3bxiKnI,EAAE,CAAAwC,YAAA,CA2bmjK,CAAC;IAAA;IAAA,IAAAf,EAAA;MA3btjKzB,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAuV,MA2b62C,CAAC;MA3bh3CjX,EAAE,CAAAoE,SAAA,EA2b2vD,CAAC;MA3b9vDpE,EAAE,CAAAyC,UAAA,YAAAf,GAAA,CAAAkI,OA2b2vD,CAAC;MA3b9vD5J,EAAE,CAAAoE,SAAA,CA2bk9D,CAAC;MA3br9DpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAub,QAAA,IAAAvb,GAAA,CAAAwC,GA2bk9D,CAAC;MA3br9DlE,EAAE,CAAAoE,SAAA,CA2bkpE,CAAC;MA3brpEpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAwY,IA2bkpE,CAAC;MA3brpEla,EAAE,CAAAoE,SAAA,CA2bwzE,CAAC;MA3b3zEpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAwY,IA2bwzE,CAAC;MA3b3zEla,EAAE,CAAAoE,SAAA,CA2b69E,CAAC;MA3bh+EpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAsb,MA2b69E,CAAC;MA3bh+Ehd,EAAE,CAAAoE,SAAA,CA2bqlF,CAAC;MA3bxlFpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAsb,MA2bqlF,CAAC;MA3bxlFhd,EAAE,CAAAoE,SAAA,CA2bmtF,CAAC;MA3bttFpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAA0X,UA2bmtF,CAAC;MA3bttFpZ,EAAE,CAAAoE,SAAA,CA2bm6F,CAAC;MA3bt6FpE,EAAE,CAAAyC,UAAA,gCAAAf,GAAA,CAAAob,SA2bm6F,CAAC;MA3bt6F9c,EAAE,CAAAoE,SAAA,CA2bskG,CAAC;MA3bzkGpE,EAAE,CAAAiH,WAAA,uBAAAvF,GAAA,CAAA0U,WA2bskG,CAAC;MA3bzkGpW,EAAE,CAAAoE,SAAA,CA2b4oG,CAAC;MA3b/oGpE,EAAE,CAAAqE,sBAAA,0CAAA3C,GAAA,CAAAqb,WAAA,IA2b4oG,CAAC;MA3b/oG/c,EAAE,CAAAoE,SAAA,EA2b+/G,CAAC;MA3blgHpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAwC,GAAA,IAAAxC,GAAA,CAAAgJ,IAAA,YA2b+/G,CAAC;MA3blgH1K,EAAE,CAAAoE,SAAA,CA2bwkI,CAAC;MA3b3kIpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAwC,GAAA,IAAAxC,GAAA,CAAAgJ,IAAA,YA2bwkI,CAAC;MA3b3kI1K,EAAE,CAAAoE,SAAA,CA2bsvJ,CAAC;MA3bzvJpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAwb,OA2bsvJ,CAAC;MA3bzvJld,EAAE,CAAAoE,SAAA,CA2by9J,CAAC;MA3b59JpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAmI,eAAA,IAAAnI,GAAA,CAAA4G,WA2by9J,CAAC;IAAA;EAAA;EAAAsN,YAAA,GAAysDxC,yBAAyB,EAA6JyB,yBAAyB,EAAgHQ,0BAA0B,EAAmHtU,EAAE,CAAC6c,IAAI,EAA0E7c,EAAE,CAAC8U,OAAO;EAAApB,MAAA;EAAAC,eAAA;AAAA,EAA8I;AAC97O;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KA5boGjT,EAAE,CAAAkT,iBAAA,CA4bX6C,0BAA0B,EAAc,CAAC;IACxHrL,IAAI,EAAEvK,SAAS;IACfgT,IAAI,EAAE,CAAC;MAAEwB,QAAQ,EAAE,qBAAqB;MAAED,eAAe,EAAEtU,uBAAuB,CAACwU,MAAM;MAAEP,QAAQ,EAAE,4uHAA4uH;MAAEI,MAAM,EAAE,CAAC,ykDAAykD;IAAE,CAAC;EAC56K,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/J,IAAI,EAAE9J,EAAE,CAACob;IAAa,CAAC,EAAE;MAAEtR,IAAI,EAAE1K,EAAE,CAACic;IAAW,CAAC,EAAE;MAAEvR,IAAI,EAAE6F;IAAkB,CAAC,EAAE;MAAE7F,IAAI,EAAE1K,EAAE,CAAC2S;IAAU,CAAC,EAAE;MAAEjI,IAAI,EAAE1K,EAAE,CAACkc;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEjU,MAAM,EAAE,CAAC;MAC1MyC,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE2I,YAAY,EAAE,CAAC;MACf0B,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEwJ,eAAe,EAAE,CAAC;MAClBa,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE4W,MAAM,EAAE,CAAC;MACTvM,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE6W,cAAc,EAAE,CAAC;MACjBxM,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEgX,KAAK,EAAE,CAAC;MACR3M,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE+Y,UAAU,EAAE,CAAC;MACb1O,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE6X,eAAe,EAAE,CAAC;MAClBxN,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEwc,YAAY,EAAE,CAAC;MACfnS,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEyX,UAAU,EAAE,CAAC;MACbpN,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEsX,kBAAkB,EAAE,CAAC;MACrBjN,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEgD,aAAa,EAAE,CAAC;MAChBqH,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEiD,aAAa,EAAE,CAAC;MAChBoH,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEyc,SAAS,EAAE,CAAC;MACZpS,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE4F,cAAc,EAAE,CAAC;MACjByE,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE0c,WAAW,EAAE,CAAC;MACdrS,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEuY,QAAQ,EAAE,CAAC;MACXlO,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE4Y,gBAAgB,EAAE,CAAC;MACnBvO,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEwY,oBAAoB,EAAE,CAAC;MACvBnO,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE8Y,YAAY,EAAE,CAAC;MACfzO,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE6Z,IAAI,EAAE,CAAC;MACPxP,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEwZ,QAAQ,EAAE,CAAC;MACXnP,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEyZ,OAAO,EAAE,CAAC;MACVpP,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE0Z,OAAO,EAAE,CAAC;MACVrP,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE4E,UAAU,EAAE,CAAC;MACbyF,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEsE,WAAW,EAAE,CAAC;MACd+F,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE8G,SAAS,EAAE,CAAC;MACZuD,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEuJ,OAAO,EAAE,CAAC;MACVc,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE2c,MAAM,EAAE,CAAC;MACTtS,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEkF,cAAc,EAAE,CAAC;MACjBmF,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEuF,eAAe,EAAE,CAAC;MAClB8E,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE4c,QAAQ,EAAE,CAAC;MACXvS,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEiE,YAAY,EAAE,CAAC;MACfoG,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE6c,OAAO,EAAE,CAAC;MACVxS,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEkW,WAAW,EAAE,CAAC;MACd7L,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEkW,YAAY,EAAE,CAAC;MACf9L,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEmW,YAAY,EAAE,CAAC;MACf/L,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAE8X,YAAY,EAAE,CAAC;MACf1N,IAAI,EAAEnK,SAAS;MACf4S,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEqE,YAAY,EAAE,CAAC;MACf9M,IAAI,EAAElK,YAAY;MAClB2S,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEsE,YAAY,EAAE,CAAC;MACf/M,IAAI,EAAElK,YAAY;MAClB2S,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0K,mBAAmB,CAAC;AAE1BA,mBAAmB,CAACC,IAAI,GAAG,MAAM;AACjCD,mBAAmB,CAACE,KAAK,GAAG,OAAO;AACnCF,mBAAmB,CAACG,MAAM,GAAG,QAAQ;AACrCH,mBAAmB,CAACI,IAAI,GAAG,MAAM;AAEjC,MAAMC,wBAAwB,CAAC;EAC3B1N,WAAWA,CAACwF,YAAY,EAAEG,iBAAiB,EAAEF,UAAU,EAAEC,aAAa,EAAE;IACpE,IAAI,CAACF,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACG,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACiI,UAAU,GAAG,IAAIje,YAAY,CAAC,CAAC;IACpC,IAAI,CAACuW,YAAY,GAAG,IAAIvW,YAAY,CAAC,CAAC;IACtC,IAAI,CAACke,SAAS,GAAG,IAAIle,YAAY,CAAC,CAAC;IACnC,IAAI,CAACme,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC9O,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC4G,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACxM,MAAM,GAAG,MAAM;EACxB;EACA,IAAI8B,aAAaA,CAACtJ,KAAK,EAAE;IACrB,IAAIA,KAAK,GAAG,IAAI,CAACwI,cAAc,EAAE;MAC7B,IAAIhB,MAAM;MACV,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACE,KAAK,EAAE;QAC9CpU,MAAM,GAAG,YAAY;MACzB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACC,IAAI,EAAE;QAClDnU,MAAM,GAAG,MAAM;MACnB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACG,MAAM,EAAE;QACpDrU,MAAM,GAAG,aAAa;MAC1B,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACI,IAAI,EAAE;QAClDtU,MAAM,GAAG,MAAM;MACnB;MACA,IAAI,CAAC2U,SAAS,CAAC3U,MAAM,CAAC;IAC1B,CAAC,MACI,IAAIxH,KAAK,GAAG,IAAI,CAACwI,cAAc,EAAE;MAClC,IAAIhB,MAAM;MACV,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACE,KAAK,EAAE;QAC9CpU,MAAM,GAAG,WAAW;MACxB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACC,IAAI,EAAE;QAClDnU,MAAM,GAAG,MAAM;MACnB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACG,MAAM,EAAE;QACpDrU,MAAM,GAAG,YAAY;MACzB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACI,IAAI,EAAE;QAClDtU,MAAM,GAAG,MAAM;MACnB;MACA,IAAI,CAAC2U,SAAS,CAAC3U,MAAM,CAAC;IAC1B;IACA,IAAI,CAACgB,cAAc,GAAGxI,KAAK;EAC/B;EACA;EACA;EACA6U,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAACC,cAAc,EAAE;MACpC,IAAI,CAACD,MAAM,GAAG,KAAK;IACvB;IACA,IAAI,IAAI,CAAC2B,QAAQ,EAAE;MACf,IAAI,CAACE,aAAa,CAAC,CAAC;IACxB;EACJ;EACA3B,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,OAAO,CAAC,EAAE;MAClB,IAAI,CAAClB,aAAa,CAACtF,WAAW,CAAC,IAAI,CAACyG,KAAK,EAAE,IAAI,CAACpB,UAAU,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC/S,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAC;IACtH;EACJ;EACAwU,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACN,cAAc,IAAI,CAAC,IAAI,CAACD,MAAM,EAAE;MACrC,IAAI,CAACA,MAAM,GAAG,IAAI;IACtB;IACA,IAAI,IAAI,CAAC2B,QAAQ,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC5C,IAAI,CAACF,YAAY,CAAC,CAAC;IACvB;EACJ;EACAlB,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACP,cAAc,IAAI,IAAI,CAACD,MAAM,EAAE;MACpC,IAAI,CAACA,MAAM,GAAG,KAAK;IACvB;IACA,IAAI,IAAI,CAAC2B,QAAQ,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC5C,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAyF,KAAKA,CAACpc,KAAK,EAAE;IACT,IAAI,CAACwI,cAAc,GAAGxI,KAAK;IAC3B,IAAI,CAACwH,MAAM,GAAG,MAAM;EACxB;EACA6U,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACvW,MAAM,EAAE;MACd,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAAC2G,WAAW,EAAE;MAClB,MAAM6P,OAAO,GAAG,CAAC,IAAI,CAAC9T,cAAc,CAAC;MACrC,MAAM+T,SAAS,GAAG,IAAI,CAAC/T,cAAc,GAAG,CAAC;MACzC,IAAI+T,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAACvF,YAAY,EAAE;QACvCsF,OAAO,CAACE,IAAI,CAAC,IAAI,CAAC1W,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;MACxC,CAAC,MACI,IAAIwW,SAAS,IAAI,CAAC,EAAE;QACrBD,OAAO,CAACE,IAAI,CAACD,SAAS,CAAC;MAC3B;MACA,MAAME,SAAS,GAAG,IAAI,CAACjU,cAAc,GAAG,CAAC;MACzC,IAAIiU,SAAS,KAAK,IAAI,CAAC3W,MAAM,CAACC,MAAM,IAAI,IAAI,CAACiR,YAAY,EAAE;QACvDsF,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC;MACnB,CAAC,MACI,IAAIC,SAAS,GAAG,IAAI,CAAC3W,MAAM,CAACC,MAAM,EAAE;QACrCuW,OAAO,CAACE,IAAI,CAACC,SAAS,CAAC;MAC3B;MACA,OAAO,IAAI,CAAC3W,MAAM,CAAC4W,MAAM,CAAC,CAAClD,GAAG,EAAEmD,CAAC,KAAKL,OAAO,CAACM,OAAO,CAACD,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACpE,CAAC,MACI;MACD,OAAO,IAAI,CAAC7W,MAAM;IACtB;EACJ;EACA6Q,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACH,YAAY,CAAC,CAAC;IACnB,IAAI,CAACI,KAAK,GAAGiG,WAAW,CAAC,MAAM;MAC3B,IAAI,CAAC,IAAI,CAAC9b,QAAQ,CAAC,CAAC,EAAE;QAClB,IAAI,CAACyH,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAACzH,QAAQ,CAAC,CAAC;MACnB;IACJ,CAAC,EAAE,IAAI,CAAC+V,gBAAgB,CAAC;EAC7B;EACAN,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACI,KAAK,EAAE;MACZkG,aAAa,CAAC,IAAI,CAAClG,KAAK,CAAC;IAC7B;EACJ;EACA5P,WAAWA,CAAC4L,KAAK,EAAE5S,KAAK,EAAE;IACtB,IAAI,IAAI,CAACuH,SAAS,EAAE;MAChB,IAAI,CAACyU,UAAU,CAAC3K,IAAI,CAACrR,KAAK,CAAC;MAC3B4S,KAAK,CAAC1O,eAAe,CAAC,CAAC;MACvB0O,KAAK,CAACC,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAlK,IAAIA,CAAC3I,KAAK,EAAE;IACR,IAAI,IAAI,CAACoN,WAAW,EAAE;MAClB;IACJ;IACA,IAAIpN,KAAK,GAAG,IAAI,CAACwI,cAAc,EAAE;MAC7B,IAAIhB,MAAM;MACV,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACE,KAAK,EAAE;QAC9CpU,MAAM,GAAG,YAAY;MACzB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACC,IAAI,EAAE;QAClDnU,MAAM,GAAG,MAAM;MACnB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACG,MAAM,EAAE;QACpDrU,MAAM,GAAG,aAAa;MAC1B,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACI,IAAI,EAAE;QAClDtU,MAAM,GAAG,MAAM;MACnB;MACA,IAAI,CAAC2U,SAAS,CAAC3U,MAAM,CAAC;IAC1B,CAAC,MACI;MACD,IAAIA,MAAM;MACV,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACE,KAAK,EAAE;QAC9CpU,MAAM,GAAG,WAAW;MACxB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACC,IAAI,EAAE;QAClDnU,MAAM,GAAG,MAAM;MACnB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACG,MAAM,EAAE;QACpDrU,MAAM,GAAG,YAAY;MACzB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACI,IAAI,EAAE;QAClDtU,MAAM,GAAG,MAAM;MACnB;MACA,IAAI,CAAC2U,SAAS,CAAC3U,MAAM,CAAC;IAC1B;IACA,IAAI,CAACgB,cAAc,GAAGxI,KAAK;IAC3B,IAAI,CAACsU,YAAY,CAACjD,IAAI,CAAC,IAAI,CAAC7I,cAAc,CAAC;IAC3C,IAAI,CAACuU,gBAAgB,CAAC,CAAC;EAC3B;EACAZ,SAASA,CAAC3U,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACwM,iBAAiB,CAACgJ,aAAa,CAAC,CAAC;EAC1C;EACAjc,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACqM,WAAW,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACnM,WAAW,CAAC,CAAC,IAAI,IAAI,CAACib,cAAc,EAAE;MAC3C,IAAI1U,MAAM;MACV,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACE,KAAK,EAAE;QAC9CpU,MAAM,GAAG,YAAY;MACzB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACC,IAAI,EAAE;QAClDnU,MAAM,GAAG,MAAM;MACnB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACG,MAAM,EAAE;QACpDrU,MAAM,GAAG,aAAa;MAC1B,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACI,IAAI,EAAE;QAClDtU,MAAM,GAAG,MAAM;MACnB;MACA,IAAI,CAAC2U,SAAS,CAAC3U,MAAM,CAAC;MACtB,IAAI,CAACgB,cAAc,EAAE;MACrB,IAAI,IAAI,CAACA,cAAc,KAAK,IAAI,CAAC1C,MAAM,CAACC,MAAM,EAAE;QAC5C,IAAI,CAACyC,cAAc,GAAG,CAAC;MAC3B;MACA,IAAI,CAAC8L,YAAY,CAACjD,IAAI,CAAC,IAAI,CAAC7I,cAAc,CAAC;MAC3C,IAAI,CAACuU,gBAAgB,CAAC,CAAC;MACvB,OAAO,IAAI;IACf,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACAlc,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACuM,WAAW,EAAE;MAClB;IACJ;IACA,IAAI,IAAI,CAACpM,WAAW,CAAC,CAAC,IAAI,IAAI,CAACkb,cAAc,EAAE;MAC3C,IAAI1U,MAAM;MACV,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACE,KAAK,EAAE;QAC9CpU,MAAM,GAAG,WAAW;MACxB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACC,IAAI,EAAE;QAClDnU,MAAM,GAAG,MAAM;MACnB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACG,MAAM,EAAE;QACpDrU,MAAM,GAAG,YAAY;MACzB,CAAC,MACI,IAAI,IAAI,CAACxC,SAAS,KAAK0W,mBAAmB,CAACI,IAAI,EAAE;QAClDtU,MAAM,GAAG,MAAM;MACnB;MACA,IAAI,CAAC2U,SAAS,CAAC3U,MAAM,CAAC;MACtB,IAAI,CAACgB,cAAc,EAAE;MACrB,IAAI,IAAI,CAACA,cAAc,GAAG,CAAC,EAAE;QACzB,IAAI,CAACA,cAAc,GAAG,IAAI,CAAC1C,MAAM,CAACC,MAAM,GAAG,CAAC;MAChD;MACA,IAAI,CAACuO,YAAY,CAACjD,IAAI,CAAC,IAAI,CAAC7I,cAAc,CAAC;MAC3C,IAAI,CAACuU,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAA,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACb,cAAc,GAAG,KAAK;IAC3B,IAAIe,OAAO,GAAG,IAAI;IAClB,IAAI,IAAI,CAACjY,SAAS,KAAK0W,mBAAmB,CAACE,KAAK,IACzC,IAAI,CAAC5W,SAAS,KAAK0W,mBAAmB,CAACC,IAAI,EAAE;MAChDsB,OAAO,GAAG,GAAG;IACjB;IACApG,UAAU,CAAC,MAAM;MACb,IAAI,CAACqF,cAAc,GAAG,IAAI;IAC9B,CAAC,EAAEe,OAAO,CAAC;EACf;EACAhc,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC6E,MAAM,EAAE;MACb,OAAO,IAAI,CAACkR,YAAY,IAAI,IAAI,CAACxO,cAAc,GAAG,IAAI,CAAC1C,MAAM,CAACC,MAAM,GAAG,CAAC;IAC5E,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACA/E,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC8E,MAAM,EAAE;MACb,OAAO,IAAI,CAACkR,YAAY,IAAI,IAAI,CAACxO,cAAc,GAAG,CAAC;IACvD,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACAlB,UAAUA,CAACsG,KAAK,EAAE;IACd,OAAO,IAAI,CAACiG,YAAY,CAACiE,wBAAwB,CAAC,IAAI,CAAC/D,aAAa,CAACnE,gBAAgB,CAAChC,KAAK,CAACsP,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5G;EACAzS,WAAWA,CAACoF,UAAU,EAAE;IACpB,OAAO,IAAI,CAACkE,aAAa,CAACtJ,WAAW,CAACoF,UAAU,CAAC;EACrD;EACA3I,OAAOA,CAAC0L,KAAK,EAAE;IACX,IAAI,CAACxF,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC6O,SAAS,CAAC5K,IAAI,CAAC,IAAI,CAAC;EAC7B;EACAjK,MAAMA,CAACwL,KAAK,EAAE;IACV,IAAI,CAACxF,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC6O,SAAS,CAAC5K,IAAI,CAAC,KAAK,CAAC;EAC9B;AACJ;AACA0K,wBAAwB,CAAC3L,IAAI,YAAA+M,iCAAA7M,iBAAA;EAAA,YAAAA,iBAAA,IAAwFyL,wBAAwB,EA9yBzCle,EAAE,CAAA+b,iBAAA,CA8yByDnb,EAAE,CAACob,YAAY,GA9yB1Ehc,EAAE,CAAA+b,iBAAA,CA8yBqF/b,EAAE,CAACkc,iBAAiB,GA9yB3Glc,EAAE,CAAA+b,iBAAA,CA8yBsH/b,EAAE,CAACic,UAAU,GA9yBrIjc,EAAE,CAAA+b,iBAAA,CA8yBgJxL,iBAAiB;AAAA,CAA4C;AACnT2N,wBAAwB,CAACvK,IAAI,kBA/yBuE3T,EAAE,CAAA4T,iBAAA;EAAAlJ,IAAA,EA+yBGwT,wBAAwB;EAAArK,SAAA;EAAA4I,YAAA,WAAA8C,sCAAA9d,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA/yB7BzB,EAAE,CAAA8B,UAAA,wBAAA0d,uDAAA;QAAA,OA+yBG9d,GAAA,CAAA8V,YAAA,CAAa,CAAC;MAAA,CAAS,CAAC,wBAAAiI,uDAAA;QAAA,OAAxB/d,GAAA,CAAA+V,YAAA,CAAa,CAAC;MAAA,CAAS,CAAC;IAAA;EAAA;EAAA3D,MAAA;IAAA7L,MAAA;IAAAyB,SAAA;IAAA+B,aAAA;IAAAwL,MAAA;IAAAC,cAAA;IAAAG,KAAA;IAAAlQ,SAAA;IAAA+D,IAAA;IAAA7H,aAAA;IAAAC,aAAA;IAAAsV,QAAA;IAAAK,gBAAA;IAAAJ,oBAAA;IAAAM,YAAA;IAAAvK,WAAA;IAAAhF,OAAA;IAAAZ,YAAA;IAAAa,eAAA;IAAAqT,OAAA;EAAA;EAAAjJ,OAAA;IAAAkK,UAAA;IAAA1H,YAAA;IAAA2H,SAAA;EAAA;EAAAjB,QAAA,GA/yB7Bnd,EAAE,CAAAod,oBAAA;EAAAlJ,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAqL,kCAAAje,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFzB,EAAE,CAAA6B,cAAA,SA+yB05B,CAAC;MA/yB75B7B,EAAE,CAAAwJ,UAAA,IAAAe,gDAAA,yBA+yBg+B,CAAC,IAAAK,uDAAA,gCAA0vE,CAAC,IAAAG,sDAAA,+BAAoV,CAAC;MA/yBnjH/K,EAAE,CAAAwC,YAAA,CA+yB6kH,CAAC;IAAA;IAAA,IAAAf,EAAA;MA/yBhlHzB,EAAE,CAAA2f,sBAAA,qDAAAje,GAAA,CAAAyF,SAAA,8BAAAzF,GAAA,CAAAwJ,IAAA,IA+yBy5B,CAAC;MA/yB55BlL,EAAE,CAAAoE,SAAA,CA+yB+8B,CAAC;MA/yBl9BpE,EAAE,CAAAyC,UAAA,YAAAf,GAAA,CAAA8c,SAAA,EA+yB+8B,CAAC;MA/yBl9Bxe,EAAE,CAAAoE,SAAA,CA+yB0mG,CAAC;MA/yB7mGpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAwb,OA+yB0mG,CAAC;MA/yB7mGld,EAAE,CAAAoE,SAAA,CA+yBi0G,CAAC;MA/yBp0GpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAuV,MA+yBi0G,CAAC;IAAA;EAAA;EAAArB,YAAA,GAAowCf,yBAAyB,EAAgHQ,0BAA0B,EAAqGjC,yBAAyB,EAA2KrS,EAAE,CAAC8U,OAAO,EAAgG9U,EAAE,CAAC6c,IAAI,EAA0E7c,EAAE,CAAC+U,OAAO;EAAArB,MAAA;EAAAmL,IAAA;IAAAzY,SAAA,EAAsE,CAC33KlG,OAAO,CAAC,WAAW,EAAE;IACjB;IACAC,KAAK,CAAC,YAAY,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAC9BD,KAAK,CAAC,WAAW,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7BD,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EACxBD,KAAK,CAAC,YAAY,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAC9BD,KAAK,CAAC,aAAa,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/BD,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EACxBC,UAAU,CAAC,oBAAoB,EAAE,CAC7BC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE;IAAoB,CAAC,CAAC,CAAC,CAC1E,CAAC,EACFze,UAAU,CAAC,oBAAoB,EAAE,CAC7BD,KAAK,CAAC;MAAE0e,SAAS,EAAE;IAAmB,CAAC,CAAC,EACxCxe,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE;IAAgB,CAAC,CAAC,CAAC,CACtE,CAAC,EACFze,UAAU,CAAC,mBAAmB,EAAE,CAC5BC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE;IAAmB,CAAC,CAAC,CAAC,CACzE,CAAC,EACFze,UAAU,CAAC,mBAAmB,EAAE,CAC5BD,KAAK,CAAC;MAAE0e,SAAS,EAAE;IAAoB,CAAC,CAAC,EACzCxe,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE;IAAgB,CAAC,CAAC,CAAC,CACtE,CAAC,EACFze,UAAU,CAAC,cAAc,EAAE,CACvBC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE2e,OAAO,EAAE;IAAI,CAAC,CAAC,CAAC,CACxD,CAAC,EACF1e,UAAU,CAAC,cAAc,EAAE,CACvBD,KAAK,CAAC;MAAE2e,OAAO,EAAE;IAAI,CAAC,CAAC,EACvBze,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE2e,OAAO,EAAE;IAAI,CAAC,CAAC,CAAC,CACxD,CAAC,EACF1e,UAAU,CAAC,oBAAoB,EAAE,CAC7BC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE,4BAA4B;MAAEC,OAAO,EAAE;IAAI,CAAC,CAAC,CAAC,CACjG,CAAC,EACF1e,UAAU,CAAC,oBAAoB,EAAE,CAC7BD,KAAK,CAAC;MAAE0e,SAAS,EAAE,4BAA4B;MAAEC,OAAO,EAAE;IAAI,CAAC,CAAC,EAChEze,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE,0BAA0B;MAAEC,OAAO,EAAE;IAAI,CAAC,CAAC,CAAC,CAC/F,CAAC,EACF1e,UAAU,CAAC,qBAAqB,EAAE,CAC9BC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE,2BAA2B;MAAEC,OAAO,EAAE;IAAI,CAAC,CAAC,CAAC,CAChG,CAAC,EACF1e,UAAU,CAAC,qBAAqB,EAAE,CAC9BD,KAAK,CAAC;MAAE0e,SAAS,EAAE,2BAA2B;MAAEC,OAAO,EAAE;IAAI,CAAC,CAAC,EAC/Dze,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE,0BAA0B;MAAEC,OAAO,EAAE;IAAI,CAAC,CAAC,CAAC,CAC/F,CAAC,EACF1e,UAAU,CAAC,cAAc,EAAE,CACvBC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE,gBAAgB;MAAEC,OAAO,EAAE;IAAI,CAAC,CAAC,CAAC,CACrF,CAAC,EACF1e,UAAU,CAAC,cAAc,EAAE,CACvBD,KAAK,CAAC;MAAE0e,SAAS,EAAE,gBAAgB;MAAEC,OAAO,EAAE;IAAI,CAAC,CAAC,EACpDze,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAAE0e,SAAS,EAAE,aAAa;MAAEC,OAAO,EAAE;IAAI,CAAC,CAAC,CAAC,CAClF,CAAC,CACL,CAAC;EACL;EAAApL,eAAA;AAAA,EAAuD;AAC5D;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KAp2BoGjT,EAAE,CAAAkT,iBAAA,CAo2BXgL,wBAAwB,EAAc,CAAC;IACtHxT,IAAI,EAAEvK,SAAS;IACfgT,IAAI,EAAE,CAAC;MAAEwB,QAAQ,EAAE,mBAAmB;MAAED,eAAe,EAAEtU,uBAAuB,CAACwU,MAAM;MAAEmL,UAAU,EAAE,CACzF9e,OAAO,CAAC,WAAW,EAAE;MACjB;MACAC,KAAK,CAAC,YAAY,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAC9BD,KAAK,CAAC,WAAW,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7BD,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EACxBD,KAAK,CAAC,YAAY,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAC9BD,KAAK,CAAC,aAAa,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/BD,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EACxBC,UAAU,CAAC,oBAAoB,EAAE,CAC7BC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE;MAAoB,CAAC,CAAC,CAAC,CAC1E,CAAC,EACFze,UAAU,CAAC,oBAAoB,EAAE,CAC7BD,KAAK,CAAC;QAAE0e,SAAS,EAAE;MAAmB,CAAC,CAAC,EACxCxe,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE;MAAgB,CAAC,CAAC,CAAC,CACtE,CAAC,EACFze,UAAU,CAAC,mBAAmB,EAAE,CAC5BC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE;MAAmB,CAAC,CAAC,CAAC,CACzE,CAAC,EACFze,UAAU,CAAC,mBAAmB,EAAE,CAC5BD,KAAK,CAAC;QAAE0e,SAAS,EAAE;MAAoB,CAAC,CAAC,EACzCxe,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE;MAAgB,CAAC,CAAC,CAAC,CACtE,CAAC,EACFze,UAAU,CAAC,cAAc,EAAE,CACvBC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE2e,OAAO,EAAE;MAAI,CAAC,CAAC,CAAC,CACxD,CAAC,EACF1e,UAAU,CAAC,cAAc,EAAE,CACvBD,KAAK,CAAC;QAAE2e,OAAO,EAAE;MAAI,CAAC,CAAC,EACvBze,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE2e,OAAO,EAAE;MAAI,CAAC,CAAC,CAAC,CACxD,CAAC,EACF1e,UAAU,CAAC,oBAAoB,EAAE,CAC7BC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE,4BAA4B;QAAEC,OAAO,EAAE;MAAI,CAAC,CAAC,CAAC,CACjG,CAAC,EACF1e,UAAU,CAAC,oBAAoB,EAAE,CAC7BD,KAAK,CAAC;QAAE0e,SAAS,EAAE,4BAA4B;QAAEC,OAAO,EAAE;MAAI,CAAC,CAAC,EAChEze,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE,0BAA0B;QAAEC,OAAO,EAAE;MAAI,CAAC,CAAC,CAAC,CAC/F,CAAC,EACF1e,UAAU,CAAC,qBAAqB,EAAE,CAC9BC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE,2BAA2B;QAAEC,OAAO,EAAE;MAAI,CAAC,CAAC,CAAC,CAChG,CAAC,EACF1e,UAAU,CAAC,qBAAqB,EAAE,CAC9BD,KAAK,CAAC;QAAE0e,SAAS,EAAE,2BAA2B;QAAEC,OAAO,EAAE;MAAI,CAAC,CAAC,EAC/Dze,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE,0BAA0B;QAAEC,OAAO,EAAE;MAAI,CAAC,CAAC,CAAC,CAC/F,CAAC,EACF1e,UAAU,CAAC,cAAc,EAAE,CACvBC,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAI,CAAC,CAAC,CAAC,CACrF,CAAC,EACF1e,UAAU,CAAC,cAAc,EAAE,CACvBD,KAAK,CAAC;QAAE0e,SAAS,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAI,CAAC,CAAC,EACpDze,OAAO,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAAE0e,SAAS,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAI,CAAC,CAAC,CAAC,CAClF,CAAC,CACL,CAAC,CACL;MAAEzL,QAAQ,EAAE,oyFAAoyF;MAAEI,MAAM,EAAE,CAAC,k9BAAk9B;IAAE,CAAC;EAC7xH,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/J,IAAI,EAAE9J,EAAE,CAACob;IAAa,CAAC,EAAE;MAAEtR,IAAI,EAAE1K,EAAE,CAACkc;IAAkB,CAAC,EAAE;MAAExR,IAAI,EAAE1K,EAAE,CAACic;IAAW,CAAC,EAAE;MAAEvR,IAAI,EAAE6F;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEtI,MAAM,EAAE,CAAC;MAClLyC,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEqJ,SAAS,EAAE,CAAC;MACZgB,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEoL,aAAa,EAAE,CAAC;MAChBf,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE4W,MAAM,EAAE,CAAC;MACTvM,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE6W,cAAc,EAAE,CAAC;MACjBxM,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEgX,KAAK,EAAE,CAAC;MACR3M,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE8G,SAAS,EAAE,CAAC;MACZuD,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE6K,IAAI,EAAE,CAAC;MACPR,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEgD,aAAa,EAAE,CAAC;MAChBqH,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEiD,aAAa,EAAE,CAAC;MAChBoH,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEuY,QAAQ,EAAE,CAAC;MACXlO,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE4Y,gBAAgB,EAAE,CAAC;MACnBvO,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEwY,oBAAoB,EAAE,CAAC;MACvBnO,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE8Y,YAAY,EAAE,CAAC;MACfzO,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEuO,WAAW,EAAE,CAAC;MACdlE,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEuJ,OAAO,EAAE,CAAC;MACVc,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE2I,YAAY,EAAE,CAAC;MACf0B,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEwJ,eAAe,EAAE,CAAC;MAClBa,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE6c,OAAO,EAAE,CAAC;MACVxS,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE8d,UAAU,EAAE,CAAC;MACbzT,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEmW,YAAY,EAAE,CAAC;MACf/L,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAE8d,SAAS,EAAE,CAAC;MACZ1T,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEkX,YAAY,EAAE,CAAC;MACf9M,IAAI,EAAElK,YAAY;MAClB2S,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEsE,YAAY,EAAE,CAAC;MACf/M,IAAI,EAAElK,YAAY;MAClB2S,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6M,eAAe,CAAC;AAEtBA,eAAe,CAACC,MAAM,GAAG,CAAC;AAC1BD,eAAe,CAACE,GAAG,GAAG,CAAC;AACvBF,eAAe,CAACG,IAAI,GAAG,CAAC;AAExB,MAAMC,6BAA6B,CAAC;EAChC5P,WAAWA,CAACwF,YAAY,EAAEC,UAAU,EAAEC,aAAa,EAAE;IACjD,IAAI,CAACF,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACmK,YAAY,GAAG,CAAC;IACrB,IAAI,CAAC5J,YAAY,GAAG,IAAIvW,YAAY,CAAC,CAAC;IACtC,IAAI,CAACiC,KAAK,GAAG,CAAC;EAClB;EACAgV,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,eAAe,CAAC,EAAE;MAC1B,IAAI,CAACkJ,aAAa,CAAC,CAAC;IACxB;IACA,IAAIlJ,OAAO,CAAC,OAAO,CAAC,EAAE;MAClB,IAAI,CAAClB,aAAa,CAACtF,WAAW,CAAC,IAAI,CAACyG,KAAK,EAAE,IAAI,CAACpB,UAAU,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC7I,SAAS,CAAC,CAAC,EAAE,MAAM,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAC;IAC5H;IACA,IAAI,IAAI,CAACjF,MAAM,EAAE;MACb,IAAI,CAAC+D,mBAAmB,GAAG,IAAI,CAAC/D,MAAM,CAACC,MAAM,GAAI,IAAI,CAAC4E,IAAI,GAAG,IAAI,CAACC,OAAQ;IAC9E;EACJ;EACAyK,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC+I,UAAU,GAAG,IAAI;EAC1B;EACA9I,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC8I,UAAU,GAAG,KAAK;EAC3B;EACAhC,KAAKA,CAACpc,KAAK,EAAE;IACT,IAAI,CAACsJ,aAAa,GAAGtJ,KAAK;IAC1B,IAAI,CAACqe,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACre,KAAK,GAAG,CAAC;IACd,IAAI,CAACme,aAAa,CAAC,CAAC;EACxB;EACA9B,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACvW,MAAM,EAAE;MACd,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAAC4E,cAAc,EAAE;MACrB,OAAO,IAAI,CAAC5E,MAAM,CAACwY,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC3T,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC;IACzD,CAAC,MACI,IAAI,IAAI,CAAC6B,WAAW,IAAI,IAAI,CAAC8R,KAAK,KAAKV,eAAe,CAACE,GAAG,EAAE;MAC7D,IAAIS,SAAS,GAAG,CAAC;MACjB,IAAI,IAAI,CAACD,KAAK,KAAKV,eAAe,CAACC,MAAM,EAAE;QACvCU,SAAS,GAAG,CAAC,IAAI,CAACxe,KAAK,GAAG,IAAI,CAAC4K,OAAO,GAAG,IAAI,CAAC6T,QAAQ,IAAI,IAAI,CAAC9T,IAAI;MACvE,CAAC,MACI,IAAI,IAAI,CAAC4T,KAAK,KAAKV,eAAe,CAACG,IAAI,EAAE;QAC1CQ,SAAS,GAAG,IAAI,CAACxe,KAAK,GAAK,IAAI,CAAC4K,OAAO,GAAG,IAAI,CAACD,IAAI,GAAI,CAAE;MAC7D;MACA,IAAI6T,SAAS,IAAI,IAAI,CAACN,YAAY,EAAE;QAChCM,SAAS,GAAG,IAAI,CAACN,YAAY;MACjC,CAAC,MACI;QACD,IAAI,CAACA,YAAY,GAAGM,SAAS;MACjC;MACA,OAAO,IAAI,CAAC1Y,MAAM,CAACwY,KAAK,CAAC,CAAC,EAAEE,SAAS,CAAC;IAC1C,CAAC,MACI;MACD,OAAO,IAAI,CAAC1Y,MAAM;IACtB;EACJ;EACAkB,WAAWA,CAAC4L,KAAK,EAAE5S,KAAK,EAAE;IACtB,IAAI,CAAC,IAAI,CAACoK,OAAO,CAACpK,KAAK,CAAC,IAAI,CAAC,IAAI,CAACoN,WAAW,EAAE;MAC3C,IAAI,CAAC9D,aAAa,GAAGtJ,KAAK;MAC1B,IAAI,CAACsU,YAAY,CAACjD,IAAI,CAACrR,KAAK,CAAC;IACjC;IACA4S,KAAK,CAAC1O,eAAe,CAAC,CAAC;IACvB0O,KAAK,CAACC,cAAc,CAAC,CAAC;EAC1B;EACAzI,OAAOA,CAACpK,KAAK,EAAE;IACX,OAAO,CAAC,EAAE,IAAI,CAACqK,KAAK,IAAI,IAAI,CAACA,KAAK,CAACtE,MAAM,IAAI,IAAI,CAACsE,KAAK,CAACrK,KAAK,CAAC,CAAC;EACnE;EACAiL,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACE,YAAY,CAAC,CAAC,EAAE;MACrB,IAAI,CAACnL,KAAK,IAAI,IAAI,CAACye,QAAQ;MAC3B,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC/T,OAAO;MAClD,IAAI,IAAI,CAAC5K,KAAK,GAAG0e,QAAQ,EAAE;QACvB,IAAI,CAAC1e,KAAK,GAAG0e,QAAQ;MACzB;MACA,IAAI,CAACE,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACA7T,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE;MACpB,IAAI,CAAClL,KAAK,IAAI,IAAI,CAACye,QAAQ;MAC3B,IAAI,IAAI,CAACze,KAAK,GAAG,CAAC,EAAE;QAChB,IAAI,CAACA,KAAK,GAAG,CAAC;MAClB;MACA,IAAI,CAAC4e,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACAzT,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACnL,KAAK,GAAG,IAAI,CAAC4K,OAAO,GAAG,IAAI,CAAC+T,WAAW,CAAC,CAAC;EACzD;EACAzT,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAClL,KAAK,KAAK,CAAC;EAC3B;EACAkK,gBAAgBA,CAAClK,KAAK,EAAE;IACpB,IAAI6e,eAAe;IACnB,IAAI,IAAI,CAACN,KAAK,KAAKV,eAAe,CAACC,MAAM,EAAE;MACvCe,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC/e,KAAK,GAAG,IAAI,CAAC2K,IAAI,CAAC;IACnD,CAAC,MACI,IAAI,IAAI,CAAC4T,KAAK,KAAKV,eAAe,CAACG,IAAI,EAAE;MAC1Ca,eAAe,GAAI7e,KAAK,GAAG,IAAI,CAAC4K,OAAO,GAAKkU,IAAI,CAACC,KAAK,CAAC/e,KAAK,IAAI,IAAI,CAAC2K,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAACA,OAAQ;IAC9G,CAAC,MACI,IAAI,IAAI,CAAC2T,KAAK,KAAKV,eAAe,CAACE,GAAG,IAAI,IAAI,CAACrT,cAAc,EAAE;MAChEmU,eAAe,GAAG7e,KAAK,GAAG,IAAI,CAAC4K,OAAO;IAC1C,CAAC,MACI;MACDiU,eAAe,GAAG7e,KAAK,GAAG8e,IAAI,CAACE,IAAI,CAAC,IAAI,CAAClZ,MAAM,CAACC,MAAM,GAAG,IAAI,CAAC4E,IAAI,CAAC;IACvE;IACA,OAAO,IAAI,CAACsU,oBAAoB,CAACJ,eAAe,EAAE,IAAI,CAACjU,OAAO,CAAC;EACnE;EACAT,eAAeA,CAACnK,KAAK,EAAE;IACnB,IAAI6e,eAAe;IACnB,IAAI,IAAI,CAACN,KAAK,KAAKV,eAAe,CAACC,MAAM,EAAE;MACvCe,eAAe,GAAG7e,KAAK,GAAG,IAAI,CAAC2K,IAAI;IACvC,CAAC,MACI,IAAI,IAAI,CAAC4T,KAAK,KAAKV,eAAe,CAACG,IAAI,EAAE;MAC1Ca,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC/e,KAAK,GAAG,IAAI,CAAC4K,OAAO,CAAC,GAAIkU,IAAI,CAACC,KAAK,CAAC/e,KAAK,IAAI,IAAI,CAAC2K,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAACD,IAAK;IACrH,CAAC,MACI,IAAI,IAAI,CAAC4T,KAAK,KAAKV,eAAe,CAACE,GAAG,IAAI,IAAI,CAACrT,cAAc,EAAE;MAChEmU,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC/e,KAAK,GAAG,IAAI,CAAC4K,OAAO,CAAC;IACtD,CAAC,MACI;MACDiU,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC/e,KAAK,GAAG8e,IAAI,CAACE,IAAI,CAAC,IAAI,CAAClZ,MAAM,CAACC,MAAM,GAAG,IAAI,CAAC4E,IAAI,CAAC,CAAC;IACnF;IACA,OAAO,IAAI,CAACsU,oBAAoB,CAACJ,eAAe,EAAE,IAAI,CAAClU,IAAI,CAAC;EAChE;EACAX,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACkV,qBAAqB,CAAC,IAAI,CAACtU,OAAO,CAAC;EACnD;EACAX,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACiV,qBAAqB,CAAC,IAAI,CAACvU,IAAI,CAAC;EAChD;EACAiU,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACO,cAAc,GAAG,EAAG,GAAG,GAAG,IAAI,CAACvU,OAAO,GAAI,IAAI,CAAC5K,KAAK,CAAC,GAAG,GAAG;IAChE,IAAI,CAACof,oBAAoB,GAAG,EAAE,CAAC,IAAI,CAACC,MAAM,GAAK,CAAC,IAAI,CAACzU,OAAO,GAAG,CAAC,IAC1D,IAAI,CAACyU,MAAM,GAAI,IAAI,CAACzU,OAAQ,IAAI,IAAI,CAAC5K,KAAK,CAAC,GAAG,IAAI;EAC5D;EACAqe,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACc,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,oBAAoB,GAAG,KAAK;EACrC;EACAE,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC5U,cAAc,EAAE;MACrB,OAAO,KAAK;IAChB,CAAC,MACI;MACD,OAAO,IAAI,CAACoK,MAAM,IAAI,IAAI,CAAChP,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,GAAG,IAAI,CAACwZ,eAAe,CAAC,CAAC,KACxE,CAAC,IAAI,CAACxK,cAAc,IAAI,IAAI,CAACqJ,UAAU,CAAC;IACpD;EACJ;EACAD,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACrY,MAAM,EAAE;MACb,IAAI0Z,QAAQ;MACZ,IAAI,IAAI,CAACjB,KAAK,KAAKV,eAAe,CAACC,MAAM,EAAE;QACvC0B,QAAQ,GAAGV,IAAI,CAACC,KAAK,CAAC,IAAI,CAACzV,aAAa,GAAG,IAAI,CAACqB,IAAI,CAAC;MACzD,CAAC,MACI;QACD6U,QAAQ,GAAG,IAAI,CAAClW,aAAa,GAAGwV,IAAI,CAACE,IAAI,CAAC,IAAI,CAAClZ,MAAM,CAACC,MAAM,GAAG,IAAI,CAAC4E,IAAI,CAAC;MAC7E;MACA,IAAI,IAAI,CAACD,cAAc,EAAE;QACrB8U,QAAQ,GAAG,CAAC;MAChB;MACA,IAAIA,QAAQ,GAAG,IAAI,CAACxf,KAAK,IAAIwf,QAAQ,IAAI,IAAI,CAACxf,KAAK,GAAG,IAAI,CAAC4K,OAAO,EAAE;QAChE,MAAM8T,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC/T,OAAO;QAClD,IAAI,CAAC5K,KAAK,GAAGwf,QAAQ,GAAGd,QAAQ,GAAGA,QAAQ,GAAGc,QAAQ;QACtD,IAAI,CAACZ,qBAAqB,CAAC,CAAC;MAChC;IACJ;EACJ;EACAtX,UAAUA,CAACsG,KAAK,EAAE;IACd,OAAO,IAAI,CAACiG,YAAY,CAACiE,wBAAwB,CAAC,IAAI,CAAC/D,aAAa,CAACnE,gBAAgB,CAAChC,KAAK,CAACsP,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5G;EACAzS,WAAWA,CAACoF,UAAU,EAAE;IACpB,OAAO,IAAI,CAACkE,aAAa,CAACtJ,WAAW,CAACoF,UAAU,CAACqN,QAAQ,CAAC,CAAC,CAAC;EAChE;EACA+B,oBAAoBA,CAACjf,KAAK,EAAEsT,KAAK,EAAE;IAC/B,OAAO,IAAI,CAACmM,YAAY,CAAC,OAAO,GAAK,GAAG,GAAGnM,KAAK,GAAItT,KAAM,GAAG,MAAM,GAC5D,CAAC,IAAI,CAACqf,MAAM,GAAK,CAAC/L,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC+L,MAAM,GAAI/L,KAAM,IAAItT,KAAM,GAAG,KAAK,CAAC;EAClF;EACAkf,qBAAqBA,CAAC5L,KAAK,EAAE;IACzB,IAAI,IAAI,CAAC+L,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO,IAAI,CAACI,YAAY,CAAC,OAAO,GAAI,GAAG,GAAGnM,KAAM,GAAG,MAAM,GACjD,CAACA,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC+L,MAAM,GAAI/L,KAAM,GAAG,KAAK,CAAC;IACxD,CAAC,MACI;MACD,OAAO,IAAI,CAACmM,YAAY,CAAC,OAAO,GAAI,GAAG,GAAGnM,KAAM,GAAG,UAAU,CAAC;IAClE;EACJ;EACAqL,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACJ,KAAK,KAAKV,eAAe,CAACG,IAAI,EAAE;MACrC,IAAIU,QAAQ,GAAII,IAAI,CAACC,KAAK,CAAC,IAAI,CAACjZ,MAAM,CAACC,MAAM,GAAG,IAAI,CAACwZ,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC3U,OAAQ;MACvF,IAAI,IAAI,CAAC9E,MAAM,CAACC,MAAM,GAAG,IAAI,CAACwZ,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC3U,OAAO,EAAE;QAC5D8T,QAAQ,IAAI,IAAI,CAAC9T,OAAO;MAC5B,CAAC,MACI;QACD8T,QAAQ,IAAI,IAAI,CAAC5Y,MAAM,CAACC,MAAM,GAAG,IAAI,CAACwZ,eAAe,CAAC,CAAC;MAC3D;MACA,OAAOb,QAAQ;IACnB,CAAC,MACI;MACD,OAAOI,IAAI,CAACE,IAAI,CAAC,IAAI,CAAClZ,MAAM,CAACC,MAAM,GAAG,IAAI,CAAC4E,IAAI,CAAC;IACpD;EACJ;EACA4U,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC3U,OAAO,GAAG,IAAI,CAACD,IAAI;EACnC;EACA8U,YAAYA,CAACC,KAAK,EAAE;IAChB,OAAO,IAAI,CAAC7L,YAAY,CAACiE,wBAAwB,CAAC4H,KAAK,CAAC;EAC5D;AACJ;AACAzB,6BAA6B,CAAC7N,IAAI,YAAAuP,sCAAArP,iBAAA;EAAA,YAAAA,iBAAA,IAAwF2N,6BAA6B,EAxqCnDpgB,EAAE,CAAA+b,iBAAA,CAwqCmEnb,EAAE,CAACob,YAAY,GAxqCpFhc,EAAE,CAAA+b,iBAAA,CAwqC+F/b,EAAE,CAACic,UAAU,GAxqC9Gjc,EAAE,CAAA+b,iBAAA,CAwqCyHxL,iBAAiB;AAAA,CAA4C;AAC5R6P,6BAA6B,CAACzM,IAAI,kBAzqCkE3T,EAAE,CAAA4T,iBAAA;EAAAlJ,IAAA,EAyqCQ0V,6BAA6B;EAAAvM,SAAA;EAAA4I,YAAA,WAAAsF,2CAAAtgB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAzqCvCzB,EAAE,CAAA8B,UAAA,wBAAAkgB,4DAAA;QAAA,OAyqCQtgB,GAAA,CAAA8V,YAAA,CAAa,CAAC;MAAA,CAAc,CAAC,wBAAAyK,4DAAA;QAAA,OAA7BvgB,GAAA,CAAA+V,YAAA,CAAa,CAAC;MAAA,CAAc,CAAC;IAAA;EAAA;EAAA3D,MAAA;IAAA7L,MAAA;IAAAsH,WAAA;IAAA/C,KAAA;IAAAG,MAAA;IAAAF,UAAA;IAAAM,OAAA;IAAAD,IAAA;IAAAmK,MAAA;IAAAC,cAAA;IAAAsK,MAAA;IAAA/V,aAAA;IAAA/B,SAAA;IAAA2N,KAAA;IAAAnM,IAAA;IAAA7H,aAAA;IAAAC,aAAA;IAAAsd,QAAA;IAAAF,KAAA;IAAA7T,cAAA;IAAA+B,WAAA;IAAAhF,OAAA;EAAA;EAAAqK,OAAA;IAAAwC,YAAA;EAAA;EAAA0G,QAAA,GAzqCvCnd,EAAE,CAAAod,oBAAA;EAAAlJ,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA6N,uCAAAzgB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFzB,EAAE,CAAA6B,cAAA,SAyqC0zB,CAAC,YAAwJ,CAAC;MAzqCt9B7B,EAAE,CAAAwJ,UAAA,IAAAyC,0CAAA,eAyqCg9C,CAAC;MAzqCn9CjM,EAAE,CAAAwC,YAAA,CAyqCuwF,CAAC,CAAO,CAAC;MAzqClxFxC,EAAE,CAAAwJ,UAAA,IAAAwD,2DAAA,+BAyqCyiG,CAAC;IAAA;IAAA,IAAAvL,EAAA;MAzqC5iGzB,EAAE,CAAAqE,sBAAA,+DAAA3C,GAAA,CAAAwJ,IAAA,IAyqCyzB,CAAC;MAzqC5zBlL,EAAE,CAAAoE,SAAA,CAyqC85B,CAAC;MAzqCj6BpE,EAAE,CAAA6G,WAAA,8BAAAnF,GAAA,CAAA4f,cAAA,MAyqC85B,CAAC,gBAAA5f,GAAA,CAAA6f,oBAAmD,CAAC;MAzqCr9BvhB,EAAE,CAAAoE,SAAA,CAyqCsmC,CAAC;MAzqCzmCpE,EAAE,CAAAyC,UAAA,YAAAf,GAAA,CAAA8c,SAAA,EAyqCsmC,CAAC;MAzqCzmCxe,EAAE,CAAAoE,SAAA,CAyqC4zF,CAAC;MAzqC/zFpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAA+f,aAAA,EAyqC4zF,CAAC;IAAA;EAAA;EAAA7L,YAAA,GAAuwDf,yBAAyB,EAAgHzB,yBAAyB,EAA2KrS,EAAE,CAAC8U,OAAO,EAAgG9U,EAAE,CAAC+U,OAAO,EAAiE/U,EAAE,CAAC6c,IAAI;EAAAnJ,MAAA;EAAAC,eAAA;AAAA,EAAwH;AAC3yK;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KA1qCoGjT,EAAE,CAAAkT,iBAAA,CA0qCXkN,6BAA6B,EAAc,CAAC;IAC3H1V,IAAI,EAAEvK,SAAS;IACfgT,IAAI,EAAE,CAAC;MAAEwB,QAAQ,EAAE,wBAAwB;MAAED,eAAe,EAAEtU,uBAAuB,CAACwU,MAAM;MAAEP,QAAQ,EAAE,01EAA01E;MAAEI,MAAM,EAAE,CAAC,+9CAA+9C;IAAE,CAAC;EACn7H,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/J,IAAI,EAAE9J,EAAE,CAACob;IAAa,CAAC,EAAE;MAAEtR,IAAI,EAAE1K,EAAE,CAACic;IAAW,CAAC,EAAE;MAAEvR,IAAI,EAAE6F;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEtI,MAAM,EAAE,CAAC;MAClJyC,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEkP,WAAW,EAAE,CAAC;MACd7E,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEmM,KAAK,EAAE,CAAC;MACR9B,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEsM,MAAM,EAAE,CAAC;MACTjC,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEoM,UAAU,EAAE,CAAC;MACb/B,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE0M,OAAO,EAAE,CAAC;MACVrC,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEyM,IAAI,EAAE,CAAC;MACPpC,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE4W,MAAM,EAAE,CAAC;MACTvM,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE6W,cAAc,EAAE,CAAC;MACjBxM,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEmhB,MAAM,EAAE,CAAC;MACT9W,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEoL,aAAa,EAAE,CAAC;MAChBf,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEqJ,SAAS,EAAE,CAAC;MACZgB,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEgX,KAAK,EAAE,CAAC;MACR3M,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE6K,IAAI,EAAE,CAAC;MACPR,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEgD,aAAa,EAAE,CAAC;MAChBqH,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEiD,aAAa,EAAE,CAAC;MAChBoH,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEugB,QAAQ,EAAE,CAAC;MACXlW,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEqgB,KAAK,EAAE,CAAC;MACRhW,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEwM,cAAc,EAAE,CAAC;MACjBnC,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEuO,WAAW,EAAE,CAAC;MACdlE,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEuJ,OAAO,EAAE,CAAC;MACVc,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEoW,YAAY,EAAE,CAAC;MACf/L,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEkX,YAAY,EAAE,CAAC;MACf9M,IAAI,EAAElK,YAAY;MAClB2S,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEsE,YAAY,EAAE,CAAC;MACf/M,IAAI,EAAElK,YAAY;MAClB2S,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgP,gBAAgB,CAAC;EACnB3R,WAAWA,CAAC7G,MAAM,EAAE;IAChB,IAAI,CAAC9F,IAAI,GAAG8F,MAAM,CAAC9F,IAAI;IACvB,IAAI,CAACC,QAAQ,GAAG6F,MAAM,CAAC7F,QAAQ,GAAG6F,MAAM,CAAC7F,QAAQ,GAAG,KAAK;IACzD,IAAI,CAACC,SAAS,GAAG4F,MAAM,CAAC5F,SAAS,GAAG4F,MAAM,CAAC5F,SAAS,GAAG,EAAE;IACzD,IAAI,CAACH,OAAO,GAAG+F,MAAM,CAAC/F,OAAO;EACjC;AACJ;AAEA,MAAMwe,gBAAgB,CAAC;AAEvBA,gBAAgB,CAACC,aAAa,GAAG,gBAAgB;AACjDD,gBAAgB,CAACE,gBAAgB,GAAG,mBAAmB;AAEvD,MAAMC,mBAAmB,CAAC;AAE1BA,mBAAmB,CAACC,KAAK,GAAG,OAAO;AACnCD,mBAAmB,CAACE,OAAO,GAAG,SAAS;AAEvC,MAAMC,iBAAiB,CAAC;EACpBlS,WAAWA,CAACmS,GAAG,EAAE;IACb,MAAMC,eAAe,GAAGD,GAAG,CAACE,UAAU,KAAKC,SAAS,GAAG,KAAK,GAAG,IAAI;IACnE,SAASC,GAAGA,CAACC,MAAM,EAAEC,YAAY,EAAE;MAC/B,OAAON,GAAG,KAAKK,MAAM,KAAKF,SAAS,IAAIF,eAAe,CAAC,GAAGI,MAAM,GAAGC,YAAY;IACnF;IACA,IAAI,CAACJ,UAAU,GAAGE,GAAG,CAACJ,GAAG,CAACE,UAAU,EAAEC,SAAS,CAAC;IAChD,IAAI,CAACI,KAAK,GAAGH,GAAG,CAACJ,GAAG,CAACO,KAAK,EAAE,OAAO,CAAC;IACpC,IAAI,CAACC,MAAM,GAAGJ,GAAG,CAACJ,GAAG,CAACQ,MAAM,EAAE,OAAO,CAAC;IACtC,IAAI,CAACC,SAAS,GAAGL,GAAG,CAACJ,GAAG,CAACS,SAAS,EAAE,KAAK,CAAC;IAC1C,IAAI,CAACC,MAAM,GAAGN,GAAG,CAACJ,GAAG,CAACU,MAAM,EAAEjB,gBAAgB,CAACE,gBAAgB,CAAC;IAChE,IAAI,CAACgB,UAAU,GAAGP,GAAG,CAACJ,GAAG,CAACW,UAAU,EAAE,CAAC,CAAC;IACxC,IAAI,CAAC7W,UAAU,GAAGsW,GAAG,CAACJ,GAAG,CAAClW,UAAU,EAAE,QAAQ,CAAC;IAC/C,IAAI,CAACmC,WAAW,GAAGmU,GAAG,CAACJ,GAAG,CAAC/T,WAAW,EAAE,IAAI,CAAC;IAC7C,IAAI,CAACmB,KAAK,GAAGgT,GAAG,CAACJ,GAAG,CAAC5S,KAAK,EAAE,IAAI,CAAC;IACjC,IAAI,CAACwT,YAAY,GAAGR,GAAG,CAACJ,GAAG,CAACY,YAAY,EAAE,EAAE,CAAC;IAC7C,IAAI,CAACpV,WAAW,GAAG4U,GAAG,CAACJ,GAAG,CAACxU,WAAW,EAAE,IAAI,CAAC;IAC7C,IAAI,CAACC,mBAAmB,GAAG2U,GAAG,CAACJ,GAAG,CAACvU,mBAAmB,EAAE,KAAK,CAAC;IAC9D,IAAI,CAACC,UAAU,GAAG0U,GAAG,CAACJ,GAAG,CAACtU,UAAU,EAAE,KAAK,CAAC;IAC5C,IAAI,CAACC,cAAc,GAAGyU,GAAG,CAACJ,GAAG,CAACrU,cAAc,EAAEuP,mBAAmB,CAACC,IAAI,CAAC;IACvE,IAAI,CAACvP,SAAS,GAAGwU,GAAG,CAACJ,GAAG,CAACpU,SAAS,EAAEgU,mBAAmB,CAACC,KAAK,CAAC;IAC9D,IAAI,CAAChU,aAAa,GAAGuU,GAAG,CAACJ,GAAG,CAACnU,aAAa,EAAE,KAAK,CAAC;IAClD,IAAI,CAACC,qBAAqB,GAAGsU,GAAG,CAACJ,GAAG,CAAClU,qBAAqB,EAAE,IAAI,CAAC;IACjE,IAAI,CAACC,yBAAyB,GAAGqU,GAAG,CAACJ,GAAG,CAACjU,yBAAyB,EAAE,KAAK,CAAC;IAC1E,IAAI,CAACC,iBAAiB,GAAGoU,GAAG,CAACJ,GAAG,CAAChU,iBAAiB,EAAE,KAAK,CAAC;IAC1D,IAAIgU,GAAG,IAAIA,GAAG,CAAC9T,YAAY,IAAI8T,GAAG,CAAC9T,YAAY,CAAC3G,MAAM,EAAE;MACpDya,GAAG,CAAC9T,YAAY,GAAG8T,GAAG,CAAC9T,YAAY,CAAC0C,GAAG,CAAC5H,MAAM,IAAI,IAAIwY,gBAAgB,CAACxY,MAAM,CAAC,CAAC;IACnF;IACA,IAAI,CAACkF,YAAY,GAAGkU,GAAG,CAACJ,GAAG,CAAC9T,YAAY,EAAE,EAAE,CAAC;IAC7C,IAAI,CAACC,gBAAgB,GAAGiU,GAAG,CAACJ,GAAG,CAAC7T,gBAAgB,EAAE,KAAK,CAAC;IACxD,IAAI,CAACC,YAAY,GAAGgU,GAAG,CAACJ,GAAG,CAAC5T,YAAY,EAAE,KAAK,CAAC;IAChD,IAAI,CAACyU,UAAU,GAAGT,GAAG,CAACJ,GAAG,CAACa,UAAU,EAAE,IAAI,CAAC;IAC3C,IAAI,CAAC9T,iBAAiB,GAAGqT,GAAG,CAACJ,GAAG,CAACjT,iBAAiB,EAAE,CAAC,CAAC;IACtD,IAAI,CAACC,cAAc,GAAGoT,GAAG,CAACJ,GAAG,CAAChT,cAAc,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC8T,iBAAiB,GAAGV,GAAG,CAACJ,GAAG,CAACc,iBAAiB,EAAE,EAAE,CAAC;IACvD,IAAI,CAACC,gBAAgB,GAAGX,GAAG,CAACJ,GAAG,CAACe,gBAAgB,EAAE,EAAE,CAAC;IACrD,IAAI,CAAC7T,gBAAgB,GAAGkT,GAAG,CAACJ,GAAG,CAAC9S,gBAAgB,EAAE,IAAI,CAAC;IACvD,IAAI,CAACC,wBAAwB,GAAGiT,GAAG,CAACJ,GAAG,CAAC7S,wBAAwB,EAAE,KAAK,CAAC;IACxE,IAAI,CAACE,eAAe,GAAG+S,GAAG,CAACJ,GAAG,CAAC3S,eAAe,EAAE,KAAK,CAAC;IACtD,IAAI,CAACE,kBAAkB,GAAG6S,GAAG,CAACJ,GAAG,CAACzS,kBAAkB,EAAE,CAAC,CAAC;IACxD,IAAI,CAACC,eAAe,GAAG4S,GAAG,CAACJ,GAAG,CAACxS,eAAe,EAAE6P,eAAe,CAACC,MAAM,CAAC;IACvE,IAAI,CAAC7P,wBAAwB,GAAG2S,GAAG,CAACJ,GAAG,CAACvS,wBAAwB,EAAE,KAAK,CAAC;IACxE,IAAI,CAACZ,iBAAiB,GAAGuT,GAAG,CAACJ,GAAG,CAACnT,iBAAiB,EAAE,KAAK,CAAC;IAC1D,IAAI,CAACmU,kBAAkB,GAAGZ,GAAG,CAACJ,GAAG,CAACgB,kBAAkB,EAAE,KAAK,CAAC;IAC5D,IAAI,CAAC/T,eAAe,GAAGmT,GAAG,CAACJ,GAAG,CAAC/S,eAAe,EAAE,EAAE,CAAC;IACnD,IAAI,CAACK,aAAa,GAAG8S,GAAG,CAACJ,GAAG,CAAC1S,aAAa,EAAEsS,mBAAmB,CAACC,KAAK,CAAC;IACtE,IAAIG,GAAG,IAAIA,GAAG,CAACtS,gBAAgB,IAAIsS,GAAG,CAACtS,gBAAgB,CAACnI,MAAM,EAAE;MAC5Dya,GAAG,CAACtS,gBAAgB,GAAGsS,GAAG,CAACtS,gBAAgB,CAACkB,GAAG,CAAC5H,MAAM,IAAI,IAAIwY,gBAAgB,CAACxY,MAAM,CAAC,CAAC;IAC3F;IACA,IAAI,CAAC0G,gBAAgB,GAAG0S,GAAG,CAACJ,GAAG,CAACtS,gBAAgB,EAAE,EAAE,CAAC;IACrD,IAAI,CAACC,gBAAgB,GAAGyS,GAAG,CAACJ,GAAG,CAACrS,gBAAgB,EAAE,EAAE,CAAC;IACrD,IAAI,CAACpC,OAAO,GAAG6U,GAAG,CAACJ,GAAG,CAACzU,OAAO,EAAE,IAAI,CAAC;IACrC,IAAI,CAAC0V,kBAAkB,GAAGb,GAAG,CAACJ,GAAG,CAACiB,kBAAkB,EAAE,IAAI,CAAC;IAC3D,IAAI,CAACC,aAAa,GAAGd,GAAG,CAACJ,GAAG,CAACkB,aAAa,EAAE,IAAI,CAAC;IACjD,IAAI,CAACC,qBAAqB,GAAGf,GAAG,CAACJ,GAAG,CAACmB,qBAAqB,EAAE,KAAK,CAAC;IAClE,IAAI,CAACC,YAAY,GAAGhB,GAAG,CAACJ,GAAG,CAACoB,YAAY,EAAE,KAAK,CAAC;IAChD,IAAI,CAACC,iBAAiB,GAAGjB,GAAG,CAACJ,GAAG,CAACqB,iBAAiB,EAAE,KAAK,CAAC;IAC1D,IAAI,CAACC,sBAAsB,GAAGlB,GAAG,CAACJ,GAAG,CAACsB,sBAAsB,EAAE,KAAK,CAAC;IACpE,IAAI,CAACC,mBAAmB,GAAGnB,GAAG,CAACJ,GAAG,CAACuB,mBAAmB,EAAE,KAAK,CAAC;IAC9D,IAAI,CAACC,iBAAiB,GAAGpB,GAAG,CAACJ,GAAG,CAACwB,iBAAiB,EAAE,KAAK,CAAC;IAC1D,IAAI,CAACC,yBAAyB,GAAGrB,GAAG,CAACJ,GAAG,CAACyB,yBAAyB,EAAE,KAAK,CAAC;IAC1E,IAAI,CAACC,gBAAgB,GAAGtB,GAAG,CAACJ,GAAG,CAAC0B,gBAAgB,EAAE,IAAI,CAAC;IACvD,IAAI,CAACC,eAAe,GAAGvB,GAAG,CAACJ,GAAG,CAAC2B,eAAe,EAAE,KAAK,CAAC;IACtD,IAAI,CAACC,uBAAuB,GAAGxB,GAAG,CAACJ,GAAG,CAAC4B,uBAAuB,EAAE,IAAI,CAAC;IACrE,IAAI,CAACC,2BAA2B,GAAGzB,GAAG,CAACJ,GAAG,CAAC6B,2BAA2B,EAAE,KAAK,CAAC;IAC9E,IAAI,CAACC,mBAAmB,GAAG1B,GAAG,CAACJ,GAAG,CAAC8B,mBAAmB,EAAE,KAAK,CAAC;IAC9D,IAAI,CAACC,WAAW,GAAG3B,GAAG,CAACJ,GAAG,CAAC+B,WAAW,EAAE,KAAK,CAAC;IAC9C,IAAI,CAACC,eAAe,GAAG5B,GAAG,CAACJ,GAAG,CAACgC,eAAe,EAAE,GAAG,CAAC;IACpD,IAAI,CAACC,cAAc,GAAG7B,GAAG,CAACJ,GAAG,CAACiC,cAAc,EAAE,CAAC,CAAC;IAChD,IAAI,CAACC,cAAc,GAAG9B,GAAG,CAACJ,GAAG,CAACkC,cAAc,EAAE,GAAG,CAAC;IAClD,IAAI,CAACC,aAAa,GAAG/B,GAAG,CAACJ,GAAG,CAACmC,aAAa,EAAE,KAAK,CAAC;IAClD,IAAI,CAACC,eAAe,GAAGhC,GAAG,CAACJ,GAAG,CAACoC,eAAe,EAAE,KAAK,CAAC;IACtD,IAAI,CAACC,aAAa,GAAGjC,GAAG,CAACJ,GAAG,CAACqC,aAAa,EAAElC,SAAS,CAAC;IACtD,IAAI,CAACmC,cAAc,GAAGlC,GAAG,CAACJ,GAAG,CAACsC,cAAc,EAAE,KAAK,CAAC;IACpD,IAAI,CAAC5hB,aAAa,GAAG0f,GAAG,CAACJ,GAAG,CAACtf,aAAa,EAAE,yBAAyB,CAAC;IACtE,IAAI,CAACC,aAAa,GAAGyf,GAAG,CAACJ,GAAG,CAACrf,aAAa,EAAE,0BAA0B,CAAC;IACvE,IAAI,CAACwZ,SAAS,GAAGiG,GAAG,CAACJ,GAAG,CAAC7F,SAAS,EAAE,oBAAoB,CAAC;IACzD,IAAI,CAAC7W,cAAc,GAAG8c,GAAG,CAACJ,GAAG,CAAC1c,cAAc,EAAE,kBAAkB,CAAC;IACjE,IAAI,CAAC8W,WAAW,GAAGgG,GAAG,CAACJ,GAAG,CAAC5F,WAAW,EAAE,oCAAoC,CAAC;IAC7E,IAAI,CAAC9X,UAAU,GAAG8d,GAAG,CAACJ,GAAG,CAAC1d,UAAU,EAAE,mBAAmB,CAAC;IAC1D,IAAI,CAACN,WAAW,GAAGoe,GAAG,CAACJ,GAAG,CAAChe,WAAW,EAAE,oBAAoB,CAAC;IAC7D,IAAI,CAACY,cAAc,GAAGwd,GAAG,CAACJ,GAAG,CAACpd,cAAc,EAAE,YAAY,CAAC;IAC3D,IAAI,CAACK,eAAe,GAAGmd,GAAG,CAACJ,GAAG,CAAC/c,eAAe,EAAE,cAAc,CAAC;IAC/D,IAAI,CAACtB,YAAY,GAAGye,GAAG,CAACJ,GAAG,CAACre,YAAY,EAAE,yBAAyB,CAAC;IACpE,IAAIqe,GAAG,IAAIA,GAAG,CAAC/Y,OAAO,IAAI+Y,GAAG,CAAC/Y,OAAO,CAAC1B,MAAM,EAAE;MAC1Cya,GAAG,CAAC/Y,OAAO,GAAG+Y,GAAG,CAAC/Y,OAAO,CAAC2H,GAAG,CAAC5H,MAAM,IAAI,IAAIwY,gBAAgB,CAACxY,MAAM,CAAC,CAAC;IACzE;IACA,IAAI,CAACC,OAAO,GAAGmZ,GAAG,CAACJ,GAAG,CAAC/Y,OAAO,EAAE,EAAE,CAAC;EACvC;AACJ;AAEA,MAAMsb,sBAAsB,CAAC;EACzB1U,WAAWA,CAACmS,GAAG,EAAE;IACb,IAAI,CAACze,GAAG,GAAGye,GAAG,CAACze,GAAG;IAClB,IAAI,CAACwG,IAAI,GAAGiY,GAAG,CAACjY,IAAI;IACpB,IAAI,CAACvI,KAAK,GAAGwgB,GAAG,CAACxgB,KAAK;EAC1B;AACJ;AAEA,MAAMgjB,mBAAmB,CAAC;EACtB3U,WAAWA,CAAC4U,SAAS,EAAElP,aAAa,EAAE;IAClC,IAAI,CAACkP,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAClP,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACmP,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;IACnB,IAAI,CAACC,WAAW,GAAG,IAAIplB,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAACqlB,MAAM,GAAG,IAAIrlB,YAAY,CAAC,CAAC;IAChC,IAAI,CAACqW,WAAW,GAAG,IAAIrW,YAAY,CAAC,CAAC;IACrC,IAAI,CAACsW,YAAY,GAAG,IAAItW,YAAY,CAAC,CAAC;IACtC,IAAI,CAACslB,aAAa,GAAG,IAAItlB,YAAY,CAAC,CAAC;IACvC,IAAI,CAACulB,eAAe,GAAG,CAAC;IACxB,IAAI,CAACha,aAAa,GAAG,CAAC;IACtB,IAAI,CAACoX,UAAU,GAAGC,SAAS;IAC3B,IAAI,CAAC4C,cAAc,GAAG5C,SAAS;EACnC;EACA9L,QAAQA,CAAA,EAAG;IACP,IAAI,CAACqO,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC9T,GAAG,CAAEoU,GAAG,IAAK,IAAIjD,iBAAiB,CAACiD,GAAG,CAAC,CAAC;IACpE,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,IAAI,CAAC9X,cAAc,EAAE;MACrB,IAAI,CAACxC,aAAa,GAAG,IAAI,CAACwC,cAAc,CAACqV,UAAU;IACvD;EACJ;EACA0C,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC/d,MAAM,KAAK6a,SAAS,IAAK,IAAI,CAAC7a,MAAM,CAACC,MAAM,KAAK,IAAI,CAACud,eAAgB,IACtE,IAAI,CAACxd,MAAM,KAAK,IAAI,CAACge,SAAU,EAAE;MACrC,IAAI,CAACR,eAAe,GAAG,IAAI,CAACxd,MAAM,CAACC,MAAM;MACzC,IAAI,CAAC+d,SAAS,GAAG,IAAI,CAAChe,MAAM;MAC5B,IAAI,CAAC6d,UAAU,CAAC,CAAC;MACjB,IAAI,CAACI,SAAS,CAAC,CAAC;MAChB,IAAI,IAAI,CAACje,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;QACnC,IAAI,CAACod,WAAW,CAAC9R,IAAI,CAAC,CAAC;MAC3B;MACA,IAAI,IAAI,CAACzD,KAAK,EAAE;QACZ,IAAI,CAACA,KAAK,CAACwO,KAAK,CAAC,IAAI,CAACtQ,cAAc,CAACqV,UAAU,CAAC;MACpD;MACA,IAAI,IAAI,CAACrV,cAAc,CAAC0V,kBAAkB,IAAI,IAAI,CAAC1V,cAAc,CAACuV,UAAU,IACrE,IAAI,CAACvb,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;QAC5B,IAAI,CAAC+F,cAAc,CAACuV,UAAU,GAAG,KAAK;QACtC,IAAI,CAACvV,cAAc,CAACE,WAAW,GAAG,KAAK;MAC3C;MACA,IAAI,CAACgY,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACL,cAAc,CAAC,CAAC;EACzB;EACAM,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,aAAa,CAAC,CAAC;IACpB,IAAI,IAAI,CAACH,cAAc,KAAK,IAAI,CAAC7C,UAAU,EAAE;MACzC,IAAI,CAACiD,UAAU,CAAC,CAAC;MACjB,IAAI,CAACK,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI,IAAI,CAAClY,cAAc,IAAI,IAAI,CAACA,cAAc,CAACmV,SAAS,EAAE;MACtD,IAAI,IAAI,CAACkD,gBAAgB,EAAE;QACvBpN,YAAY,CAAC,IAAI,CAACoN,gBAAgB,CAAC;MACvC;MACA,IAAI,CAACA,gBAAgB,GAAGtN,UAAU,CAAC,MAAM;QACrC,IAAI,CAAC+M,cAAc,CAAC,CAAC;MACzB,CAAC,EAAE,GAAG,CAAC;IACX;EACJ;EACAhY,cAAcA,CAAA,EAAG;IACb,OAAQ,IAAI,CAACE,cAAc,IAAI,IAAI,CAACA,cAAc,CAACuV,UAAU,GACzD,IAAI,CAACvV,cAAc,CAACsV,YAAY,GAAG,GAAG,GAAG,MAAM;EACvD;EACAlU,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACpB,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC8B,KAAK,EAAE;MAClD,OAAO,OAAO,GAAG,IAAI,CAAC9B,cAAc,CAACwV,iBAAiB,GAAG,MAAM,GACzD,IAAI,CAACxV,cAAc,CAACyV,gBAAgB,GAAG,KAAK;IACtD,CAAC,MACI;MACD,OAAO,MAAM;IACjB;EACJ;EACAvU,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAClB,cAAc,IAAI,IAAI,CAACA,cAAc,CAACoV,MAAM,KAAKjB,gBAAgB,CAACE,gBAAgB,EAAE;MACzF,OAAO,IAAI,CAACrU,cAAc,CAACyV,gBAAgB,GAAG,IAAI;IACtD,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACAtU,yBAAyBA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACnB,cAAc,IAAI,IAAI,CAACA,cAAc,CAACoV,MAAM,KAAKjB,gBAAgB,CAACC,aAAa,EAAE;MACtF,OAAO,IAAI,CAACpU,cAAc,CAACyV,gBAAgB,GAAG,IAAI;IACtD,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACAhW,WAAWA,CAACvL,KAAK,EAAE;IACf,IAAI,IAAI,CAAC8L,cAAc,CAAC+W,aAAa,EAAE;MACnC,IAAI,CAAC/W,cAAc,CAAC+W,aAAa,CAAC7iB,KAAK,CAAC;IAC5C,CAAC,MACI;MACD,IAAI,CAACokB,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACrY,OAAO,CAAC+J,IAAI,CAAC9V,KAAK,CAAC;IAC5B;EACJ;EACAqkB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACjQ,WAAW,CAAC/C,IAAI,CAAC,CAAC;IACvB,IAAI,IAAI,CAACzD,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC6I,QAAQ,EAAE;MACnC,IAAI,CAAC7I,KAAK,CAAC4I,YAAY,CAAC,CAAC;IAC7B;EACJ;EACA8N,cAAcA,CAAA,EAAG;IACb,IAAI,CAACF,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAC/P,YAAY,CAAChD,IAAI,CAAC,CAAC;IACxB,IAAI,IAAI,CAACzD,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC6I,QAAQ,EAAE;MACnC,IAAI,CAAC7I,KAAK,CAAC+I,aAAa,CAAC,CAAC;IAC9B;EACJ;EACAlL,eAAeA,CAACzL,KAAK,EAAE;IACnB,IAAI,CAACukB,MAAM,CAACvkB,KAAK,CAAC;EACtB;EACA+M,oBAAoBA,CAAC/M,KAAK,EAAE;IACxB,IAAI,CAACukB,MAAM,CAACvkB,KAAK,CAAC;IAClB,IAAI,IAAI,CAAC8L,cAAc,IAAI,IAAI,CAACA,cAAc,CAACuV,UAAU,IAAI,IAAI,CAACvV,cAAc,CAACC,OAAO,KAChF,CAAC,IAAI,CAACD,cAAc,CAAC8B,KAAK,IAAI,IAAI,CAAC9B,cAAc,CAACmC,wBAAwB,CAAC,EAAE;MACjF,IAAI,CAAC1C,WAAW,CAAC,IAAI,CAACjC,aAAa,CAAC;IACxC;EACJ;EACAX,IAAIA,CAAC3I,KAAK,EAAE;IACR,IAAI,CAACukB,MAAM,CAACvkB,KAAK,CAAC;EACtB;EACAe,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC6M,KAAK,CAAC7M,QAAQ,CAAC,CAAC;EACzB;EACAF,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC+M,KAAK,CAAC/M,QAAQ,CAAC,CAAC;EACzB;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC6E,MAAM,IAAI,IAAI,CAACgG,cAAc,EAAE;MACpC,OAAO,CAAC,EAAE,IAAI,CAACA,cAAc,CAACU,iBAAiB,IAAI,IAAI,CAAClD,aAAa,GAAG,IAAI,CAACxD,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IACnG,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACA/E,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC8E,MAAM,IAAI,IAAI,CAACgG,cAAc,EAAE;MACpC,OAAO,CAAC,EAAE,IAAI,CAACA,cAAc,CAACU,iBAAiB,IAAI,IAAI,CAAClD,aAAa,GAAG,CAAC,CAAC;IAC9E,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACAkb,aAAaA,CAACxkB,KAAK,EAAE;IACjB,IAAI,CAACqjB,aAAa,CAAChS,IAAI,CAAC;MAAErR,KAAK;MAAE4N,KAAK,EAAE,IAAI,CAAC9H,MAAM,CAAC9F,KAAK;IAAE,CAAC,CAAC;EACjE;EACAykB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACpD,UAAU,CAACpW,SAAS,CAAC,CAAC;EAC/B;EACAyZ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACrD,UAAU,CAACtW,QAAQ,CAAC,CAAC;EAC9B;EACA4Z,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACtD,UAAU,CAAClW,YAAY,CAAC,CAAC;EACzC;EACAyZ,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACvD,UAAU,CAACnW,WAAW,CAAC,CAAC;EACxC;EACA8Y,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC3C,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACjF,KAAK,CAAC,IAAI,CAACtQ,cAAc,CAACqV,UAAU,CAAC;IACzD;EACJ;EACAoD,MAAMA,CAACvkB,KAAK,EAAE;IACV,IAAI,CAACsJ,aAAa,GAAGtJ,KAAK;IAC1B,IAAI,CAACojB,MAAM,CAAC/R,IAAI,CAAC;MACbrR,KAAK;MACL4N,KAAK,EAAE,IAAI,CAAC9H,MAAM,CAAC9F,KAAK;IAC5B,CAAC,CAAC;EACN;EACA4jB,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC9X,cAAc,IAAI,IAAI,CAACA,cAAc,CAACmV,SAAS,EAAE;MACtD,IAAI,CAACF,KAAK,GAAG5J,QAAQ,CAAC0N,IAAI,CAACC,WAAW,GAAG,IAAI;MAC7C,IAAI,CAACC,IAAI,GAAG,aAAa,GAAI,EAAE5N,QAAQ,CAAC0N,IAAI,CAACC,WAAW,GACpD,IAAI,CAAC7B,SAAS,CAAC9T,aAAa,CAAC6V,UAAU,CAACC,UAAU,CAAC,GAAG,CAAE,GAAG,KAAK;IACxE;EACJ;EACAlB,SAASA,CAAA,EAAG;IACR,IAAI,CAACje,MAAM,CAACof,OAAO,CAAE1L,GAAG,IAAKA,GAAG,CAACjR,IAAI,GAAG,IAAI,CAACwL,aAAa,CAACtJ,WAAW,CAAC+O,GAAG,CAAC/J,GAAG,IAAI+J,GAAG,CAAC2L,GAAG,IAAI3L,GAAG,CAAC4L,MAAM,IAAI5L,GAAG,CAAC6L,KAAK,IAAI,EAAE,CAAC,CAAC;IAC5H,IAAI,CAAClY,WAAW,GAAG,IAAI,CAACrH,MAAM,CAACsJ,GAAG,CAAEoK,GAAG,IAAKA,GAAG,CAAC6L,KAAK,CAAC;IACtD,IAAI,CAACxZ,YAAY,GAAG,IAAI,CAAC/F,MAAM,CAACsJ,GAAG,CAAC,CAACoK,GAAG,EAAEmD,CAAC,KAAK,IAAIoG,sBAAsB,CAAC;MACvEhhB,GAAG,EAAEyX,GAAG,CAAC4L,MAAM;MACf7c,IAAI,EAAEiR,GAAG,CAACjR,IAAI;MACdvI,KAAK,EAAE2c;IACX,CAAC,CAAC,CAAC;IACH,IAAI,CAAC2I,SAAS,GAAG,IAAI,CAACxf,MAAM,CAACsJ,GAAG,CAAEoK,GAAG,IAAKA,GAAG,CAAC2L,GAAG,CAAC;IAClD,IAAI,CAACte,YAAY,GAAG,IAAI,CAACf,MAAM,CAACsJ,GAAG,CAAEoK,GAAG,IAAKA,GAAG,CAACrT,WAAW,CAAC;IAC7D,IAAI,CAACkE,KAAK,GAAG,IAAI,CAACvE,MAAM,CAACsJ,GAAG,CAAEoK,GAAG,IAAKA,GAAG,CAAC/J,GAAG,CAAC;IAC9C,IAAI,CAACjF,MAAM,GAAG,IAAI,CAAC1E,MAAM,CAACsJ,GAAG,CAAEoK,GAAG,IAAKA,GAAG,CAAC+L,KAAK,CAAC;EACrD;EACA7B,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACH,cAAc,GAAG,IAAI,CAAC7C,UAAU;IACrC,IAAI8E,WAAW;IACf,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MAC/BD,WAAW,GAAG,IAAI,CAACtC,OAAO,CAACxG,MAAM,CAAE8G,GAAG,IAAKA,GAAG,CAAC9C,UAAU,IAAI+E,MAAM,CAACR,UAAU,CAAC,CAC1E7V,GAAG,CAAEoU,GAAG,IAAKA,GAAG,CAAC9C,UAAU,CAAC;IACrC;IACA,IAAI8E,WAAW,IAAIA,WAAW,CAACzf,MAAM,EAAE;MACnC,IAAI,CAAC2a,UAAU,GAAG8E,WAAW,CAACxV,GAAG,CAAC,CAAC;IACvC,CAAC,MACI;MACD,IAAI,CAAC0Q,UAAU,GAAGC,SAAS;IAC/B;EACJ;EACA8C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACP,OAAO,GAAG,CACX,GAAG,IAAI,CAACA,OAAO,CAACxG,MAAM,CAAEgJ,CAAC,IAAKA,CAAC,CAAChF,UAAU,KAAKC,SAAS,CAAC,EACzD,GAAG,IAAI,CAACuC,OAAO,CACVxG,MAAM,CAAEgJ,CAAC,IAAKA,CAAC,CAAChF,UAAU,KAAKC,SAAS,CAAC,CACzCgF,IAAI,CAAC,CAACD,CAAC,EAAEE,CAAC,KAAKA,CAAC,CAAClF,UAAU,GAAGgF,CAAC,CAAChF,UAAU,CAAC,CACnD;EACL;EACAiD,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC7X,cAAc,GAAG,IAAIyU,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC/C,IAAI,CAAC2C,OAAO,CACPxG,MAAM,CAAE8G,GAAG,IAAKA,GAAG,CAAC9C,UAAU,KAAKC,SAAS,IAAI6C,GAAG,CAAC9C,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC,CAClFtR,GAAG,CAAEoU,GAAG,IAAK,IAAI,CAACqC,cAAc,CAAC,IAAI,CAAC/Z,cAAc,EAAE0X,GAAG,CAAC,CAAC;IAChE,IAAI,CAACzC,KAAK,GAAG,IAAI,CAACjV,cAAc,CAACiV,KAAK;IACtC,IAAI,CAACC,MAAM,GAAG,IAAI,CAAClV,cAAc,CAACkV,MAAM;EAC5C;EACA6E,cAAcA,CAAC3M,KAAK,EAAE4M,MAAM,EAAE;IAC1BC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAAC1W,GAAG,CAAE6W,GAAG,IAAK/M,KAAK,CAAC+M,GAAG,CAAC,GAAGH,MAAM,CAACG,GAAG,CAAC,KAAKtF,SAAS,GAAGmF,MAAM,CAACG,GAAG,CAAC,GAAG/M,KAAK,CAAC+M,GAAG,CAAC,CAAC;EACvG;EACAta,YAAYA,CAACiH,KAAK,EAAE;IAChB,IAAI,CAACxF,WAAW,GAAGwF,KAAK;EAC5B;AACJ;AACAoQ,mBAAmB,CAAC5S,IAAI,YAAA8V,4BAAA5V,iBAAA;EAAA,YAAAA,iBAAA,IAAwF0S,mBAAmB,EAlkD/BnlB,EAAE,CAAA+b,iBAAA,CAkkD+C/b,EAAE,CAACic,UAAU,GAlkD9Djc,EAAE,CAAA+b,iBAAA,CAkkDyExL,iBAAiB;AAAA,CAA4C;AAC5O4U,mBAAmB,CAACxR,IAAI,kBAnkD4E3T,EAAE,CAAA4T,iBAAA;EAAAlJ,IAAA,EAmkDFya,mBAAmB;EAAAtR,SAAA;EAAAsI,SAAA,WAAAmM,0BAAA7mB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAnkDnBzB,EAAE,CAAAqc,WAAA,CAmkDwftG,0BAA0B;MAnkDphB/V,EAAE,CAAAqc,WAAA,CAmkD0lB6B,wBAAwB;MAnkDpnBle,EAAE,CAAAqc,WAAA,CAmkD+rB+D,6BAA6B;IAAA;IAAA,IAAA3e,EAAA;MAAA,IAAA6a,EAAA;MAnkD9tBtc,EAAE,CAAAuc,cAAA,CAAAD,EAAA,GAAFtc,EAAE,CAAAwc,WAAA,QAAA9a,GAAA,CAAAwM,OAAA,GAAAoO,EAAA,CAAAjB,KAAA;MAAFrb,EAAE,CAAAuc,cAAA,CAAAD,EAAA,GAAFtc,EAAE,CAAAwc,WAAA,QAAA9a,GAAA,CAAAqO,KAAA,GAAAuM,EAAA,CAAAjB,KAAA;MAAFrb,EAAE,CAAAuc,cAAA,CAAAD,EAAA,GAAFtc,EAAE,CAAAwc,WAAA,QAAA9a,GAAA,CAAA8hB,UAAA,GAAAlH,EAAA,CAAAjB,KAAA;IAAA;EAAA;EAAAkN,QAAA;EAAA9L,YAAA,WAAA+L,iCAAA/mB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFzB,EAAE,CAAA8B,UAAA,oBAAA2mB,8CAAA;QAAA,OAmkDF/mB,GAAA,CAAA2kB,QAAA,CAAS,CAAC;MAAA,UAnkDVrmB,EAAE,CAAA0oB,eAmkDgB,CAAC;IAAA;IAAA,IAAAjnB,EAAA;MAnkDnBzB,EAAE,CAAA6G,WAAA,UAAAnF,GAAA,CAAAwhB,KAmkDgB,CAAC,WAAAxhB,GAAA,CAAAyhB,MAAD,CAAC,cAAAzhB,GAAA,CAAAwlB,IAAD,CAAC;IAAA;EAAA;EAAApT,MAAA;IAAAuR,OAAA;IAAApd,MAAA;EAAA;EAAAgM,OAAA;IAAAqR,WAAA;IAAAC,MAAA;IAAAhP,WAAA;IAAAC,YAAA;IAAAgP,aAAA;EAAA;EAAArI,QAAA,GAnkDnBnd,EAAE,CAAA2oB,kBAAA,CAmkDka,CAACpY,iBAAiB,CAAC;EAAA2D,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAuU,6BAAAnnB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAnkDvbzB,EAAE,CAAA6B,cAAA,SAmkDy0B,CAAC;MAnkD50B7B,EAAE,CAAAwJ,UAAA,IAAAgE,gDAAA,+BAmkD+mE,CAAC,IAAAwB,qDAAA,oCAAsnD,CAAC;MAnkDzuHhP,EAAE,CAAA6B,cAAA,4BAmkDylM,CAAC;MAnkD5lM7B,EAAE,CAAA8B,UAAA,0BAAA+mB,yEAAA;QAAA,OAmkD85LnnB,GAAA,CAAA+kB,cAAA,CAAe,CAAC;MAAA,CAAC,CAAC,yBAAAqC,wEAAA;QAAA,OAAiBpnB,GAAA,CAAA8kB,aAAA,CAAc,CAAC;MAAA,CAAC,CAAC,0BAAAuC,yEAAA/mB,MAAA;QAAA,OAA0CN,GAAA,CAAAilB,aAAA,CAAA3kB,MAAoB,CAAC;MAAA,CAAC,CAAC;MAnkDrhMhC,EAAE,CAAAwC,YAAA,CAmkD+mM,CAAC,CAAO,CAAC;IAAA;IAAA,IAAAf,EAAA;MAnkD1nMzB,EAAE,CAAAqE,sBAAA,wBAAA3C,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAoV,MAAA,IAmkDw0B,CAAC;MAnkD30BrjB,EAAE,CAAAoE,SAAA,CAmkD63B,CAAC;MAnkDh4BpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA8B,KAmkD63B,CAAC;MAnkDh4B/P,EAAE,CAAAoE,SAAA,CAmkDmsE,CAAC;MAnkDtsEpE,EAAE,CAAAyC,UAAA,SAAAf,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAuV,UAmkDmsE,CAAC;MAnkDtsExjB,EAAE,CAAAoE,SAAA,CAmkDwlM,CAAC;MAnkD3lMpE,EAAE,CAAAiH,WAAA,uBAAAvF,GAAA,CAAA6kB,cAmkDwlM,CAAC;MAnkD3lMvmB,EAAE,CAAAyC,UAAA,WAAAf,GAAA,CAAA+lB,SAmkDgzH,CAAC,iBAAA/lB,GAAA,CAAAsH,YAA+B,CAAC,oBAAAtH,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA2V,kBAAgF,CAAC,kBAAAliB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA5K,aAAyE,CAAC,kBAAA3B,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA3K,aAAiD,CAAC,cAAA5B,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA6O,SAAiE,CAAC,mBAAApb,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAhI,cAAmD,CAAC,gBAAAvE,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA8O,WAAqE,CAAC,WAAArb,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA4V,aAA0C,CAAC,mBAAAniB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA6V,qBAAkF,CAAC,UAAApiB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA8V,YAAwC,CAAC,eAAAriB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA+V,iBAA0E,CAAC,oBAAAtiB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAgW,sBAAoF,CAAC,iBAAAviB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAiW,mBAA8E,CAAC,eAAAxiB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAkW,iBAA0E,CAAC,uBAAAziB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAmW,yBAA0F,CAAC,cAAA1iB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAoW,gBAAwE,CAAC,aAAA3iB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAqW,eAA8C,CAAC,qBAAA5iB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAsW,uBAAsF,CAAC,yBAAA7iB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAuW,2BAA8F,CAAC,iBAAA9iB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAwW,mBAA8E,CAAC,SAAA/iB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAyW,WAAsC,CAAC,aAAAhjB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA0W,eAAsE,CAAC,YAAAjjB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA2W,cAA4C,CAAC,YAAAljB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA4W,cAAoE,CAAC,eAAAnjB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAhJ,UAA2C,CAAC,gBAAAvD,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAtJ,WAAqE,CAAC,YAAAjD,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAArE,OAAqC,CAAC,WAAAlI,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA6W,aAAkE,CAAC,mBAAApjB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA1I,cAAmD,CAAC,oBAAA7D,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAArI,eAA6E,CAAC,aAAAlE,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA8W,eAA8C,CAAC,iBAAArjB,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAA3J,YAAuE,CAAC,YAAA5C,GAAA,CAAAuM,cAAA,kBAAAvM,GAAA,CAAAuM,cAAA,CAAAgX,cAA4C,CAAC;IAAA;EAAA;EAAArP,YAAA,GAAomCsI,wBAAwB,EAA+XkC,6BAA6B,EAA0VrK,0BAA0B,EAAmnBhV,EAAE,CAAC6c,IAAI,EAA0E7c,EAAE,CAAC+U,OAAO;EAAArB,MAAA;EAAAuU,aAAA;EAAAtU,eAAA;AAAA,EAAyJ;AAC7sR;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KApkDoGjT,EAAE,CAAAkT,iBAAA,CAokDXiS,mBAAmB,EAAc,CAAC;IACjHza,IAAI,EAAEvK,SAAS;IACfgT,IAAI,EAAE,CAAC;MAAEwB,QAAQ,EAAE,aAAa;MAAEqU,aAAa,EAAEvoB,iBAAiB,CAACwoB,IAAI;MAAEC,SAAS,EAAE,CAAC3Y,iBAAiB,CAAC;MAAEmE,eAAe,EAAEtU,uBAAuB,CAACwU,MAAM;MAAEP,QAAQ,EAAE,+2KAA+2K;MAAEI,MAAM,EAAE,CAAC,0zBAA0zB;IAAE,CAAC;EAC/1M,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/J,IAAI,EAAE1K,EAAE,CAACic;IAAW,CAAC,EAAE;MAAEvR,IAAI,EAAE6F;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE8U,OAAO,EAAE,CAAC;MACxH3a,IAAI,EAAErK;IACV,CAAC,CAAC;IAAE4H,MAAM,EAAE,CAAC;MACTyC,IAAI,EAAErK;IACV,CAAC,CAAC;IAAEilB,WAAW,EAAE,CAAC;MACd5a,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEilB,MAAM,EAAE,CAAC;MACT7a,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEiW,WAAW,EAAE,CAAC;MACd7L,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEkW,YAAY,EAAE,CAAC;MACf9L,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEklB,aAAa,EAAE,CAAC;MAChB9a,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAE4N,OAAO,EAAE,CAAC;MACVxD,IAAI,EAAEnK,SAAS;MACf4S,IAAI,EAAE,CAAC4C,0BAA0B;IACrC,CAAC,CAAC;IAAEhG,KAAK,EAAE,CAAC;MACRrF,IAAI,EAAEnK,SAAS;MACf4S,IAAI,EAAE,CAAC+K,wBAAwB;IACnC,CAAC,CAAC;IAAEsF,UAAU,EAAE,CAAC;MACb9Y,IAAI,EAAEnK,SAAS;MACf4S,IAAI,EAAE,CAACiN,6BAA6B;IACxC,CAAC,CAAC;IAAE8C,KAAK,EAAE,CAAC;MACRxY,IAAI,EAAEhK,WAAW;MACjByS,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAEgQ,MAAM,EAAE,CAAC;MACTzY,IAAI,EAAEhK,WAAW;MACjByS,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAE+T,IAAI,EAAE,CAAC;MACPxc,IAAI,EAAEhK,WAAW;MACjByS,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEkT,QAAQ,EAAE,CAAC;MACX3b,IAAI,EAAElK,YAAY;MAClB2S,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgW,kBAAkB,SAAStoB,mBAAmB,CAAC;EACjD2P,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG4Y,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAG;MACbC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAM,CAAC;MACxBvM,MAAM,EAAE;QAAEuM,MAAM,EAAE;MAAM;IAC5B,CAAC;EACL;AACJ;AACAJ,kBAAkB,CAAC5W,IAAI;EAAA,IAAAiX,+BAAA;EAAA,gBAAAC,2BAAAhX,iBAAA;IAAA,QAAA+W,+BAAA,KAAAA,+BAAA,GArnD6ExpB,EAAE,CAAA0pB,qBAAA,CAqnDSP,kBAAkB,IAAA1W,iBAAA,IAAlB0W,kBAAkB;EAAA;AAAA,IAAsD;AACvLA,kBAAkB,CAACvW,KAAK,kBAtnD4E5S,EAAE,CAAA6S,kBAAA;EAAAC,KAAA,EAsnDaqW,kBAAkB;EAAApW,OAAA,EAAlBoW,kBAAkB,CAAA5W;AAAA,EAAG;AACxI;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KAvnDoGjT,EAAE,CAAAkT,iBAAA,CAunDXiW,kBAAkB,EAAc,CAAC;IAChHze,IAAI,EAAEzK;EACV,CAAC,CAAC;AAAA;AACV,MAAM0pB,gBAAgB,CAAC;AAEvBA,gBAAgB,CAACpX,IAAI,YAAAqX,yBAAAnX,iBAAA;EAAA,YAAAA,iBAAA,IAAwFkX,gBAAgB;AAAA,CAAkD;AAC/KA,gBAAgB,CAACE,IAAI,kBA7nD+E7pB,EAAE,CAAA8pB,gBAAA;EAAApf,IAAA,EA6nDQif;AAAgB,EAM/B;AAC/FA,gBAAgB,CAACI,IAAI,kBApoD+E/pB,EAAE,CAAAgqB,gBAAA;EAAAd,SAAA,EAooDqC,CACnI;IAAEe,OAAO,EAAEnpB,qBAAqB;IAAEopB,QAAQ,EAAEf;EAAmB,CAAC,CACnE;EAAAgB,OAAA,GAAY,CAACnpB,YAAY,CAAC;AAAA,EAAI;AACnC;EAAA,QAAAiS,SAAA,oBAAAA,SAAA,KAvoDoGjT,EAAE,CAAAkT,iBAAA,CAuoDXyW,gBAAgB,EAAc,CAAC;IAC9Gjf,IAAI,EAAE/J,QAAQ;IACdwS,IAAI,EAAE,CAAC;MACCiX,YAAY,EAAE,CACVjF,mBAAmB,EACnBjH,wBAAwB,EACxB9K,yBAAyB,EACzBgN,6BAA6B,EAC7BrK,0BAA0B,EAC1BlB,yBAAyB,EACzBQ,0BAA0B,CAC7B;MACD8U,OAAO,EAAE,CAACnpB,YAAY,CAAC;MACvBqpB,OAAO,EAAE,CAAClF,mBAAmB,CAAC;MAC9B+D,SAAS,EAAE,CACP;QAAEe,OAAO,EAAEnpB,qBAAqB;QAAEopB,QAAQ,EAAEf;MAAmB,CAAC;IAExE,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMmB,eAAe,CAAC;EAClB9Z,WAAWA,CAACmS,GAAG,EAAE;IACb,IAAI,CAAC6E,KAAK,GAAG7E,GAAG,CAAC6E,KAAK;IACtB,IAAI,CAACD,MAAM,GAAG5E,GAAG,CAAC4E,MAAM;IACxB,IAAI,CAACD,GAAG,GAAG3E,GAAG,CAAC2E,GAAG;IAClB,IAAI,CAAChf,WAAW,GAAGqa,GAAG,CAACra,WAAW;IAClC,IAAI,CAACsJ,GAAG,GAAG+Q,GAAG,CAAC/Q,GAAG;IAClB,IAAI,CAAClH,IAAI,GAAGiY,GAAG,CAACjY,IAAI;IACpB,IAAI,CAACgd,KAAK,GAAG/E,GAAG,CAAC+E,KAAK;EAC1B;AACJ;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASyB,kBAAkB,EAAEhH,gBAAgB,EAAEtE,mBAAmB,EAAEzK,yBAAyB,EAAEiC,0BAA0B,EAAE8P,mBAAmB,EAAEmF,eAAe,EAAE/H,mBAAmB,EAAEH,gBAAgB,EAAEuH,gBAAgB,EAAEjH,iBAAiB,EAAE1C,eAAe,EAAEkF,sBAAsB,EAAEnP,0BAA0B,EAAExF,iBAAiB,EAAE6P,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}