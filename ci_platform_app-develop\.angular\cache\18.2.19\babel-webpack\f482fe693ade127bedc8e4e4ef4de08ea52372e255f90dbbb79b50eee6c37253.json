{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport const defaultThrottleConfig = {\n  leading: true,\n  trailing: false\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n  return operate((source, subscriber) => {\n    const {\n      leading,\n      trailing\n    } = config;\n    let hasValue = false;\n    let sendValue = null;\n    let throttled = null;\n    let isComplete = false;\n    const endThrottling = () => {\n      throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n      throttled = null;\n      if (trailing) {\n        send();\n        isComplete && subscriber.complete();\n      }\n    };\n    const cleanupThrottling = () => {\n      throttled = null;\n      isComplete && subscriber.complete();\n    };\n    const startThrottle = value => throttled = innerFrom(durationSelector(value)).subscribe(createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling));\n    const send = () => {\n      if (hasValue) {\n        hasValue = false;\n        const value = sendValue;\n        sendValue = null;\n        subscriber.next(value);\n        !isComplete && startThrottle(value);\n      }\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      sendValue = value;\n      !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n    }, () => {\n      isComplete = true;\n      !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "defaultThrottleConfig", "leading", "trailing", "throttle", "durationSelector", "config", "source", "subscriber", "hasValue", "sendValue", "throttled", "isComplete", "endThrottling", "unsubscribe", "send", "complete", "cleanupThrottling", "startThrottle", "value", "subscribe", "next", "closed"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/rxjs/dist/esm/internal/operators/throttle.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport const defaultThrottleConfig = {\n    leading: true,\n    trailing: false,\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n    return operate((source, subscriber) => {\n        const { leading, trailing } = config;\n        let hasValue = false;\n        let sendValue = null;\n        let throttled = null;\n        let isComplete = false;\n        const endThrottling = () => {\n            throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n            throttled = null;\n            if (trailing) {\n                send();\n                isComplete && subscriber.complete();\n            }\n        };\n        const cleanupThrottling = () => {\n            throttled = null;\n            isComplete && subscriber.complete();\n        };\n        const startThrottle = (value) => (throttled = innerFrom(durationSelector(value)).subscribe(createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling)));\n        const send = () => {\n            if (hasValue) {\n                hasValue = false;\n                const value = sendValue;\n                sendValue = null;\n                subscriber.next(value);\n                !isComplete && startThrottle(value);\n            }\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            sendValue = value;\n            !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n        }, () => {\n            isComplete = true;\n            !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,MAAMC,qBAAqB,GAAG;EACjCC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE;AACd,CAAC;AACD,OAAO,SAASC,QAAQA,CAACC,gBAAgB,EAAEC,MAAM,GAAGL,qBAAqB,EAAE;EACvE,OAAOH,OAAO,CAAC,CAACS,MAAM,EAAEC,UAAU,KAAK;IACnC,MAAM;MAAEN,OAAO;MAAEC;IAAS,CAAC,GAAGG,MAAM;IACpC,IAAIG,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,UAAU,GAAG,KAAK;IACtB,MAAMC,aAAa,GAAGA,CAAA,KAAM;MACxBF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,WAAW,CAAC,CAAC;MAC7EH,SAAS,GAAG,IAAI;MAChB,IAAIR,QAAQ,EAAE;QACVY,IAAI,CAAC,CAAC;QACNH,UAAU,IAAIJ,UAAU,CAACQ,QAAQ,CAAC,CAAC;MACvC;IACJ,CAAC;IACD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC5BN,SAAS,GAAG,IAAI;MAChBC,UAAU,IAAIJ,UAAU,CAACQ,QAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAME,aAAa,GAAIC,KAAK,IAAMR,SAAS,GAAGX,SAAS,CAACK,gBAAgB,CAACc,KAAK,CAAC,CAAC,CAACC,SAAS,CAACrB,wBAAwB,CAACS,UAAU,EAAEK,aAAa,EAAEI,iBAAiB,CAAC,CAAE;IACnK,MAAMF,IAAI,GAAGA,CAAA,KAAM;MACf,IAAIN,QAAQ,EAAE;QACVA,QAAQ,GAAG,KAAK;QAChB,MAAMU,KAAK,GAAGT,SAAS;QACvBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACa,IAAI,CAACF,KAAK,CAAC;QACtB,CAACP,UAAU,IAAIM,aAAa,CAACC,KAAK,CAAC;MACvC;IACJ,CAAC;IACDZ,MAAM,CAACa,SAAS,CAACrB,wBAAwB,CAACS,UAAU,EAAGW,KAAK,IAAK;MAC7DV,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGS,KAAK;MACjB,EAAER,SAAS,IAAI,CAACA,SAAS,CAACW,MAAM,CAAC,KAAKpB,OAAO,GAAGa,IAAI,CAAC,CAAC,GAAGG,aAAa,CAACC,KAAK,CAAC,CAAC;IAClF,CAAC,EAAE,MAAM;MACLP,UAAU,GAAG,IAAI;MACjB,EAAET,QAAQ,IAAIM,QAAQ,IAAIE,SAAS,IAAI,CAACA,SAAS,CAACW,MAAM,CAAC,IAAId,UAAU,CAACQ,QAAQ,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}