{"ast": null, "code": "export default (element, until) => {\n  const result = [];\n  let next = element.parentNode;\n  while (next) {\n    result.push(next);\n    if (next === until) {\n      break;\n    }\n    next = next.parentNode;\n  }\n  return result;\n};", "map": {"version": 3, "names": ["element", "until", "result", "next", "parentNode", "push"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/parents.js"], "sourcesContent": ["export default (element, until) => {\n    const result = [];\n    let next = element.parentNode;\n\n    while (next) {\n        result.push(next);\n\n        if (next === until) { break; }\n\n        next = next.parentNode;\n    }\n\n    return result;\n};\n"], "mappings": "AAAA,eAAe,CAACA,OAAO,EAAEC,KAAK,KAAK;EAC/B,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,IAAI,GAAGH,OAAO,CAACI,UAAU;EAE7B,OAAOD,IAAI,EAAE;IACTD,MAAM,CAACG,IAAI,CAACF,IAAI,CAAC;IAEjB,IAAIA,IAAI,KAAKF,KAAK,EAAE;MAAE;IAAO;IAE7BE,IAAI,GAAGA,IAAI,CAACC,UAAU;EAC1B;EAEA,OAAOF,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}