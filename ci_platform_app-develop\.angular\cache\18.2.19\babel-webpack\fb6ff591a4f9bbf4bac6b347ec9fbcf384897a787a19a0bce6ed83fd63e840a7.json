{"ast": null, "code": "export default element => {\n  const result = [];\n  let sibling = element.parentNode.firstElementChild;\n  while (sibling) {\n    if (sibling !== element) {\n      result.push(sibling);\n    }\n    sibling = sibling.nextElementSibling;\n  }\n  return result;\n};", "map": {"version": 3, "names": ["element", "result", "sibling", "parentNode", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "nextElement<PERSON><PERSON>ling"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/siblings.js"], "sourcesContent": ["export default (element) => {\n    const result = [];\n\n    let sibling = element.parentNode.firstElementChild;\n\n    while (sibling) {\n        if (sibling !== element) {\n            result.push(sibling);\n        }\n\n        sibling = sibling.nextElementSibling;\n    }\n    return result;\n};\n"], "mappings": "AAAA,eAAgBA,OAAO,IAAK;EACxB,MAAMC,MAAM,GAAG,EAAE;EAEjB,IAAIC,OAAO,GAAGF,OAAO,CAACG,UAAU,CAACC,iBAAiB;EAElD,OAAOF,OAAO,EAAE;IACZ,IAAIA,OAAO,KAAKF,OAAO,EAAE;MACrBC,MAAM,CAACI,IAAI,CAACH,OAAO,CAAC;IACxB;IAEAA,OAAO,GAAGA,OAAO,CAACI,kBAAkB;EACxC;EACA,OAAOL,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}