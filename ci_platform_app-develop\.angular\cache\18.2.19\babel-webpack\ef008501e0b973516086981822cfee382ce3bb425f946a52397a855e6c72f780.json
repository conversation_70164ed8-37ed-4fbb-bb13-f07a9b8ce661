{"ast": null, "code": "import { EMPTY } from './empty';\nimport { onErrorResumeNext as onErrorResumeNextWith } from '../operators/onErrorResumeNext';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nexport function onErrorResumeNext(...sources) {\n  return onErrorResumeNextWith(argsOrArgArray(sources))(EMPTY);\n}", "map": {"version": 3, "names": ["EMPTY", "onErrorResumeNext", "onErrorResumeNextWith", "argsOrArgArray", "sources"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/rxjs/dist/esm/internal/observable/onErrorResumeNext.js"], "sourcesContent": ["import { EMPTY } from './empty';\nimport { onErrorResumeNext as onErrorResumeNextWith } from '../operators/onErrorResumeNext';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nexport function onErrorResumeNext(...sources) {\n    return onErrorResumeNextWith(argsOrArgArray(sources))(EMPTY);\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,SAASC,iBAAiB,IAAIC,qBAAqB,QAAQ,gCAAgC;AAC3F,SAASC,cAAc,QAAQ,wBAAwB;AACvD,OAAO,SAASF,iBAAiBA,CAAC,GAAGG,OAAO,EAAE;EAC1C,OAAOF,qBAAqB,CAACC,cAAc,CAACC,OAAO,CAAC,CAAC,CAACJ,KAAK,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}