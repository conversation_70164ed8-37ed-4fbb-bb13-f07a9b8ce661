{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Injector, ElementRef, TemplateRef, Injectable, Inject } from '@angular/core';\nimport { listenToTriggersV2, registerOutsideClick, registerEscClick } from 'ngx-bootstrap/utils';\nimport * as i1 from 'ngx-bootstrap/positioning';\nimport { DOCUMENT } from '@angular/common';\nclass BsComponentRef {}\n\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\nclass ContentRef {\n  constructor(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  nodes, viewRef,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  componentRef) {\n    this.nodes = nodes;\n    this.viewRef = viewRef;\n    this.componentRef = componentRef;\n  }\n}\n\n// todo: add delay support\nclass ComponentLoader {\n  /**\n   * Do not use this directly, it should be instanced via\n   * `ComponentLoadFactory.attach`\n   * @internal\n   */\n  constructor(_viewContainerRef, _renderer, _elementRef, _injector, _componentFactoryResolver, _ngZone, _applicationRef, _posService, _document) {\n    this._viewContainerRef = _viewContainerRef;\n    this._renderer = _renderer;\n    this._elementRef = _elementRef;\n    this._injector = _injector;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._ngZone = _ngZone;\n    this._applicationRef = _applicationRef;\n    this._posService = _posService;\n    this._document = _document;\n    this.onBeforeShow = new EventEmitter();\n    this.onShown = new EventEmitter();\n    this.onBeforeHide = new EventEmitter();\n    this.onHidden = new EventEmitter();\n    this._providers = [];\n    this._isHiding = false;\n    /**\n     * A selector used if container element was not found\n     */\n    this.containerDefaultSelector = 'body';\n    this._listenOpts = {};\n    this._globalListener = Function.prototype;\n  }\n  get isShown() {\n    if (this._isHiding) {\n      return false;\n    }\n    return !!this._componentRef;\n  }\n  attach(compType) {\n    this._componentFactory = this._componentFactoryResolver.resolveComponentFactory(compType);\n    return this;\n  }\n  // todo: add behaviour: to target element, `body`, custom element\n  to(container) {\n    this.container = container || this.container;\n    return this;\n  }\n  position(opts) {\n    if (!opts) {\n      return this;\n    }\n    this.attachment = opts.attachment || this.attachment;\n    this._elementRef = opts.target || this._elementRef;\n    return this;\n  }\n  provide(provider) {\n    this._providers.push(provider);\n    return this;\n  }\n  // todo: appendChild to element or document.querySelector(this.container)\n  show(opts = {}) {\n    this._subscribePositioning();\n    this._innerComponent = void 0;\n    if (!this._componentRef) {\n      this.onBeforeShow.emit();\n      this._contentRef = this._getContentRef(opts.content, opts.context, opts.initialState);\n      const injector = Injector.create({\n        providers: this._providers,\n        parent: this._injector\n      });\n      if (!this._componentFactory) {\n        return;\n      }\n      this._componentRef = this._componentFactory.create(injector, this._contentRef.nodes);\n      this._applicationRef.attachView(this._componentRef.hostView);\n      // this._componentRef = this._viewContainerRef\n      //   .createComponent(this._componentFactory, 0, injector, this._contentRef.nodes);\n      this.instance = this._componentRef.instance;\n      Object.assign(this._componentRef.instance, opts);\n      if (this.container instanceof ElementRef) {\n        this.container.nativeElement.appendChild(this._componentRef.location.nativeElement);\n      }\n      if (typeof this.container === 'string' && typeof this._document !== 'undefined') {\n        const selectedElement = this._document.querySelector(this.container) || this._document.querySelector(this.containerDefaultSelector);\n        if (!selectedElement) {\n          return;\n        }\n        selectedElement.appendChild(this._componentRef.location.nativeElement);\n      }\n      if (!this.container && this._elementRef && this._elementRef.nativeElement.parentElement) {\n        this._elementRef.nativeElement.parentElement.appendChild(this._componentRef.location.nativeElement);\n      }\n      // we need to manually invoke change detection since events registered\n      // via\n      // Renderer::listen() are not picked up by change detection with the\n      // OnPush strategy\n      if (this._contentRef.componentRef) {\n        this._innerComponent = this._contentRef.componentRef.instance;\n        this._contentRef.componentRef.changeDetectorRef.markForCheck();\n        this._contentRef.componentRef.changeDetectorRef.detectChanges();\n      }\n      this._componentRef.changeDetectorRef.markForCheck();\n      this._componentRef.changeDetectorRef.detectChanges();\n      this.onShown.emit(opts.id ? {\n        id: opts.id\n      } : this._componentRef.instance);\n    }\n    this._registerOutsideClick();\n    return this._componentRef;\n  }\n  hide(id) {\n    if (!this._componentRef) {\n      return this;\n    }\n    this._posService.deletePositionElement(this._componentRef.location);\n    this.onBeforeHide.emit(this._componentRef.instance);\n    const componentEl = this._componentRef.location.nativeElement;\n    componentEl.parentNode?.removeChild(componentEl);\n    this._contentRef?.componentRef?.destroy();\n    if (this._viewContainerRef && this._contentRef?.viewRef) {\n      this._viewContainerRef.remove(this._viewContainerRef.indexOf(this._contentRef.viewRef));\n    }\n    this._contentRef?.viewRef?.destroy();\n    this._contentRef = void 0;\n    this._componentRef = void 0;\n    this._removeGlobalListener();\n    this.onHidden.emit(id ? {\n      id\n    } : null);\n    return this;\n  }\n  toggle() {\n    if (this.isShown) {\n      this.hide();\n      return;\n    }\n    this.show();\n  }\n  dispose() {\n    if (this.isShown) {\n      this.hide();\n    }\n    this._unsubscribePositioning();\n    if (this._unregisterListenersFn) {\n      this._unregisterListenersFn();\n    }\n  }\n  listen(listenOpts) {\n    this.triggers = listenOpts.triggers || this.triggers;\n    this._listenOpts.outsideClick = listenOpts.outsideClick;\n    this._listenOpts.outsideEsc = listenOpts.outsideEsc;\n    listenOpts.target = listenOpts.target || this._elementRef?.nativeElement;\n    const hide = this._listenOpts.hide = () => listenOpts.hide ? listenOpts.hide() : void this.hide();\n    const show = this._listenOpts.show = registerHide => {\n      listenOpts.show ? listenOpts.show(registerHide) : this.show(registerHide);\n      registerHide();\n    };\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const toggle = registerHide => {\n      this.isShown ? hide() : show(registerHide);\n    };\n    if (this._renderer) {\n      this._unregisterListenersFn = listenToTriggersV2(this._renderer, {\n        target: listenOpts.target,\n        triggers: listenOpts.triggers,\n        show,\n        hide,\n        toggle\n      });\n    }\n    return this;\n  }\n  _removeGlobalListener() {\n    if (this._globalListener) {\n      this._globalListener();\n      this._globalListener = Function.prototype;\n    }\n  }\n  attachInline(vRef,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  template) {\n    if (vRef && template) {\n      this._inlineViewRef = vRef.createEmbeddedView(template);\n    }\n    return this;\n  }\n  _registerOutsideClick() {\n    if (!this._componentRef || !this._componentRef.location) {\n      return;\n    }\n    // why: should run after first event bubble\n    if (this._listenOpts.outsideClick) {\n      const target = this._componentRef.location.nativeElement;\n      setTimeout(() => {\n        if (this._renderer && this._elementRef) {\n          this._globalListener = registerOutsideClick(this._renderer, {\n            targets: [target, this._elementRef.nativeElement],\n            outsideClick: this._listenOpts.outsideClick,\n            hide: () => this._listenOpts.hide && this._listenOpts.hide()\n          });\n        }\n      });\n    }\n    if (this._listenOpts.outsideEsc && this._renderer && this._elementRef) {\n      const target = this._componentRef.location.nativeElement;\n      this._globalListener = registerEscClick(this._renderer, {\n        targets: [target, this._elementRef.nativeElement],\n        outsideEsc: this._listenOpts.outsideEsc,\n        hide: () => this._listenOpts.hide && this._listenOpts.hide()\n      });\n    }\n  }\n  getInnerComponent() {\n    return this._innerComponent;\n  }\n  _subscribePositioning() {\n    if (this._zoneSubscription || !this.attachment) {\n      return;\n    }\n    this.onShown.subscribe(() => {\n      this._posService.position({\n        element: this._componentRef?.location,\n        target: this._elementRef,\n        attachment: this.attachment,\n        appendToBody: this.container === 'body'\n      });\n    });\n    this._zoneSubscription = this._ngZone.onStable.subscribe(() => {\n      if (!this._componentRef) {\n        return;\n      }\n      this._posService.calcPosition();\n    });\n  }\n  _unsubscribePositioning() {\n    if (!this._zoneSubscription) {\n      return;\n    }\n    this._zoneSubscription.unsubscribe();\n    this._zoneSubscription = void 0;\n  }\n  _getContentRef(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  content,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  context,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  initialState) {\n    if (!content) {\n      return new ContentRef([]);\n    }\n    if (content instanceof TemplateRef) {\n      if (this._viewContainerRef) {\n        const _viewRef = this._viewContainerRef.createEmbeddedView(content, context);\n        _viewRef.markForCheck();\n        return new ContentRef([_viewRef.rootNodes], _viewRef);\n      }\n      const viewRef = content.createEmbeddedView({});\n      this._applicationRef.attachView(viewRef);\n      return new ContentRef([viewRef.rootNodes], viewRef);\n    }\n    if (typeof content === 'function') {\n      const contentCmptFactory = this._componentFactoryResolver.resolveComponentFactory(content);\n      const modalContentInjector = Injector.create({\n        providers: this._providers,\n        parent: this._injector\n      });\n      const componentRef = contentCmptFactory.create(modalContentInjector);\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      Object.assign(componentRef.instance, initialState);\n      this._applicationRef.attachView(componentRef.hostView);\n      return new ContentRef([[componentRef.location.nativeElement]], componentRef.hostView, componentRef);\n    }\n    const nodes = this._renderer ? [this._renderer.createText(`${content}`)] : [];\n    return new ContentRef([nodes]);\n  }\n}\nclass ComponentLoaderFactory {\n  constructor(_componentFactoryResolver, _ngZone, _injector, _posService, _applicationRef, _document) {\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._ngZone = _ngZone;\n    this._injector = _injector;\n    this._posService = _posService;\n    this._applicationRef = _applicationRef;\n    this._document = _document;\n  }\n  /**\n   *\n   * @param _elementRef\n   * @param _viewContainerRef\n   * @param _renderer\n   */\n  createLoader(_elementRef, _viewContainerRef, _renderer) {\n    return new ComponentLoader(_viewContainerRef, _renderer, _elementRef, this._injector, this._componentFactoryResolver, this._ngZone, this._applicationRef, this._posService, this._document);\n  }\n}\nComponentLoaderFactory.ɵfac = function ComponentLoaderFactory_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ComponentLoaderFactory)(i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i1.PositioningService), i0.ɵɵinject(i0.ApplicationRef), i0.ɵɵinject(DOCUMENT));\n};\nComponentLoaderFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ComponentLoaderFactory,\n  factory: ComponentLoaderFactory.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ComponentLoaderFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.Injector\n    }, {\n      type: i1.PositioningService\n    }, {\n      type: i0.ApplicationRef\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsComponentRef, ComponentLoader, ComponentLoaderFactory, ContentRef };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Injector", "ElementRef", "TemplateRef", "Injectable", "Inject", "listenToTriggersV2", "registerOutsideClick", "registerEscClick", "i1", "DOCUMENT", "BsComponentRef", "ContentRef", "constructor", "nodes", "viewRef", "componentRef", "ComponentLoader", "_viewContainerRef", "_renderer", "_elementRef", "_injector", "_componentFactoryResolver", "_ngZone", "_applicationRef", "_posService", "_document", "onBeforeShow", "onShown", "onBeforeHide", "onHidden", "_providers", "_isHiding", "containerDefaultSelector", "_listenOpts", "_globalListener", "Function", "prototype", "isShown", "_componentRef", "attach", "compType", "_componentFactory", "resolveComponentFactory", "to", "container", "position", "opts", "attachment", "target", "provide", "provider", "push", "show", "_subscribePositioning", "_innerComponent", "emit", "_contentRef", "_getContentRef", "content", "context", "initialState", "injector", "create", "providers", "parent", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "instance", "Object", "assign", "nativeElement", "append<PERSON><PERSON><PERSON>", "location", "selectedElement", "querySelector", "parentElement", "changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "id", "_registerOutsideClick", "hide", "deletePositionElement", "componentEl", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "remove", "indexOf", "_removeGlobalListener", "toggle", "dispose", "_unsubscribePositioning", "_unregisterListenersFn", "listen", "listenOpts", "triggers", "outsideClick", "outsideEsc", "registerHide", "attachInline", "vRef", "template", "_inlineViewRef", "createEmbeddedView", "setTimeout", "targets", "getInnerComponent", "_zoneSubscription", "subscribe", "element", "appendToBody", "onStable", "calcPosition", "unsubscribe", "_viewRef", "rootNodes", "contentCmptFactory", "modalContentInjector", "createText", "ComponentLoaderFactory", "createLoader", "ɵfac", "ComponentLoaderFactory_Factory", "__ngFactoryType__", "ɵɵinject", "ComponentFactoryResolver", "NgZone", "PositioningService", "ApplicationRef", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "Document", "decorators"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/ngx-bootstrap/component-loader/fesm2020/ngx-bootstrap-component-loader.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Injector, ElementRef, TemplateRef, Injectable, Inject } from '@angular/core';\nimport { listenToTriggersV2, registerOutsideClick, registerEscClick } from 'ngx-bootstrap/utils';\nimport * as i1 from 'ngx-bootstrap/positioning';\nimport { DOCUMENT } from '@angular/common';\n\nclass BsComponentRef {\n}\n\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\nclass ContentRef {\n    constructor(\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    nodes, viewRef, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    componentRef) {\n        this.nodes = nodes;\n        this.viewRef = viewRef;\n        this.componentRef = componentRef;\n    }\n}\n\n// todo: add delay support\nclass ComponentLoader {\n    /**\n     * Do not use this directly, it should be instanced via\n     * `ComponentLoadFactory.attach`\n     * @internal\n     */\n    constructor(_viewContainerRef, _renderer, _elementRef, _injector, _componentFactoryResolver, _ngZone, _applicationRef, _posService, _document) {\n        this._viewContainerRef = _viewContainerRef;\n        this._renderer = _renderer;\n        this._elementRef = _elementRef;\n        this._injector = _injector;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._ngZone = _ngZone;\n        this._applicationRef = _applicationRef;\n        this._posService = _posService;\n        this._document = _document;\n        this.onBeforeShow = new EventEmitter();\n        this.onShown = new EventEmitter();\n        this.onBeforeHide = new EventEmitter();\n        this.onHidden = new EventEmitter();\n        this._providers = [];\n        this._isHiding = false;\n        /**\n         * A selector used if container element was not found\n         */\n        this.containerDefaultSelector = 'body';\n        this._listenOpts = {};\n        this._globalListener = Function.prototype;\n    }\n    get isShown() {\n        if (this._isHiding) {\n            return false;\n        }\n        return !!this._componentRef;\n    }\n    attach(compType) {\n        this._componentFactory = this._componentFactoryResolver\n            .resolveComponentFactory(compType);\n        return this;\n    }\n    // todo: add behaviour: to target element, `body`, custom element\n    to(container) {\n        this.container = container || this.container;\n        return this;\n    }\n    position(opts) {\n        if (!opts) {\n            return this;\n        }\n        this.attachment = opts.attachment || this.attachment;\n        this._elementRef = opts.target || this._elementRef;\n        return this;\n    }\n    provide(provider) {\n        this._providers.push(provider);\n        return this;\n    }\n    // todo: appendChild to element or document.querySelector(this.container)\n    show(opts = {}) {\n        this._subscribePositioning();\n        this._innerComponent = void 0;\n        if (!this._componentRef) {\n            this.onBeforeShow.emit();\n            this._contentRef = this._getContentRef(opts.content, opts.context, opts.initialState);\n            const injector = Injector.create({\n                providers: this._providers,\n                parent: this._injector\n            });\n            if (!this._componentFactory) {\n                return;\n            }\n            this._componentRef = this._componentFactory.create(injector, this._contentRef.nodes);\n            this._applicationRef.attachView(this._componentRef.hostView);\n            // this._componentRef = this._viewContainerRef\n            //   .createComponent(this._componentFactory, 0, injector, this._contentRef.nodes);\n            this.instance = this._componentRef.instance;\n            Object.assign(this._componentRef.instance, opts);\n            if (this.container instanceof ElementRef) {\n                this.container.nativeElement.appendChild(this._componentRef.location.nativeElement);\n            }\n            if (typeof this.container === 'string' && typeof this._document !== 'undefined') {\n                const selectedElement = this._document.querySelector(this.container) ||\n                    this._document.querySelector(this.containerDefaultSelector);\n                if (!selectedElement) {\n                    return;\n                }\n                selectedElement.appendChild(this._componentRef.location.nativeElement);\n            }\n            if (!this.container &&\n                this._elementRef &&\n                this._elementRef.nativeElement.parentElement) {\n                this._elementRef.nativeElement.parentElement.appendChild(this._componentRef.location.nativeElement);\n            }\n            // we need to manually invoke change detection since events registered\n            // via\n            // Renderer::listen() are not picked up by change detection with the\n            // OnPush strategy\n            if (this._contentRef.componentRef) {\n                this._innerComponent = this._contentRef.componentRef.instance;\n                this._contentRef.componentRef.changeDetectorRef.markForCheck();\n                this._contentRef.componentRef.changeDetectorRef.detectChanges();\n            }\n            this._componentRef.changeDetectorRef.markForCheck();\n            this._componentRef.changeDetectorRef.detectChanges();\n            this.onShown.emit(opts.id ? { id: opts.id } : this._componentRef.instance);\n        }\n        this._registerOutsideClick();\n        return this._componentRef;\n    }\n    hide(id) {\n        if (!this._componentRef) {\n            return this;\n        }\n        this._posService.deletePositionElement(this._componentRef.location);\n        this.onBeforeHide.emit(this._componentRef.instance);\n        const componentEl = this._componentRef.location.nativeElement;\n        componentEl.parentNode?.removeChild(componentEl);\n        this._contentRef?.componentRef?.destroy();\n        if (this._viewContainerRef && this._contentRef?.viewRef) {\n            this._viewContainerRef.remove(this._viewContainerRef.indexOf(this._contentRef.viewRef));\n        }\n        this._contentRef?.viewRef?.destroy();\n        this._contentRef = void 0;\n        this._componentRef = void 0;\n        this._removeGlobalListener();\n        this.onHidden.emit(id ? { id } : null);\n        return this;\n    }\n    toggle() {\n        if (this.isShown) {\n            this.hide();\n            return;\n        }\n        this.show();\n    }\n    dispose() {\n        if (this.isShown) {\n            this.hide();\n        }\n        this._unsubscribePositioning();\n        if (this._unregisterListenersFn) {\n            this._unregisterListenersFn();\n        }\n    }\n    listen(listenOpts) {\n        this.triggers = listenOpts.triggers || this.triggers;\n        this._listenOpts.outsideClick = listenOpts.outsideClick;\n        this._listenOpts.outsideEsc = listenOpts.outsideEsc;\n        listenOpts.target = listenOpts.target || this._elementRef?.nativeElement;\n        const hide = (this._listenOpts.hide = () => listenOpts.hide ? listenOpts.hide() : void this.hide());\n        const show = (this._listenOpts.show = (registerHide) => {\n            listenOpts.show ? listenOpts.show(registerHide) : this.show(registerHide);\n            registerHide();\n        });\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const toggle = (registerHide) => {\n            this.isShown ? hide() : show(registerHide);\n        };\n        if (this._renderer) {\n            this._unregisterListenersFn = listenToTriggersV2(this._renderer, {\n                target: listenOpts.target,\n                triggers: listenOpts.triggers,\n                show,\n                hide,\n                toggle\n            });\n        }\n        return this;\n    }\n    _removeGlobalListener() {\n        if (this._globalListener) {\n            this._globalListener();\n            this._globalListener = Function.prototype;\n        }\n    }\n    attachInline(vRef, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    template) {\n        if (vRef && template) {\n            this._inlineViewRef = vRef.createEmbeddedView(template);\n        }\n        return this;\n    }\n    _registerOutsideClick() {\n        if (!this._componentRef || !this._componentRef.location) {\n            return;\n        }\n        // why: should run after first event bubble\n        if (this._listenOpts.outsideClick) {\n            const target = this._componentRef.location.nativeElement;\n            setTimeout(() => {\n                if (this._renderer && this._elementRef) {\n                    this._globalListener = registerOutsideClick(this._renderer, {\n                        targets: [target, this._elementRef.nativeElement],\n                        outsideClick: this._listenOpts.outsideClick,\n                        hide: () => this._listenOpts.hide && this._listenOpts.hide()\n                    });\n                }\n            });\n        }\n        if (this._listenOpts.outsideEsc && this._renderer && this._elementRef) {\n            const target = this._componentRef.location.nativeElement;\n            this._globalListener = registerEscClick(this._renderer, {\n                targets: [target, this._elementRef.nativeElement],\n                outsideEsc: this._listenOpts.outsideEsc,\n                hide: () => this._listenOpts.hide && this._listenOpts.hide()\n            });\n        }\n    }\n    getInnerComponent() {\n        return this._innerComponent;\n    }\n    _subscribePositioning() {\n        if (this._zoneSubscription || !this.attachment) {\n            return;\n        }\n        this.onShown.subscribe(() => {\n            this._posService.position({\n                element: this._componentRef?.location,\n                target: this._elementRef,\n                attachment: this.attachment,\n                appendToBody: this.container === 'body'\n            });\n        });\n        this._zoneSubscription = this._ngZone.onStable.subscribe(() => {\n            if (!this._componentRef) {\n                return;\n            }\n            this._posService.calcPosition();\n        });\n    }\n    _unsubscribePositioning() {\n        if (!this._zoneSubscription) {\n            return;\n        }\n        this._zoneSubscription.unsubscribe();\n        this._zoneSubscription = void 0;\n    }\n    _getContentRef(\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    content, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    context, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    initialState) {\n        if (!content) {\n            return new ContentRef([]);\n        }\n        if (content instanceof TemplateRef) {\n            if (this._viewContainerRef) {\n                const _viewRef = this._viewContainerRef\n                    .createEmbeddedView(content, context);\n                _viewRef.markForCheck();\n                return new ContentRef([_viewRef.rootNodes], _viewRef);\n            }\n            const viewRef = content.createEmbeddedView({});\n            this._applicationRef.attachView(viewRef);\n            return new ContentRef([viewRef.rootNodes], viewRef);\n        }\n        if (typeof content === 'function') {\n            const contentCmptFactory = this._componentFactoryResolver.resolveComponentFactory(content);\n            const modalContentInjector = Injector.create({\n                providers: this._providers,\n                parent: this._injector\n            });\n            const componentRef = contentCmptFactory.create(modalContentInjector);\n            // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n            // @ts-ignore\n            Object.assign(componentRef.instance, initialState);\n            this._applicationRef.attachView(componentRef.hostView);\n            return new ContentRef([[componentRef.location.nativeElement]], componentRef.hostView, componentRef);\n        }\n        const nodes = this._renderer\n            ? [this._renderer.createText(`${content}`)]\n            : [];\n        return new ContentRef([nodes]);\n    }\n}\n\nclass ComponentLoaderFactory {\n    constructor(_componentFactoryResolver, _ngZone, _injector, _posService, _applicationRef, _document) {\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._ngZone = _ngZone;\n        this._injector = _injector;\n        this._posService = _posService;\n        this._applicationRef = _applicationRef;\n        this._document = _document;\n    }\n    /**\n     *\n     * @param _elementRef\n     * @param _viewContainerRef\n     * @param _renderer\n     */\n    createLoader(_elementRef, _viewContainerRef, _renderer) {\n        return new ComponentLoader(_viewContainerRef, _renderer, _elementRef, this._injector, this._componentFactoryResolver, this._ngZone, this._applicationRef, this._posService, this._document);\n    }\n}\nComponentLoaderFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ComponentLoaderFactory, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.NgZone }, { token: i0.Injector }, { token: i1.PositioningService }, { token: i0.ApplicationRef }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nComponentLoaderFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ComponentLoaderFactory, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ComponentLoaderFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.ComponentFactoryResolver }, { type: i0.NgZone }, { type: i0.Injector }, { type: i1.PositioningService }, { type: i0.ApplicationRef }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsComponentRef, ComponentLoader, ComponentLoaderFactory, ContentRef };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AACnG,SAASC,kBAAkB,EAAEC,oBAAoB,EAAEC,gBAAgB,QAAQ,qBAAqB;AAChG,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,QAAQ,QAAQ,iBAAiB;AAE1C,MAAMC,cAAc,CAAC;;AAGrB;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,WAAWA;EACX;EACAC,KAAK,EAAEC,OAAO;EACd;EACAC,YAAY,EAAE;IACV,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,YAAY,GAAGA,YAAY;EACpC;AACJ;;AAEA;AACA,MAAMC,eAAe,CAAC;EAClB;AACJ;AACA;AACA;AACA;EACIJ,WAAWA,CAACK,iBAAiB,EAAEC,SAAS,EAAEC,WAAW,EAAEC,SAAS,EAAEC,yBAAyB,EAAEC,OAAO,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,EAAE;IAC3I,IAAI,CAACR,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,YAAY,GAAG,IAAI3B,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC4B,OAAO,GAAG,IAAI5B,YAAY,CAAC,CAAC;IACjC,IAAI,CAAC6B,YAAY,GAAG,IAAI7B,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC8B,QAAQ,GAAG,IAAI9B,YAAY,CAAC,CAAC;IAClC,IAAI,CAAC+B,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,MAAM;IACtC,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,eAAe,GAAGC,QAAQ,CAACC,SAAS;EAC7C;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,IAAI,IAAI,CAACN,SAAS,EAAE;MAChB,OAAO,KAAK;IAChB;IACA,OAAO,CAAC,CAAC,IAAI,CAACO,aAAa;EAC/B;EACAC,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACpB,yBAAyB,CAClDqB,uBAAuB,CAACF,QAAQ,CAAC;IACtC,OAAO,IAAI;EACf;EACA;EACAG,EAAEA,CAACC,SAAS,EAAE;IACV,IAAI,CAACA,SAAS,GAAGA,SAAS,IAAI,IAAI,CAACA,SAAS;IAC5C,OAAO,IAAI;EACf;EACAC,QAAQA,CAACC,IAAI,EAAE;IACX,IAAI,CAACA,IAAI,EAAE;MACP,OAAO,IAAI;IACf;IACA,IAAI,CAACC,UAAU,GAAGD,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU;IACpD,IAAI,CAAC5B,WAAW,GAAG2B,IAAI,CAACE,MAAM,IAAI,IAAI,CAAC7B,WAAW;IAClD,OAAO,IAAI;EACf;EACA8B,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAACpB,UAAU,CAACqB,IAAI,CAACD,QAAQ,CAAC;IAC9B,OAAO,IAAI;EACf;EACA;EACAE,IAAIA,CAACN,IAAI,GAAG,CAAC,CAAC,EAAE;IACZ,IAAI,CAACO,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,eAAe,GAAG,KAAK,CAAC;IAC7B,IAAI,CAAC,IAAI,CAAChB,aAAa,EAAE;MACrB,IAAI,CAACZ,YAAY,CAAC6B,IAAI,CAAC,CAAC;MACxB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,cAAc,CAACX,IAAI,CAACY,OAAO,EAAEZ,IAAI,CAACa,OAAO,EAAEb,IAAI,CAACc,YAAY,CAAC;MACrF,MAAMC,QAAQ,GAAG7D,QAAQ,CAAC8D,MAAM,CAAC;QAC7BC,SAAS,EAAE,IAAI,CAACjC,UAAU;QAC1BkC,MAAM,EAAE,IAAI,CAAC5C;MACjB,CAAC,CAAC;MACF,IAAI,CAAC,IAAI,CAACqB,iBAAiB,EAAE;QACzB;MACJ;MACA,IAAI,CAACH,aAAa,GAAG,IAAI,CAACG,iBAAiB,CAACqB,MAAM,CAACD,QAAQ,EAAE,IAAI,CAACL,WAAW,CAAC3C,KAAK,CAAC;MACpF,IAAI,CAACU,eAAe,CAAC0C,UAAU,CAAC,IAAI,CAAC3B,aAAa,CAAC4B,QAAQ,CAAC;MAC5D;MACA;MACA,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAC7B,aAAa,CAAC6B,QAAQ;MAC3CC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC/B,aAAa,CAAC6B,QAAQ,EAAErB,IAAI,CAAC;MAChD,IAAI,IAAI,CAACF,SAAS,YAAY3C,UAAU,EAAE;QACtC,IAAI,CAAC2C,SAAS,CAAC0B,aAAa,CAACC,WAAW,CAAC,IAAI,CAACjC,aAAa,CAACkC,QAAQ,CAACF,aAAa,CAAC;MACvF;MACA,IAAI,OAAO,IAAI,CAAC1B,SAAS,KAAK,QAAQ,IAAI,OAAO,IAAI,CAACnB,SAAS,KAAK,WAAW,EAAE;QAC7E,MAAMgD,eAAe,GAAG,IAAI,CAAChD,SAAS,CAACiD,aAAa,CAAC,IAAI,CAAC9B,SAAS,CAAC,IAChE,IAAI,CAACnB,SAAS,CAACiD,aAAa,CAAC,IAAI,CAAC1C,wBAAwB,CAAC;QAC/D,IAAI,CAACyC,eAAe,EAAE;UAClB;QACJ;QACAA,eAAe,CAACF,WAAW,CAAC,IAAI,CAACjC,aAAa,CAACkC,QAAQ,CAACF,aAAa,CAAC;MAC1E;MACA,IAAI,CAAC,IAAI,CAAC1B,SAAS,IACf,IAAI,CAACzB,WAAW,IAChB,IAAI,CAACA,WAAW,CAACmD,aAAa,CAACK,aAAa,EAAE;QAC9C,IAAI,CAACxD,WAAW,CAACmD,aAAa,CAACK,aAAa,CAACJ,WAAW,CAAC,IAAI,CAACjC,aAAa,CAACkC,QAAQ,CAACF,aAAa,CAAC;MACvG;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACd,WAAW,CAACzC,YAAY,EAAE;QAC/B,IAAI,CAACuC,eAAe,GAAG,IAAI,CAACE,WAAW,CAACzC,YAAY,CAACoD,QAAQ;QAC7D,IAAI,CAACX,WAAW,CAACzC,YAAY,CAAC6D,iBAAiB,CAACC,YAAY,CAAC,CAAC;QAC9D,IAAI,CAACrB,WAAW,CAACzC,YAAY,CAAC6D,iBAAiB,CAACE,aAAa,CAAC,CAAC;MACnE;MACA,IAAI,CAACxC,aAAa,CAACsC,iBAAiB,CAACC,YAAY,CAAC,CAAC;MACnD,IAAI,CAACvC,aAAa,CAACsC,iBAAiB,CAACE,aAAa,CAAC,CAAC;MACpD,IAAI,CAACnD,OAAO,CAAC4B,IAAI,CAACT,IAAI,CAACiC,EAAE,GAAG;QAAEA,EAAE,EAAEjC,IAAI,CAACiC;MAAG,CAAC,GAAG,IAAI,CAACzC,aAAa,CAAC6B,QAAQ,CAAC;IAC9E;IACA,IAAI,CAACa,qBAAqB,CAAC,CAAC;IAC5B,OAAO,IAAI,CAAC1C,aAAa;EAC7B;EACA2C,IAAIA,CAACF,EAAE,EAAE;IACL,IAAI,CAAC,IAAI,CAACzC,aAAa,EAAE;MACrB,OAAO,IAAI;IACf;IACA,IAAI,CAACd,WAAW,CAAC0D,qBAAqB,CAAC,IAAI,CAAC5C,aAAa,CAACkC,QAAQ,CAAC;IACnE,IAAI,CAAC5C,YAAY,CAAC2B,IAAI,CAAC,IAAI,CAACjB,aAAa,CAAC6B,QAAQ,CAAC;IACnD,MAAMgB,WAAW,GAAG,IAAI,CAAC7C,aAAa,CAACkC,QAAQ,CAACF,aAAa;IAC7Da,WAAW,CAACC,UAAU,EAAEC,WAAW,CAACF,WAAW,CAAC;IAChD,IAAI,CAAC3B,WAAW,EAAEzC,YAAY,EAAEuE,OAAO,CAAC,CAAC;IACzC,IAAI,IAAI,CAACrE,iBAAiB,IAAI,IAAI,CAACuC,WAAW,EAAE1C,OAAO,EAAE;MACrD,IAAI,CAACG,iBAAiB,CAACsE,MAAM,CAAC,IAAI,CAACtE,iBAAiB,CAACuE,OAAO,CAAC,IAAI,CAAChC,WAAW,CAAC1C,OAAO,CAAC,CAAC;IAC3F;IACA,IAAI,CAAC0C,WAAW,EAAE1C,OAAO,EAAEwE,OAAO,CAAC,CAAC;IACpC,IAAI,CAAC9B,WAAW,GAAG,KAAK,CAAC;IACzB,IAAI,CAAClB,aAAa,GAAG,KAAK,CAAC;IAC3B,IAAI,CAACmD,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAAC5D,QAAQ,CAAC0B,IAAI,CAACwB,EAAE,GAAG;MAAEA;IAAG,CAAC,GAAG,IAAI,CAAC;IACtC,OAAO,IAAI;EACf;EACAW,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACrD,OAAO,EAAE;MACd,IAAI,CAAC4C,IAAI,CAAC,CAAC;MACX;IACJ;IACA,IAAI,CAAC7B,IAAI,CAAC,CAAC;EACf;EACAuC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACtD,OAAO,EAAE;MACd,IAAI,CAAC4C,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAACW,uBAAuB,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACC,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAC,MAAMA,CAACC,UAAU,EAAE;IACf,IAAI,CAACC,QAAQ,GAAGD,UAAU,CAACC,QAAQ,IAAI,IAAI,CAACA,QAAQ;IACpD,IAAI,CAAC/D,WAAW,CAACgE,YAAY,GAAGF,UAAU,CAACE,YAAY;IACvD,IAAI,CAAChE,WAAW,CAACiE,UAAU,GAAGH,UAAU,CAACG,UAAU;IACnDH,UAAU,CAAC/C,MAAM,GAAG+C,UAAU,CAAC/C,MAAM,IAAI,IAAI,CAAC7B,WAAW,EAAEmD,aAAa;IACxE,MAAMW,IAAI,GAAI,IAAI,CAAChD,WAAW,CAACgD,IAAI,GAAG,MAAMc,UAAU,CAACd,IAAI,GAAGc,UAAU,CAACd,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,CAACA,IAAI,CAAC,CAAE;IACnG,MAAM7B,IAAI,GAAI,IAAI,CAACnB,WAAW,CAACmB,IAAI,GAAI+C,YAAY,IAAK;MACpDJ,UAAU,CAAC3C,IAAI,GAAG2C,UAAU,CAAC3C,IAAI,CAAC+C,YAAY,CAAC,GAAG,IAAI,CAAC/C,IAAI,CAAC+C,YAAY,CAAC;MACzEA,YAAY,CAAC,CAAC;IAClB,CAAE;IACF;IACA,MAAMT,MAAM,GAAIS,YAAY,IAAK;MAC7B,IAAI,CAAC9D,OAAO,GAAG4C,IAAI,CAAC,CAAC,GAAG7B,IAAI,CAAC+C,YAAY,CAAC;IAC9C,CAAC;IACD,IAAI,IAAI,CAACjF,SAAS,EAAE;MAChB,IAAI,CAAC2E,sBAAsB,GAAGxF,kBAAkB,CAAC,IAAI,CAACa,SAAS,EAAE;QAC7D8B,MAAM,EAAE+C,UAAU,CAAC/C,MAAM;QACzBgD,QAAQ,EAAED,UAAU,CAACC,QAAQ;QAC7B5C,IAAI;QACJ6B,IAAI;QACJS;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI;EACf;EACAD,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACvD,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC,CAAC;MACtB,IAAI,CAACA,eAAe,GAAGC,QAAQ,CAACC,SAAS;IAC7C;EACJ;EACAgE,YAAYA,CAACC,IAAI;EACjB;EACAC,QAAQ,EAAE;IACN,IAAID,IAAI,IAAIC,QAAQ,EAAE;MAClB,IAAI,CAACC,cAAc,GAAGF,IAAI,CAACG,kBAAkB,CAACF,QAAQ,CAAC;IAC3D;IACA,OAAO,IAAI;EACf;EACAtB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAAC1C,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACkC,QAAQ,EAAE;MACrD;IACJ;IACA;IACA,IAAI,IAAI,CAACvC,WAAW,CAACgE,YAAY,EAAE;MAC/B,MAAMjD,MAAM,GAAG,IAAI,CAACV,aAAa,CAACkC,QAAQ,CAACF,aAAa;MACxDmC,UAAU,CAAC,MAAM;QACb,IAAI,IAAI,CAACvF,SAAS,IAAI,IAAI,CAACC,WAAW,EAAE;UACpC,IAAI,CAACe,eAAe,GAAG5B,oBAAoB,CAAC,IAAI,CAACY,SAAS,EAAE;YACxDwF,OAAO,EAAE,CAAC1D,MAAM,EAAE,IAAI,CAAC7B,WAAW,CAACmD,aAAa,CAAC;YACjD2B,YAAY,EAAE,IAAI,CAAChE,WAAW,CAACgE,YAAY;YAC3ChB,IAAI,EAAEA,CAAA,KAAM,IAAI,CAAChD,WAAW,CAACgD,IAAI,IAAI,IAAI,CAAChD,WAAW,CAACgD,IAAI,CAAC;UAC/D,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAChD,WAAW,CAACiE,UAAU,IAAI,IAAI,CAAChF,SAAS,IAAI,IAAI,CAACC,WAAW,EAAE;MACnE,MAAM6B,MAAM,GAAG,IAAI,CAACV,aAAa,CAACkC,QAAQ,CAACF,aAAa;MACxD,IAAI,CAACpC,eAAe,GAAG3B,gBAAgB,CAAC,IAAI,CAACW,SAAS,EAAE;QACpDwF,OAAO,EAAE,CAAC1D,MAAM,EAAE,IAAI,CAAC7B,WAAW,CAACmD,aAAa,CAAC;QACjD4B,UAAU,EAAE,IAAI,CAACjE,WAAW,CAACiE,UAAU;QACvCjB,IAAI,EAAEA,CAAA,KAAM,IAAI,CAAChD,WAAW,CAACgD,IAAI,IAAI,IAAI,CAAChD,WAAW,CAACgD,IAAI,CAAC;MAC/D,CAAC,CAAC;IACN;EACJ;EACA0B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACrD,eAAe;EAC/B;EACAD,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACuD,iBAAiB,IAAI,CAAC,IAAI,CAAC7D,UAAU,EAAE;MAC5C;IACJ;IACA,IAAI,CAACpB,OAAO,CAACkF,SAAS,CAAC,MAAM;MACzB,IAAI,CAACrF,WAAW,CAACqB,QAAQ,CAAC;QACtBiE,OAAO,EAAE,IAAI,CAACxE,aAAa,EAAEkC,QAAQ;QACrCxB,MAAM,EAAE,IAAI,CAAC7B,WAAW;QACxB4B,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BgE,YAAY,EAAE,IAAI,CAACnE,SAAS,KAAK;MACrC,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACgE,iBAAiB,GAAG,IAAI,CAACtF,OAAO,CAAC0F,QAAQ,CAACH,SAAS,CAAC,MAAM;MAC3D,IAAI,CAAC,IAAI,CAACvE,aAAa,EAAE;QACrB;MACJ;MACA,IAAI,CAACd,WAAW,CAACyF,YAAY,CAAC,CAAC;IACnC,CAAC,CAAC;EACN;EACArB,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACgB,iBAAiB,EAAE;MACzB;IACJ;IACA,IAAI,CAACA,iBAAiB,CAACM,WAAW,CAAC,CAAC;IACpC,IAAI,CAACN,iBAAiB,GAAG,KAAK,CAAC;EACnC;EACAnD,cAAcA;EACd;EACAC,OAAO;EACP;EACAC,OAAO;EACP;EACAC,YAAY,EAAE;IACV,IAAI,CAACF,OAAO,EAAE;MACV,OAAO,IAAI/C,UAAU,CAAC,EAAE,CAAC;IAC7B;IACA,IAAI+C,OAAO,YAAYxD,WAAW,EAAE;MAChC,IAAI,IAAI,CAACe,iBAAiB,EAAE;QACxB,MAAMkG,QAAQ,GAAG,IAAI,CAAClG,iBAAiB,CAClCuF,kBAAkB,CAAC9C,OAAO,EAAEC,OAAO,CAAC;QACzCwD,QAAQ,CAACtC,YAAY,CAAC,CAAC;QACvB,OAAO,IAAIlE,UAAU,CAAC,CAACwG,QAAQ,CAACC,SAAS,CAAC,EAAED,QAAQ,CAAC;MACzD;MACA,MAAMrG,OAAO,GAAG4C,OAAO,CAAC8C,kBAAkB,CAAC,CAAC,CAAC,CAAC;MAC9C,IAAI,CAACjF,eAAe,CAAC0C,UAAU,CAACnD,OAAO,CAAC;MACxC,OAAO,IAAIH,UAAU,CAAC,CAACG,OAAO,CAACsG,SAAS,CAAC,EAAEtG,OAAO,CAAC;IACvD;IACA,IAAI,OAAO4C,OAAO,KAAK,UAAU,EAAE;MAC/B,MAAM2D,kBAAkB,GAAG,IAAI,CAAChG,yBAAyB,CAACqB,uBAAuB,CAACgB,OAAO,CAAC;MAC1F,MAAM4D,oBAAoB,GAAGtH,QAAQ,CAAC8D,MAAM,CAAC;QACzCC,SAAS,EAAE,IAAI,CAACjC,UAAU;QAC1BkC,MAAM,EAAE,IAAI,CAAC5C;MACjB,CAAC,CAAC;MACF,MAAML,YAAY,GAAGsG,kBAAkB,CAACvD,MAAM,CAACwD,oBAAoB,CAAC;MACpE;MACA;MACAlD,MAAM,CAACC,MAAM,CAACtD,YAAY,CAACoD,QAAQ,EAAEP,YAAY,CAAC;MAClD,IAAI,CAACrC,eAAe,CAAC0C,UAAU,CAAClD,YAAY,CAACmD,QAAQ,CAAC;MACtD,OAAO,IAAIvD,UAAU,CAAC,CAAC,CAACI,YAAY,CAACyD,QAAQ,CAACF,aAAa,CAAC,CAAC,EAAEvD,YAAY,CAACmD,QAAQ,EAAEnD,YAAY,CAAC;IACvG;IACA,MAAMF,KAAK,GAAG,IAAI,CAACK,SAAS,GACtB,CAAC,IAAI,CAACA,SAAS,CAACqG,UAAU,CAAC,GAAG7D,OAAO,EAAE,CAAC,CAAC,GACzC,EAAE;IACR,OAAO,IAAI/C,UAAU,CAAC,CAACE,KAAK,CAAC,CAAC;EAClC;AACJ;AAEA,MAAM2G,sBAAsB,CAAC;EACzB5G,WAAWA,CAACS,yBAAyB,EAAEC,OAAO,EAAEF,SAAS,EAAEI,WAAW,EAAED,eAAe,EAAEE,SAAS,EAAE;IAChG,IAAI,CAACJ,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACE,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgG,YAAYA,CAACtG,WAAW,EAAEF,iBAAiB,EAAEC,SAAS,EAAE;IACpD,OAAO,IAAIF,eAAe,CAACC,iBAAiB,EAAEC,SAAS,EAAEC,WAAW,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,yBAAyB,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,eAAe,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,SAAS,CAAC;EAC/L;AACJ;AACA+F,sBAAsB,CAACE,IAAI,YAAAC,+BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAwFJ,sBAAsB,EAAhC1H,EAAE,CAAA+H,QAAA,CAAgD/H,EAAE,CAACgI,wBAAwB,GAA7EhI,EAAE,CAAA+H,QAAA,CAAwF/H,EAAE,CAACiI,MAAM,GAAnGjI,EAAE,CAAA+H,QAAA,CAA8G/H,EAAE,CAACE,QAAQ,GAA3HF,EAAE,CAAA+H,QAAA,CAAsIrH,EAAE,CAACwH,kBAAkB,GAA7JlI,EAAE,CAAA+H,QAAA,CAAwK/H,EAAE,CAACmI,cAAc,GAA3LnI,EAAE,CAAA+H,QAAA,CAAsMpH,QAAQ;AAAA,CAA6C;AACtW+G,sBAAsB,CAACU,KAAK,kBAD6EpI,EAAE,CAAAqI,kBAAA;EAAAC,KAAA,EACYZ,sBAAsB;EAAAa,OAAA,EAAtBb,sBAAsB,CAAAE,IAAA;EAAAY,UAAA,EAAc;AAAM,EAAG;AACpK;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFyGzI,EAAE,CAAA0I,iBAAA,CAEhBhB,sBAAsB,EAAc,CAAC;IACpHiB,IAAI,EAAEtI,UAAU;IAChBuI,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE3I,EAAE,CAACgI;IAAyB,CAAC,EAAE;MAAEW,IAAI,EAAE3I,EAAE,CAACiI;IAAO,CAAC,EAAE;MAAEU,IAAI,EAAE3I,EAAE,CAACE;IAAS,CAAC,EAAE;MAAEyI,IAAI,EAAEjI,EAAE,CAACwH;IAAmB,CAAC,EAAE;MAAES,IAAI,EAAE3I,EAAE,CAACmI;IAAe,CAAC,EAAE;MAAEQ,IAAI,EAAEE,QAAQ;MAAEC,UAAU,EAAE,CAAC;QAC9MH,IAAI,EAAErI,MAAM;QACZsI,IAAI,EAAE,CAACjI,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;;AAEA,SAASC,cAAc,EAAEM,eAAe,EAAEwG,sBAAsB,EAAE7G,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}