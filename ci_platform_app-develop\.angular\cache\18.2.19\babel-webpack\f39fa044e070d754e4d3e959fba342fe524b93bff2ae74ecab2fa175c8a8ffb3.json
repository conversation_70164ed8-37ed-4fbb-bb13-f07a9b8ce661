{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { Component } from '@angular/core';\nimport { NgxGalleryAnimation, NgxGalleryModule } from '@kolkov/ngx-gallery';\nimport * as moment from 'moment';\nimport { APP_CONFIG } from '../../configs/environment.config';\nimport { FooterComponent } from '../footer/footer.component';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { SearchingSortingComponent } from '../searching-sorting/searching-sorting.component';\nimport { NavbarComponent } from '../navbar/navbar.component';\nlet VolunteeringMissionComponent = class VolunteeringMissionComponent {\n  constructor(_service, _toast, _activeRoute, _router, _datePipe, _adminservice) {\n    this._service = _service;\n    this._toast = _toast;\n    this._activeRoute = _activeRoute;\n    this._router = _router;\n    this._datePipe = _datePipe;\n    this._adminservice = _adminservice;\n    this.imageList = [];\n    this.recentVolunteerList = [];\n    this.loginUserId = 0;\n    this.btnText = 'Apply Now';\n    this.missionCommentList = [];\n    this.missionFavourite = false;\n    this.favImag = 'assets/Img/heart1.png';\n    this.favImag1 = 'assets/Img/heart11.png';\n    this.unsubscribe = [];\n    this.missionId = this._activeRoute.snapshot.paramMap.get('missionId');\n  }\n  ngOnInit() {\n    const currentUserSubscribe = this._adminservice.getCurrentUser().subscribe(data => {\n      const loginUserDetail = this._adminservice.getUserDetail();\n      data == null ? this.loginUserId = loginUserDetail.userId : this.loginUserId = data.userId;\n      data == null ? this.loginUserName = loginUserDetail.fullName : this.loginUserName = data.fullName;\n    });\n    if (this.missionId != null) {\n      this.fetchMissionDetail(this.missionId);\n    }\n    this.galleryOptions = [{\n      width: '100%',\n      height: '465px',\n      thumbnailsColumns: 4,\n      imageAnimation: NgxGalleryAnimation.Slide,\n      preview: true\n    }];\n    this.applyModal = new window.bootstrap.Modal(document.getElementById('applyMissionModal'));\n    this.getRecentVolunteerList();\n    this.unsubscribe.push(currentUserSubscribe);\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach(sb => sb.unsubscribe());\n  }\n  fetchMissionDetail(missionId) {\n    const value = {\n      missionId: missionId,\n      userId: this.loginUserId\n    };\n    const missionDetailSubscribe = this._service.missionDetailByMissionId(value).subscribe(data => {\n      if (data.result == 1) {\n        this.missionDetail = data.data;\n        this.imageList = this.missionDetail.missionImages.split(',');\n        this.galleryImages = this.getImages();\n        if (this.missionDetail.missionDocuments) {\n          this.missionDoc = this._service.imageUrl + '/' + this.missionDetail.missionDocuments;\n        }\n        this.btnText = this.missionDetail.missionApplyStatus == 'Applied' ? 'Already Apply' : 'Apply Now';\n        this.getMissionCommentList();\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration\n        });\n      }\n    }, err => this._toast.error({\n      detail: 'ERROR',\n      summary: err.message,\n      duration: APP_CONFIG.toastDuration\n    }));\n    this.unsubscribe.push(missionDetailSubscribe);\n  }\n  getImages() {\n    const imageUrls = [];\n    for (const photo of this.imageList) {\n      imageUrls.push({\n        small: this._service.imageUrl + '/' + photo.replaceAll('\\\\', '/'),\n        medium: this._service.imageUrl + '/' + photo.replaceAll('\\\\', '/'),\n        big: this._service.imageUrl + '/' + photo.replaceAll('\\\\', '/')\n      });\n    }\n    return imageUrls;\n  }\n  openApplyMissionModal(id) {\n    this.applyModal.show();\n    this.missionId = id;\n  }\n  closeApplyMissionModal() {\n    this.applyModal.hide();\n  }\n  applyMission(id) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        appliedDate: moment().format('yyyy-MM-DDTHH:mm:ssZ'),\n        status: false,\n        sheet: 1\n      };\n      const missionSubscribe = this._service.applyMission(value).subscribe(data => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data\n          });\n          setTimeout(() => {\n            this.closeApplyMissionModal();\n            this._router.navigate(['/home']);\n          }, 1000);\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration\n          });\n        }\n      }, err => this._toast.error({\n        detail: 'ERROR',\n        summary: err.message,\n        duration: APP_CONFIG.toastDuration\n      }));\n      this.unsubscribe.push(missionSubscribe);\n    }\n  }\n  postComment(commentdesc) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        CommentDescription: commentdesc,\n        commentDate: moment().format('yyyy-MM-DDTHH:mm:ssZ')\n      };\n      const missionCommentSubscribe = this._service.addMissionComment(value).subscribe(data => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data,\n            duration: APP_CONFIG.toastDuration\n          });\n          window.location.reload();\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration\n          });\n        }\n      }, err => this._toast.error({\n        detail: 'ERROR',\n        summary: err.message,\n        duration: APP_CONFIG.toastDuration\n      }));\n      this.unsubscribe.push(missionCommentSubscribe);\n    }\n  }\n  getMissionCommentList() {\n    const missionCommentSubscribe = this._service.missionCommentListByMissionId(this.missionDetail.id).subscribe(data => {\n      if (data.result == 1) {\n        this.missionCommentList = data.data;\n        this.missionCommentList = this.missionCommentList.map(x => {\n          return {\n            id: x.id,\n            commentDescription: x.commentDescription,\n            commentDate: x.commentDate ? this._datePipe.transform(x.commentDate, 'EEEE, MMMM d, y, h:mm a') : '',\n            missionId: x.missionId,\n            userId: x.userId,\n            userFullName: x.userFullName,\n            userImage: x.userImage ? this._service.imageUrl + '/' + x.userImage : 'assets/NoImg.png'\n          };\n        });\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration\n        });\n      }\n    }, err => this._toast.error({\n      detail: 'ERROR',\n      summary: err.message,\n      duration: APP_CONFIG.toastDuration\n    }));\n    this.unsubscribe.push(missionCommentSubscribe);\n  }\n  getMissionFavourite(missionId) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      this.missionFavourite = !this.missionFavourite;\n      const value = {\n        missionId: missionId,\n        userId: this.loginUserId\n      };\n      if (this.missionFavourite) {\n        const addMissionFavouriteSubscribe = this._service.addMissionFavourite(value).subscribe(data => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration\n            });\n          }\n        });\n        this.unsubscribe.push(addMissionFavouriteSubscribe);\n      } else {\n        const removeMissionFavouriteSubscribe = this._service.removeMissionFavourite(value).subscribe(data => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration\n            });\n          }\n        });\n        this.unsubscribe.push(removeMissionFavouriteSubscribe);\n      }\n    }\n  }\n  getRecentVolunteerList() {\n    const value = {\n      missionId: this.missionId,\n      userId: this.loginUserId\n    };\n    const volunteerListSubscribe = this._service.recentVolunteerList(value).subscribe(data => {\n      if (data.result == 1) {\n        this.recentVolunteerList = data.data;\n        this.recentVolunteerList = this.recentVolunteerList.map(x => {\n          return {\n            id: x.id,\n            missioId: x.missioId,\n            userId: x.userId,\n            userName: x.userName,\n            userImage: x.userImage ? this._service.imageUrl + '/' + x.userImage : 'assets/NoImg.png'\n          };\n        });\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration\n        });\n      }\n    });\n    this.unsubscribe.push(volunteerListSubscribe);\n  }\n};\nVolunteeringMissionComponent = __decorate([Component({\n  selector: 'app-volunteering-mission',\n  templateUrl: './volunteering-mission.component.html',\n  styleUrls: ['./volunteering-mission.component.css'],\n  standalone: true,\n  imports: [FooterComponent, TabsModule, NgxGalleryModule, CommonModule, SearchingSortingComponent, NavbarComponent]\n})], VolunteeringMissionComponent);\nexport { VolunteeringMissionComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "NgxGalleryAnimation", "NgxGalleryModule", "moment", "APP_CONFIG", "FooterComponent", "TabsModule", "SearchingSortingComponent", "NavbarComponent", "VolunteeringMissionComponent", "constructor", "_service", "_toast", "_activeRoute", "_router", "_datePipe", "_adminservice", "imageList", "recentVolunteerList", "loginUserId", "btnText", "missionCommentList", "missionFavourite", "favImag", "favImag1", "unsubscribe", "missionId", "snapshot", "paramMap", "get", "ngOnInit", "currentUserSubscribe", "getCurrentUser", "subscribe", "data", "loginUserDetail", "getUserDetail", "userId", "loginUserName", "fullName", "fetchMissionDetail", "galleryOptions", "width", "height", "thumbnailsColumns", "imageAnimation", "Slide", "preview", "applyModal", "window", "bootstrap", "Modal", "document", "getElementById", "getRecentVolunteerList", "push", "ngOnDestroy", "for<PERSON>ach", "sb", "value", "missionDetailSubscribe", "missionDetailByMissionId", "result", "missionDetail", "missionImages", "split", "galleryImages", "getImages", "missionDocuments", "missionDoc", "imageUrl", "missionApplyStatus", "getMissionCommentList", "error", "detail", "summary", "message", "duration", "toastDuration", "err", "imageUrls", "photo", "small", "replaceAll", "medium", "big", "openApplyMissionModal", "id", "show", "closeApplyMissionModal", "hide", "applyMission", "tokenDetail", "decodedToken", "userType", "navigate", "userImage", "warning", "appliedDate", "format", "status", "sheet", "missionSubscribe", "success", "setTimeout", "postComment", "commentdesc", "CommentDescription", "commentDate", "missionCommentSubscribe", "addMissionComment", "location", "reload", "missionCommentListByMissionId", "map", "x", "commentDescription", "transform", "userFullName", "getMissionFavourite", "addMissionFavouriteSubscribe", "addMissionFavourite", "removeMissionFavouriteSubscribe", "removeMissionFavourite", "volunteerListSubscribe", "missioId", "userName", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\components\\volunteering-mission\\volunteering-mission.component.ts"], "sourcesContent": ["import { CommonModule, DatePipe } from '@angular/common';\nimport { Component, OnDestroy, type OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport {\n  NgxGalleryOptions,\n  NgxGalleryImage,\n  NgxGalleryAnimation,\n  NgxGalleryModule,\n} from '@kolkov/ngx-gallery';\nimport * as moment from 'moment';\nimport { NgToastService } from 'ng-angular-popup';\nimport { AuthService } from '../../services/auth.service';\nimport { APP_CONFIG } from '../../configs/environment.config';\nimport { ClientMissionService } from '../../services/client-mission.service';\nimport { FooterComponent } from '../footer/footer.component';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { SearchingSortingComponent } from '../searching-sorting/searching-sorting.component';\nimport { NavbarComponent } from '../navbar/navbar.component';\nimport { Subscription } from 'rxjs';\ndeclare var window: any;\n@Component({\n  selector: 'app-volunteering-mission',\n  templateUrl: './volunteering-mission.component.html',\n  styleUrls: ['./volunteering-mission.component.css'],\n  standalone: true,\n  imports: [FooterComponent, TabsModule, NgxGalleryModule, CommonModule, SearchingSortingComponent, NavbarComponent]\n})\nexport class VolunteeringMissionComponent implements OnInit, OnDestroy {\n  galleryOptions: NgxGalleryOptions[];\n  galleryImages: NgxGalleryImage[];\n  applyModal: any;\n  missionId: any;\n  missionDetail: any;\n  imageList: any = [];\n  recentVolunteerList: any[] = [];\n  missionDoc: any;\n  loginUserId = 0;\n  loginUserName: any;\n  btnText: any = 'Apply Now';\n  missionCommentList: any[] = [];\n  missionFavourite = false;\n  favImag = 'assets/Img/heart1.png';\n  favImag1 = 'assets/Img/heart11.png';\n  private unsubscribe: Subscription[] = [];\n\n  constructor(\n    private _service: ClientMissionService,\n    private _toast: NgToastService,\n    private _activeRoute: ActivatedRoute,\n    private _router: Router,\n    private _datePipe: DatePipe,\n    private _adminservice: AuthService\n  ) {\n    this.missionId = this._activeRoute.snapshot.paramMap.get('missionId');\n  }\n\n  ngOnInit(): void {\n    const currentUserSubscribe = this._adminservice.getCurrentUser().subscribe((data: any) => {\n      const loginUserDetail = this._adminservice.getUserDetail();\n      data == null\n        ? (this.loginUserId = loginUserDetail.userId)\n        : (this.loginUserId = data.userId);\n      data == null\n        ? (this.loginUserName = loginUserDetail.fullName)\n        : (this.loginUserName = data.fullName);\n    });\n    if (this.missionId != null) {\n      this.fetchMissionDetail(this.missionId);\n    }\n    this.galleryOptions = [\n      {\n        width: '100%',\n        height: '465px',\n        thumbnailsColumns: 4,\n        imageAnimation: NgxGalleryAnimation.Slide,\n        preview: true,\n      },\n    ];\n    this.applyModal = new window.bootstrap.Modal(\n      document.getElementById('applyMissionModal')\n    );\n    this.getRecentVolunteerList();\n    this.unsubscribe.push(currentUserSubscribe);\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n  \n  fetchMissionDetail(missionId: any) {\n    const value = {\n      missionId: missionId,\n      userId: this.loginUserId,\n    };\n    const missionDetailSubscribe = this._service.missionDetailByMissionId(value).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.missionDetail = data.data;\n\n          this.imageList = this.missionDetail.missionImages.split(',');\n          this.galleryImages = this.getImages();\n          if (this.missionDetail.missionDocuments) {\n            this.missionDoc =\n              this._service.imageUrl + '/' + this.missionDetail.missionDocuments;\n          }\n          this.btnText =\n            this.missionDetail.missionApplyStatus == 'Applied'\n              ? 'Already Apply'\n              : 'Apply Now';\n          this.getMissionCommentList();\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(missionDetailSubscribe);\n  }\n\n  getImages(): NgxGalleryImage[] {\n    const imageUrls: NgxGalleryImage[] = [];\n    for (const photo of this.imageList) {\n      imageUrls.push({\n        small: this._service.imageUrl + '/' + photo.replaceAll('\\\\', '/'),\n        medium: this._service.imageUrl + '/' + photo.replaceAll('\\\\', '/'),\n        big: this._service.imageUrl + '/' + photo.replaceAll('\\\\', '/'),\n      });\n    }\n    return imageUrls;\n  }\n\n  openApplyMissionModal(id: any) {\n    this.applyModal.show();\n    this.missionId = id;\n  }\n\n  closeApplyMissionModal() {\n    this.applyModal.hide();\n  }\n\n  applyMission(id: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        appliedDate: moment().format('yyyy-MM-DDTHH:mm:ssZ'),\n        status: false,\n        sheet: 1,\n      };\n      const missionSubscribe = this._service.applyMission(value).subscribe(\n        (data: any) => {\n          if (data.result == 1) {\n            this._toast.success({ detail: 'SUCCESS', summary: data.data });\n            setTimeout(() => {\n              this.closeApplyMissionModal();\n              this._router.navigate(['/home']);\n            }, 1000);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        },\n        (err) =>\n          this._toast.error({\n            detail: 'ERROR',\n            summary: err.message,\n            duration: APP_CONFIG.toastDuration,\n          })\n      );\n      this.unsubscribe.push(missionSubscribe);\n    }\n  }\n\n  postComment(commentdesc: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        CommentDescription: commentdesc,\n        commentDate: moment().format('yyyy-MM-DDTHH:mm:ssZ'),\n      };\n      const missionCommentSubscribe = this._service.addMissionComment(value).subscribe(\n        (data: any) => {\n          if (data.result == 1) {\n            this._toast.success({\n              detail: 'SUCCESS',\n              summary: data.data,\n              duration: APP_CONFIG.toastDuration,\n            });\n            window.location.reload();\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        },\n        (err) =>\n          this._toast.error({\n            detail: 'ERROR',\n            summary: err.message,\n            duration: APP_CONFIG.toastDuration,\n          })\n      );\n      this.unsubscribe.push(missionCommentSubscribe);\n    }\n  }\n\n  getMissionCommentList() {\n    const missionCommentSubscribe = this._service.missionCommentListByMissionId(this.missionDetail.id).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.missionCommentList = data.data;\n\n          this.missionCommentList = this.missionCommentList.map((x) => {\n            return {\n              id: x.id,\n              commentDescription: x.commentDescription,\n              commentDate: x.commentDate\n                ? this._datePipe.transform(\n                    x.commentDate,\n                    'EEEE, MMMM d, y, h:mm a'\n                  )\n                : '',\n              missionId: x.missionId,\n              userId: x.userId,\n              userFullName: x.userFullName,\n              userImage: x.userImage\n                ? this._service.imageUrl + '/' + x.userImage\n                : 'assets/NoImg.png',\n            };\n          });\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(missionCommentSubscribe);\n  }\n  \n  getMissionFavourite(missionId: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      this.missionFavourite = !this.missionFavourite;\n      const value = {\n        missionId: missionId,\n        userId: this.loginUserId,\n      };\n      if (this.missionFavourite) {\n        const addMissionFavouriteSubscribe = this._service.addMissionFavourite(value).subscribe((data: any) => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        });\n        this.unsubscribe.push(addMissionFavouriteSubscribe);\n      } else {\n        const removeMissionFavouriteSubscribe = this._service.removeMissionFavourite(value).subscribe((data: any) => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        });\n        this.unsubscribe.push(removeMissionFavouriteSubscribe);\n      }\n    }\n  }\n\n  getRecentVolunteerList() {\n    const value = {\n      missionId: this.missionId,\n      userId: this.loginUserId,\n    };\n    const volunteerListSubscribe = this._service.recentVolunteerList(value).subscribe((data: any) => {\n      if (data.result == 1) {\n        this.recentVolunteerList = data.data;\n        this.recentVolunteerList = this.recentVolunteerList.map((x) => {\n          return {\n            id: x.id,\n            missioId: x.missioId,\n            userId: x.userId,\n            userName: x.userName,\n            userImage: x.userImage\n              ? this._service.imageUrl + '/' + x.userImage\n              : 'assets/NoImg.png',\n          };\n        });\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(volunteerListSubscribe);\n  }\n}"], "mappings": ";AAAA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,SAAS,QAAgC,eAAe;AAEjE,SAGEC,mBAAmB,EACnBC,gBAAgB,QACX,qBAAqB;AAC5B,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,UAAU,QAAQ,kCAAkC;AAE7D,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,eAAe,QAAQ,4BAA4B;AAUrD,IAAMC,4BAA4B,GAAlC,MAAMA,4BAA4B;EAkBvCC,YACUC,QAA8B,EAC9BC,MAAsB,EACtBC,YAA4B,EAC5BC,OAAe,EACfC,SAAmB,EACnBC,aAA0B;IAL1B,KAAAL,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,aAAa,GAAbA,aAAa;IAlBvB,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,mBAAmB,GAAU,EAAE;IAE/B,KAAAC,WAAW,GAAG,CAAC;IAEf,KAAAC,OAAO,GAAQ,WAAW;IAC1B,KAAAC,kBAAkB,GAAU,EAAE;IAC9B,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,OAAO,GAAG,uBAAuB;IACjC,KAAAC,QAAQ,GAAG,wBAAwB;IAC3B,KAAAC,WAAW,GAAmB,EAAE;IAUtC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACb,YAAY,CAACc,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;EACvE;EAEAC,QAAQA,CAAA;IACN,MAAMC,oBAAoB,GAAG,IAAI,CAACf,aAAa,CAACgB,cAAc,EAAE,CAACC,SAAS,CAAEC,IAAS,IAAI;MACvF,MAAMC,eAAe,GAAG,IAAI,CAACnB,aAAa,CAACoB,aAAa,EAAE;MAC1DF,IAAI,IAAI,IAAI,GACP,IAAI,CAACf,WAAW,GAAGgB,eAAe,CAACE,MAAM,GACzC,IAAI,CAAClB,WAAW,GAAGe,IAAI,CAACG,MAAO;MACpCH,IAAI,IAAI,IAAI,GACP,IAAI,CAACI,aAAa,GAAGH,eAAe,CAACI,QAAQ,GAC7C,IAAI,CAACD,aAAa,GAAGJ,IAAI,CAACK,QAAS;IAC1C,CAAC,CAAC;IACF,IAAI,IAAI,CAACb,SAAS,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACc,kBAAkB,CAAC,IAAI,CAACd,SAAS,CAAC;IACzC;IACA,IAAI,CAACe,cAAc,GAAG,CACpB;MACEC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,OAAO;MACfC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE5C,mBAAmB,CAAC6C,KAAK;MACzCC,OAAO,EAAE;KACV,CACF;IACD,IAAI,CAACC,UAAU,GAAG,IAAIC,MAAM,CAACC,SAAS,CAACC,KAAK,CAC1CC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAC7C;IACD,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAAC7B,WAAW,CAAC8B,IAAI,CAACxB,oBAAoB,CAAC;EAC7C;EAEAyB,WAAWA,CAAA;IACT,IAAI,CAAC/B,WAAW,CAACgC,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACjC,WAAW,EAAE,CAAC;EACpD;EAEAe,kBAAkBA,CAACd,SAAc;IAC/B,MAAMiC,KAAK,GAAG;MACZjC,SAAS,EAAEA,SAAS;MACpBW,MAAM,EAAE,IAAI,CAAClB;KACd;IACD,MAAMyC,sBAAsB,GAAG,IAAI,CAACjD,QAAQ,CAACkD,wBAAwB,CAACF,KAAK,CAAC,CAAC1B,SAAS,CACnFC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAAC4B,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACC,aAAa,GAAG7B,IAAI,CAACA,IAAI;QAE9B,IAAI,CAACjB,SAAS,GAAG,IAAI,CAAC8C,aAAa,CAACC,aAAa,CAACC,KAAK,CAAC,GAAG,CAAC;QAC5D,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,SAAS,EAAE;QACrC,IAAI,IAAI,CAACJ,aAAa,CAACK,gBAAgB,EAAE;UACvC,IAAI,CAACC,UAAU,GACb,IAAI,CAAC1D,QAAQ,CAAC2D,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACP,aAAa,CAACK,gBAAgB;QACtE;QACA,IAAI,CAAChD,OAAO,GACV,IAAI,CAAC2C,aAAa,CAACQ,kBAAkB,IAAI,SAAS,GAC9C,eAAe,GACf,WAAW;QACjB,IAAI,CAACC,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAAC5D,MAAM,CAAC6D,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAEzC,IAAI,CAAC0C,OAAO;UACrBC,QAAQ,EAAEzE,UAAU,CAAC0E;SACtB,CAAC;MACJ;IACF,CAAC,EACAC,GAAG,IACF,IAAI,CAACnE,MAAM,CAAC6D,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAEI,GAAG,CAACH,OAAO;MACpBC,QAAQ,EAAEzE,UAAU,CAAC0E;KACtB,CAAC,CACL;IACD,IAAI,CAACrD,WAAW,CAAC8B,IAAI,CAACK,sBAAsB,CAAC;EAC/C;EAEAO,SAASA,CAAA;IACP,MAAMa,SAAS,GAAsB,EAAE;IACvC,KAAK,MAAMC,KAAK,IAAI,IAAI,CAAChE,SAAS,EAAE;MAClC+D,SAAS,CAACzB,IAAI,CAAC;QACb2B,KAAK,EAAE,IAAI,CAACvE,QAAQ,CAAC2D,QAAQ,GAAG,GAAG,GAAGW,KAAK,CAACE,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;QACjEC,MAAM,EAAE,IAAI,CAACzE,QAAQ,CAAC2D,QAAQ,GAAG,GAAG,GAAGW,KAAK,CAACE,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;QAClEE,GAAG,EAAE,IAAI,CAAC1E,QAAQ,CAAC2D,QAAQ,GAAG,GAAG,GAAGW,KAAK,CAACE,UAAU,CAAC,IAAI,EAAE,GAAG;OAC/D,CAAC;IACJ;IACA,OAAOH,SAAS;EAClB;EAEAM,qBAAqBA,CAACC,EAAO;IAC3B,IAAI,CAACvC,UAAU,CAACwC,IAAI,EAAE;IACtB,IAAI,CAAC9D,SAAS,GAAG6D,EAAE;EACrB;EAEAE,sBAAsBA,CAAA;IACpB,IAAI,CAACzC,UAAU,CAAC0C,IAAI,EAAE;EACxB;EAEAC,YAAYA,CAACJ,EAAO;IAClB,MAAMK,WAAW,GAAG,IAAI,CAAC5E,aAAa,CAAC6E,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAAChF,OAAO,CAACiF,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAACI,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACpF,MAAM,CAACqF,OAAO,CAAC;QAClBvB,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAEzE,UAAU,CAAC0E;OACtB,CAAC;MACF,IAAI,CAAChE,OAAO,CAACiF,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAACvD,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,MAAMsB,KAAK,GAAG;QACZjC,SAAS,EAAE,IAAI,CAACqC,aAAa,CAACwB,EAAE;QAChClD,MAAM,EAAE,IAAI,CAAClB,WAAW;QACxB+E,WAAW,EAAE/F,MAAM,EAAE,CAACgG,MAAM,CAAC,sBAAsB,CAAC;QACpDC,MAAM,EAAE,KAAK;QACbC,KAAK,EAAE;OACR;MACD,MAAMC,gBAAgB,GAAG,IAAI,CAAC3F,QAAQ,CAACgF,YAAY,CAAChC,KAAK,CAAC,CAAC1B,SAAS,CACjEC,IAAS,IAAI;QACZ,IAAIA,IAAI,CAAC4B,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAAClD,MAAM,CAAC2F,OAAO,CAAC;YAAE7B,MAAM,EAAE,SAAS;YAAEC,OAAO,EAAEzC,IAAI,CAACA;UAAI,CAAE,CAAC;UAC9DsE,UAAU,CAAC,MAAK;YACd,IAAI,CAACf,sBAAsB,EAAE;YAC7B,IAAI,CAAC3E,OAAO,CAACiF,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;UAClC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAI,CAACnF,MAAM,CAAC6D,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAEzC,IAAI,CAAC0C,OAAO;YACrBC,QAAQ,EAAEzE,UAAU,CAAC0E;WACtB,CAAC;QACJ;MACF,CAAC,EACAC,GAAG,IACF,IAAI,CAACnE,MAAM,CAAC6D,KAAK,CAAC;QAChBC,MAAM,EAAE,OAAO;QACfC,OAAO,EAAEI,GAAG,CAACH,OAAO;QACpBC,QAAQ,EAAEzE,UAAU,CAAC0E;OACtB,CAAC,CACL;MACD,IAAI,CAACrD,WAAW,CAAC8B,IAAI,CAAC+C,gBAAgB,CAAC;IACzC;EACF;EAEAG,WAAWA,CAACC,WAAgB;IAC1B,MAAMd,WAAW,GAAG,IAAI,CAAC5E,aAAa,CAAC6E,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAAChF,OAAO,CAACiF,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAACI,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACpF,MAAM,CAACqF,OAAO,CAAC;QAClBvB,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAEzE,UAAU,CAAC0E;OACtB,CAAC;MACF,IAAI,CAAChE,OAAO,CAACiF,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAACvD,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,MAAMsB,KAAK,GAAG;QACZjC,SAAS,EAAE,IAAI,CAACqC,aAAa,CAACwB,EAAE;QAChClD,MAAM,EAAE,IAAI,CAAClB,WAAW;QACxBwF,kBAAkB,EAAED,WAAW;QAC/BE,WAAW,EAAEzG,MAAM,EAAE,CAACgG,MAAM,CAAC,sBAAsB;OACpD;MACD,MAAMU,uBAAuB,GAAG,IAAI,CAAClG,QAAQ,CAACmG,iBAAiB,CAACnD,KAAK,CAAC,CAAC1B,SAAS,CAC7EC,IAAS,IAAI;QACZ,IAAIA,IAAI,CAAC4B,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAAClD,MAAM,CAAC2F,OAAO,CAAC;YAClB7B,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAEzC,IAAI,CAACA,IAAI;YAClB2C,QAAQ,EAAEzE,UAAU,CAAC0E;WACtB,CAAC;UACF7B,MAAM,CAAC8D,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,MAAM;UACL,IAAI,CAACpG,MAAM,CAAC6D,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAEzC,IAAI,CAAC0C,OAAO;YACrBC,QAAQ,EAAEzE,UAAU,CAAC0E;WACtB,CAAC;QACJ;MACF,CAAC,EACAC,GAAG,IACF,IAAI,CAACnE,MAAM,CAAC6D,KAAK,CAAC;QAChBC,MAAM,EAAE,OAAO;QACfC,OAAO,EAAEI,GAAG,CAACH,OAAO;QACpBC,QAAQ,EAAEzE,UAAU,CAAC0E;OACtB,CAAC,CACL;MACD,IAAI,CAACrD,WAAW,CAAC8B,IAAI,CAACsD,uBAAuB,CAAC;IAChD;EACF;EAEArC,qBAAqBA,CAAA;IACnB,MAAMqC,uBAAuB,GAAG,IAAI,CAAClG,QAAQ,CAACsG,6BAA6B,CAAC,IAAI,CAAClD,aAAa,CAACwB,EAAE,CAAC,CAACtD,SAAS,CACzGC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAAC4B,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACzC,kBAAkB,GAAGa,IAAI,CAACA,IAAI;QAEnC,IAAI,CAACb,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC6F,GAAG,CAAEC,CAAC,IAAI;UAC1D,OAAO;YACL5B,EAAE,EAAE4B,CAAC,CAAC5B,EAAE;YACR6B,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;YACxCR,WAAW,EAAEO,CAAC,CAACP,WAAW,GACtB,IAAI,CAAC7F,SAAS,CAACsG,SAAS,CACtBF,CAAC,CAACP,WAAW,EACb,yBAAyB,CAC1B,GACD,EAAE;YACNlF,SAAS,EAAEyF,CAAC,CAACzF,SAAS;YACtBW,MAAM,EAAE8E,CAAC,CAAC9E,MAAM;YAChBiF,YAAY,EAAEH,CAAC,CAACG,YAAY;YAC5BtB,SAAS,EAAEmB,CAAC,CAACnB,SAAS,GAClB,IAAI,CAACrF,QAAQ,CAAC2D,QAAQ,GAAG,GAAG,GAAG6C,CAAC,CAACnB,SAAS,GAC1C;WACL;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACpF,MAAM,CAAC6D,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAEzC,IAAI,CAAC0C,OAAO;UACrBC,QAAQ,EAAEzE,UAAU,CAAC0E;SACtB,CAAC;MACJ;IACF,CAAC,EACAC,GAAG,IACF,IAAI,CAACnE,MAAM,CAAC6D,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAEI,GAAG,CAACH,OAAO;MACpBC,QAAQ,EAAEzE,UAAU,CAAC0E;KACtB,CAAC,CACL;IACD,IAAI,CAACrD,WAAW,CAAC8B,IAAI,CAACsD,uBAAuB,CAAC;EAChD;EAEAU,mBAAmBA,CAAC7F,SAAc;IAChC,MAAMkE,WAAW,GAAG,IAAI,CAAC5E,aAAa,CAAC6E,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAAChF,OAAO,CAACiF,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAACI,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACpF,MAAM,CAACqF,OAAO,CAAC;QAClBvB,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAEzE,UAAU,CAAC0E;OACtB,CAAC;MACF,IAAI,CAAChE,OAAO,CAACiF,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAACvD,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAACf,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;MAC9C,MAAMqC,KAAK,GAAG;QACZjC,SAAS,EAAEA,SAAS;QACpBW,MAAM,EAAE,IAAI,CAAClB;OACd;MACD,IAAI,IAAI,CAACG,gBAAgB,EAAE;QACzB,MAAMkG,4BAA4B,GAAG,IAAI,CAAC7G,QAAQ,CAAC8G,mBAAmB,CAAC9D,KAAK,CAAC,CAAC1B,SAAS,CAAEC,IAAS,IAAI;UACpG,IAAIA,IAAI,CAAC4B,MAAM,IAAI,CAAC,EAAE;YACpB,IAAI,CAACtB,kBAAkB,CAACd,SAAS,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAACd,MAAM,CAAC6D,KAAK,CAAC;cAChBC,MAAM,EAAE,OAAO;cACfC,OAAO,EAAEzC,IAAI,CAAC0C,OAAO;cACrBC,QAAQ,EAAEzE,UAAU,CAAC0E;aACtB,CAAC;UACJ;QACF,CAAC,CAAC;QACF,IAAI,CAACrD,WAAW,CAAC8B,IAAI,CAACiE,4BAA4B,CAAC;MACrD,CAAC,MAAM;QACL,MAAME,+BAA+B,GAAG,IAAI,CAAC/G,QAAQ,CAACgH,sBAAsB,CAAChE,KAAK,CAAC,CAAC1B,SAAS,CAAEC,IAAS,IAAI;UAC1G,IAAIA,IAAI,CAAC4B,MAAM,IAAI,CAAC,EAAE;YACpB,IAAI,CAACtB,kBAAkB,CAACd,SAAS,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAACd,MAAM,CAAC6D,KAAK,CAAC;cAChBC,MAAM,EAAE,OAAO;cACfC,OAAO,EAAEzC,IAAI,CAAC0C,OAAO;cACrBC,QAAQ,EAAEzE,UAAU,CAAC0E;aACtB,CAAC;UACJ;QACF,CAAC,CAAC;QACF,IAAI,CAACrD,WAAW,CAAC8B,IAAI,CAACmE,+BAA+B,CAAC;MACxD;IACF;EACF;EAEApE,sBAAsBA,CAAA;IACpB,MAAMK,KAAK,GAAG;MACZjC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBW,MAAM,EAAE,IAAI,CAAClB;KACd;IACD,MAAMyG,sBAAsB,GAAG,IAAI,CAACjH,QAAQ,CAACO,mBAAmB,CAACyC,KAAK,CAAC,CAAC1B,SAAS,CAAEC,IAAS,IAAI;MAC9F,IAAIA,IAAI,CAAC4B,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAAC5C,mBAAmB,GAAGgB,IAAI,CAACA,IAAI;QACpC,IAAI,CAAChB,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACgG,GAAG,CAAEC,CAAC,IAAI;UAC5D,OAAO;YACL5B,EAAE,EAAE4B,CAAC,CAAC5B,EAAE;YACRsC,QAAQ,EAAEV,CAAC,CAACU,QAAQ;YACpBxF,MAAM,EAAE8E,CAAC,CAAC9E,MAAM;YAChByF,QAAQ,EAAEX,CAAC,CAACW,QAAQ;YACpB9B,SAAS,EAAEmB,CAAC,CAACnB,SAAS,GAClB,IAAI,CAACrF,QAAQ,CAAC2D,QAAQ,GAAG,GAAG,GAAG6C,CAAC,CAACnB,SAAS,GAC1C;WACL;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACpF,MAAM,CAAC6D,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAEzC,IAAI,CAAC0C,OAAO;UACrBC,QAAQ,EAAEzE,UAAU,CAAC0E;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAACrD,WAAW,CAAC8B,IAAI,CAACqE,sBAAsB,CAAC;EAC/C;CACD;AA5UYnH,4BAA4B,GAAAsH,UAAA,EAPxC/H,SAAS,CAAC;EACTgI,QAAQ,EAAE,0BAA0B;EACpCC,WAAW,EAAE,uCAAuC;EACpDC,SAAS,EAAE,CAAC,sCAAsC,CAAC;EACnDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC/H,eAAe,EAAEC,UAAU,EAAEJ,gBAAgB,EAAEH,YAAY,EAAEQ,yBAAyB,EAAEC,eAAe;CAClH,CAAC,C,EACWC,4BAA4B,CA4UxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}