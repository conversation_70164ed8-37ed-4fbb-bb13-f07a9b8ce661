{"ast": null, "code": "import utils from \"./utils\";\nimport domUtils from \"./dom-utils\";\nconst positionElement = settings => {\n  const {\n    anchor,\n    currentLocation,\n    element,\n    anchorAlign,\n    elementAlign,\n    collisions,\n    margin,\n    scale\n  } = settings;\n  const currentScale = scale || 1;\n  const elementOffset = domUtils.offsetAtPoint(element, currentLocation);\n  const elementRect = utils.scaleRect(elementOffset, currentScale);\n  const anchorOffset = utils.scaleRect(domUtils.offset(anchor), currentScale);\n  const anchorRect = utils.eitherRect(anchorOffset, currentLocation);\n  const viewPort = settings.viewPort || domUtils.windowViewPort(element);\n  viewPort.width = viewPort.width / currentScale;\n  viewPort.height = viewPort.height / currentScale;\n  const result = domUtils.restrictToView({\n    anchorAlign,\n    anchorRect,\n    collisions,\n    elementAlign,\n    elementRect,\n    margin,\n    viewPort\n  });\n  const offset = domUtils.addOffset(currentLocation, result.offset);\n  return {\n    flip: result.flip,\n    flipped: result.flipped,\n    fit: result.fit,\n    fitted: result.fitted,\n    offset: offset\n  };\n};\nexport default positionElement;", "map": {"version": 3, "names": ["utils", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "positionElement", "settings", "anchor", "currentLocation", "element", "anchorAlign", "elementAlign", "collisions", "margin", "scale", "currentScale", "elementOffset", "offsetAtPoint", "elementRect", "scaleRect", "anchorOffset", "offset", "anchorRect", "eitherRect", "viewPort", "windowViewPort", "width", "height", "result", "restrict<PERSON><PERSON><PERSON>iew", "addOffset", "flip", "flipped", "fit", "fitted"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/position-element.js"], "sourcesContent": ["import utils from \"./utils\";\nimport domUtils from \"./dom-utils\";\n\nconst positionElement = (settings) => {\n    const {\n        anchor,\n        currentLocation,\n        element,\n        anchorAlign,\n        elementAlign,\n        collisions,\n        margin,\n        scale\n    } = settings;\n\n    const currentScale = scale || 1;\n    const elementOffset = domUtils.offsetAtPoint(element, currentLocation);\n    const elementRect = utils.scaleRect(elementOffset, currentScale);\n    const anchorOffset = utils.scaleRect(domUtils.offset(anchor), currentScale);\n    const anchorRect = utils.eitherRect(anchorOffset, currentLocation);\n\n    const viewPort = settings.viewPort || domUtils.windowViewPort(element);\n    viewPort.width = viewPort.width / currentScale;\n    viewPort.height = viewPort.height / currentScale;\n\n    const result = domUtils.restrictToView({\n        anchorAlign,\n        anchorRect,\n        collisions,\n        elementAlign,\n        elementRect,\n        margin,\n        viewPort\n    });\n\n    const offset = domUtils.addOffset(currentLocation, result.offset);\n\n    return {\n        flip: result.flip,\n        flipped: result.flipped,\n        fit: result.fit,\n        fitted: result.fitted,\n        offset: offset\n    };\n};\n\nexport default positionElement;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,QAAQ,MAAM,aAAa;AAElC,MAAMC,eAAe,GAAIC,QAAQ,IAAK;EAClC,MAAM;IACFC,MAAM;IACNC,eAAe;IACfC,OAAO;IACPC,WAAW;IACXC,YAAY;IACZC,UAAU;IACVC,MAAM;IACNC;EACJ,CAAC,GAAGR,QAAQ;EAEZ,MAAMS,YAAY,GAAGD,KAAK,IAAI,CAAC;EAC/B,MAAME,aAAa,GAAGZ,QAAQ,CAACa,aAAa,CAACR,OAAO,EAAED,eAAe,CAAC;EACtE,MAAMU,WAAW,GAAGf,KAAK,CAACgB,SAAS,CAACH,aAAa,EAAED,YAAY,CAAC;EAChE,MAAMK,YAAY,GAAGjB,KAAK,CAACgB,SAAS,CAACf,QAAQ,CAACiB,MAAM,CAACd,MAAM,CAAC,EAAEQ,YAAY,CAAC;EAC3E,MAAMO,UAAU,GAAGnB,KAAK,CAACoB,UAAU,CAACH,YAAY,EAAEZ,eAAe,CAAC;EAElE,MAAMgB,QAAQ,GAAGlB,QAAQ,CAACkB,QAAQ,IAAIpB,QAAQ,CAACqB,cAAc,CAAChB,OAAO,CAAC;EACtEe,QAAQ,CAACE,KAAK,GAAGF,QAAQ,CAACE,KAAK,GAAGX,YAAY;EAC9CS,QAAQ,CAACG,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAGZ,YAAY;EAEhD,MAAMa,MAAM,GAAGxB,QAAQ,CAACyB,cAAc,CAAC;IACnCnB,WAAW;IACXY,UAAU;IACVV,UAAU;IACVD,YAAY;IACZO,WAAW;IACXL,MAAM;IACNW;EACJ,CAAC,CAAC;EAEF,MAAMH,MAAM,GAAGjB,QAAQ,CAAC0B,SAAS,CAACtB,eAAe,EAAEoB,MAAM,CAACP,MAAM,CAAC;EAEjE,OAAO;IACHU,IAAI,EAAEH,MAAM,CAACG,IAAI;IACjBC,OAAO,EAAEJ,MAAM,CAACI,OAAO;IACvBC,GAAG,EAAEL,MAAM,CAACK,GAAG;IACfC,MAAM,EAAEN,MAAM,CAACM,MAAM;IACrBb,MAAM,EAAEA;EACZ,CAAC;AACL,CAAC;AAED,eAAehB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}