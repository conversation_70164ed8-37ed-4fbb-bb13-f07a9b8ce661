{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { defaultThrottleConfig, throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config = defaultThrottleConfig) {\n  const duration$ = timer(duration, scheduler);\n  return throttle(() => duration$, config);\n}", "map": {"version": 3, "names": ["asyncScheduler", "defaultThrottleConfig", "throttle", "timer", "throttleTime", "duration", "scheduler", "config", "duration$"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/rxjs/dist/esm/internal/operators/throttleTime.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { defaultThrottleConfig, throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config = defaultThrottleConfig) {\n    const duration$ = timer(duration, scheduler);\n    return throttle(() => duration$, config);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,qBAAqB,EAAEC,QAAQ,QAAQ,YAAY;AAC5D,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAEC,SAAS,GAAGN,cAAc,EAAEO,MAAM,GAAGN,qBAAqB,EAAE;EAC/F,MAAMO,SAAS,GAAGL,KAAK,CAACE,QAAQ,EAAEC,SAAS,CAAC;EAC5C,OAAOJ,QAAQ,CAAC,MAAMM,SAAS,EAAED,MAAM,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}