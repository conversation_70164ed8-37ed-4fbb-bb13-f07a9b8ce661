{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FilterPipe {\n  transform(value, args) {\n    if (!value) return null;\n    if (!args) return value;\n    args = args.toLowerCase();\n    return value.filter(item => {\n      return JSON.stringify(item).toLowerCase().includes(args);\n    });\n  }\n  static {\n    this.ɵfac = function FilterPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FilterPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"filter\",\n      type: FilterPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["FilterPipe", "transform", "value", "args", "toLowerCase", "filter", "item", "JSON", "stringify", "includes", "pure", "standalone"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\pipes\\filter.pipe.ts"], "sourcesContent": ["import { Pipe, type PipeTransform } from \"@angular/core\"\n\n@Pipe({\n  name: \"filter\",\n  standalone: true,\n})\nexport class FilterPipe implements PipeTransform {\n  transform(value: any[], args?: any): any {\n    if (!value) return null\n    if (!args) return value\n\n    args = args.toLowerCase()\n\n    return value.filter((item: any) => {\n      return JSON.stringify(item).toLowerCase().includes(args)\n    })\n  }\n}\n"], "mappings": ";AAMA,OAAM,MAAOA,UAAU;EACrBC,SAASA,CAACC,KAAY,EAAEC,IAAU;IAChC,IAAI,CAACD,KAAK,EAAE,OAAO,IAAI;IACvB,IAAI,CAACC,IAAI,EAAE,OAAOD,KAAK;IAEvBC,IAAI,GAAGA,IAAI,CAACC,WAAW,EAAE;IAEzB,OAAOF,KAAK,CAACG,MAAM,CAAEC,IAAS,IAAI;MAChC,OAAOC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAACF,WAAW,EAAE,CAACK,QAAQ,CAACN,IAAI,CAAC;IAC1D,CAAC,CAAC;EACJ;;;uCAVWH,UAAU;IAAA;EAAA;;;;YAAVA,UAAU;MAAAU,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}