{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;;;;;;;AAC+C;AACE;;;AAU3C,MAAOE,YAAY;EARzBC,YAAA;IASE,KAAAC,KAAK,GAAG,UAAU;;;;uCADPF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZzBN,4DAAA,UAAK;UAEHA,uDADA,oBAA+B,mBACF;UAC/BA,0DAAA,EAAM;;;qBDIMR,yDAAY,EAAEC,2DAAa,EAAAkB,8DAAA;MAAAE,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AENa;AACiB;AACoB;AACS;AACkB;AACA;AAC7B;AACS;AACiB;AACP;AACgB;AAEnH,MAAMY,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEP,iGAAcA;AAAA,CAAE,EACvC;EAAEM,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEN,0GAAiBA;AAAA,CAAE,EAClD;EAAEK,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAER,8HAAuBA;AAAA,CAAE,EAC9D;EAAEO,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEL,2HAAsBA;AAAA,CAAE,EAC5D;EAAEI,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEZ,+EAAaA;AAAA,CAAE,EAC1C;EAAEW,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEX,mGAAmBA;AAAA,CAAE,EACzD;EAAEU,IAAI,EAAE,gCAAgC;EAAEC,SAAS,EAAET,8HAA4B;EAAEU,WAAW,EAAE,CAACd,8DAAS;AAAC,CAAE,EAC7G;EAAEY,IAAI,EAAE,qBAAqB;EAAEC,SAAS,EAAEJ,oHAAwB;EAAEK,WAAW,EAAE,CAACd,8DAAS;AAAC,CAAE,EAC9F;EAAEY,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEV,4GAAsBA;AAAA,CAAE,EAC5D;EAAES,IAAI,EAAE,uBAAuB;EAAEC,SAAS,EAAEH,qIAA8B;EAAEI,WAAW,EAAE,CAACd,8DAAS;AAAC,CAAE,EACtG;EAAEY,IAAI,EAAE,OAAO;EAAEG,YAAY,EAAEA,CAAA,KAAM,8MAAuD;AAAA,CAAE,CAC/F;;;;;;;;;;;;;;;;;ACxB8C;;;AASzC,MAAOE,eAAe;EAC1BpC,YAAA,GAAe;EAEfqC,QAAQA,CAAA,GAAU;;;uCAHPD,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAlC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAA8B,yBAAA5B,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BN,4DAAA,aAAoC;UAChCA,uDAAA,SAAK;UACLA,4DAAA,WAA0F;UAAAA,oDAAA,qBAAc;UAC1GA,0DAD0G,EAAI,EACxG;;;qBDEI8B,yDAAY,EAAAnB,uDAAA;MAAAE,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AEFiC;AACrB;AACH;AACuB;AAGM;AAED;AACA;AACR;AACA;;;;;;;;;;;;;;;;;IC4BvCb,4DAAA,cAA+D;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAM;;;;;IAC3EA,4DAAA,cAAqE;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAM;;;;;IAClFA,4DAAA,cAAyE;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAM;;;;;IACvFA,4DAAA,cAAmJ;IAAAA,oDAAA,UAAG;IAAAA,0DAAA,EAAM;;;;;;IA6BhKA,4DAAA,iBAAmK;IAAjFA,wDAAA,mBAAA8C,qFAAA;MAAA9C,2DAAA,CAAAgD,GAAA;MAAA,MAAAC,OAAA,GAAAjD,2DAAA,GAAAmD,SAAA;MAAA,MAAAC,MAAA,GAAApD,2DAAA;MAAA,OAAAA,yDAAA,CAASoD,MAAA,CAAAE,mBAAA,CAAAL,OAAA,CAAAM,EAAA,CAA4B;IAAA,EAAC;IAA2CvD,4DAAA,eAAoB;IAAAA,oDAAA,mBAAY;IAAAA,uDAAA,YAAqE;IAAOA,0DAAP,EAAO,EAAS;;;;IAA/JA,wDAAA,aAAAiD,OAAA,CAAAQ,aAAA,aAAyC;;;;;IACmBzD,4DAArL,iBAAqL,eAAoB;IAAAA,oDAAA,cAAO;IAAOA,0DAAP,EAAO,EAAS;;;;IAA9FA,wDAAA,aAAAiD,OAAA,CAAAS,kBAAA,eAAkD;;;;;IACrC1D,4DAA/I,iBAA+I,eAAoB;IAAAA,oDAAA,cAAO;IAAOA,0DAAP,EAAO,EAAS;;;;IAAjGA,wDAAA,aAAAiD,OAAA,CAAAU,oBAAA,gBAAqD;;;;;IApC9I3D,4DADJ,cAA4E,cAC9C;IACtBA,uDAAA,cAAyH;IAIzHA,wDAHA,IAAA6D,wDAAA,kBAA+D,IAAAC,wDAAA,kBACM,IAAAC,wDAAA,kBACI,IAAAC,wDAAA,kBAC0E;IACnJhE,4DAAA,cAAsB;IAAAA,oDAAA,GAAyB;IACnDA,0DADmD,EAAM,EACnD;IAEJA,4DADF,cAAuB,aACF;IAAAA,oDAAA,IAAqB;IAAAA,0DAAA,EAAI;IAC5CA,4DAAA,aAAmB;IAACA,oDAAA,IAAkC;IAAAA,0DAAA,EAAI;IAExDA,4DADF,eAAuC,eACD;IAClCA,oDAAA,IACF;IACFA,0DADE,EAAM,EACF;IAGAA,4DAFN,eAAqB,eACkB,aACJ;IAAAA,oDAAA,IAAwF;;;IAE3HA,0DAF2H,EAAI,EACrH,EACJ;IAEFA,4DADJ,eAA8B,cACJ;IACpBA,uDAAA,aAAyC;IAAAA,oDAAA,eACzC;IAAAA,4DAAA,gBAA0C;IAAAA,oDAAA,IAAoB;IAAAA,0DAAA,EAAO;IAACA,uDAAA,UAAK;IAAAA,4DAAA,gBAA+B;IAAAA,oDAAA,kBAAU;IACtHA,0DADsH,EAAO,EACvH;IACNA,4DAAA,cAAsB;IACpBA,uDAAA,aAAmC;IAAAA,oDAAA,eACnC;IAAAA,4DAAA,gBAA0C;IAAAA,oDAAA,IAAkD;;IAAAA,0DAAA,EAAO;IAACA,uDAAA,UAAK;IAAAA,4DAAA,gBAA+B;IAAAA,oDAAA,gBAAQ;IAGxJA,0DAHwJ,EAAO,EACnJ,EACJ,EACF;IACNA,uDAAA,aAAoE;IACpEA,4DAAA,eAA+E;IAG/EA,wDAFA,KAAAiE,4DAAA,qBAAmK,KAAAC,4DAAA,qBACkB,KAAAC,4DAAA,qBACtC;IACjJnE,0DAAA,EAAM;IACHA,4DAAA,aAA0B;IAAAA,oDAAA,IAAkB;IAEjDA,0DAFiD,EAAI,EAE/C;;;;IAvCOA,uDAAA,GAA4B;IAA5BA,mEAAA,QAAAiD,OAAA,CAAAqB,aAAA,EAAAtE,2DAAA,CAA4B;IACRA,uDAAA,EAAoC;IAApCA,wDAAA,SAAAiD,OAAA,CAAAQ,aAAA,aAAoC;IACpCzD,uDAAA,EAA0C;IAA1CA,wDAAA,SAAAiD,OAAA,CAAAS,kBAAA,cAA0C;IAC1C1D,uDAAA,EAA8C;IAA9CA,wDAAA,SAAAiD,OAAA,CAAAU,oBAAA,gBAA8C;IAC9C3D,uDAAA,EAAwH;IAAxHA,wDAAA,SAAAiD,OAAA,CAAAQ,aAAA,gBAAAR,OAAA,CAAAS,kBAAA,iBAAAT,OAAA,CAAAU,oBAAA,gBAAwH;IAC3H3D,uDAAA,GAAyB;IAAzBA,+DAAA,CAAAiD,OAAA,CAAAwB,gBAAA,CAAyB;IAG9BzE,uDAAA,GAAqB;IAArBA,+DAAA,CAAAiD,OAAA,CAAAyB,YAAA,CAAqB;IACpB1E,uDAAA,GAAkC;IAAlCA,gEAAA,MAAAiD,OAAA,CAAA2B,yBAAA,KAAkC;IAGlD5E,uDAAA,GACF;IADEA,gEAAA,MAAAiD,OAAA,CAAA4B,WAAA,MACF;IAIiC7E,uDAAA,GAAwF;IAAxFA,gEAAA,UAAAA,yDAAA,SAAAiD,OAAA,CAAA+B,SAAA,4BAAAhF,yDAAA,SAAAiD,OAAA,CAAAgC,OAAA,oBAAwF;IAM3EjF,uDAAA,GAAoB;IAApBA,+DAAA,CAAAiD,OAAA,CAAAiC,WAAA,CAAoB;IAIpBlF,uDAAA,GAAkD;IAAlDA,+DAAA,CAAAA,yDAAA,SAAAiD,OAAA,CAAAkC,oBAAA,gBAAkD;IAM3DnF,uDAAA,GAAuC;IAAvCA,wDAAA,SAAAiD,OAAA,CAAAS,kBAAA,YAAuC;IACvC1D,uDAAA,EAAuF;IAAvFA,wDAAA,SAAAiD,OAAA,CAAAS,kBAAA,kBAAAT,OAAA,CAAAU,oBAAA,gBAAuF;IACvF3D,uDAAA,EAA8C;IAA9CA,wDAAA,SAAAiD,OAAA,CAAAU,oBAAA,gBAA8C;IAE5D3D,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAiD,OAAA,CAAAmC,SAAA,CAAkB;;;;;IAIIpF,4DADrD,cAA+D,YACV,QAAG;IAAAA,oDAAA,uBAAgB;IACxEA,0DADwE,EAAI,EAAI,EAC1E;;;;;;IAEJA,4DADF,cAAgG,8BACd;IAA7BA,wDAAA,wBAAAqF,6FAAAC,MAAA;MAAAtF,2DAAA,CAAAuF,GAAA;MAAA,MAAAnC,MAAA,GAAApD,2DAAA;MAAA,OAAAA,yDAAA,CAAAoD,MAAA,CAAAoC,IAAA,GAAAF,MAAA;IAAA,EAA4B;IACjFtF,0DADkF,EAAsB,EAClG;;;;;IAhDRA,qEAAA,GAAsJ;IA8CpJA,wDA7CA,IAAA0F,kDAAA,oBAA4E,IAAAC,kDAAA,kBA0Cb,IAAAC,kDAAA,kBAGiC;;;;;IA7C/B5F,uDAAA,EAAS;IAATA,wDAAA,YAAA6F,SAAA,CAAS;IA0CtC7F,uDAAA,EAAyB;IAAzBA,wDAAA,SAAA6F,SAAA,CAAAC,MAAA,OAAyB;IAGrC9F,uDAAA,EAAwB;IAAxBA,wDAAA,SAAA6F,SAAA,CAAAC,MAAA,MAAwB;;;;;IA/CpD9F,4DAAA,cAAiD;IAC/CA,wDAAA,IAAA+F,4CAAA,2BAAsJ;;;IAkDxJ/F,0DAAA,EAAM;;;;IAlDWA,uDAAA,EAA4H;IAA5HA,wDAAA,SAAAA,yDAAA,OAAAA,yDAAA,OAAAoD,MAAA,CAAA4C,WAAA,EAAA5C,MAAA,CAAA6C,WAAA,GAAAjG,6DAAA,IAAAmG,GAAA,EAAA/C,MAAA,CAAAgD,eAAA,EAAAhD,MAAA,CAAAoC,IAAA,EAAApC,MAAA,CAAAiD,YAAA,GAA4H;;;;;IAyDvIrG,4DAAA,cAA+D;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAM;;;;;IAC3EA,4DAAA,cAAqE;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAM;;;;;IAClFA,4DAAA,cAAyE;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAM;;;;;IACvFA,4DAAA,cAAmJ;IAAAA,oDAAA,UAAG;IAAAA,0DAAA,EAAM;;;;;;IA0CxJA,4DAAA,kBAAuL;IAAjFA,wDAAA,mBAAAsG,qFAAA;MAAAtG,2DAAA,CAAAuG,GAAA;MAAA,MAAAC,OAAA,GAAAxG,2DAAA,GAAAmD,SAAA;MAAA,MAAAC,MAAA,GAAApD,2DAAA;MAAA,OAAAA,yDAAA,CAASoD,MAAA,CAAAE,mBAAA,CAAAkD,OAAA,CAAAjD,EAAA,CAA4B;IAAA,EAAC;IAA2CvD,4DAAA,gBAAyB;IAAAA,oDAAA,mBAAY;IAAAA,uDAAA,YAAqE;IAAOA,0DAAP,EAAO,EAAS;;;;IAApKA,wDAAA,aAAAwG,OAAA,CAAA/C,aAAA,aAAyC;;;;;IACjDzD,4DAArI,iBAAqI,eAAoB;IAAAA,oDAAA,cAAO;IAAOA,0DAAP,EAAO,EAAS;;;;IAA3FA,wDAAA,aAAAwG,OAAA,CAAA9C,kBAAA,cAA+C;;;;;IACW1D,4DAA/I,iBAA+I,eAAoB;IAAAA,oDAAA,cAAO;IAAOA,0DAAP,EAAO,EAAS;;;;IAAjGA,wDAAA,aAAAwG,OAAA,CAAA7C,oBAAA,gBAAqD;;;;;IAjDpJ3D,4DAFF,cAA8H,cAC7F,cACT;IACpBA,uDAAA,cAAkH;IAIlHA,wDAHA,IAAAyG,wDAAA,kBAA+D,IAAAC,wDAAA,kBACM,IAAAC,wDAAA,kBACI,IAAAC,wDAAA,kBAC0E;IACnJ5G,4DAAA,cAA2B;IAAAA,oDAAA,GAAyB;IACtDA,0DADsD,EAAM,EACtD;IAGFA,4DAFJ,eAAsB,eACO,eACH;IACpBA,uDAAA,eAA6C;IAAAA,oDAAA,IAC/C;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAAsB;IACpBA,uDAAA,eAA4C;IAAAA,oDAAA,cAAM;IAAAA,4DAAA,YAAM;IAAAA,oDAAA,IAAyB;IACnFA,0DADmF,EAAO,EACpF;IACNA,4DAAA,eAAsB;IACpBA,uDAAA,eAAqD;IAAAA,oDAAA,cAAM;IAAAA,4DAAA,YAAM;IAAAA,oDAAA,IAAgC;IAErGA,0DAFqG,EAAO,EACpG,EACF;IAEFA,4DADJ,eAAkB,eACgB;IAC5BA,oDAAA,IACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAAc;IACZA,oDAAA,IACF;IACJA,0DADI,EAAM,EACJ;IAEJA,4DADF,eAAgC,eACR;IACpBA,uDAAA,eAAmD;IAAAA,oDAAA,eACnD;IAAAA,4DAAA,YAAM;IAAAA,oDAAA,IAAoB;IAAAA,0DAAA,EAAO;IAACA,uDAAA,UAAK;IAAAA,4DAAA,gBAA+B;IAAAA,oDAAA,kBAAU;IAClFA,0DADkF,EAAO,EACnF;IACNA,4DAAA,eAAsB;IACpBA,uDAAA,eAAiD;IAAAA,oDAAA,IACG;;IAAAA,uDAAA,UAAK;IAACA,4DAAA,gBAAgC;IAAAA,oDAAA,gBAAQ;IACpGA,0DADoG,EAAO,EACrG;IACNA,4DAAA,eAAsB;IACpBA,uDAAA,eAAiD;IAAAA,oDAAA,IACJ;;IAAAA,uDAAA,UAAK;IAACA,4DAAA,gBAAgC;IAAAA,oDAAA,IAA2C;;IAChIA,0DADgI,EAAO,EACjI;IACNA,4DAAA,eAAsB;IACpBA,uDAAA,eAAiD;IAAAA,oDAAA,qBAChD;IAAAA,uDAAA,UAAK;IAACA,4DAAA,gBAAqD;IAAAA,oDAAA,IAAyB;IACvFA,0DADuF,EAAO,EACxF;IACNA,4DAAA,eAAsB;IAIpBA,wDAFA,KAAA6G,4DAAA,sBAAuL,KAAAC,4DAAA,qBAClD,KAAAC,4DAAA,qBACU;IAKvJ/G,0DAJM,EAAM,EACF,EACF,EACI,EACN;;;;IArDGA,uDAAA,GAA4B;IAA5BA,mEAAA,QAAAwG,OAAA,CAAAlC,aAAA,EAAAtE,2DAAA,CAA4B;IACRA,uDAAA,EAAoC;IAApCA,wDAAA,SAAAwG,OAAA,CAAA/C,aAAA,aAAoC;IACpCzD,uDAAA,EAA0C;IAA1CA,wDAAA,SAAAwG,OAAA,CAAA9C,kBAAA,cAA0C;IAC1C1D,uDAAA,EAA8C;IAA9CA,wDAAA,SAAAwG,OAAA,CAAA7C,oBAAA,gBAA8C;IAC9C3D,uDAAA,EAAwH;IAAxHA,wDAAA,SAAAwG,OAAA,CAAA/C,aAAA,gBAAA+C,OAAA,CAAA9C,kBAAA,iBAAA8C,OAAA,CAAA7C,oBAAA,gBAAwH;IACtH3D,uDAAA,GAAyB;IAAzBA,+DAAA,CAAAwG,OAAA,CAAA/B,gBAAA,CAAyB;IAKHzE,uDAAA,GAC/C;IAD+CA,gEAAA,WAAAwG,OAAA,CAAAQ,QAAA,MAC/C;IAE0DhH,uDAAA,GAAyB;IAAzBA,+DAAA,CAAAwG,OAAA,CAAA/B,gBAAA,CAAyB;IAGhBzE,uDAAA,GAAgC;IAAhCA,+DAAA,CAAAwG,OAAA,CAAAS,uBAAA,CAAgC;IAK/FjH,uDAAA,GACF;IADEA,gEAAA,MAAAwG,OAAA,CAAA9B,YAAA,MACF;IAEE1E,uDAAA,GACF;IADEA,gEAAA,MAAAwG,OAAA,CAAAU,kBAAA,MACF;IAKMlH,uDAAA,GAAoB;IAApBA,+DAAA,CAAAwG,OAAA,CAAAtB,WAAA,CAAoB;IAGuBlF,uDAAA,GACG;IADHA,gEAAA,YAAAA,yDAAA,SAAAwG,OAAA,CAAArB,oBAAA,qBACG;IAGHnF,uDAAA,GACJ;IADIA,gEAAA,iBAAAA,yDAAA,SAAAwG,OAAA,CAAAxB,SAAA,qBACJ;IAAsChF,uDAAA,GAA2C;IAA3CA,gEAAA,WAAAA,yDAAA,SAAAwG,OAAA,CAAAvB,OAAA,oBAA2C;IAIlEjF,uDAAA,GAAyB;IAAzBA,+DAAA,CAAAwG,OAAA,CAAAW,gBAAA,CAAyB;IAIxBnH,uDAAA,GAAuC;IAAvCA,wDAAA,SAAAwG,OAAA,CAAA9C,kBAAA,YAAuC;IAC3D1D,uDAAA,EAA0C;IAA1CA,wDAAA,SAAAwG,OAAA,CAAA9C,kBAAA,eAA0C;IAC1C1D,uDAAA,EAA8C;IAA9CA,wDAAA,SAAAwG,OAAA,CAAA7C,oBAAA,gBAA8C;;;;;IAO5C3D,4DADnD,cAA+D,YACZ,QAAG;IAAAA,oDAAA,uBAAgB;IACtEA,0DADsE,EAAI,EAAI,EACxE;;;;;;IAEJA,4DADF,cAAgG,8BACd;IAA7BA,wDAAA,wBAAAoH,6FAAA9B,MAAA;MAAAtF,2DAAA,CAAAqH,GAAA;MAAA,MAAAjE,MAAA,GAAApD,2DAAA;MAAA,OAAAA,yDAAA,CAAAoD,MAAA,CAAAoC,IAAA,GAAAF,MAAA;IAAA,EAA4B;IACjFtF,0DADkF,EAAsB,EAClG;;;;;IA/DNA,qEAAA,GAA0J;IA6D1JA,wDA5DA,IAAAsH,kDAAA,oBAA8H,IAAAC,kDAAA,kBAyD/D,IAAAC,kDAAA,kBAGiC;;;;;IA5DhDxH,uDAAA,EAAS;IAATA,wDAAA,YAAAyH,SAAA,CAAS;IAyDrBzH,uDAAA,EAAyB;IAAzBA,wDAAA,SAAAyH,SAAA,CAAA3B,MAAA,OAAyB;IAGrC9F,uDAAA,EAAwB;IAAxBA,wDAAA,SAAAyH,SAAA,CAAA3B,MAAA,MAAwB;;;;;IA9DlD9F,4DAAA,cAA2C;IACzCA,wDAAA,IAAA0H,4CAAA,2BAA0J;;;IAiE5J1H,0DAAA,EAAM;;;;IAjEWA,uDAAA,EAAgI;IAAhIA,wDAAA,SAAAA,yDAAA,OAAAA,yDAAA,OAAAoD,MAAA,CAAA4C,WAAA,EAAA5C,MAAA,CAAA6C,WAAA,GAAAjG,6DAAA,IAAAmG,GAAA,EAAA/C,MAAA,CAAAuE,mBAAA,EAAAvE,MAAA,CAAAoC,IAAA,EAAApC,MAAA,CAAAiD,YAAA,GAAgI;;;ADhE7I,MAAOtF,aAAa;EA0BxBpB,YACUiI,QAA8B,EAC9BC,MAAsB,EACtBC,OAAe,EACfC,cAA6B,EAC7BC,aAA0B;IAJ1B,KAAAJ,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IA9BvB,KAAAhC,WAAW,GAAU,EAAE;IACvB,KAAAiC,QAAQ,GAAU,EAAE;IACpB,KAAAzC,IAAI,GAAG,CAAC;IACR,KAAAY,eAAe,GAAG,CAAC;IACnB,KAAAuB,mBAAmB,GAAG,CAAC;IAGvB,KAAAO,WAAW,GAAG,CAAC;IAOf,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,OAAO,GAAG,uBAAuB;IACjC,KAAAC,QAAQ,GAAG,wBAAwB;IACnC,KAAAC,IAAI,GAAoB,MAAM;IAC9B,KAAAC,gBAAgB,GAAG,KAAK;IAIxB,KAAAC,eAAe,GAAU,EAAE;IACnB,KAAAC,WAAW,GAAmB,EAAE;EAQrC;EAEHzG,QAAQA,CAAA;IACN,MAAM0G,oBAAoB,GAAG,IAAI,CAACV,aAAa,CAACW,cAAc,EAAE,CAACC,SAAS,CAAEC,IAAS,IAAI;MACvF,MAAMC,eAAe,GAAG,IAAI,CAACd,aAAa,CAACe,aAAa,EAAE;MAC1DF,IAAI,IAAI,IAAI,GACP,IAAI,CAACX,WAAW,GAAGY,eAAe,CAACE,MAAM,GACzC,IAAI,CAACd,WAAW,GAAGW,IAAI,CAACG,MAAO;MACpCH,IAAI,IAAI,IAAI,GACP,IAAI,CAACI,aAAa,GAAGH,eAAe,CAACI,QAAQ,GAC7C,IAAI,CAACD,aAAa,GAAGJ,IAAI,CAACK,QAAS;MACxCL,IAAI,IAAI,IAAI,GACP,IAAI,CAACM,iBAAiB,GAAGL,eAAe,CAACM,YAAY,GACrD,IAAI,CAACD,iBAAiB,GAAGN,IAAI,CAACO,YAAa;IAClD,CAAC,CAAC;IACF,IAAI,CAACC,cAAc,EAAE;IACrB,MAAMC,mBAAmB,GAAG,IAAI,CAACvB,cAAc,CAACwB,UAAU,CAACX,SAAS,CAAEC,IAAS,IAAI;MACjF,IAAI,CAAC5C,WAAW,GAAG4C,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACW,WAAW,GAAG,EAAE;IACrB,IAAI,CAACf,WAAW,CAACgB,IAAI,CAACf,oBAAoB,EAAEY,mBAAmB,CAAC;EAClE;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;EAEAoB,YAAYA,CAAA;IACV,IAAI,CAACvB,IAAI,GAAG,MAAM;EACpB;EAEAwB,YAAYA,CAAA;IACV,IAAI,CAACxB,IAAI,GAAG,MAAM;EACpB;EAEAe,cAAcA,CAAA;IACZ,MAAMU,oBAAoB,GAAG,IAAI,CAACnC,QAAQ,CAAC5B,WAAW,CAAC,IAAI,CAACkC,WAAW,CAAC,CAACU,SAAS,CAAEC,IAAS,IAAI;MAC/F,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAAChE,WAAW,GAAG6C,IAAI,CAACA,IAAI;QAC5B,IAAI,CAAC7C,WAAW,GAAG,IAAI,CAACA,WAAW,CAACiE,GAAG,CAAEC,CAAC,IAAI;UAC5C,IAAIC,UAAU,GAAGD,CAAC,CAAC5F,aAAa,GAC5B,IAAI,CAACsD,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAGF,CAAC,CAAC5F,aAAa,GAC9C,kBAAkB;UACtB,IAAI,CAAC+F,OAAO,GAAGH,CAAC,CAACI,MAAM;UACvB,OAAO;YACL/G,EAAE,EAAE2G,CAAC,CAAC3G,EAAE;YACRmB,YAAY,EAAEwF,CAAC,CAACxF,YAAY;YAC5BwC,kBAAkB,EAAEgD,CAAC,CAAChD,kBAAkB;YACxC9B,SAAS,EAAE8E,CAAC,CAAC9E,SAAS;YACtBP,WAAW,EAAEqF,CAAC,CAACrF,WAAW;YAC1B0F,MAAM,EAAEL,CAAC,CAACK,MAAM;YAChBvD,QAAQ,EAAEkD,CAAC,CAAClD,QAAQ;YACpBhC,SAAS,EAAEkF,CAAC,CAAClF,SAAS;YACtBC,OAAO,EAAEiF,CAAC,CAACjF,OAAO;YAClBC,WAAW,EAAEgF,CAAC,CAAChF,WAAW;YAC1BC,oBAAoB,EAAE+E,CAAC,CAAC/E,oBAAoB;YAC5CqF,cAAc,EAAEN,CAAC,CAACM,cAAc;YAChCC,cAAc,EAAEP,CAAC,CAACO,cAAc;YAChCnG,aAAa,EAAE6F,UAAU,CAACO,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;YACvCjG,gBAAgB,EAAEyF,CAAC,CAACzF,gBAAgB;YACpC0C,gBAAgB,EAAE+C,CAAC,CAAC/C,gBAAgB;YACpC1D,aAAa,EAAEyG,CAAC,CAACzG,aAAa;YAC9BC,kBAAkB,EAAEwG,CAAC,CAACxG,kBAAkB;YACxCC,oBAAoB,EAAEuG,CAAC,CAACvG,oBAAoB;YAC5CgH,iBAAiB,EAAET,CAAC,CAACS,iBAAiB;YACtCC,qBAAqB,EAAEV,CAAC,CAACU;WAC1B;QACH,CAAC,CAAC;QACF,IAAI,CAACvE,YAAY,GAAGwC,IAAI,CAACA,IAAI,CAAC/C,MAAM;MACtC,CAAC,MAAM;QACL,IAAI,CAAC+B,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;QACF;MACF;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACM,oBAAoB,CAAC;EAC7C;EAEAoB,WAAWA,CAACC,CAAM;IAChB,MAAMC,aAAa,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IACpC,IAAIF,aAAa,IAAI,KAAK,EAAE;MAC1B,IAAI,CAACrF,WAAW,CAACwF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QAC7B,IAAID,CAAC,GAAGA,CAAC,CAAC,cAAc,CAAC,CAACE,WAAW,EAAE;UACrCD,CAAC,GAAGA,CAAC,CAAC,cAAc,CAAC,CAACC,WAAW,EAAE;QACrC,OAAOF,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC1F,WAAW,CAACwF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QAC7B,IAAID,CAAC,GAAGA,CAAC,CAAC,cAAc,CAAC,CAACE,WAAW,EAAE;UACrCD,CAAC,GAAGA,CAAC,CAAC,cAAc,CAAC,CAACC,WAAW,EAAE;QACrC,OAAOF,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACnC,CAAC,CAAC;IACJ;EACF;EACAE,WAAWA,CAACR,CAAM;IAChB,IAAIS,WAAW,GAAGT,CAAC,CAACE,MAAM,CAACC,KAAK;IAChCM,WAAW,GAAGA,WAAW,IAAI,EAAE,GAAG,MAAM,GAAGA,WAAW;IACtD,MAAMN,KAAK,GAAG;MACZvC,MAAM,EAAE,IAAI,CAACd,WAAW;MACxB4D,YAAY,EAAED;KACf;IACD,MAAME,sBAAsB,GAAG,IAAI,CAACnE,QAAQ,CAACoE,iBAAiB,CAACT,KAAK,CAAC,CAAC3C,SAAS,CAAEC,IAAS,IAAI;MAC5F,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAAChE,WAAW,GAAG6C,IAAI,CAACA,IAAI;QAC5B,IAAI,CAAC7C,WAAW,GAAG,IAAI,CAACA,WAAW,CAACiE,GAAG,CAAEC,CAAC,IAAI;UAC5C,MAAMC,UAAU,GAAGD,CAAC,CAAC5F,aAAa,GAC9B,IAAI,CAACsD,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAGF,CAAC,CAAC5F,aAAa,GAC9C,kBAAkB;UACtB,OAAO;YACLf,EAAE,EAAE2G,CAAC,CAAC3G,EAAE;YACRmB,YAAY,EAAEwF,CAAC,CAACxF,YAAY;YAC5BwC,kBAAkB,EAAEgD,CAAC,CAAChD,kBAAkB;YACxC9B,SAAS,EAAE8E,CAAC,CAAC9E,SAAS;YACtBP,WAAW,EAAEqF,CAAC,CAACrF,WAAW;YAC1B0F,MAAM,EAAEL,CAAC,CAACK,MAAM;YAChBvD,QAAQ,EAAEkD,CAAC,CAAClD,QAAQ;YACpBhC,SAAS,EAAEkF,CAAC,CAAClF,SAAS;YACtBC,OAAO,EAAEiF,CAAC,CAACjF,OAAO;YAClBC,WAAW,EAAEgF,CAAC,CAAChF,WAAW;YAC1BC,oBAAoB,EAAE+E,CAAC,CAAC/E,oBAAoB;YAC5CqF,cAAc,EAAEN,CAAC,CAACM,cAAc;YAChCC,cAAc,EAAEP,CAAC,CAACO,cAAc;YAChCnG,aAAa,EAAE6F,UAAU,CAACO,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;YACvCjG,gBAAgB,EAAEyF,CAAC,CAACzF,gBAAgB;YACpC0C,gBAAgB,EAAE+C,CAAC,CAAC/C,gBAAgB;YACpC1D,aAAa,EAAEyG,CAAC,CAACzG,aAAa;YAC9BC,kBAAkB,EAAEwG,CAAC,CAACxG,kBAAkB;YACxCC,oBAAoB,EAAEuG,CAAC,CAACvG,oBAAoB;YAC5CgH,iBAAiB,EAAET,CAAC,CAACS,iBAAiB;YACtCC,qBAAqB,EAAEV,CAAC,CAACU;WAC1B;QACH,CAAC,CAAC;QACF,IAAI,CAACvE,YAAY,GAAGwC,IAAI,CAACA,IAAI,CAAC/C,MAAM;MACtC,CAAC,MAAM;QACL,IAAI,CAAC+B,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;QACF;MACF;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACsC,sBAAsB,CAAC;EAC/C;EAEAE,qBAAqBA,CAAA;IACnB,IAAI,CAACC,iBAAiB,CAACC,IAAI,EAAE;EAC/B;EAEAC,sBAAsBA,CAAA;IACpB,IAAI,CAACF,iBAAiB,CAACG,IAAI,EAAE;EAC/B;EAEA/I,mBAAmBA,CAACC,EAAO;IACzB,MAAM+I,WAAW,GAAG,IAAI,CAACtE,aAAa,CAACuE,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAAC1E,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM;MACL,MAAM5D,IAAI,GAAG,IAAI,CAAC7C,WAAW,CAAC0G,IAAI,CAAEC,CAAU,IAAKA,CAAC,CAACpJ,EAAE,IAAIA,EAAE,CAAC;MAC9D,IAAI,CAACiG,WAAW,GAAGX,IAAI;MACvB,MAAM+D,GAAG,GAAG,IAAIC,IAAI,EAAE;MACtB,IAAI,CAACC,WAAW,GAAGxK,sDAAU,CAACsK,GAAG,EAAE,uBAAuB,CAAC;MAC3D,IAAI,CAACG,YAAY,EAAE;IACrB;EACF;EAEAC,oBAAoBA,CAACC,SAAc;IACjC,MAAMX,WAAW,GAAG,IAAI,CAACtE,aAAa,CAACuE,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAAC1E,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAACY,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACrF,MAAM,CAACsF,OAAO,CAAC;QAClBrC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAExI,mEAAU,CAACyI;OACtB,CAAC;MACF,IAAI,CAACpD,OAAO,CAAC2E,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAACtD,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAAClB,OAAO,CAAC2E,QAAQ,CAAC,CAAC,uBAAuBQ,SAAS,EAAE,CAAC,CAAC;IAC7D;EACF;EAEAF,YAAYA,CAAA;IACV,MAAMxB,KAAK,GAAG;MACZ0B,SAAS,EAAE,IAAI,CAACzD,WAAW,CAACjG,EAAE;MAC9ByF,MAAM,EAAE,IAAI,CAACd,WAAW;MACxB4E,WAAW,EAAEvK,mCAAM,EAAE,CAAC6K,MAAM,CAAC,sBAAsB,CAAC;MACpDC,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE;KACR;IACD,MAAMC,qBAAqB,GAAG,IAAI,CAAC3F,QAAQ,CAACmF,YAAY,CAACxB,KAAK,CAAC,CAAC3C,SAAS,CACtEC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;UAAE1C,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAElC,IAAI,CAACA;QAAI,CAAE,CAAC;QAC9D4E,UAAU,CAAC,MAAK;UACd,IAAI,CAACjE,WAAW,CAACtE,WAAW,GAAG,IAAI,CAACsE,WAAW,CAACtE,WAAW,GAAG,CAAC;QACjE,CAAC,EAAE,IAAI,CAAC;QACRwI,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;MAC1B,CAAC,MAAM;QACL,IAAI,CAAC/F,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAAC8D,qBAAqB,CAAC;EAC9C;EAEAO,WAAWA,CAAA;IACT,MAAMC,iBAAiB,GAAG,IAAI,CAACnG,QAAQ,CAACkG,WAAW,CAAC,IAAI,CAAC5F,WAAW,CAAC,CAACU,SAAS,CAAEC,IAAS,IAAI;MAC5F,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAAC/B,QAAQ,GAAGY,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACsE,iBAAiB,CAAC;EAC1C;EAEAC,kBAAkBA,CAACC,UAAU,EAAEC,IAAI;IACjC,IAAID,UAAU,IAAI,IAAI,EAAE;MACtB,IAAI,CAACzF,eAAe,CAACiB,IAAI,CAAC;QACxBlG,EAAE,EAAE2K,IAAI,CAAC3K,EAAE;QACX4K,YAAY,EAAED,IAAI,CAACC,YAAY;QAC/B/E,YAAY,EAAE8E,IAAI,CAAC9E,YAAY;QAC/BgF,4BAA4B,EAAE,IAAI,CAACjF,iBAAiB;QACpDkF,OAAO,EAAEC,QAAQ,CAACX,QAAQ,CAACY,MAAM;QACjCtB,SAAS,EAAE,IAAI,CAACuB;OACjB,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAChG,eAAe,CAACyB,GAAG,CAAC,CAACwB,CAAM,EAAEgD,KAAU,KAAI;QAC9C,IAAIP,IAAI,CAAC3K,EAAE,IAAIkI,CAAC,CAAClI,EAAE,EAAE;UACnB,IAAI,CAACiF,eAAe,CAACkG,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;IACJ;EACF;;;uCA3RW1N,aAAa,EAAAf,+DAAA,CAAAW,kFAAA,GAAAX,+DAAA,CAAA6O,6DAAA,GAAA7O,+DAAA,CAAA+O,oDAAA,GAAA/O,+DAAA,CAAAiP,+EAAA,GAAAjP,+DAAA,CAAAmP,+DAAA;IAAA;EAAA;;;YAAbpO,aAAa;MAAAlB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAAiP,uBAAA/O,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1B1BN,4DAAA,UAAK;UACLA,uDAAA,iBAAyB;UACzBA,0DAAA,EAAM;UACNA,4DAAA,aAA0C;UACxCA,uDAAA,aAEM;UAGAA,4DAFN,aAAiB,aACO,WAC4B;UAAAA,oDAAA,eAAQ;UAAMA,4DAAN,WAAM,QAAG;UAAAA,oDAAA,IAAyB;UAC5FA,0DAD4F,EAAI,EAAO,EAAI,EACrG;UAIAA,4DAHN,cAA2E,cAC3B,iBAEiB;UAA/BA,wDAAA,oBAAAsP,iDAAAhK,MAAA;YAAA,OAAU/E,GAAA,CAAAqL,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EAAC;UAC1DtF,4DAAA,iBAAiB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAS;UACjCA,4DAAA,iBAAuB;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAS;UACtCA,4DAAA,kBAAuB;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAS;UACtCA,4DAAA,kBAAuC;UAAAA,oDAAA,8BAAsB;UAAAA,0DAAA,EAAS;UACtEA,4DAAA,kBAAwC;UAAAA,oDAAA,+BAAuB;UAAAA,0DAAA,EAAS;UACxEA,4DAAA,kBAA8B;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAS;UACpDA,4DAAA,kBAAsC;UAAAA,oDAAA,6BAAqB;UAE/DA,0DAF+D,EAAS,EAC7D,EACL;UAGFA,4DAFJ,eAA8D,eACnC,eACyK;UAAzBA,wDAAA,mBAAAuP,6CAAA;YAAA,OAAShP,GAAA,CAAAsJ,YAAA,EAAc;UAAA,EAAC;UAC7L7J,uDAAA,eAA+B;UAGrCA,0DAFE,EAAM,EACF,EACA;UAEJA,4DADF,eAAuC,eAC2F;UAAzBA,wDAAA,mBAAAwP,6CAAA;YAAA,OAASjP,GAAA,CAAAuJ,YAAA,EAAc;UAAA,EAAC;UAGrI9J,0DAHM,EAAgI,EAC5H,EACF,EACF;UACNA,4DAAA,iBAA+C;UAA1BA,8DAAA,2BAAA0P,uDAAApK,MAAA;YAAAtF,gEAAA,CAAAO,GAAA,CAAA0F,WAAA,EAAAX,MAAA,MAAA/E,GAAA,CAAA0F,WAAA,GAAAX,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAA9CtF,0DAAA,EAA+C;UAqD/CA,wDApDA,KAAA4P,6BAAA,mBAAiD,KAAAC,6BAAA,mBAoDN;UAmE7C7P,0DAAA,EAAM;UAGNA,4DAAA,WAAK;UACHA,uDAAA,kBAAyB;UAC3BA,0DAAA,EAAM;UAQEA,4DAJR,eAAqI,eACzF,eACb,eACC,cACyB;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAK;UACnEA,4DAAA,kBAAmH;UAAnCA,wDAAA,mBAAA8P,gDAAA;YAAA,OAASvP,GAAA,CAAA6L,sBAAA,EAAwB;UAAA,EAAC;UAEpHpM,0DADE,EAAS,EACL;UAK6BA,4DAHnC,eAAwB,cACL,eACS,iBACO,SAAG;UAAAA,oDAAA,uBAAe;UAAIA,0DAAJ,EAAI,EAAQ;UAC5DA,4DAAA,iBAAyE;UAAAA,oDAAA,IAA4B;UACtGA,0DADsG,EAAQ,EACxG;UAE0BA,4DADhC,eAAwB,iBACQ,SAAG;UAAAA,oDAAA,mBAAW;UAAIA,0DAAJ,EAAI,EAAQ;UACxDA,4DAAA,iBAAsD;UAAAA,oDAAA,IAAiB;UACxEA,0DADwE,EAAQ,EAC1E;UAEyBA,4DAD/B,eAAwB,iBACO,SAAG;UAAAA,oDAAA,sBAAc;UAAIA,0DAAJ,EAAI,EAAQ;UAC3DA,4DAAA,iBAAsD;UAAAA,oDAAA,IAAe;UACtEA,0DADsE,EAAQ,EACxE;UAEyBA,4DAD/B,eAAwB,iBACO,SAAG;UAAAA,oDAAA,eAAO;UAAIA,0DAAJ,EAAI,EAAQ;UACpDA,4DAAA,iBAAsD;UAAAA,oDAAA,SAAC;UAG7DA,0DAH6D,EAAQ,EAC1D,EACH,EACF;UAEJA,4DADF,eAA0B,kBACwE;UAAnCA,wDAAA,mBAAA+P,gDAAA;YAAA,OAASxP,GAAA,CAAA6L,sBAAA,EAAwB;UAAA,EAAC;UAACpM,4DAAA,gBAAoB;UAACA,oDAAA,eAAM;UAAQA,0DAAR,EAAO,EAAU;UAC5IA,4DAAA,kBAAuE;UAAzBA,wDAAA,mBAAAgQ,gDAAA;YAAA,OAASzP,GAAA,CAAAwM,YAAA,EAAc;UAAA,EAAC;UAAC/M,4DAAA,gBAA2B;UAAAA,oDAAA,cAAM;UAKhHA,0DALgH,EAAO,EAAS,EACpH,EAEF,EACF,EACF;;;UAhMiEA,uDAAA,IAAyB;UAAzBA,gEAAA,KAAAO,GAAA,CAAA8F,YAAA,cAAyB;UA2BzErG,uDAAA,IAAyB;UAAzBA,8DAAA,YAAAO,GAAA,CAAA0F,WAAA,CAAyB;UACxCjG,uDAAA,EAAmB;UAAnBA,wDAAA,SAAAO,GAAA,CAAA+H,IAAA,WAAmB;UAoDFtI,uDAAA,EAAkB;UAAlBA,wDAAA,SAAAO,GAAA,CAAA+H,IAAA,WAAkB;UAyFyCtI,uDAAA,IAA4B;UAA5BA,+DAAA,CAAAO,GAAA,CAAAiJ,WAAA,CAAA9E,YAAA,CAA4B;UAI9C1E,uDAAA,GAAiB;UAAjBA,+DAAA,CAAAO,GAAA,CAAA0I,aAAA,CAAiB;UAIjBjJ,uDAAA,GAAe;UAAfA,+DAAA,CAAAO,GAAA,CAAAuM,WAAA,CAAe;;;qBDlKrEzK,0DAAY,EAAA6N,qDAAA,EAAAA,kDAAA,EAAAA,sDAAA,EAAEnO,qEAAe,EAAEW,qEAAe,EAAEC,gEAAmB,EAAA2N,yDAAA,EAAAA,wEAAA,EAAE9N,wDAAW,EAAAiO,2DAAA,EAAAA,uEAAA,EAAAA,iEAAA,EAAAA,4DAAA,EAAAA,oDAAA,EAAE7N,0DAAU;MAAA/B,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;AExBlE;AAE+D;AAC/C;AAIc;;;;;;;;ICqBhDb,4DAAA,WAAgD;IAC9CA,oDAAA,kCACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAA6C;IAC3CA,oDAAA,wCACF;IAAAA,0DAAA,EAAO;;;;;IANTA,4DAAA,eAA8F;IAI5FA,wDAHA,IAAAiR,+CAAA,mBAAgD,IAAAC,+CAAA,mBAGH;IAG/ClR,0DAAA,EAAO;;;;IANEA,uDAAA,EAAuC;IAAvCA,wDAAA,SAAAmR,MAAA,CAAA/H,YAAA,CAAAgI,QAAA,aAAuC;IAGvCpR,uDAAA,EAAoC;IAApCA,wDAAA,SAAAmR,MAAA,CAAA/H,YAAA,CAAAgI,QAAA,UAAoC;;;ADdzD,MAAOjQ,uBAAuB;EAGlCxB,YACU0R,GAAgB,EAChBzJ,QAAqB,EACrBE,OAAe,EACfD,MAAsB;IAHtB,KAAAwJ,GAAG,GAAHA,GAAG;IACH,KAAAzJ,QAAQ,GAARA,QAAQ;IACR,KAAAE,OAAO,GAAPA,OAAO;IACP,KAAAD,MAAM,GAANA,MAAM;IANR,KAAAY,WAAW,GAAmB,EAAE;EAOpC;EAGJzG,QAAQA,CAAA;IACN,IAAI,CAACsP,cAAc,EAAE;EACvB;EACAA,cAAcA,CAAA;IACZ,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACF,GAAG,CAACG,KAAK,CAAC;MACvCpI,YAAY,EAAE,CAAC,IAAI,EAAE4H,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,EAAEV,sDAAU,CAACW,KAAK,CAAC,CAAC;KACjF,CAAC;EACJ;EACA,IAAIvI,YAAYA,CAAA;IACd,OAAO,IAAI,CAACmI,kBAAkB,CAACK,GAAG,CAAC,cAAc,CAAgB;EACnE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACP,kBAAkB,CAACQ,KAAK,EAAE;MACjC,MAAMC,YAAY,GAAG,IAAI,CAACT,kBAAkB,CAAChG,KAAK;MAClDyG,YAAY,CAAC3D,OAAO,GAAGC,QAAQ,CAACX,QAAQ,CAACY,MAAM;MAE/C,MAAM0D,uBAAuB,GAAG,IAAI,CAACrK,QAAQ,CAACsK,wBAAwB,CAACF,YAAY,CAAC,CAACpJ,SAAS,CAAEC,IAAS,IAAI;QAC3G,IAAI,CAACA,IAAI,EAAE;UACT;UACA,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;YAAEC,MAAM,EAAE,OAAO;YAAEC,OAAO,EAAE,wCAAwC;YAAEE,QAAQ,EAAExI,+EAAU,CAACyI;UAAa,CAAE,CAAC;QAC/H,CAAC,MAAM;UACL;UACA,IAAI,CAACrD,MAAM,CAAC2F,OAAO,CAAC;YAClB1C,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAE,iFAAiF;YAC1FE,QAAQ,EAAExI,+EAAU,CAACyI;WACtB,CAAC;UACFuC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC3F,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;UAC7B,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,CAAC;MACF,IAAI,CAACqF,SAAS,GAAG,KAAK;MACtB,IAAI,CAACrJ,WAAW,CAACgB,IAAI,CAACwI,uBAAuB,CAAC;IAChD;EACF;EAEAvI,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;;;uCApDWtH,uBAAuB,EAAAnB,+DAAA,CAAAW,uDAAA,GAAAX,+DAAA,CAAA6O,2EAAA,GAAA7O,+DAAA,CAAA+O,mDAAA,GAAA/O,+DAAA,CAAAiP,4DAAA;IAAA;EAAA;;;YAAvB9N,uBAAuB;MAAAtB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAAgS,iCAAA9R,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfhCN,4DAFJ,aAAqC,aACd,aAC4B;UAC7CA,uDAAA,aAAkD;UAEhDA,4DADF,aAAyC,WACpB;UAAAA,oDAAA,4DACK;UAAAA,0DAAA,EAAI;UAC5BA,4DAAA,WAAmB;UAACA,oDAAA,qcAG+E;UAEvGA,0DAFuG,EAAI,EACrG,EACA;UAMMA,4DALZ,aAA+B,cACgB,cACnB,eACL,eACyC,aAClC;UAAAA,oDAAA,uBAAe;UAAAA,0DAAA,EAAI;UACrCA,4DAAA,eAA2B;UACvBA,oDAAA,iHAEJ;UAAAA,0DAAA,EAAM;UACNA,4DAAA,gBAA+D;UAAxBA,wDAAA,sBAAAqS,2DAAA;YAAA,OAAY9R,GAAA,CAAAsR,QAAA,EAAU;UAAA,EAAC;UAE5D7R,4DADF,eAAwB,iBACQ;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAQ;UACnDA,uDAAA,iBAA0H;UAC1HA,wDAAA,KAAAsS,wCAAA,mBAA8F;UAQhGtS,0DAAA,EAAM;UAEoCA,4DAD1C,eAAyB,kBACiB,gBAAoB;UAAAA,oDAAA,yBAAiB;UAAOA,0DAAP,EAAO,EAAS;UAE3FA,4DADF,eAAyB,aACW;UAAAA,oDAAA,aAAK;UAKjDA,0DALiD,EAAI,EACvC,EACF,EACD,EACH,EACF;UAEJA,4DADF,eAAiC,SAC5B;UAAAA,oDAAA,sBAAc;UAM7BA,0DAN6B,EAAI,EACjB,EACF,EACF,EACF,EACF,EACF;;;UA7BgBA,uDAAA,IAAgC;UAAhCA,wDAAA,cAAAO,GAAA,CAAAgR,kBAAA,CAAgC;UAITvR,uDAAA,GAAiE;UAAjEA,wDAAA,SAAAO,GAAA,CAAA6I,YAAA,CAAAmJ,OAAA,KAAAhS,GAAA,CAAA6I,YAAA,CAAAoJ,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAAiE;;;qBDdlGf,+DAAmB,EAAApQ,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,8DAAA,EAAAA,2DAAA,EAAEyP,iDAAI,EAAEtO,yDAAY,EAAAiN,uDAAA;MAAAlO,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;AEbb;AAE+D;AAC/C;AAGc;;;;;;;;ICoBhDb,4DAAA,WAAgD;IAC5CA,oDAAA,kCACJ;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAA6C;IAC3CA,oDAAA,wCACF;IAAAA,0DAAA,EAAO;;;;;IANTA,4DAAA,eAA8F;IAI5FA,wDAHA,IAAA6S,sCAAA,mBAAgD,IAAAC,sCAAA,mBAGH;IAG/C9S,0DAAA,EAAO;;;;IANEA,uDAAA,EAAuC;IAAvCA,wDAAA,SAAAmR,MAAA,CAAA/H,YAAA,CAAAgI,QAAA,aAAuC;IAGvCpR,uDAAA,EAAoC;IAApCA,wDAAA,SAAAmR,MAAA,CAAA/H,YAAA,CAAAgI,QAAA,UAAoC;;;;;IAQ7CpR,4DAAA,eAAsF;IACpFA,oDAAA,8BACF;IAAAA,0DAAA,EAAO;;;ADvBnB,MAAOoB,cAAc;EAGzBzB,YACU0R,GAAgB,EAChBzJ,QAAqB,EACrBE,OAAe,EACfD,MAAsB;IAHtB,KAAAwJ,GAAG,GAAHA,GAAG;IACH,KAAAzJ,QAAQ,GAARA,QAAQ;IACR,KAAAE,OAAO,GAAPA,OAAO;IACP,KAAAD,MAAM,GAANA,MAAM;IANR,KAAAY,WAAW,GAAmB,EAAE;EAOpC;EAGJzG,QAAQA,CAAA;IACN,IAAI,CAAC+Q,SAAS,EAAE;EAClB;EACAA,SAASA,CAAA;IACP,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC3B,GAAG,CAACG,KAAK,CAAC;MAC9BpI,YAAY,EAAE,CAAC,IAAI,EAAE4H,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,EAAEV,sDAAU,CAACW,KAAK,CAAC,CAAC,CAAC;MACjFsB,QAAQ,EAAE,CAAC,IAAI,EAAEjC,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC;KAC3D,CAAC;EACJ;EACA,IAAItI,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC4J,SAAS,CAACpB,GAAG,CAAC,cAAc,CAAgB;EAC1D;EACA,IAAIqB,QAAQA,CAAA;IACV,OAAO,IAAI,CAACD,SAAS,CAACpB,GAAG,CAAC,UAAU,CAAgB;EACtD;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACkB,SAAS,CAACjB,KAAK,EAAE;MACxB,MAAMmB,kBAAkB,GAAG,IAAI,CAACtL,QAAQ,CACrCmL,SAAS,CAAC,CAAC,IAAI,CAACC,SAAS,CAACzH,KAAK,CAACnC,YAAY,EAAE,IAAI,CAAC4J,SAAS,CAACzH,KAAK,CAAC0H,QAAQ,CAAC,CAAC,CAC7ErK,SAAS,CAAEuK,GAAQ,IAAI;QACtB,IAAIA,GAAG,CAACnJ,MAAM,IAAI,CAAC,EAAE;UACnB,IAAImJ,GAAG,CAACtK,IAAI,CAACmC,OAAO,IAAI,oBAAoB,EAAE;YAC5C,IAAI,CAACpD,QAAQ,CAACwL,QAAQ,CAACD,GAAG,CAACtK,IAAI,CAACA,IAAI,CAAC;YACrC,MAAMwK,YAAY,GAAG,IAAI,CAACzL,QAAQ,CAAC2E,YAAY,EAAE;YACjD,IAAI,CAAC3E,QAAQ,CAAC0L,cAAc,CAACD,YAAY,CAAC;YAE1C,IAAI,CAACxL,MAAM,CAAC2F,OAAO,CAAC;cAAE1C,MAAM,EAAE,SAAS;cAAEC,OAAO,EAAEoI,GAAG,CAACtK,IAAI,CAACmC,OAAO;cAAEC,QAAQ,EAAExI,+EAAU,CAACyI;YAAa,CAAE,CAAC;YACzG,IAAImI,YAAY,CAAC7G,QAAQ,IAAI,OAAO,EAAE;cACpC,IAAI,CAAC1E,OAAO,CAAC2E,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;YAC5C,CAAC,MAAM;cACL,IAAI,CAAC3E,OAAO,CAAC2E,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YAClC;UACF,CAAC,MAAM;YACL;YACA,IAAI,CAAC5E,MAAM,CAACgD,KAAK,CAAC;cAAEC,MAAM,EAAE,OAAO;cAAEC,OAAO,EAAEoI,GAAG,CAACtK,IAAI,CAACmC,OAAO;cAAEC,QAAQ,EAAExI,+EAAU,CAACyI;YAAa,CAAE,CAAC;UACvG;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAACrD,MAAM,CAACgD,KAAK,CAAC;YAAEC,MAAM,EAAE,OAAO;YAAEC,OAAO,EAAEoI,GAAG,CAACnI,OAAO;YAAEC,QAAQ,EAAExI,+EAAU,CAACyI;UAAa,CAAE,CAAC;QAClG;MACF,CAAC,CAAC;MACJ,IAAI,CAAC4G,SAAS,GAAG,KAAK;MACtB,IAAI,CAACrJ,WAAW,CAACgB,IAAI,CAACyJ,kBAAkB,CAAC;IAC3C;EACF;EACAxJ,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;;;uCA3DWrH,cAAc,EAAApB,+DAAA,CAAAW,uDAAA,GAAAX,+DAAA,CAAA6O,2EAAA,GAAA7O,+DAAA,CAAA+O,mDAAA,GAAA/O,+DAAA,CAAAiP,4DAAA;IAAA;EAAA;;;YAAd7N,cAAc;MAAAvB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAAmT,wBAAAjT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdvBN,4DAFJ,aAAqC,aACd,aACwD;UACzEA,uDAAA,aAAgE;UAE9DA,4DADF,aAAyC,WACpB;UAAAA,oDAAA,4DACK;UAAAA,0DAAA,EAAI;UAC5BA,4DAAA,WAAmB;UAACA,oDAAA,qcAG+E;UAEvGA,0DAFuG,EAAI,EACnG,EACF;UAMIA,4DALV,aAA+B,cACqB,cACzB,eACmB,eACY,gBACM;UAAxBA,wDAAA,sBAAAwT,kDAAA;YAAA,OAAYjT,GAAA,CAAAsR,QAAA,EAAU;UAAA,EAAC;UACnD7R,uDAAA,eAEM;UAEJA,4DADF,eAA6B,iBACG;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAQ;UACnDA,uDAAA,iBAA6G;UAC7GA,wDAAA,KAAAyT,+BAAA,mBAA8F;UAQhGzT,0DAAA,EAAM;UAEJA,4DADF,eAA6B,iBACG;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAQ;UAC9CA,uDAAA,iBAAqH;UACrHA,wDAAA,KAAA0T,+BAAA,mBAAsF;UAGxF1T,0DAAA,EAAM;UAEoCA,4DAD1C,eAA8B,kBACY,gBAAoB;UAAAA,oDAAA,aAAK;UAAOA,0DAAP,EAAO,EAAS;UAE/EA,4DADF,eAA8B,aACZ;UAAAA,oDAAA,+BAAuB;UAAMA,4DAAN,YAAM,aAA0B;UAAAA,oDAAA,oBAAY;UAK7FA,0DAL6F,EAAI,EAAO,EAAI,EAC9F,EACF,EACD,EACH,EACF;UAEJA,4DADF,eAA8B,aACqF;UAAAA,oDAAA,sBAAc;UAM3IA,0DAN2I,EAAI,EAC/H,EACF,EACF,EACF,EACF,EACF;;;UAvCcA,uDAAA,IAAuB;UAAvBA,wDAAA,cAAAO,GAAA,CAAAyS,SAAA,CAAuB;UAOEhT,uDAAA,GAAiE;UAAjEA,wDAAA,SAAAO,GAAA,CAAA6I,YAAA,CAAAmJ,OAAA,KAAAhS,GAAA,CAAA6I,YAAA,CAAAoJ,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAAiE;UAYjE9R,uDAAA,GAAyD;UAAzDA,wDAAA,SAAAO,GAAA,CAAA0S,QAAA,CAAAV,OAAA,KAAAhS,GAAA,CAAA0S,QAAA,CAAAT,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAAyD;;;qBDzB1Ff,+DAAmB,EAAApQ,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,8DAAA,EAAAA,2DAAA,EAAEyP,iDAAI,EAAEtO,yDAAY,EAAAiN,uDAAA;MAAAlO,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;AEZb;AAUf;AAC+B;AAGc;;;;;;;;ICUlDb,4DAAA,eAA8F;IAC5FA,oDAAA,+BACF;IAAAA,0DAAA,EAAO;;;;;IAKPA,4DAAA,eAAuF;IACrFA,oDAAA,8BACF;IAAAA,0DAAA,EAAO;;;;;IAMLA,4DAAA,WAA+C;IAC7CA,oDAAA,iCACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAAgD;IAC9CA,oDAAA,uCACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAAgD;IAC9CA,oDAAA,uCACF;IAAAA,0DAAA,EAAO;;;;;IATTA,4DAAA,eAA6F;IAO3FA,wDANA,IAAA2T,yCAAA,mBAA+C,IAAAC,yCAAA,mBAGC,IAAAC,yCAAA,mBAGA;IAGlD7T,0DAAA,EAAO;;;;IATEA,uDAAA,EAAsC;IAAtCA,wDAAA,SAAAmR,MAAA,CAAA2C,WAAA,CAAAC,MAAA,kBAAA5C,MAAA,CAAA2C,WAAA,CAAAC,MAAA,aAAsC;IAGtC/T,uDAAA,EAAuC;IAAvCA,wDAAA,SAAAmR,MAAA,CAAA2C,WAAA,CAAAC,MAAA,kBAAA5C,MAAA,CAAA2C,WAAA,CAAAC,MAAA,cAAuC;IAGvC/T,uDAAA,EAAuC;IAAvCA,wDAAA,SAAAmR,MAAA,CAAA2C,WAAA,CAAAC,MAAA,kBAAA5C,MAAA,CAAA2C,WAAA,CAAAC,MAAA,cAAuC;;;;;IAS9C/T,4DAAA,WAAgD;IAC9CA,oDAAA,kCACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAA6C;IAC3CA,oDAAA,wCACF;IAAAA,0DAAA,EAAO;;;;;IANTA,4DAAA,eAA+F;IAI7FA,wDAHA,IAAAgU,yCAAA,mBAAgD,IAAAC,yCAAA,mBAGH;IAG/CjU,0DAAA,EAAO;;;;IANEA,uDAAA,EAAuC;IAAvCA,wDAAA,SAAAmR,MAAA,CAAA/H,YAAA,CAAAgI,QAAA,aAAuC;IAGvCpR,uDAAA,EAAoC;IAApCA,wDAAA,SAAAmR,MAAA,CAAA/H,YAAA,CAAAgI,QAAA,UAAoC;;;;;IASvCpR,4DAAA,WAA4C;IAC1CA,oDAAA,8BACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAA6C;IAC3CA,oDAAA,qDACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAA6C;IAC3CA,oDAAA,yDACF;IAAAA,0DAAA,EAAO;;;;;IATbA,4DAAA,eAAsF;IAOhFA,wDANA,IAAAkU,yCAAA,mBAA4C,IAAAC,yCAAA,mBAGC,IAAAC,yCAAA,mBAGA;IAGnDpU,0DAAA,EAAO;;;;IATMA,uDAAA,EAAmC;IAAnCA,wDAAA,SAAAmR,MAAA,CAAA8B,QAAA,CAAAc,MAAA,kBAAA5C,MAAA,CAAA8B,QAAA,CAAAc,MAAA,aAAmC;IAGnC/T,uDAAA,EAAoC;IAApCA,wDAAA,SAAAmR,MAAA,CAAA8B,QAAA,CAAAc,MAAA,kBAAA5C,MAAA,CAAA8B,QAAA,CAAAc,MAAA,cAAoC;IAGpC/T,uDAAA,EAAoC;IAApCA,wDAAA,SAAAmR,MAAA,CAAA8B,QAAA,CAAAc,MAAA,kBAAA5C,MAAA,CAAA8B,QAAA,CAAAc,MAAA,cAAoC;;;;;IAS7C/T,4DAAA,WAAmD;IACjDA,oDAAA,sCACF;IAAAA,0DAAA,EAAO;;;;;IAHXA,4DAAA,eAAoG;IAChGA,wDAAA,IAAAqU,yCAAA,mBAAmD;IAGvDrU,0DAAA,EAAO;;;;IAHIA,uDAAA,EAA0C;IAA1CA,wDAAA,SAAAmR,MAAA,CAAAmD,eAAA,CAAAlD,QAAA,aAA0C;;;;;IAIrDpR,4DAAA,eAA+F;IAC3FA,oDAAA,kDACJ;IAAAA,0DAAA,EAAO;;;AD/DnB,MAAOqB,iBAAiB;EAG5B1B,YACU0R,GAAgB,EAChBzJ,QAAqB,EACrBE,OAAe,EACfD,MAAsB;IAHtB,KAAAwJ,GAAG,GAAHA,GAAG;IACH,KAAAzJ,QAAQ,GAARA,QAAQ;IACR,KAAAE,OAAO,GAAPA,OAAO;IACP,KAAAD,MAAM,GAANA,MAAM;IANR,KAAAY,WAAW,GAAmB,EAAE;EAOrC;EAGHzG,QAAQA,CAAA;IACN,IAAI,CAACuS,kBAAkB,EAAE;EAC3B;EACAA,kBAAkBA,CAAA;IAChB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACnD,GAAG,CAACG,KAAK,CAChC;MACEiD,SAAS,EAAE,CAAC,IAAI,EAAEzD,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MAC5DgD,QAAQ,EAAE,CAAC,IAAI,EAAE1D,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MAC3DoC,WAAW,EAAE,CACX,IAAI,EACJ9C,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,EAAEV,sDAAU,CAAC2D,SAAS,CAAC,EAAE,CAAC,EAAE3D,sDAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAC9F;MACDxL,YAAY,EAAE,CAAC,IAAI,EAAE4H,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,EAAEV,sDAAU,CAACW,KAAK,CAAC,CAAC,CAAC;MACjFsB,QAAQ,EAAE,CAAC,IAAI,EAAEjC,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,EAAEV,sDAAU,CAAC2D,SAAS,CAAC,CAAC,CAAC,EAAE3D,sDAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9GN,eAAe,EAAE,CAAC,IAAI,EAAEtD,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC;KAClE,EACD;MAAEmD,SAAS,EAAE,CAAC,IAAI,CAACC,wBAAwB;IAAC,CAAE,CAC/C;EACH;EACAA,wBAAwBA,CAACC,EAAmB;IAC1C,OAAOA,EAAE,CAACnD,GAAG,CAAC,UAAU,CAAC,EAAErG,KAAK,KAAKwJ,EAAE,CAACnD,GAAG,CAAC,iBAAiB,CAAC,EAAErG,KAAK,GAAG,IAAI,GAAG;MAAEyJ,UAAU,EAAE;IAAI,CAAE;EACrG;EACA,IAAIP,SAASA,CAAA;IACX,OAAO,IAAI,CAACD,YAAY,CAAC5C,GAAG,CAAC,WAAW,CAAgB;EAC1D;EACA,IAAI8C,QAAQA,CAAA;IACV,OAAO,IAAI,CAACF,YAAY,CAAC5C,GAAG,CAAC,UAAU,CAAgB;EACzD;EACA,IAAIkC,WAAWA,CAAA;IACb,OAAO,IAAI,CAACU,YAAY,CAAC5C,GAAG,CAAC,aAAa,CAAgB;EAC5D;EACA,IAAIxI,YAAYA,CAAA;IACd,OAAO,IAAI,CAACoL,YAAY,CAAC5C,GAAG,CAAC,cAAc,CAAgB;EAC7D;EACA,IAAIqB,QAAQA,CAAA;IACV,OAAO,IAAI,CAACuB,YAAY,CAAC5C,GAAG,CAAC,UAAU,CAAgB;EACzD;EACA,IAAI0C,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACE,YAAY,CAAC5C,GAAG,CAAC,iBAAiB,CAAgB;EAChE;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAAC0C,YAAY,CAACzC,KAAK,EAAE;MAC3B,MAAMkD,QAAQ,GAAG,IAAI,CAACT,YAAY,CAACjJ,KAAK;MACxC0J,QAAQ,CAACzI,QAAQ,GAAG,MAAM;MAE1B,MAAM0I,iBAAiB,GAAG,IAAI,CAACtN,QAAQ,CAACuN,YAAY,CAACF,QAAQ,CAAC,CAACrM,SAAS,CAAEC,IAAS,IAAI;QACrF,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;UACpB;UACA,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;YAAE1C,MAAM,EAAE,SAAS;YAAEC,OAAO,EAAElC,IAAI,CAACA,IAAI;YAAEoC,QAAQ,EAAExI,+EAAU,CAACyI;UAAa,CAAE,CAAC;UAClGuC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC3F,OAAO,CAAC2E,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;UAC9B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL;UACA,IAAI,CAAC5E,MAAM,CAACgD,KAAK,CAAC;YAAEC,MAAM,EAAE,OAAO;YAAEC,OAAO,EAAElC,IAAI,CAACmC,OAAO;YAAEC,QAAQ,EAAExI,+EAAU,CAACyI;UAAa,CAAE,CAAC;QACnG;MACF,CAAC,CAAC;MACF,IAAI,CAAC4G,SAAS,GAAG,KAAK;MACtB,IAAI,CAACrJ,WAAW,CAACgB,IAAI,CAACyL,iBAAiB,CAAC;IAC1C;EACF;EACAxL,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;;;uCA3EWpH,iBAAiB,EAAArB,+DAAA,CAAAW,uDAAA,GAAAX,+DAAA,CAAA6O,2EAAA,GAAA7O,+DAAA,CAAA+O,mDAAA,GAAA/O,+DAAA,CAAAiP,4DAAA;IAAA;EAAA;;;YAAjB5N,iBAAiB;MAAAxB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAAgV,2BAAA9U,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrB1BN,4DAHJ,aAAqC,aACd,aAEwD;UACzEA,uDAAA,aAAgE;UAE9DA,4DADF,aAA8B,WACT;UAAAA,oDAAA,4DACK;UAAAA,0DAAA,EAAI;UAC5BA,4DAAA,WAAmB;UAACA,oDAAA,qcAG+E;UAEvGA,0DAFuG,EAAI,EACnG,EACF;UAOIA,4DALV,aAAsC,cACc,cACzB,eACmB,eACY,gBACS;UAAxBA,wDAAA,sBAAAqV,qDAAA;YAAA,OAAY9U,GAAA,CAAAsR,QAAA,EAAU;UAAA,EAAC;UAEpD7R,4DADF,eAA6B,iBACG;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAQ;UAChDA,uDAAA,iBAAuG;UACvGA,wDAAA,KAAAsV,kCAAA,mBAA8F;UAGhGtV,0DAAA,EAAM;UAEJA,4DADF,eAA6B,iBACG;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EAAQ;UACzDA,uDAAA,iBAA2F;UAC3FA,wDAAA,KAAAuV,kCAAA,mBAAuF;UAGzFvV,0DAAA,EAAM;UAEJA,4DADF,eAA6B,iBACG;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAQ;UAClDA,uDAAA,iBAAiG;UACjGA,wDAAA,KAAAwV,kCAAA,mBAA6F;UAW/FxV,0DAAA,EAAM;UAEJA,4DADF,eAA6B,iBACG;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAQ;UACnDA,uDAAA,iBAAmG;UACnGA,wDAAA,KAAAyV,kCAAA,mBAA+F;UAQjGzV,0DAAA,EAAM;UAEJA,4DADF,eAA6B,iBACG;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAQ;UAC9CA,uDAAA,iBAA8F;UAC9FA,wDAAA,KAAA0V,kCAAA,mBAAsF;UAWxF1V,0DAAA,EAAM;UAEJA,4DADF,eAA6B,iBACG;UAAAA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAQ;UACtDA,uDAAA,iBAA6G;UAM7GA,wDALA,KAAA2V,kCAAA,mBAAoG,KAAAC,kCAAA,mBAKL;UAGjG5V,0DAAA,EAAM;UAEoCA,4DAD1C,eAA8B,kBACY,gBAAoB;UAAAA,oDAAA,gBAAQ;UAAOA,0DAAP,EAAO,EAAS;UAElFA,4DADF,eAA8B,aACZ;UAAAA,oDAAA,4BAAoB;UAAMA,4DAAN,YAAM,aAAkB;UAAAA,oDAAA,iBAAS;UAK/EA,0DAL+E,EAAI,EAAO,EAAI,EAChF,EACF,EACD,EACH,EACF;UAEJA,4DADF,eAA8B,aACqF;UAAAA,oDAAA,sBAAc;UAM3IA,0DAN2I,EAAI,EAC/H,EACF,EACF,EACF,EACF,EACF;;;UArFcA,uDAAA,IAA0B;UAA1BA,wDAAA,cAAAO,GAAA,CAAAiU,YAAA,CAA0B;UAIIxU,uDAAA,GAA4D;UAA5DA,wDAAA,SAAAO,GAAA,CAAAkU,SAAA,CAAAlC,OAAA,KAAAhS,GAAA,CAAAkU,SAAA,CAAAjC,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAA4D;UAOjE9R,uDAAA,GAA0D;UAA1DA,wDAAA,SAAAO,GAAA,CAAAmU,QAAA,CAAAnC,OAAA,KAAAhS,GAAA,CAAAmU,QAAA,CAAAlC,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAA0D;UAO1D9R,uDAAA,GAAgE;UAAhEA,wDAAA,SAAAO,GAAA,CAAAuT,WAAA,CAAAvB,OAAA,KAAAhS,GAAA,CAAAuT,WAAA,CAAAtB,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAAgE;UAehE9R,uDAAA,GAAkE;UAAlEA,wDAAA,SAAAO,GAAA,CAAA6I,YAAA,CAAAmJ,OAAA,KAAAhS,GAAA,CAAA6I,YAAA,CAAAoJ,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAAkE;UAYlE9R,uDAAA,GAAyD;UAAzDA,wDAAA,SAAAO,GAAA,CAAA0S,QAAA,CAAAV,OAAA,KAAAhS,GAAA,CAAA0S,QAAA,CAAAT,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAAyD;UAezD9R,uDAAA,GAAuE;UAAvEA,wDAAA,SAAAO,GAAA,CAAA+T,eAAA,CAAA/B,OAAA,KAAAhS,GAAA,CAAA+T,eAAA,CAAA9B,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAAuE;UAKvE9R,uDAAA,EAAkE;UAAlEA,wDAAA,SAAAO,GAAA,CAAAiU,YAAA,CAAApD,QAAA,kBAAA7Q,GAAA,CAAA+T,eAAA,CAAAvC,KAAA,CAAkE;;;qBDjEnGhB,+DAAmB,EAAApQ,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,8DAAA,EAAAA,2DAAA,EAAEyP,iDAAI,EAAEtO,yDAAY,EAAAiN,uDAAA;MAAAlO,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;AEpBb;AAUf;AAC+C;AAGF;;;;;;;;ICa9Cb,4DAAA,WAA4C;IAC1CA,oDAAA,8BACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAA6C;IAC3CA,oDAAA,qDACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAA6C;IAC3CA,oDAAA,yDACF;IAAAA,0DAAA,EAAO;;;;;IATXA,4DAAA,eAAsF;IAOlFA,wDANA,IAAA6V,8CAAA,mBAA4C,IAAAC,8CAAA,mBAGC,IAAAC,8CAAA,mBAGA;IAGjD/V,0DAAA,EAAO;;;;IATIA,uDAAA,EAAmC;IAAnCA,wDAAA,SAAAmR,MAAA,CAAA8B,QAAA,CAAAc,MAAA,kBAAA5C,MAAA,CAAA8B,QAAA,CAAAc,MAAA,aAAmC;IAGnC/T,uDAAA,EAAoC;IAApCA,wDAAA,SAAAmR,MAAA,CAAA8B,QAAA,CAAAc,MAAA,kBAAA5C,MAAA,CAAA8B,QAAA,CAAAc,MAAA,cAAoC;IAGpC/T,uDAAA,EAAoC;IAApCA,wDAAA,SAAAmR,MAAA,CAAA8B,QAAA,CAAAc,MAAA,kBAAA5C,MAAA,CAAA8B,QAAA,CAAAc,MAAA,cAAoC;;;;;IAS7C/T,4DAAA,WAAmD;IACjDA,oDAAA,sCACF;IAAAA,0DAAA,EAAO;;;;;IAHTA,4DAAA,eAAoG;IAClGA,wDAAA,IAAAgW,8CAAA,mBAAmD;IAGrDhW,0DAAA,EAAO;;;;IAHEA,uDAAA,EAA0C;IAA1CA,wDAAA,SAAAmR,MAAA,CAAAmD,eAAA,CAAAP,MAAA,kBAAA5C,MAAA,CAAAmD,eAAA,CAAAP,MAAA,aAA0C;;;;;IAInD/T,4DAAA,eAA4F;IAC1FA,oDAAA,kDACF;IAAAA,0DAAA,EAAO;;;ADxBnB,MAAOsB,sBAAsB;EAGjC3B,YACU0R,GAAgB,EAChBzJ,QAAqB,EACrBE,OAAe,EACfmO,cAA8B,EAC9BpO,MAAsB;IAJtB,KAAAwJ,GAAG,GAAHA,GAAG;IACH,KAAAzJ,QAAQ,GAARA,QAAQ;IACR,KAAAE,OAAO,GAAPA,OAAO;IACP,KAAAmO,cAAc,GAAdA,cAAc;IACd,KAAApO,MAAM,GAANA,MAAM;IAPR,KAAAY,WAAW,GAAmB,EAAE;EAQrC;EAIHzG,QAAQA,CAAA;IACN,IAAI,CAACkU,cAAc,EAAE;EACvB;EACAC,eAAeA,CAAA;IACb,IAAI,CAACF,cAAc,CAACG,WAAW,CAACxN,SAAS,CAAEyN,MAAM,IAAI;MACnD,IAAIA,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;QACzB,IAAI,CAACrN,MAAM,GAAGqN,MAAM,CAAC,KAAK,CAAC;MAC7B,CAAC,MAAM;QACL,IAAI,CAACvO,OAAO,CAAC2E,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QACzC;MACF;IACF,CAAC,CAAC;EACJ;EACAyJ,cAAcA,CAAA;IACZ,IAAI,CAACI,SAAS,GAAG,IAAI,CAACjF,GAAG,CAACG,KAAK,CAC7B;MACEyB,QAAQ,EAAE,CAAC,IAAI,EAAEjC,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,EAAEV,sDAAU,CAAC2D,SAAS,CAAC,CAAC,CAAC,EAAE3D,sDAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9GN,eAAe,EAAE,CAAC,IAAI,EAAEtD,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC;KAClE,EACD;MAAEmD,SAAS,EAAE,CAAC,IAAI,CAACC,wBAAwB;IAAC,CAAE,CAC/C;EACH;EACAA,wBAAwBA,CAACC,EAAmB;IAC1C,OAAOA,EAAE,CAACnD,GAAG,CAAC,UAAU,CAAC,EAAErG,KAAK,KAAKwJ,EAAE,CAACnD,GAAG,CAAC,iBAAiB,CAAC,EAAErG,KAAK,GAAG,IAAI,GAAG;MAAEyJ,UAAU,EAAE;IAAI,CAAE;EACrG;EAEA,IAAI/B,QAAQA,CAAA;IACV,OAAO,IAAI,CAACqD,SAAS,CAAC1E,GAAG,CAAC,UAAU,CAAgB;EACtD;EACA,IAAI0C,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACgC,SAAS,CAAC1E,GAAG,CAAC,iBAAiB,CAAgB;EAC7D;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACwE,SAAS,CAACvE,KAAK,EAAE;MACxB,MAAMwE,cAAc,GAAG,IAAI,CAACD,SAAS,CAAC/K,KAAK;MAC3CgL,cAAc,CAACC,GAAG,GAAG,IAAI,CAACxN,MAAM;MAChC,MAAMyN,sBAAsB,GAAG,IAAI,CAAC7O,QAAQ,CAAC8O,aAAa,CAACH,cAAc,CAAC,CAAC3N,SAAS,CAAEC,IAAI,IAAI;QAC5F,IAAIA,IAAI,IAAI,SAAS,EAAE;UACrB;UACA,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;YAAEC,MAAM,EAAE,OAAO;YAAEC,OAAO,EAAE,uBAAuB;YAAEE,QAAQ,EAAExI,+EAAU,CAACyI;UAAa,CAAE,CAAC;QAC9G,CAAC,MAAM;UACL;UACA,IAAI,CAACrD,MAAM,CAAC2F,OAAO,CAAC;YAAE1C,MAAM,EAAE,SAAS;YAAEC,OAAO,EAAE,gCAAgC;YAAEE,QAAQ,EAAExI,+EAAU,CAACyI;UAAa,CAAE,CAAC;UACzHuC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC3F,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;UAC7B,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,CAAC;MACF,IAAI,CAACqF,SAAS,GAAG,KAAK;MACtB,IAAI,CAACrJ,WAAW,CAACgB,IAAI,CAACgN,sBAAsB,CAAC;IAC/C;EACF;EACA/M,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;;;uCArEWnH,sBAAsB,EAAAtB,+DAAA,CAAAW,uDAAA,GAAAX,+DAAA,CAAA6O,2EAAA,GAAA7O,+DAAA,CAAA+O,mDAAA,GAAA/O,+DAAA,CAAA+O,2DAAA,GAAA/O,+DAAA,CAAAiP,4DAAA;IAAA;EAAA;;;YAAtB3N,sBAAsB;MAAAzB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAAwW,gCAAAtW,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtB/BN,4DAFJ,aAAqC,aACd,aAC4B;UAC7CA,uDAAA,aAAkD;UAEhDA,4DADF,aAAyC,WACpB;UAAAA,oDAAA,4DACK;UAAAA,0DAAA,EAAI;UAC5BA,4DAAA,WAAmB;UAACA,oDAAA,qcAG+E;UAEvGA,0DAFuG,EAAI,EACrG,EACA;UAMMA,4DALZ,aAA+B,cACgB,cACnB,eACL,eACyC,aAClC;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAI;UAClCA,4DAAA,eAA2B;UACvBA,oDAAA,0DACJ;UAAAA,0DAAA,EAAM;UACNA,4DAAA,gBAAsD;UAAxBA,wDAAA,sBAAA6W,0DAAA;YAAA,OAAYtW,GAAA,CAAAsR,QAAA,EAAU;UAAA,EAAC;UAEnD7R,4DADF,eAAwB,iBACQ;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAQ;UAClDA,uDAAA,iBAA0G;UAC1GA,wDAAA,KAAA8W,uCAAA,mBAAsF;UAWxF9W,0DAAA,EAAM;UAEJA,4DADF,eAAwB,iBACQ;UAAAA,oDAAA,4BAAoB;UAAAA,0DAAA,EAAQ;UAC1DA,uDAAA,iBAAuG;UAMvGA,wDALA,KAAA+W,uCAAA,mBAAoG,KAAAC,uCAAA,mBAKR;UAG9FhX,0DAAA,EAAM;UAEoCA,4DAD1C,eAAyB,kBACiB,gBAAoB;UAAAA,oDAAA,uBAAe;UAAOA,0DAAP,EAAO,EAAS;UAEzFA,4DADF,eAAyB,aACS;UAAAA,oDAAA,aAAK;UAK/CA,0DAL+C,EAAI,EACrC,EACF,EACD,EACH,EACF;UAEJA,4DADF,eAAiC,aACkF;UAAAA,oDAAA,sBAAc;UAM3IA,0DAN2I,EAAI,EAC/H,EACF,EACF,EACF,EACF,EACF;;;UA5CgBA,uDAAA,IAAuB;UAAvBA,wDAAA,cAAAO,GAAA,CAAA+V,SAAA,CAAuB;UAIAtW,uDAAA,GAAyD;UAAzDA,wDAAA,SAAAO,GAAA,CAAA0S,QAAA,CAAAV,OAAA,KAAAhS,GAAA,CAAA0S,QAAA,CAAAT,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAAyD;UAezD9R,uDAAA,GAAuE;UAAvEA,wDAAA,SAAAO,GAAA,CAAA+T,eAAA,CAAA/B,OAAA,KAAAhS,GAAA,CAAA+T,eAAA,CAAA9B,OAAA,IAAAjS,GAAA,CAAAuR,SAAA,EAAuE;UAKvE9R,uDAAA,EAA+D;UAA/DA,wDAAA,SAAAO,GAAA,CAAA+V,SAAA,CAAAlF,QAAA,kBAAA7Q,GAAA,CAAA+T,eAAA,CAAAvC,KAAA,CAA+D;;;qBD1BhGhB,+DAAmB,EAAApQ,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,8DAAA,EAAAA,2DAAA,EAAEyP,iDAAI,EAAEtO,yDAAY,EAAAiN,uDAAA;MAAAlO,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;AEnBI;AAER;AACF;AAEa;;;;;;;;ICUxBb,4DADtB,aAAoD,aAC9B,YAAyD;IAAAA,oDAAA,aAAM;IACrFA,0DADqF,EAAI,EAAK,EACzF;;;;;;IAKLA,4DADF,YAAsC,YACkC;IAA1BA,wDAAA,mBAAAkX,kDAAA;MAAAlX,2DAAA,CAAAgD,GAAA;MAAA,MAAAmU,MAAA,GAAAnX,2DAAA;MAAA,OAAAA,yDAAA,CAASmX,MAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAACpX,oDAAA,YAAK;IAC7EA,0DAD6E,EAAI,EAC5E;;;;;;IAEHA,4DADF,YAAsC,YACqC;IAA7BA,wDAAA,mBAAAqX,kDAAA;MAAArX,2DAAA,CAAAsX,GAAA;MAAA,MAAAH,MAAA,GAAAnX,2DAAA;MAAA,OAAAA,yDAAA,CAASmX,MAAA,CAAAI,gBAAA,EAAkB;IAAA,EAAC;IAACvX,oDAAA,eAAQ;IACnFA,0DADmF,EAAI,EAClF;;;;;;IAOmBA,4DADtB,aAAoD,aAC9B,YAAsI;IAAAA,oDAAA,iBAAU;IAAIA,0DAAJ,EAAI,EAAK;IAC7KA,uDAAA,aAA0C;IACtBA,4DAApB,aAAoB,YAAyE;IAAtBA,wDAAA,mBAAAwX,uDAAA;MAAAxX,2DAAA,CAAAuF,GAAA;MAAA,MAAA4R,MAAA,GAAAnX,2DAAA;MAAA,OAAAA,yDAAA,CAASmX,MAAA,CAAAM,SAAA,EAAW;IAAA,EAAC;IAACzX,oDAAA,cAAO;IAEtGA,0DAFsG,EAAI,EAAK,EAE1G;;;;IAJ0GA,uDAAA,GAA2C;IAA3CA,oEAAA,kCAAAmX,MAAA,CAAAjP,WAAA,KAA2C;;;;;IAL1JlI,4DADF,YAAuD,WACF;IACjDA,uDAAA,cAAgG;IAAAA,oDAAA,GAC/F;IAAAA,uDAAA,eAA2B;IAC9BA,0DAAA,EAAI;IACJA,wDAAA,IAAA2X,mCAAA,iBAAoD;IAMtD3X,0DAAA,EAAK;;;;IATIA,uDAAA,GAAmB;IAAnBA,mEAAA,QAAAmX,MAAA,CAAAjK,SAAA,EAAAlN,2DAAA,CAAmB;IAAwEA,uDAAA,EAC/F;IAD+FA,gEAAA,iBAAAmX,MAAA,CAAAS,UAAA,MAC/F;;;ADhBT,MAAOlV,eAAe;EAQ1B/C,YAAoBiI,QAAqB,EAAUE,OAAe;IAA9C,KAAAF,QAAQ,GAARA,QAAQ;IAAuB,KAAAE,OAAO,GAAPA,OAAO;IAP1D,KAAA+P,OAAO,GAAG,KAAK;IAKP,KAAApP,WAAW,GAAmB,EAAE;EAE6B;EAErEzG,QAAQA,CAAA;IACN,MAAM0G,oBAAoB,GAAG,IAAI,CAACd,QAAQ,CACvCe,cAAc,EAAE,CAChBC,SAAS,CAAEC,IAAS,IAAI;MACvB,MAAMiP,QAAQ,GAAG,IAAI,CAAClQ,QAAQ,CAACmB,aAAa,EAAE;MAC9C,IAAI+O,QAAQ,IAAI,IAAI,IAAIjP,IAAI,IAAI,IAAI,EAAE;QACpC,IAAI,CAACgP,OAAO,GAAG,IAAI;QACnBhP,IAAI,IAAI,IAAI,GACP,IAAI,CAAC+O,UAAU,GAAGE,QAAQ,CAAC5O,QAAQ,GACnC,IAAI,CAAC0O,UAAU,GAAG/O,IAAI,CAACK,QAAS;QACrCL,IAAI,IAAI,IAAI,GACP,IAAI,CAACX,WAAW,GAAG4P,QAAQ,CAAC9O,MAAM,GAClC,IAAI,CAACd,WAAW,GAAGW,IAAI,CAACG,MAAO;QACpCH,IAAI,IAAI,IAAI,GACP,IAAI,CAACqE,SAAS,GACb,IAAI,CAACtF,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAG0N,QAAQ,CAAC5K,SAAS,GAClD,IAAI,CAACA,SAAS,GAAG,IAAI,CAACtF,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAGvB,IAAI,CAACqE,SAAU;MACtE;IACF,CAAC,CAAC;IACJ,IAAIZ,WAAW,GAAG,IAAI,CAAC1E,QAAQ,CAAC2E,YAAY,EAAE;IAC9C,IAAID,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MAClC,IAAI,CAACqL,OAAO,GAAG,KAAK;IACtB;IACA,IAAI,CAACpP,WAAW,CAACgB,IAAI,CAACf,oBAAoB,CAAC;EAC7C;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;EAEA2O,aAAaA,CAAA;IACX,IAAI,CAACtP,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B;EAEA8K,gBAAgBA,CAAA;IACd,IAAI,CAACzP,OAAO,CAAC2E,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACrC;EAEAgL,SAASA,CAAA;IACP,IAAI,CAAC7P,QAAQ,CAAC6P,SAAS,EAAE;IACzB,IAAI,CAACI,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC/P,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B;;;uCApDW/J,eAAe,EAAA1C,+DAAA,CAAAW,+DAAA,GAAAX,+DAAA,CAAA6O,mDAAA;IAAA;EAAA;;;YAAfnM,eAAe;MAAA7C,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAA2X,yBAAAzX,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZtBN,4DAFN,aAA2D,aACoB,gBACmI;UAC1MA,uDAAA,cAAyC;UAC3CA,0DAAA,EAAS;UAK+CA,4DAJxD,aAAkE,YACpB,YAErB,WAC+B,QAAG;UAAAA,oDAAA,gCAAyB;UAChFA,0DADgF,EAAI,EAAI,EACnF;UAEHA,4DADF,aAAuC,YACc;UACjDA,oDAAA,gBACF;UAAAA,0DAAA,EAAI;UACJA,wDAAA,KAAAgY,8BAAA,iBAAoD;UAIxDhY,0DADE,EAAK,EACF;UACLA,4DAAA,cAAyD;UAOvDA,wDANA,KAAAiY,8BAAA,iBAAsC,KAAAC,8BAAA,iBAGA,KAAAC,8BAAA,iBAGiB;UAe/DnY,0DAHK,EAAK,EACA,EACF,EACF;;;UArBwBA,uDAAA,IAAc;UAAdA,wDAAA,UAAAO,GAAA,CAAAsX,OAAA,CAAc;UAGd7X,uDAAA,EAAc;UAAdA,wDAAA,UAAAO,GAAA,CAAAsX,OAAA,CAAc;UAGI7X,uDAAA,EAAa;UAAbA,wDAAA,SAAAO,GAAA,CAAAsX,OAAA,CAAa;;;qBDfnDxV,yDAAY,EAAA0M,iDAAA,EAAEvM,uDAAW,EAAEV,yDAAY,EAAA+M,uDAAA,EAAAA,6DAAA,EAAEoI,oEAAgB,EAAAhI,2EAAA,EAAAA,6EAAA,EAAAA,uEAAA;MAAApO,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;AEXR;AACd;AAC8C;;;;;;AASvF,MAAOG,mBAAmB;EAPhCrB,YAAA;IAUE,KAAA8Y,YAAY,GAAG,MAAM;;EADrBzW,QAAQA,CAAA,GAAU;EAGlB0W,SAASA,CAAA;IACP,IAAI,CAACD,YAAY,GAAG,OAAO;EAC7B;EACAE,UAAUA,CAAA;IACR,IAAI,CAACF,YAAY,GAAG,MAAM;EAC5B;;;uCAVWzX,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAnB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAAwY,6BAAAtY,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZhCN,4DAAA,UAAK;UAEHA,uDADA,iBAAyB,4BACsB;UAC/CA,0DAAA,EAAM;UACNA,4DAAA,gBAIsB;UAAtBA,wDAAA,mBAAA6Y,qDAAA;YAAA,OAAStY,GAAA,CAAAmY,SAAA,EAAW;UAAA,EAAC;UAAC1Y,oDAAA,kBACxB;UAAAA,0DAAA,EAAS;UAKDA,4DAJN,aAAqF,aAC3C,aACb,aACC,YACA;UAAAA,oDAAA,uBAAe;UACzCA,0DADyC,EAAK,EACxC;UAIMA,4DAHZ,cAAwB,cACD,cACO,gBACY;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAQ;UAE3CA,4DADF,kBAA4B,kBACT;UAAAA,oDAAA,sBAAc;UAErCA,0DAFqC,EAAS,EACjC,EACP;UAEJA,4DADF,cAAsB,gBACU;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAQ;UAExCA,4DADF,kBAA4B,kBACT;UAAAA,oDAAA,mBAAW;UAGlCA,0DAHkC,EAAS,EAC9B,EACL,EACF;UAEFA,4DADJ,eAAwB,gBACU;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAQ;UACnDA,uDAAA,iBAA0E;UAC9EA,0DAAA,EAAM;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EAAQ;UACzDA,uDAAA,oBAAmF;UACrFA,0DAAA,EAAM;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,iCAAyB;UAAAA,0DAAA,EAAQ;UAC/DA,uDAAA,iBAAsF;UACxFA,0DAAA,EAAM;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,mCAA2B;UAAAA,0DAAA,EAAQ;UACjEA,uDAAA,oBAAmF;UACrFA,0DAAA,EAAM;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAQ;UAChDA,uDAAA,iBAAwE;UAC1EA,0DAAA,EAAM;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAQ;UAC9CA,uDAAA,iBAAsE;UACxEA,0DAAA,EAAM;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAQ;UACjDA,uDAAA,iBAAwE;UAC1EA,0DAAA,EAAM;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,qCAA6B;UAAAA,0DAAA,EAAQ;UACnEA,uDAAA,iBAA0F;UAC5FA,0DAAA,EAAM;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAQ;UAE7CA,4DADF,kBAA4B,kBACT;UAAAA,oDAAA,4BAAoB;UAE7CA,0DAF6C,EAAS,EACvC,EACT;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAQ;UAE9CA,4DADF,kBAA4B,kBACT;UAAAA,oDAAA,6BAAqB;UAE9CA,0DAF8C,EAAS,EACxC,EACT;UAEFA,4DADJ,eAAwB,gBACU;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAQ;UACpDA,4DAAA,eAAyC;UACvCA,uDAAA,iBAAyN;UAGpNA,4DAFJ,eAAiC,eACD,eACC;UAC3BA,uDAAA,aAA4B;UAC9BA,0DAAA,EAAM;UAEJA,4DADF,eAA6B,UACvB;UAAAA,oDAAA,yBAAiB;UAKlCA,0DALkC,EAAK,EACtB,EACF,EACF,EACJ,EACH;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,yBAAiB;UAAAA,0DAAA,EAAQ;UACnDA,4DAAA,eAAyC;UACtCA,uDAAA,iBAAyN;UAGpNA,4DAFJ,eAAiC,eACD,eACC;UAC3BA,uDAAA,aAA4B;UAC9BA,0DAAA,EAAM;UAEJA,4DADF,eAA6B,UACvB;UAAAA,oDAAA,mCAA2B;UAK/CA,0DAL+C,EAAK,EAChC,EACF,EACF,EACJ,EACN;UAEJA,4DADF,eAAwB,gBACQ;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EAAQ;UAEnDA,4DADF,kBAA4B,kBACT;UAAAA,oDAAA,kCAA0B;UAGzDA,0DAHyD,EAAS,EAC7C,EACT,EACN;UAEJA,4DADF,eAAkD,mBACe;UAAvBA,wDAAA,mBAAA8Y,uDAAA;YAAA,OAASvY,GAAA,CAAAoY,UAAA,EAAY;UAAA,EAAC;UAAC3Y,4DAAA,iBAAqB;UAACA,oDAAA,gBAAM;UAAQA,0DAAR,EAAO,EAAU;UACtEA,4DAAtC,mBAAsC,iBAAmB;UAAAA,oDAAA,aAAI;UAIrEA,0DAJqE,EAAO,EAAS,EACzE,EACF,EACF,EACF;;;UA9G2CA,uDAAA,GAAoC;UAApCA,wDAAA,YAAAA,6DAAA,IAAAmG,GAAA,EAAA5F,GAAA,CAAAkY,YAAA,EAAoC;;;qBDAzE/V,qEAAe,EAAEL,yDAAY,EAAA1B,oDAAA,EAAE6X,qGAAyB;MAAA3X,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;AETP;AACA;;AASvD,MAAOI,sBAAsB;EACjCtB,YAAA,GAAe;EAEfqC,QAAQA,CAAA,GAAU;;;uCAHPf,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAApB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAA6Y,gCAAA3Y,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCFnCN,4DAAA,UAAK;UAEHA,uDADA,iBAAyB,YACc;UACzCA,0DAAA,EAAM;UAMEA,4DALR,aAAuB,aAC0D,aACzD,aACQ,YACmB,gBAC+G;UACxJA,oDAAA,qBACF;UAEJA,0DAFI,EAAS,EACN,EACD;UAGFA,4DAFJ,cAA4B,aACmB,iBAC8G;UACvJA,oDAAA,+BACF;UAEJA,0DAFI,EAAS,EACN,EACD;UAGFA,4DAFJ,cAA4B,aACqB,kBACgH;UAC3JA,oDAAA,gDAEF;UAGNA,0DAHM,EAAS,EACN,EACC,EACJ;UAIAA,4DAHN,eAAsB,eAC2G,eACjG,cACT;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAK;UAChCA,oDAAA,ucACA;UAAIA,uDAAJ,UAAI,UAAI;UACRA,oDAAA,4XACA;UAAIA,uDAAJ,UAAI,UAAI;UACRA,oDAAA,ucAEJ;UACFA,0DADE,EAAM,EACF;UAEEA,uDADR,UAAI,UACA,UAAI,UAAI;UAGRA,4DAFJ,eAA+H,eACjG,cACT;UAAAA,oDAAA,6BAAqB;UAAAA,0DAAA,EAAK;UAC3CA,oDAAA,4XACA;UAAIA,uDAAJ,UAAI,UAAI;UACRA,4DAAA,cAAQ;UAAAA,oDAAA,2CAAmC;UAAAA,0DAAA,EAAS;UAAAA,uDAAA,UAAI;UACxDA,4DAAA,aAA4B;UAACA,oDAAA,qEAA4D;UAAAA,0DAAA,EAAI;UAGzFA,4DAFJ,WAAK,UACC,UACE;UAAAA,oDAAA,sGAA8F;UAAAA,0DAAA,EAAK;UACvGA,4DAAA,UAAI;UACFA,oDAAA,wOAC+G;UAAAA,0DAAA,EAAK;UACpHA,4DAAA,UAAI;UAAAA,oDAAA,oNAC0F;UAAAA,0DAAA,EAAK;UACnGA,4DAAA,UAAI;UAAAA,oDAAA,qHAA6G;UAI3HA,0DAJ2H,EAAK,EACrH,EACD,EACF,EACF;UAGFA,uDAFJ,UAAI,UACA,UACA,UAAI;UAGJA,4DAFJ,eAAmI,eACrG,cACT;UAAAA,oDAAA,8CAAsC;UAAAA,0DAAA,EAAK;UAC5DA,oDAAA,ucACA;UAAIA,uDAAJ,UAAI,UAAI;UACRA,oDAAA,22BACA;UAAIA,uDAAJ,UAAI,UAAI;UACRA,4DAAA,UAAI;UAAAA,oDAAA,0BAAkB;UAAAA,0DAAA,EAAK;UAGvBA,4DAFJ,eAA2B,UACrB,UACE;UAAAA,oDAAA,0GACF;UACEA,4DADF,UAAI,UACE;UAAAA,oDAAA,2GAAmG;UAAAA,0DAAA,EAAK;UAC5GA,4DAAA,UAAI;UAAAA,oDAAA,2GAAmG;UAE3GA,0DAF2G,EAAK,EACzG,EACF;UACLA,4DAAA,UAAI;UACFA,oDAAA,4OAEF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,UAAI;UACFA,oDAAA,0HACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,UAAI;UACFA,oDAAA,2GACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,UAAI;UACFA,oDAAA,0HACF;UAEJA,0DAFI,EAAK,EACF,EACD;UAEJA,4DADF,eAA0B,UACpB;UAAAA,oDAAA,oCAA4B;UAAAA,0DAAA,EAAK;UACrCA,4DAAA,UAAI;UAAAA,oDAAA,qCAA6B;UAAAA,0DAAA,EAAK;UACtCA,4DAAA,UAAI;UAAAA,oDAAA,oCAA4B;UAAAA,0DAAA,EAAK;UACrCA,4DAAA,UAAI;UAAAA,oDAAA,oCAA4B;UAAAA,0DAAA,EAAK;UACrCA,4DAAA,UAAI;UAAAA,oDAAA,oCAA4B;UAAAA,0DAAA,EAAK;UACrCA,4DAAA,WAAI;UAAAA,oDAAA,qCAA4B;UAClCA,0DADkC,EAAK,EACjC;UACJA,oDAAA,wcACJ;UAINA,0DAJM,EAAM,EACF,EACF,EACF,EACA;UAGJA,uDAAA,mBAAyB;;;qBDlHf0C,qEAAe,EAAEX,qEAAe;MAAAlB,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;AENkB;AACf;;;;;;;ICOzCb,4DAAA,iBAAsE;IAAAA,oDAAA,GAAa;IAAAA,0DAAA,EAAS;;;;IAA5CA,mEAAA,UAAAkZ,OAAA,CAAAC,IAAA,CAAqB;IAACnZ,uDAAA,EAAa;IAAbA,+DAAA,CAAAkZ,OAAA,CAAAC,IAAA,CAAa;;;;;IAMnFnZ,4DAAA,iBAAmE;IAAAA,oDAAA,GAAa;IAAAA,0DAAA,EAAS;;;;IAA5CA,mEAAA,UAAAoZ,OAAA,CAAAD,IAAA,CAAqB;IAACnZ,uDAAA,EAAa;IAAbA,+DAAA,CAAAoZ,OAAA,CAAAD,IAAA,CAAa;;;;;IAMhFnZ,4DAAA,iBAAoE;IAAAA,oDAAA,GAAa;IAAAA,0DAAA,EAAS;;;;IAA5CA,mEAAA,UAAAqZ,OAAA,CAAAF,IAAA,CAAqB;IAACnZ,uDAAA,EAAa;IAAbA,+DAAA,CAAAqZ,OAAA,CAAAF,IAAA,CAAa;;;;;IAMjFnZ,4DAAA,iBAAoE;IAAAA,oDAAA,GAAa;IAAAA,0DAAA,EAAS;;;;IAA5CA,mEAAA,UAAAsZ,OAAA,CAAAH,IAAA,CAAqB;IAACnZ,uDAAA,EAAa;IAAbA,+DAAA,CAAAsZ,OAAA,CAAAH,IAAA,CAAa;;;ADfjF,MAAOX,yBAAyB;EAOpC7Y,YAAoBiI,QAAuB,EAAUC,MAAsB;IAAvD,KAAAD,QAAQ,GAARA,QAAQ;IAAyB,KAAAC,MAAM,GAANA,MAAM;IAN3D,KAAA0R,kBAAkB,GAAU,EAAE;IAC9B,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAU,EAAE;IACpB,KAAAjR,WAAW,GAAmB,EAAE;EAEsC;EAE9EzG,QAAQA,CAAA;IACN,IAAI,CAAC2X,qBAAqB,EAAE;IAC5B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEApQ,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;EAEAkR,qBAAqBA,CAAA;IACnB,MAAMI,2BAA2B,GAAG,IAAI,CAACnS,QAAQ,CAAC+R,qBAAqB,EAAE,CAAC/Q,SAAS,CAAEC,IAAS,IAAI;MAChG,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACuP,kBAAkB,GAAG1Q,IAAI,CAACA,IAAI;MACrC,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACsQ,2BAA2B,CAAC;EACpD;EAEAH,kBAAkBA,CAAA;IAChB,MAAMI,wBAAwB,GAAG,IAAI,CAACpS,QAAQ,CAACgS,kBAAkB,EAAE,CAAChR,SAAS,CAAEC,IAAS,IAAI;MAC1F,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACwP,eAAe,GAAG3Q,IAAI,CAACA,IAAI;MAClC,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACuQ,wBAAwB,CAAC;EACjD;EAEAH,mBAAmBA,CAAA;IACjB,MAAMI,yBAAyB,GAAG,IAAI,CAACrS,QAAQ,CAACiS,mBAAmB,EAAE,CAACjR,SAAS,CAAEC,IAAS,IAAI;MAC5F,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACyP,gBAAgB,GAAG5Q,IAAI,CAACA,IAAI;MACnC,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACwQ,yBAAyB,CAAC;EAClD;EAEAH,mBAAmBA,CAAA;IACjB,MAAMI,yBAAyB,GAAG,IAAI,CAACtS,QAAQ,CAACkS,mBAAmB,EAAE,CAAClR,SAAS,CAAEC,IAAS,IAAI;MAC5F,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAAC0P,gBAAgB,GAAG7Q,IAAI,CAACA,IAAI;MACnC,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACyQ,yBAAyB,CAAC;EAClD;EAEAC,YAAYA,CAAChB,IAAS;IACpB,IAAI,CAACvR,QAAQ,CAAC2B,UAAU,CAAC6Q,IAAI,CAACjB,IAAI,CAAC;EACrC;EAEAkB,QAAQA,CAACjP,CAAM;IACb,IAAI,CAACxD,QAAQ,CAAC2B,UAAU,CAAC6Q,IAAI,CAAChP,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;EAC/C;;;uCAtFWiN,yBAAyB,EAAAxY,+DAAA,CAAAW,mEAAA,GAAAX,+DAAA,CAAA6O,4DAAA;IAAA;EAAA;;;YAAzB2J,yBAAyB;MAAA3Y,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAAka,mCAAAha,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCXlCN,4DAHJ,aAA+B,aAEI,aACN;UACvBA,uDAAA,cAAsC;UACtCA,4DAAA,kBAA6H;UAA1EA,wDAAA,mBAAAua,0DAAA;YAAAva,2DAAA,CAAAgD,GAAA;YAAA,MAAAwX,YAAA,GAAAxa,yDAAA;YAAA,OAAAA,yDAAA,CAASO,GAAA,CAAA4Z,YAAA,CAAAK,YAAA,CAAAjP,KAAA,CAA6B;UAAA,EAAC;UAE9FvL,0DAFI,EAA6H,EACzH,EACF;UAEJA,4DADF,aAAiC,gBACkC;UAA5BA,wDAAA,oBAAA0a,4DAAApV,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAAUO,GAAA,CAAA8Z,QAAA,CAAA/U,MAAA,CAAgB;UAAA,EAAC;UAC9DtF,4DAAA,gBAAiB;UAAAA,oDAAA,cAAO;UAAAA,0DAAA,EAAS;UACjCA,wDAAA,KAAA2a,4CAAA,oBAAsE;UAE1E3a,0DADE,EAAS,EACL;UAEJA,4DADF,cAAiC,iBACkC;UAA5BA,wDAAA,oBAAA4a,6DAAAtV,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAAUO,GAAA,CAAA8Z,QAAA,CAAA/U,MAAA,CAAgB;UAAA,EAAC;UAC9DtF,4DAAA,iBAAiB;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAS;UAC9BA,wDAAA,KAAA6a,4CAAA,oBAAmE;UAEvE7a,0DADE,EAAS,EACL;UAEJA,4DADF,cAAiC,iBACkC;UAA5BA,wDAAA,oBAAA8a,6DAAAxV,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAAUO,GAAA,CAAA8Z,QAAA,CAAA/U,MAAA,CAAgB;UAAA,EAAC;UAC9DtF,4DAAA,iBAAiB;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAS;UAC/BA,wDAAA,KAAA+a,4CAAA,oBAAoE;UAExE/a,0DADE,EAAS,EACL;UAEJA,4DADF,cAAiC,iBACkC;UAA5BA,wDAAA,oBAAAgb,6DAAA1V,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAAUO,GAAA,CAAA8Z,QAAA,CAAA/U,MAAA,CAAgB;UAAA,EAAC;UAC9DtF,4DAAA,iBAAiB;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAS;UAChCA,wDAAA,KAAAib,4CAAA,oBAAoE;UAG1Ejb,0DAFI,EAAS,EACL,EACF;;;UArByBA,uDAAA,IAAqB;UAArBA,wDAAA,YAAAO,GAAA,CAAAgZ,kBAAA,CAAqB;UAMrBvZ,uDAAA,GAAkB;UAAlBA,wDAAA,YAAAO,GAAA,CAAAiZ,eAAA,CAAkB;UAMlBxZ,uDAAA,GAAmB;UAAnBA,wDAAA,YAAAO,GAAA,CAAAkZ,gBAAA,CAAmB;UAMnBzZ,uDAAA,GAAmB;UAAnBA,wDAAA,YAAAO,GAAA,CAAAmZ,gBAAA,CAAmB;;;qBDjBtCrX,yDAAY,EAAA0M,oDAAA;MAAAlO,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AEIA;AAOiB;AAGqB;AAIA;AAGD;AACd;;;;;;;;;;;;ICXnCb,4DAAA,eAAwH;IAAAA,oDAAA,wBAAiB;IAAAA,0DAAA,EAAO;;;;;IAChJA,4DAAA,eAA+E;IAAAA,oDAAA,wCAAiC;IAAAA,0DAAA,EAAO;;;;;IAKvHA,4DAAA,eAA8H;IAAAA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAO;;;;;IACzJA,4DAAA,eAAkF;IAAAA,oDAAA,wCAAiC;IAAAA,0DAAA,EAAO;;;;;IAiB5HA,4DAAA,eAAgF;IAAAA,oDAAA,yCAAkC;IAAAA,0DAAA,EAAO;;;;;IAKzHA,4DAAA,eAAqF;IAAAA,oDAAA,wCAAiC;IAAAA,0DAAA,EAAO;;;;;IAO7HA,4DAAA,eAAkI;IAAAA,oDAAA,6BAAsB;IAAAA,0DAAA,EAAO;;;;;IAiBzJA,4DAAA,kBAAgE;IAAEA,oDAAA,GAAa;IAAAA,0DAAA,EAAS;;;;IAA/CA,mEAAA,UAAAkZ,OAAA,CAAA3N,KAAA,CAAsB;IAAGvL,uDAAA,EAAa;IAAbA,gEAAA,MAAAkZ,OAAA,CAAAC,IAAA,KAAa;;;;;IAEjFnZ,4DAAA,eAAkI;IAAAA,oDAAA,4BAAqB;IAAAA,0DAAA,EAAO;;;;;IAM5JA,4DAAA,kBAA6D;IAAAA,oDAAA,GAAa;IAAAA,0DAAA,EAAS;;;;IAA7CA,mEAAA,UAAAoZ,OAAA,CAAA7N,KAAA,CAAsB;IAACvL,uDAAA,EAAa;IAAbA,+DAAA,CAAAoZ,OAAA,CAAAD,IAAA,CAAa;;;;;IAE5EnZ,4DAAA,eAA4H;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAO;;;;;IAwCnJA,4DAAA,kBAA4D;IAAAA,oDAAA,GAAQ;IAAAA,0DAAA,EAAS;;;;IAAlCA,mEAAA,UAAAqZ,OAAA,CAAgB;IAACrZ,uDAAA,EAAQ;IAARA,+DAAA,CAAAqZ,OAAA,CAAQ;;;;;IAEtErZ,4DAAA,eAAgI;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAO;;;;;IAyC5JA,4DAAA,eAAoG;IAChGA,oDAAA,gCACJ;IAAAA,0DAAA,EAAO;;;;;IAIPA,4DAAA,eAAoG;IAClGA,oDAAA,gCACF;IAAAA,0DAAA,EAAO;;;;;IAIPA,4DAAA,eAA4G;IAC1GA,oDAAA,oCACJ;IAAAA,0DAAA,EAAO;;;;;IAuELA,4DAAA,eAAwF;IAAAA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAO;;;;;IAKnHA,4DAAA,eAAwF;IAAAA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAO;;;ADvNzH,MAAOuB,wBAAwB;EAwBnC5B,YACU0b,cAA6B,EAC7BzT,QAAuB,EACvB0T,aAA0B,EAC1BxT,OAAe,EACfD,MAAsB,EACtBwJ,GAAgB,EAChBkK,eAA+B;IAN/B,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAzT,QAAQ,GAARA,QAAQ;IACR,KAAA0T,aAAa,GAAbA,aAAa;IACb,KAAAxT,OAAO,GAAPA,OAAO;IACP,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAwJ,GAAG,GAAHA,GAAG;IACH,KAAAkK,eAAe,GAAfA,eAAe;IAvBzB,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAA3O,SAAS,GAAQ,EAAE;IACnB,KAAA4O,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAOjB,KAAAtT,WAAW,GAAmB,EAAE;IAcjC,KAAAI,IAAI,GAAa,CACtB,cAAc,EACd,YAAY,EACZ,WAAW,EACX,kBAAkB,EAClB,uBAAuB,EACvB,SAAS,EACT,kBAAkB,EAClB,aAAa,EACb,cAAc,EACd,UAAU,EACV,wBAAwB,EACxB,kBAAkB,EAClB,YAAY,EACZ,iBAAiB,EACjB,mBAAmB,EACnB,kBAAkB,EAClB,oBAAoB,EACpB,cAAc,EACd,UAAU,EACV,wBAAwB,EACxB,gBAAgB,EAChB,sBAAsB,EACtB,QAAQ,EACR,yBAAyB,EACzB,sBAAsB,EACtB,SAAS,CACV;IAEM,KAAAmT,KAAK,GAAa,CACvB,kBAAkB,EAClB,YAAY,EACZ,mBAAmB,CACpB;IAEM,KAAAC,eAAe,GAAyB;MAC7CC,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE,CAAC,YAAY,EAAE,cAAc;KACrC;IAzCC,IAAI,CAACnT,MAAM,GAAG,IAAI,CAACuS,eAAe,CAACa,QAAQ,CAACC,QAAQ,CAACzK,GAAG,CAAC,QAAQ,CAAC;EACpE;EA0CA5P,QAAQA,CAAA;IACN,IAAI,CAACsZ,aAAa,CAAC3S,cAAc,EAAE,CAACC,SAAS,CAAEC,IAAS,IAAI;MAC1D,IAAI,CAACyT,WAAW,GAAG,IAAI,CAAChB,aAAa,CAACvS,aAAa,EAAE;MACrDF,IAAI,IAAI,IAAI,GACP,IAAI,CAACX,WAAW,GAAG,IAAI,CAACoU,WAAW,CAACtT,MAAM,GAC1C,IAAI,CAACd,WAAW,GAAGW,IAAI,CAACG,MAAO;MACpCH,IAAI,IAAI,IAAI,GACP,IAAI,CAAC0T,SAAS,GAAG,IAAI,CAACD,WAAW,CAACpT,QAAQ,GAC1C,IAAI,CAACqT,SAAS,GAAG1T,IAAI,CAACK,QAAS;MACpCL,IAAI,IAAI,IAAI,GACP,IAAI,CAAC4L,SAAS,GAAG,IAAI,CAAC6H,WAAW,CAAC7H,SAAS,GAC3C,IAAI,CAACA,SAAS,GAAG5L,IAAI,CAAC4L,SAAU;MACrC5L,IAAI,IAAI,IAAI,GACP,IAAI,CAAC6L,QAAQ,GAAG,IAAI,CAAC4H,WAAW,CAAC5H,QAAQ,GACzC,IAAI,CAACA,QAAQ,GAAG7L,IAAI,CAAC6L,QAAS;MACnC7L,IAAI,IAAI,IAAI,GACP,IAAI,CAAC2T,SAAS,CAACxT,MAAM,GAAG,IAAI,CAACsT,WAAW,CAACtT,MAAM,GAC/C,IAAI,CAACwT,SAAS,CAACxT,MAAM,GAAGH,IAAI,CAACG,MAAO;MACzCH,IAAI,IAAI,IAAI,GACP,IAAI,CAAC2T,SAAS,CAACC,IAAI,GAAG,IAAI,CAACH,WAAW,CAACpT,QAAQ,GAC/C,IAAI,CAACsT,SAAS,CAACC,IAAI,GAAG5T,IAAI,CAACK,QAAS;MACzCL,IAAI,IAAI,IAAI,GACP,IAAI,CAAC2T,SAAS,CAACpT,YAAY,GAAG,IAAI,CAACkT,WAAW,CAAClT,YAAY,GAC3D,IAAI,CAACoT,SAAS,CAACpT,YAAY,GAAGP,IAAI,CAACO,YAAa;IACvD,CAAC,CAAC;IAEF,IAAI,CAACsT,kBAAkB,EAAE;IACzB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACzU,WAAW,CAAC;IAE9C,IAAI,CAAC0U,YAAY,EAAE;IACnB,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7T,MAAM,CAAC;IAE3B,IAAI,CAAC8T,cAAc,EAAE;IAErB,IAAI,CAACC,mBAAmB,GAAG,IAAIrP,MAAM,CAACsP,SAAS,CAACC,KAAK,CACnD3O,QAAQ,CAAC4O,cAAc,CAAC,qBAAqB,CAAC,CAC/C;IACD,IAAI,CAACC,iBAAiB,GAAG,IAAIzP,MAAM,CAACsP,SAAS,CAACC,KAAK,CACjD3O,QAAQ,CAAC4O,cAAc,CAAC,eAAe,CAAC,CACzC;IACD,IAAI,CAACE,cAAc,GAAG,IAAI1P,MAAM,CAACsP,SAAS,CAACC,KAAK,CAC9C3O,QAAQ,CAAC4O,cAAc,CAAC,gBAAgB,CAAC,CAC1C;EACH;EAIOG,kBAAkBA,CAACC,KAAiB;IACzC,IAAIzU,IAAI,GAAG,EAAE;MACXmT,KAAK,GAAG,EAAE;IACZ,IAAI,IAAI,CAACuB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACzX,MAAM,EAAE;MACvC,IAAI,CAACyX,OAAO,CAAC5T,OAAO,CAAC,CAACuE,IAAsB,EAAEO,KAAa,KAAI;QAC7D,IAAIA,KAAK,KAAK,CAAC,EAAE;UACf5F,IAAI,GAAGqF,IAAI,CAACrF,IAAI;QAClB,CAAC,MAAM;UACLmT,KAAK,GAAG9N,IAAI,CAACrF,IAAI;QACnB;MACF,CAAC,CAAC;MACF,IAAI,CAAC8S,UAAU,GAAGK,KAAK;IACzB;EACF;EAEAtS,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;EAEA+U,SAASA,CAAA;IACP,MAAMjS,KAAK,GAAG;MACZkS,KAAK,EAAE,IAAI,CAAC9B,UAAU,CAAC+B,IAAI,CAAC,GAAG,CAAC;MAChC1U,MAAM,EAAE,IAAI,CAACd;KACd;IACD,MAAMyV,qBAAqB,GAAG,IAAI,CAACtC,cAAc,CAACuC,YAAY,CAACrS,KAAK,CAAC,CAAC3C,SAAS,CAC5EC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;UAAE1C,MAAM,EAAE,SAAS;UAAEC,OAAO,EAAElC,IAAI,CAACA;QAAI,CAAE,CAAC;QAC9D4E,UAAU,CAAC,MAAK;UACd,IAAI,CAACoQ,sBAAsB,EAAE;QAC/B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,IAAI,CAAChW,MAAM,CAACgD,KAAK,CAAC;UAAEC,MAAM,EAAE,OAAO;UAAEC,OAAO,EAAElC,IAAI,CAACmC;QAAO,CAAE,CAAC;MAC/D;IACF,CAAC,EACA6C,GAAG,IAAK,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAAEC,MAAM,EAAE,OAAO;MAAEC,OAAO,EAAE8C,GAAG,CAAC7C;IAAO,CAAE,CAAC,CACtE;IACD,IAAI,CAACvC,WAAW,CAACgB,IAAI,CAACkU,qBAAqB,CAAC;EAC9C;EAEAf,YAAYA,CAAA;IACV,MAAMkB,kBAAkB,GAAG,IAAI,CAACzC,cAAc,CAACuB,YAAY,CAAC,IAAI,CAAC1U,WAAW,CAAC,CAACU,SAAS,CACpFC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAInB,IAAI,CAACA,IAAI,CAAC/C,MAAM,GAAG,CAAC,EAAE;UACxB,IAAI,CAAC8V,aAAa,GAAG/S,IAAI,CAACA,IAAI;UAC9B,IAAI,CAAC+S,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC,CAACzC,IAAI,CAACzO,KAAK,CAAC,GAAG,CAAC;QAC5D,CAAC,MAAM;UACL,IAAI,CAACkR,aAAa,GAAG,IAAI,CAACI,KAAK;QACjC;MACF,CAAC,MAAM;QACL,IAAI,CAACnU,MAAM,CAACgD,KAAK,CAAC;UAAEC,MAAM,EAAE,OAAO;UAAEC,OAAO,EAAElC,IAAI,CAACmC;QAAO,CAAE,CAAC;MAC/D;IACF,CAAC,EACA6C,GAAG,IAAK,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAAEC,MAAM,EAAE,OAAO;MAAEC,OAAO,EAAE8C,GAAG,CAAC7C;IAAO,CAAE,CAAC,CACtE;IACD,IAAI,CAACvC,WAAW,CAACgB,IAAI,CAACqU,kBAAkB,CAAC;EAC3C;EAEAhB,cAAcA,CAAA;IACZ,MAAMiB,oBAAoB,GAAG,IAAI,CAAC1C,cAAc,CAACG,WAAW,EAAE,CAAC5S,SAAS,CAAEC,IAAS,IAAI;MACrF,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACwR,WAAW,GAAG3S,IAAI,CAACA,IAAI;MAC9B,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACsU,oBAAoB,CAAC;EAC7C;EAEAC,WAAWA,CAAC5Y,SAAc;IACxBA,SAAS,GAAGA,SAAS,CAACkG,MAAM,CAACC,KAAK;IAClC,MAAM0S,iBAAiB,GAAG,IAAI,CAAC5C,cAAc,CAACI,QAAQ,CAACrW,SAAS,CAAC,CAACwD,SAAS,CAAEC,IAAS,IAAI;MACxF,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACyR,QAAQ,GAAG5S,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACwU,iBAAiB,CAAC;EAC1C;EAEAtB,uBAAuBA,CAACpZ,EAAO;IAC7B,MAAM2a,mBAAmB,GAAG,IAAI,CAACtW,QAAQ,CAACuW,mBAAmB,CAAC5a,EAAE,CAAC,CAACqF,SAAS,CACxEC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACoU,gBAAgB,GAAGvV,IAAI,CAACA,IAAI;MACnC,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACyU,mBAAmB,CAAC;EAC5C;EAEAG,aAAaA,CAACf,KAAU;IACtB,IAAIA,KAAK,CAAChS,MAAM,CAACgT,KAAK,IAAIhB,KAAK,CAAChS,MAAM,CAACgT,KAAK,CAAC,CAAC,CAAC,EAAE;MAC/C,IAAI,CAACxC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC9B,IAAIwC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACnB,KAAK,CAAChS,MAAM,CAACgT,KAAK,CAAC,CAAC,CAAC,CAAC;MAC3CC,MAAM,CAACG,MAAM,GAAItT,CAAM,IAAI;QACzB,IAAI,CAAC8B,SAAS,GAAG9B,CAAC,CAACE,MAAM,CAACtB,MAAM;MAClC,CAAC;MAED,KAAK,IAAI2U,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,KAAK,CAAChS,MAAM,CAACgT,KAAK,CAACxY,MAAM,EAAE6Y,CAAC,EAAE,EAAE;QAClD,IAAI,CAAC7C,QAAQ,CAAC8C,MAAM,CAAC,MAAM,EAAEtB,KAAK,CAAChS,MAAM,CAACgT,KAAK,CAACK,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC7C,QAAQ,CAAC8C,MAAM,CAAC,YAAY,EAAE,WAAW,CAAC;MACjD;MACA,IAAI,CAAC/C,YAAY,GAAG,IAAI;IAC1B;EACF;EAEAa,kBAAkBA,CAAA;IAChB,IAAI,CAACmC,eAAe,GAAG,IAAI,CAACxN,GAAG,CAACG,KAAK,CAAC;MACpCjO,EAAE,EAAE,CAAC,CAAC,CAAC;MACPkZ,IAAI,EAAE,CACJ,IAAI,CAAChI,SAAS,EACdzD,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,EAAEV,sDAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CACpE;MACDkK,OAAO,EAAE,CACP,IAAI,CAACpK,QAAQ,EACb1D,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,EAAEV,sDAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CACpE;MACDmK,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbpf,KAAK,EAAE,CAAC,EAAE,EAAEoR,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAAC4D,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5DqK,UAAU,EAAE,CAAC,EAAE,EAAEjO,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAAC4D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChEsK,SAAS,EAAE,CAAC,IAAI,EAAElO,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MAC5DyN,aAAa,EAAE,CAAC,EAAE,CAAC;MACnB/Z,SAAS,EAAE,CAAC,IAAI,EAAE4L,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MAC5DnH,MAAM,EAAE,CAAC,IAAI,EAAEyG,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MACzD0N,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,EAAEtO,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MACzDxE,SAAS,EAAE,CAAC,EAAE,EAAE8D,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MAC1D1I,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEA6T,SAASA,CAACtZ,EAAO;IACf,MAAMgc,oBAAoB,GAAG,IAAI,CAAC3X,QAAQ,CAAC4X,wBAAwB,CAACjc,EAAE,CAAC,CAACqF,SAAS,CAC9EC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACyV,QAAQ,GAAG5W,IAAI,CAACA,IAAI;QACzB,IAAI,IAAI,CAAC4W,QAAQ,IAAIC,SAAS,EAAE;UAC9B,IAAI,CAACb,eAAe,GAAG,IAAI,CAACxN,GAAG,CAACG,KAAK,CAAC;YACpCjO,EAAE,EAAE,CAAC,IAAI,CAACkc,QAAQ,CAAClc,EAAE,CAAC;YACtBkZ,IAAI,EAAE,CACJ,IAAI,CAACgD,QAAQ,CAAChD,IAAI,EAClBzL,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAC1C;YACDoN,OAAO,EAAE,CACP,IAAI,CAACW,QAAQ,CAACX,OAAO,EACrB9N,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAC1C;YACDqN,UAAU,EAAE,CAAC,IAAI,CAACU,QAAQ,CAACV,UAAU,CAAC;YACtCC,OAAO,EAAE,CAAC,IAAI,CAACS,QAAQ,CAACT,OAAO,CAAC;YAChCpf,KAAK,EAAE,CAAC,IAAI,CAAC6f,QAAQ,CAAC7f,KAAK,CAAC;YAC5Bqf,UAAU,EAAE,CAAC,IAAI,CAACQ,QAAQ,CAACR,UAAU,CAAC;YACtCC,SAAS,EAAE,CACT,IAAI,CAACO,QAAQ,CAACP,SAAS,EACvBlO,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAC1C;YACDyN,aAAa,EAAE,CAAC,IAAI,CAACM,QAAQ,CAACN,aAAa,CAAC;YAC5C/Z,SAAS,EAAE,CACT,IAAI,CAACqa,QAAQ,CAACra,SAAS,EACvB4L,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAC1C;YACDnH,MAAM,EAAE,CACN,IAAI,CAACkV,QAAQ,CAAClV,MAAM,EACpByG,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAC1C;YACD0N,WAAW,EAAE,CAAC,IAAI,CAACK,QAAQ,CAACL,WAAW,CAAC;YACxCC,UAAU,EAAE,CAAC,IAAI,CAACI,QAAQ,CAACJ,UAAU,CAAC;YACtCC,QAAQ,EAAE,CACR,IAAI,CAACG,QAAQ,CAACH,QAAQ,CAAC5U,KAAK,CAAC,GAAG,CAAC,EACjCsG,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAC1C;YACDxE,SAAS,EAAE,CAAC,EAAE,CAAC;YACflE,MAAM,EAAE,CAAC,IAAI,CAACyW,QAAQ,CAACzW,MAAM;WAC9B,CAAC;UACF,MAAMiV,iBAAiB,GAAG,IAAI,CAAC5C,cAAc,CAC1CI,QAAQ,CAAC,IAAI,CAACgE,QAAQ,CAACra,SAAS,CAAC,CACjCwD,SAAS,CAAEC,IAAS,IAAI;YACvB,IAAI,CAAC4S,QAAQ,GAAG5S,IAAI,CAACA,IAAI;UAC3B,CAAC,CAAC;UACJ,IAAI,IAAI,CAAC4W,QAAQ,CAACvS,SAAS,EAAE;YAC3B,IAAI,CAACA,SAAS,GACZ,IAAI,CAACtF,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACqV,QAAQ,CAACvS,SAAS;UAC1D;UACA,IAAI,CAACzE,WAAW,CAACgB,IAAI,CAAC8V,oBAAoB,EAAEtB,iBAAiB,CAAC;QAChE;MACF,CAAC,MAAM;QACL,IAAI,CAACpW,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;EAEH;EAEM2G,QAAQA,CAAA;IAAA,IAAA8N,KAAA;IAAA,OAAAC,+JAAA;MACZ,IAAIxV,QAAQ,GAAG,EAAE;MACjB,MAAMyV,SAAS,GAAGF,KAAI,CAACd,eAAe,CAACtT,KAAK;MAC5CsU,SAAS,CAAC7W,MAAM,GAAG2W,KAAI,CAAC3W,MAAM;MAC9B,IAAI2W,KAAI,CAACd,eAAe,CAAC9M,KAAK,EAAE;QAC9B,IAAI4N,KAAI,CAAC9D,YAAY,EAAE;UACpB,MAAM8D,KAAI,CAACtE,cAAc,CACvByE,WAAW,CAACH,KAAI,CAAC7D,QAAQ,CAAC,CAC1BiE,IAAI,EAAE,CACNC,SAAS,EAAE,CACXC,IAAI,CACF9M,GAAQ,IAAI;YACX,IAAIA,GAAG,CAAC3F,OAAO,EAAE;cACfpD,QAAQ,GAAG+I,GAAG,CAACtK,IAAI,CAAC,CAAC,CAAC;YACxB;UACF,CAAC,EACAgF,GAAG,IAAI;YACN8R,KAAI,CAAC9X,MAAM,CAACgD,KAAK,CAAC;cAChBC,MAAM,EAAE,OAAO;cACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;cACpBC,QAAQ,EAAExI,mEAAU,CAACyI;aACtB,CAAC;UACJ,CAAC,CACF;QACL;QACA,IAAIyU,KAAI,CAAC9D,YAAY,EAAE;UACrBgE,SAAS,CAAC3S,SAAS,GAAG9C,QAAQ;QAChC,CAAC,MAAM;UACLyV,SAAS,CAAC3S,SAAS,GAAGyS,KAAI,CAACF,QAAQ,CAACvS,SAAS;QAC/C;QAEA,MAAMgT,YAAY,GAAGL,SAAS,CAACP,QAAQ,CAAC5B,IAAI,CAAC,GAAG,CAAC;QACjDmC,SAAS,CAACP,QAAQ,GAAGY,YAAY;QACjCL,SAAS,CAACxS,MAAM,GAAG,IAAI;QACvB,MAAM8S,0BAA0B,GAAGR,KAAI,CAAC/X,QAAQ,CAACwY,sBAAsB,CAACP,SAAS,CAAC,CAACjX,SAAS,CACzFuK,GAAQ,IAAI;UACX,IAAIA,GAAG,CAACnJ,MAAM,IAAI,CAAC,EAAE;YACnB2V,KAAI,CAAC9X,MAAM,CAAC2F,OAAO,CAAC;cAClB1C,MAAM,EAAE,SAAS;cACjBC,OAAO,EAAEoI,GAAG,CAACtK,IAAI;cACjBoC,QAAQ,EAAExI,mEAAU,CAACyI;aACtB,CAAC;YACFuC,UAAU,CAAC,MAAK;cACdkS,KAAI,CAAC7X,OAAO,CAAC2E,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;YACjC,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM;YACLkT,KAAI,CAAC9X,MAAM,CAACgD,KAAK,CAAC;cAChBC,MAAM,EAAE,OAAO;cACfC,OAAO,EAAEoI,GAAG,CAACnI,OAAO;cACpBC,QAAQ,EAAExI,mEAAU,CAACyI;aACtB,CAAC;UACJ;QACF,CAAC,EACA2C,GAAG,IAAI;UACN8R,KAAI,CAAC9X,MAAM,CAACgD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;YACpBC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;QACJ,CAAC,CACF;QACDyU,KAAI,CAAClX,WAAW,CAACgB,IAAI,CAAC0W,0BAA0B,CAAC;MACnD,CAAC,MAAM;QACL/E,qEAAY,CAACiF,qBAAqB,CAACV,KAAI,CAACd,eAAe,CAAC;MAC1D;IAAC;EAEH;EAIAyB,iBAAiBA,CAACC,IAAY;IAC5BA,IAAI,CAAChV,KAAK,CAACvC,MAAM,GAAG,IAAI,CAACwT,SAAS,CAACxT,MAAM;IACzCuX,IAAI,CAAChV,KAAK,CAACkR,IAAI,GAAG,IAAI,CAACD,SAAS,CAACC,IAAI;IACrC8D,IAAI,CAAChV,KAAK,CAACnC,YAAY,GAAG,IAAI,CAACoT,SAAS,CAACpT,YAAY;IACrD,MAAMoX,kBAAkB,GAAG,IAAI,CAACnF,cAAc,CAACmB,SAAS,CAAC+D,IAAI,CAAChV,KAAK,CAAC,CAAC3C,SAAS,CAC3EC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;UAClB1C,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAElC,IAAI,CAACA,IAAI;UAClBoC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;QACFuC,UAAU,CAAC,MAAK;UACd8S,IAAI,CAAChV,KAAK,CAACkV,OAAO,GAAG,EAAE;UACvBF,IAAI,CAAChV,KAAK,CAACP,OAAO,GAAG,EAAE;UACvB,IAAI,CAAC0V,mBAAmB,EAAE;QAC5B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,IAAI,CAAC7Y,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAAC+W,kBAAkB,CAAC;EAC3C;EAEA1L,wBAAwBA,CAACC,EAAmB;IAC1C,OAAOA,EAAE,CAACnD,GAAG,CAAC,aAAa,CAAC,EAAErG,KAAK,KAAKwJ,EAAE,CAACnD,GAAG,CAAC,iBAAiB,CAAC,EAAErG,KAAK,GACpE,IAAI,GACJ;MAAEyJ,UAAU,EAAE;IAAI,CAAE;EAC1B;EAEA2L,sBAAsBA,CAACC,kBAA0B;IAC/C,MAAMrV,KAAK,GAAGqV,kBAAkB,CAACrV,KAAK;IACtCA,KAAK,CAACvC,MAAM,GAAG,IAAI,CAACd,WAAW;IAC/B,IAAI0Y,kBAAkB,CAAC7O,KAAK,EAAE;MAC5B,MAAM8O,uBAAuB,GAAG,IAAI,CAACvF,aAAa,CAACwF,cAAc,CAACvV,KAAK,CAAC,CAAC3C,SAAS,CAC/EC,IAAS,IAAI;QACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;YAClB1C,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAElC,IAAI,CAACA,IAAI;YAClBoC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;UACFuC,UAAU,CAAC,MAAK;YACd,IAAI,CAACsT,wBAAwB,EAAE;YAC/B,IAAI,CAACzF,aAAa,CAAC7D,SAAS,EAAE;YAC9B,IAAI,CAAC3P,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;UAC7B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAI,CAAC5E,MAAM,CAACgD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;YACrBC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;QACJ;MACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;QAChBC,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;QACpBC,QAAQ,EAAExI,mEAAU,CAACyI;OACtB,CAAC,CACL;MACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACoX,uBAAuB,CAAC;IAChD;EACF;EAEAG,QAAQA,CAAA;IACN,IAAI,CAAClZ,OAAO,CAAC2E,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC9B;EAEAwU,uBAAuBA,CAAA;IACrB,IAAI,CAAClE,mBAAmB,CAAC5Q,IAAI,EAAE;EACjC;EAEA4U,wBAAwBA,CAAA;IACtB,IAAI,CAAChE,mBAAmB,CAAC1Q,IAAI,EAAE;EACjC;EAEA6U,qBAAqBA,CAAA;IACnB,IAAI,CAAC/D,iBAAiB,CAAChR,IAAI,EAAE;IAC7B,IAAI,CAAC6P,KAAK,GAAG,IAAI,CAACJ,aAAa;EACjC;EAEAiC,sBAAsBA,CAAA;IACpB,IAAI,CAACV,iBAAiB,CAAC9Q,IAAI,EAAE;IAC7BqB,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;EAC1B;EAEAuT,kBAAkBA,CAAA;IAChB,IAAI,CAAC/D,cAAc,CAACjR,IAAI,EAAE;EAC5B;EAEAuU,mBAAmBA,CAAA;IACjB,IAAI,CAACtD,cAAc,CAAC/Q,IAAI,EAAE;EAC5B;;;uCA9gBW9K,wBAAwB,EAAAvB,+DAAA,CAAAW,mEAAA,GAAAX,+DAAA,CAAA6O,mEAAA,GAAA7O,+DAAA,CAAA+O,+DAAA,GAAA/O,+DAAA,CAAAiP,mDAAA,GAAAjP,+DAAA,CAAAmP,6DAAA,GAAAnP,+DAAA,CAAAkQ,uDAAA,GAAAlQ,+DAAA,CAAAiP,2DAAA;IAAA;EAAA;;;YAAxB1N,wBAAwB;MAAA1B,SAAA;MAAAwhB,SAAA,WAAAC,+BAAAhhB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;oEAwHrB4a,8EAAgB;;;;;;;;;;;;;;;UCpKhClb,4DAAA,UAAK;UAEHA,uDADA,iBAAyB,aACc;UACzCA,0DAAA,EAAM;UAGNA,4DAFA,cAA6B,eACS,cACrB;UACfA,uDAAA,gBAA0C;UAEtCA,4DADJ,cAA6B,cAC0H;UAAnDA,wDAAA,mBAAAuhB,uDAAA;YAAAvhB,2DAAA,CAAAgD,GAAA;YAAA,MAAAwe,UAAA,GAAAxhB,yDAAA;YAAA,OAAAA,yDAAA,CAASwhB,UAAA,CAAAC,KAAA,EAAe;UAAA,EAAC;UAAzHzhB,0DAAA,EAAmJ;UACnJA,4DAAA,mBAAgH;UAA7DA,wDAAA,oBAAA0hB,0DAAApc,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAAUO,GAAA,CAAA8d,aAAA,CAAA/Y,MAAA,CAAqB;UAAA,EAAC;UAAnFtF,0DAAA,EAAgH;UAEhHA,4DAAA,aAAoB;UAAAA,oDAAA,IAAa;UAAAA,0DAAA,EAAI;UACrCA,4DAAA,aAA8D;UAApCA,wDAAA,mBAAA2hB,sDAAA;YAAA3hB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAA0gB,uBAAA,EAAyB;UAAA,EAAC;UAACjhB,oDAAA,uBAAe;UACjFA,0DADiF,EAAI,EAC/E;UAEJA,4DADF,eAAgC,SAC3B;UAAAA,oDAAA,yBAAiB;UAAAA,0DAAA,EAAI;UACxBA,uDAAA,cAA4C;UAIpCA,4DAHR,eAAiB,eACa,eACF,iBACU;UAAAA,oDAAA,YAAI;UAAAA,4DAAA,gBAA2B;UAAAA,oDAAA,SAAC;UAAOA,0DAAP,EAAO,EAAQ;UAC7EA,uDAAA,iBAA6F;UAE7FA,wDADA,KAAA4hB,yCAAA,mBAAwH,KAAAC,yCAAA,mBACzC;UACjF7hB,0DAAA,EAAM;UAEJA,4DADF,eAAsB,iBACU;UAAAA,oDAAA,eAAO;UAAAA,4DAAA,gBAA2B;UAAAA,oDAAA,SAAC;UAAOA,0DAAP,EAAO,EAAQ;UAChFA,uDAAA,iBAAmG;UAEnGA,wDADA,KAAA8hB,yCAAA,mBAA8H,KAAAC,yCAAA,mBAC5C;UAExF/hB,0DADI,EAAM,EACJ;UAGFA,4DAFJ,eAAiC,eACT,iBACU;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAQ;UACjDA,uDAAA,iBAA0G;UAC5GA,0DAAA,EAAM;UAEJA,4DADF,eAAsB,iBACU;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAQ;UAC7CA,uDAAA,iBAA2G;UAE/GA,0DADE,EAAM,EACF;UAGFA,4DAFJ,eAAiC,eACT,iBACU;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAQ;UAC3CA,uDAAA,iBAA+F;UAC/FA,wDAAA,KAAAgiB,yCAAA,mBAAgF;UAClFhiB,0DAAA,EAAM;UAEJA,4DADF,eAAsB,iBACU;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAQ;UAChDA,uDAAA,iBAAyG;UACzGA,wDAAA,KAAAiiB,yCAAA,mBAAqF;UAEzFjiB,0DADE,EAAM,EACF;UAGFA,4DAFJ,eAAiC,eACR,iBACS;UAAAA,oDAAA,kBAAU;UAAAA,4DAAA,gBAA2B;UAAAA,oDAAA,SAAC;UAAOA,0DAAP,EAAO,EAAQ;UACnFA,uDAAA,oBAAoH;UACpHA,wDAAA,KAAAkiB,yCAAA,mBAAkI;UAEtIliB,0DADE,EAAM,EACF;UAGFA,4DAFJ,eAAiC,eACR,iBACS;UAAAA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAQ;UACtDA,uDAAA,oBAAwH;UAE5HA,0DADE,EAAM,EACF;UAEJA,4DADF,eAAiC,SAC5B;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EAAI;UAC1BA,uDAAA,cAA4C;UAItCA,4DAHN,eAAiB,eACa,eACJ,iBACU;UAAAA,oDAAA,eAAO;UAAAA,4DAAA,gBAA2B;UAAAA,oDAAA,SAAC;UAAOA,0DAAP,EAAO,EAAQ;UAChFA,4DAAA,kBAAuF;UAA3DA,wDAAA,oBAAAmiB,4DAAA7c,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAAUO,GAAA,CAAAyd,WAAA,CAAA1Y,MAAA,CAAmB;UAAA,EAAC;UACxDtF,wDAAA,KAAAoiB,2CAAA,qBAAgE;UAClEpiB,0DAAA,EAAS;UACTA,wDAAA,KAAAqiB,yCAAA,mBAAkI;UACpIriB,0DAAA,EAAM;UAEJA,4DADF,eAAsB,iBACU;UAAAA,oDAAA,YAAI;UAAAA,4DAAA,gBAA2B;UAAAA,oDAAA,SAAC;UAAOA,0DAAP,EAAO,EAAQ;UAE3EA,4DADF,kBAAqD,kBAClC;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAS;UACrCA,wDAAA,KAAAsiB,2CAAA,qBAA6D;UAC/DtiB,0DAAA,EAAS;UACTA,wDAAA,KAAAuiB,yCAAA,mBAA4H;UAIpIviB,0DAHM,EAAM,EACF,EACF,EACF;UAEJA,4DADF,eAAiC,SAC5B;UAAAA,oDAAA,gCAAwB;UAAAA,0DAAA,EAAI;UAC/BA,uDAAA,cAA4C;UAItCA,4DAHN,eAAiB,eACa,gBACJ,kBACU;UAAAA,oDAAA,oBAAW;UAAAA,0DAAA,EAAQ;UAE/CA,4DADF,mBAA0D,mBACvC;UAAAA,oDAAA,gCAAuB;UAAAA,0DAAA,EAAS;UACjDA,4DAAA,mBAAqC;UAAAA,oDAAA,6BAAoB;UAAAA,0DAAA,EAAS;UAClEA,4DAAA,mBAAmC;UAAAA,oDAAA,2BAAkB;UAAAA,0DAAA,EAAS;UAC9DA,4DAAA,mBAAoC;UAAAA,oDAAA,4BAAmB;UAAAA,0DAAA,EAAS;UAChEA,4DAAA,mBAAuC;UAAAA,oDAAA,+BAAsB;UAAAA,0DAAA,EAAS;UACtEA,4DAAA,mBAAqC;UAAAA,oDAAA,6BAAoB;UAAAA,0DAAA,EAAS;UAClEA,4DAAA,mBAAsB;UAAAA,oDAAA,cAAK;UAAAA,0DAAA,EAAS;UACpCA,4DAAA,mBAAwB;UAAAA,oDAAA,gBAAO;UAAAA,0DAAA,EAAS;UACxCA,4DAAA,mBAAwB;UAAAA,oDAAA,gBAAO;UAAAA,0DAAA,EAAS;UACxCA,4DAAA,mBAAuB;UAAAA,oDAAA,eAAM;UAEjCA,0DAFiC,EAAS,EAC/B,EACL;UAEJA,4DADF,gBAAsB,kBACU;UAAAA,oDAAA,iBAAQ;UAAAA,0DAAA,EAAQ;UAC9CA,uDAAA,kBAAsG;UACxGA,0DAAA,EAAM;UACJA,uDAAA,kBAA8C;UAGtDA,0DAFI,EAAM,EACF,EACF;UAEJA,4DADF,gBAAiC,UAC5B;UAAAA,oDAAA,kBAAS;UAAAA,0DAAA,EAAI;UAChBA,uDAAA,eAA4C;UAItCA,4DAHN,gBAAiB,gBACa,gBACH,mBACsD;UACzEA,wDAAA,MAAAwiB,4CAAA,qBAA4D;UAC9DxiB,0DAAA,EAAS;UACTA,wDAAA,MAAAyiB,0CAAA,mBAAgI;UAEpIziB,0DADE,EAAM,EACF;UAEJA,4DADF,gBAAiC,mBAC2C;UAAlCA,wDAAA,mBAAA0iB,4DAAA;YAAA1iB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAA2gB,qBAAA,EAAuB;UAAA,EAAC;UAAClhB,4DAAA,iBAAoB;UAAAA,oDAAA,kBAAS;UAOnHA,0DAPmH,EAAO,EAAS,EACnH,EACF,EACF,EACF,EACF,EACF,EACC;UAELA,4DADF,gBAAwC,mBACuB;UAArBA,wDAAA,mBAAA2iB,4DAAA;YAAA3iB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAygB,QAAA,EAAU;UAAA,EAAC;UAAChhB,4DAAA,iBAAoB;UAAAA,oDAAA,eAAM;UAAOA,0DAAP,EAAO,EAAS;UACvGA,4DAAA,mBAA4D;UAArBA,wDAAA,mBAAA4iB,4DAAA;YAAA5iB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAsR,QAAA,EAAU;UAAA,EAAC;UAAC7R,4DAAA,iBAAmB;UAAAA,oDAAA,aAAI;UAErFA,0DAFqF,EAAO,EAAS,EAC/F,EACA;UACNA,4DAAA,gBAAoC;UAClCA,uDAAA,WAAK;UAEoFA,4DADzF,gBAAiB,gBACwE,UAAG;UAAAA,oDAAA,uBAAc;UAAIA,0DAAJ,EAAI,EAAM;UAClHA,4DAAA,gBAAwH;UAA/BA,wDAAA,mBAAA6iB,yDAAA;YAAA7iB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAA4gB,kBAAA,EAAoB;UAAA,EAAC;UAACnhB,4DAAA,UAAG;UAAAA,oDAAA,mBAAU;UAGzIA,0DAHyI,EAAI,EAAM,EAC3I,EAEF;UAOEA,4DAJR,gBAA4J,gBAChH,gBACb,gBACC,eACuB;UAAAA,oDAAA,wBAAe;UAAAA,0DAAA,EAAK;UACnEA,4DAAA,mBAAqH;UAArCA,wDAAA,mBAAA8iB,4DAAA;YAAA9iB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAwgB,wBAAA,EAA0B;UAAA,EAAC;UAEtH/gB,0DADE,EAAS,EACL;UACNA,4DAAA,oBAA+I;UAAzFA,wDAAA,sBAAA+iB,6DAAA;YAAA/iB,2DAAA,CAAAgD,GAAA;YAAA,MAAAggB,qBAAA,GAAAhjB,yDAAA;YAAA,OAAAA,yDAAA,CAAAgjB,qBAAA,CAAAzC,IAAA,CAAAxO,KAAA,IAA6CxR,GAAA,CAAAogB,sBAAA,CAAAqC,qBAAA,CAA0C;UAAA,EAAC;UAKxIhjB,4DAJN,gBAAwB,gBACL,gBAES,qBAC+I;UAA9CA,8DAAA,2BAAAijB,mEAAA3d,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAAhD,gEAAA,CAAAO,GAAA,CAAA2iB,UAAA,CAAAC,WAAA,EAAA7d,MAAA,MAAA/E,GAAA,CAAA2iB,UAAA,CAAAC,WAAA,GAAA7d,MAAA;YAAA,OAAAtF,yDAAA,CAAAsF,MAAA;UAAA,EAAoC;UAA3JtF,0DAAA,EAAqK;UACrKA,wDAAA,MAAAojB,0CAAA,mBAAoG;UAGtGpjB,0DAAA,EAAM;UAEJA,4DADF,gBAA6B,qBAC2I;UAA9CA,8DAAA,2BAAAqjB,mEAAA/d,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAAhD,gEAAA,CAAAO,GAAA,CAAA2iB,UAAA,CAAAI,WAAA,EAAAhe,MAAA,MAAA/E,GAAA,CAAA2iB,UAAA,CAAAI,WAAA,GAAAhe,MAAA;YAAA,OAAAtF,yDAAA,CAAAsF,MAAA;UAAA,EAAoC;UAA5JtF,0DAAA,EAAsK;UACtKA,wDAAA,MAAAujB,0CAAA,mBAAoG;UAGtGvjB,0DAAA,EAAM;UAEJA,4DADF,gBAA6B,qBAC2J;UAAlDA,8DAAA,2BAAAwjB,mEAAAle,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAAhD,gEAAA,CAAAO,GAAA,CAAA2iB,UAAA,CAAA5O,eAAA,EAAAhP,MAAA,MAAA/E,GAAA,CAAA2iB,UAAA,CAAA5O,eAAA,GAAAhP,MAAA;YAAA,OAAAtF,yDAAA,CAAAsF,MAAA;UAAA,EAAwC;UAA5KtF,0DAAA,EAAsL;UACtLA,wDAAA,MAAAyjB,0CAAA,mBAA4G;UAKlHzjB,0DAFI,EAAM,EACF,EACF;UAEJA,4DADF,gBAA0B,mBAC0E;UAArCA,wDAAA,mBAAA0jB,4DAAA;YAAA1jB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAwgB,wBAAA,EAA0B;UAAA,EAAC;UAAC/gB,4DAAA,iBAAqB;UAACA,oDAAA,gBAAM;UAAQA,0DAAR,EAAO,EAAU;UAC/FA,4DAAhD,mBAAgD,iBAAqB;UAAAA,oDAAA,wBAAe;UAK5FA,0DAL4F,EAAO,EAAS,EAChG,EACD,EACD,EACF,EACF;UAMEA,4DAJR,gBAA4H,gBAC/C,gBAC9C,gBACC,eACmB;UAAAA,oDAAA,wBAAe;UAAAA,0DAAA,EAAK;UAC/DA,4DAAA,mBAAmH;UAAnCA,wDAAA,mBAAA2jB,4DAAA;YAAA3jB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAsd,sBAAA,EAAwB;UAAA,EAAC;UACpH7d,0DADqH,EAAS,EACxH;UAEJA,4DADF,gBAAoC,6BAQJ;UAH3BA,wDAAA,yBAAA4jB,yEAAAte,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAAeO,GAAA,CAAA8c,kBAAA,CAAA/X,MAAA,CAA0B;UAAA,EAAC;UAI9CtF,0DAAA,EAAgB;UAEhBA,4DAAA,6BAKsB;UADlBA,wDAAA,yBAAA6jB,yEAAAve,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAAeO,GAAA,CAAA8c,kBAAA,CAAA/X,MAAA,CAA0B;UAAA,EAAC;UAG/CtF,0DADE,EAAgB,EACZ;UAEJA,4DADF,gBAA6E,mBACqB;UAAnCA,wDAAA,mBAAA8jB,4DAAA;YAAA9jB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAsd,sBAAA,EAAwB;UAAA,EAAC;UAAC7d,4DAAA,iBAAqB;UAACA,oDAAA,gBAAM;UAAQA,0DAAR,EAAO,EAAU;UAC7IA,4DAAA,mBAAiE;UAAtBA,wDAAA,mBAAA+jB,4DAAA;YAAA/jB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAid,SAAA,EAAW;UAAA,EAAC;UAACxd,4DAAA,iBAAwB;UAAAA,oDAAA,aAAI;UAIrGA,0DAJqG,EAAO,EAAS,EACzG,EACF,EACF,EACF;UAOEA,4DAJR,gBAAkI,gBACtF,gBACb,gBACC,eACyB;UAAAA,oDAAA,mBAAU;UAAAA,0DAAA,EAAK;UAChEA,4DAAA,mBAAgH;UAAhCA,wDAAA,mBAAAgkB,4DAAA;YAAAhkB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAmgB,mBAAA,EAAqB;UAAA,EAAC;UAEjH1gB,0DADE,EAAS,EACL;UACNA,4DAAA,oBAAsG;UAA5DA,wDAAA,sBAAAikB,6DAAA;YAAAjkB,2DAAA,CAAAgD,GAAA;YAAA,MAAAkhB,SAAA,GAAAlkB,yDAAA;YAAA,OAAAA,yDAAA,CAAAkkB,SAAA,CAAA3D,IAAA,CAAAxO,KAAA,IAAiCxR,GAAA,CAAA+f,iBAAA,CAAA4D,SAAA,CAAyB;UAAA,EAAC;UAGjGlkB,4DAFJ,gBAAwB,gBACL,qBACqE;UAA/BA,8DAAA,2BAAAmkB,mEAAA7e,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAAhD,gEAAA,CAAAO,GAAA,CAAAic,SAAA,CAAAxT,MAAA,EAAA1D,MAAA,MAAA/E,GAAA,CAAAic,SAAA,CAAAxT,MAAA,GAAA1D,MAAA;YAAA,OAAAtF,yDAAA,CAAAsF,MAAA;UAAA,EAA8B;UAAnFtF,0DAAA,EAAoF;UAElFA,4DADF,gBAAwB,kBACQ;UAAAA,oDAAA,aAAI;UAAAA,4DAAA,iBAA0B;UAAAA,oDAAA,UAAC;UAAOA,0DAAP,EAAO,EAAQ;UAC5EA,4DAAA,sBAA2G;UAAtCA,8DAAA,2BAAAokB,mEAAA9e,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAAhD,gEAAA,CAAAO,GAAA,CAAAic,SAAA,CAAAC,IAAA,EAAAnX,MAAA,MAAA/E,GAAA,CAAAic,SAAA,CAAAC,IAAA,GAAAnX,MAAA;YAAA,OAAAtF,yDAAA,CAAAsF,MAAA;UAAA,EAA4B;UACnGtF,0DADE,EAA2G,EACvG;UAEJA,4DADF,gBAAwB,kBACQ;UAAAA,oDAAA,sBAAa;UAAAA,4DAAA,iBAA0B;UAAAA,oDAAA,UAAC;UAAOA,0DAAP,EAAO,EAAQ;UACrFA,4DAAA,uBAAmI;UAA9CA,8DAAA,2BAAAqkB,mEAAA/e,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAAhD,gEAAA,CAAAO,GAAA,CAAAic,SAAA,CAAApT,YAAA,EAAA9D,MAAA,MAAA/E,GAAA,CAAAic,SAAA,CAAApT,YAAA,GAAA9D,MAAA;YAAA,OAAAtF,yDAAA,CAAAsF,MAAA;UAAA,EAAoC;UAC3HtF,0DADE,EAAmI,EAC/H;UAEJA,4DADF,gBAA6B,kBACG;UAAAA,oDAAA,gBAAO;UAAAA,0DAAA,EAAQ;UAC7CA,4DAAA,uBAAoJ;UAAzCA,8DAAA,2BAAAskB,mEAAAhf,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAAhD,gEAAA,CAAAO,GAAA,CAAAic,SAAA,CAAAiE,OAAA,EAAAnb,MAAA,MAAA/E,GAAA,CAAAic,SAAA,CAAAiE,OAAA,GAAAnb,MAAA;YAAA,OAAAtF,yDAAA,CAAAsF,MAAA;UAAA,EAA+B;UAA1ItF,0DAAA,EAAoJ;UACpJA,wDAAA,MAAAukB,0CAAA,mBAAwF;UAC1FvkB,0DAAA,EAAM;UAEJA,4DADF,gBAA6B,kBACG;UAAAA,oDAAA,gBAAO;UAAAA,0DAAA,EAAQ;UAC7CA,4DAAA,0BAAqJ;UAAzCA,8DAAA,2BAAAwkB,sEAAAlf,MAAA;YAAAtF,2DAAA,CAAAgD,GAAA;YAAAhD,gEAAA,CAAAO,GAAA,CAAAic,SAAA,CAAAxR,OAAA,EAAA1F,MAAA,MAAA/E,GAAA,CAAAic,SAAA,CAAAxR,OAAA,GAAA1F,MAAA;YAAA,OAAAtF,yDAAA,CAAAsF,MAAA;UAAA,EAA+B;UAAUtF,0DAAA,EAAW;UAChKA,wDAAA,MAAAykB,0CAAA,mBAAwF;UAG9FzkB,0DAFI,EAAM,EACF,EACF;UAEJA,4DADF,gBAA0B,oBACqE;UAAhCA,wDAAA,mBAAA0kB,4DAAA;YAAA1kB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAmgB,mBAAA,EAAqB;UAAA,EAAC;UAAC1gB,4DAAA,iBAAoB;UAACA,oDAAA,gBAAM;UAAQA,0DAAR,EAAO,EAAU;UAClGA,4DAAvC,oBAAuC,iBAAmB;UAAAA,oDAAA,aAAI;UAKtEA,0DALsE,EAAO,EAAS,EAC1E,EACD,EACD,EACF,EACF;;;;;;;;;UAzQEA,uDAAA,GAA6B;UAA7BA,wDAAA,cAAAO,GAAA,CAAAse,eAAA,CAA6B;UAI1B7e,uDAAA,GAAmB;UAAnBA,mEAAA,QAAAO,GAAA,CAAA2M,SAAA,EAAAlN,2DAAA,CAAmB;UAGJA,uDAAA,GAAa;UAAbA,+DAAA,CAAAO,GAAA,CAAAgc,SAAA,CAAa;UAWAvc,uDAAA,IAA2F;UAA3FA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAA8F,QAAA,SAAAC,KAAA,IAAArkB,GAAA,CAAAse,eAAA,CAAAzN,QAAA,qBAA2F;UAC3FpR,uDAAA,EAAkD;UAAlDA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAAzN,QAAA,sBAAkD;UAKlDpR,uDAAA,GAAiG;UAAjGA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAA8F,QAAA,YAAAC,KAAA,IAAArkB,GAAA,CAAAse,eAAA,CAAAzN,QAAA,wBAAiG;UACjGpR,uDAAA,EAAqD;UAArDA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAAzN,QAAA,yBAAqD;UAiBvDpR,uDAAA,IAAmD;UAAnDA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAAzN,QAAA,uBAAmD;UAKnDpR,uDAAA,GAAwD;UAAxDA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAAzN,QAAA,4BAAwD;UAOxDpR,uDAAA,GAAqG;UAArGA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAA8F,QAAA,cAAAC,KAAA,IAAArkB,GAAA,CAAAse,eAAA,CAAAzN,QAAA,0BAAqG;UAiBjGpR,uDAAA,IAAc;UAAdA,wDAAA,YAAAO,GAAA,CAAAib,WAAA,CAAc;UAEdxb,uDAAA,EAAqG;UAArGA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAA8F,QAAA,cAAAC,KAAA,IAAArkB,GAAA,CAAAse,eAAA,CAAAzN,QAAA,0BAAqG;UAMrGpR,uDAAA,GAAW;UAAXA,wDAAA,YAAAO,GAAA,CAAAkb,QAAA,CAAW;UAEXzb,uDAAA,EAA+F;UAA/FA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAA8F,QAAA,WAAAC,KAAA,IAAArkB,GAAA,CAAAse,eAAA,CAAAzN,QAAA,uBAA+F;UAwC/FpR,uDAAA,IAAgB;UAAhBA,wDAAA,YAAAO,GAAA,CAAAqb,aAAA,CAAgB;UAEhB5b,uDAAA,EAAmG;UAAnGA,wDAAA,SAAAO,GAAA,CAAAse,eAAA,CAAA8F,QAAA,aAAAC,KAAA,IAAArkB,GAAA,CAAAse,eAAA,CAAAzN,QAAA,yBAAmG;UAwCTpR,uDAAA,IAAoC;UAApCA,8DAAA,YAAAO,GAAA,CAAA2iB,UAAA,CAAAC,WAAA,CAAoC;UAChInjB,uDAAA,GAAuE;UAAvEA,wDAAA,SAAA6kB,cAAA,CAAAtS,OAAA,KAAAsS,cAAA,CAAArS,OAAA,IAAAqS,cAAA,CAAAD,KAAA,EAAuE;UAKsB5kB,uDAAA,GAAoC;UAApCA,8DAAA,YAAAO,GAAA,CAAA2iB,UAAA,CAAAI,WAAA,CAAoC;UACjItjB,uDAAA,GAAuE;UAAvEA,wDAAA,SAAA8kB,cAAA,CAAAvS,OAAA,KAAAuS,cAAA,CAAAtS,OAAA,IAAAsS,cAAA,CAAAF,KAAA,EAAuE;UAKkC5kB,uDAAA,GAAwC;UAAxCA,8DAAA,YAAAO,GAAA,CAAA2iB,UAAA,CAAA5O,eAAA,CAAwC;UACjJtU,uDAAA,GAA+E;UAA/EA,wDAAA,SAAA+kB,mBAAA,CAAAxS,OAAA,KAAAwS,mBAAA,CAAAvS,OAAA,IAAAsS,cAAA,CAAAF,KAAA,EAA+E;UAyBzG5kB,uDAAA,IAAa;UAKfA,wDALE,SAAAO,GAAA,CAAAsI,IAAA,CAAa,YAAAtI,GAAA,CAAA0b,eAAA,CAIY,kBAAA+I,YAAA,CACD;UAK1BhlB,uDAAA,GAAc;UAGdA,wDAHA,SAAAO,GAAA,CAAAyb,KAAA,CAAc,kBAGG;UAuBmChc,uDAAA,IAA8B;UAA9BA,8DAAA,YAAAO,GAAA,CAAAic,SAAA,CAAAxT,MAAA,CAA8B;UAGZhJ,uDAAA,GAA4B;UAA5BA,8DAAA,YAAAO,GAAA,CAAAic,SAAA,CAAAC,IAAA,CAA4B;UAIZzc,uDAAA,GAAoC;UAApCA,8DAAA,YAAAO,GAAA,CAAAic,SAAA,CAAApT,YAAA,CAAoC;UAIdpJ,uDAAA,GAA+B;UAA/BA,8DAAA,YAAAO,GAAA,CAAAic,SAAA,CAAAiE,OAAA,CAA+B;UAC/GzgB,uDAAA,GAA2D;UAA3DA,wDAAA,SAAAilB,WAAA,CAAA1S,OAAA,KAAA0S,WAAA,CAAAzS,OAAA,IAAAyS,WAAA,CAAAL,KAAA,EAA2D;UAIsB5kB,uDAAA,GAA+B;UAA/BA,8DAAA,YAAAO,GAAA,CAAAic,SAAA,CAAAxR,OAAA,CAA+B;UAChHhL,uDAAA,GAA2D;UAA3DA,wDAAA,SAAAklB,WAAA,CAAA3S,OAAA,KAAA2S,WAAA,CAAA1S,OAAA,IAAA0S,WAAA,CAAAN,KAAA,EAA2D;;;qBDzNtFpiB,uDAAW,EAAA0N,4DAAA,EAAAA,0DAAA,EAAAA,sEAAA,EAAAA,gEAAA,EAAAA,sEAAA,EAAAA,8EAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,6DAAA,EAAAA,mDAAA,EAAAA,kDAAA,EAAEiL,2EAAa,EAAA7K,8EAAA,EAAAA,kFAAA,EAAES,+DAAmB,EAAAb,8DAAA,EAAAA,2DAAA,EAAExN,qEAAe,EAAEL,0DAAY,EAAAoO,qDAAA,EAAAA,kDAAA;MAAA5P,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AE1CjC;AAIxB;AAG6B;AAED;AACb;AAC6C;AAChC;;;;;;;;;;ICJrDb,4DAAA,eAAqE;IACnEA,uDAAA,eAG4D;IAC9DA,0DAAA,EAAM;;;;IAJCA,uDAAA,EAAyB;IAAzBA,wDAAA,QAAAmX,MAAA,CAAAsO,eAAA,IAAAzlB,2DAAA,CAAyB;;;;;IAYlBA,4DAAA,aAAmE;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAI;;;;;IACzFA,4DAAA,aAAmE;IAAAA,oDAAA,GAA0G;;;IAAAA,0DAAA,EAAI;;;;IAA9GA,uDAAA,EAA0G;IAA1GA,gEAAA,UAAAA,yDAAA,OAAAmX,MAAA,CAAAuO,aAAA,CAAA1gB,SAAA,4BAAAhF,yDAAA,OAAAmX,MAAA,CAAAuO,aAAA,CAAAzgB,OAAA,oBAA0G;;;;;IAQjLjF,4DAAA,aAAgE;IAC9DA,uDAAA,eAAmC;IAAAA,oDAAA,cACnC;IACEA,4DADF,eAA0C,eAClB;IACpBA,uDAAA,eAA0I;IAE9IA,0DADE,EAAM,EACD;IAACA,uDAAA,SAAK;IAAAA,4DAAA,eAAgC;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAO;IAC5DA,uDAAL,SAAK,UAAK;IACZA,0DAAA,EAAM;;;;;IACNA,4DAAA,aAAgE;IAC9DA,uDAAA,eAAmC;IAAAA,oDAAA,cACnC;IAAAA,4DAAA,eAA0C;IAAAA,oDAAA,GAA2D;;IAAAA,0DAAA,EAAO;IAACA,uDAAA,SAAK;IAAAA,4DAAA,eAA+B;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAO;IAC3JA,uDAAL,SAAK,UAAK;IACZA,0DAAA,EAAM;;;;IAFsCA,uDAAA,GAA2D;IAA3DA,+DAAA,CAAAA,yDAAA,OAAAmX,MAAA,CAAAuO,aAAA,CAAAvgB,oBAAA,gBAA2D;;;;;IAiE7GnF,4DAAA,YAAwD;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAI;;;;;IAEnEA,4DADF,eAAmE,eAClC;IAAAA,uDAAA,eAA8B;IAAMA,4DAAN,WAAM,aAAuE;IAAAA,oDAAA,GAAoB;IAChKA,0DADgK,EAAI,EAAO,EAAM,EAC3K;;;;IADkEA,uDAAA,GAAqB;IAArBA,mEAAA,SAAAmX,MAAA,CAAAwO,UAAA,EAAA3lB,2DAAA,CAAqB;IAA+CA,uDAAA,EAAoB;IAApBA,gEAAA,WAAAmX,MAAA,CAAAwO,UAAA,KAAoB;;;;;IAWxJ3lB,4DADJ,eAAiI,eACvG;IACpBA,uDAAA,eAAmG;IACrGA,0DAAA,EAAM;IAE6BA,4DADnC,eAAuB,gBACY,QAAG;IAAAA,oDAAA,GAAqB;IAAIA,0DAAJ,EAAI,EAAO;IAClEA,4DAAA,aAA4B;IAAAA,oDAAA,GAAqB;IAAAA,0DAAA,EAAI;IAErDA,4DAAA,aAAuB;IAAAA,oDAAA,IAA2B;IAE1DA,0DAF0D,EAAI,EACpD,EACJ;;;;IARqBA,uDAAA,GAAwB;IAAxBA,mEAAA,QAAAoZ,OAAA,CAAAlM,SAAA,EAAAlN,2DAAA,CAAwB;IAGTA,uDAAA,GAAqB;IAArBA,+DAAA,CAAAoZ,OAAA,CAAAjL,YAAA,CAAqB;IAC3BnO,uDAAA,GAAqB;IAArBA,+DAAA,CAAAoZ,OAAA,CAAAwM,WAAA,CAAqB;IAE1B5lB,uDAAA,GAA2B;IAA3BA,+DAAA,CAAAoZ,OAAA,CAAAyM,kBAAA,CAA2B;;;;;IAoC3D7lB,4DAAA,eAAiF;IAC7EA,uDAAA,eAAkH;IAClHA,4DAAA,aAAyB;IAAAA,oDAAA,GAAiB;IAC7CA,0DAD6C,EAAI,EAC3C;;;;IAFEA,uDAAA,EAAwB;IAAxBA,mEAAA,QAAAqZ,OAAA,CAAAnM,SAAA,EAAAlN,2DAAA,CAAwB;IACJA,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAqZ,OAAA,CAAAvB,QAAA,CAAiB;;;;;IAGuC9X,4DADpF,eAA0E,aACU,QAAG;IAAAA,oDAAA,oCAA6B;IACtHA,0DADsH,EAAI,EAAI,EACxH;;;;;IAPRA,qEAAA,GAAsD;IAKlDA,wDAJD,IAAA8lB,4DAAA,mBAAiF,IAAAC,4DAAA,mBAIN;;;;;IAJL/lB,uDAAA,EAAS;IAATA,wDAAA,YAAAgmB,SAAA,CAAS;IAI/BhmB,uDAAA,EAAyB;IAAzBA,wDAAA,SAAAgmB,SAAA,CAAAlgB,MAAA,OAAyB;;;ADhJlF,MAAO5E,4BAA4B;EAgBvCvB,YACUiI,QAA8B,EAC9BC,MAAsB,EACtBoe,YAA4B,EAC5Bne,OAAe,EACfoe,SAAmB,EACnBle,aAA0B;IAL1B,KAAAJ,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAoe,YAAY,GAAZA,YAAY;IACZ,KAAAne,OAAO,GAAPA,OAAO;IACP,KAAAoe,SAAS,GAATA,SAAS;IACT,KAAAle,aAAa,GAAbA,aAAa;IAlBvB,KAAAme,SAAS,GAAQ,EAAE;IACnB,KAAAC,mBAAmB,GAAU,EAAE;IAE/B,KAAAle,WAAW,GAAG,CAAC;IAEf,KAAAme,OAAO,GAAQ,WAAW;IAC1B,KAAAC,kBAAkB,GAAU,EAAE;IAC9B,KAAA/d,gBAAgB,GAAG,KAAK;IACxB,KAAAH,OAAO,GAAG,uBAAuB;IACjC,KAAAC,QAAQ,GAAG,wBAAwB;IAC3B,KAAAI,WAAW,GAAmB,EAAE;IAUtC,IAAI,CAACwE,SAAS,GAAG,IAAI,CAACgZ,YAAY,CAAC7J,QAAQ,CAACC,QAAQ,CAACzK,GAAG,CAAC,WAAW,CAAC;EACvE;EAEA5P,QAAQA,CAAA;IACN,MAAM0G,oBAAoB,GAAG,IAAI,CAACV,aAAa,CAACW,cAAc,EAAE,CAACC,SAAS,CAAEC,IAAS,IAAI;MACvF,MAAMC,eAAe,GAAG,IAAI,CAACd,aAAa,CAACe,aAAa,EAAE;MAC1DF,IAAI,IAAI,IAAI,GACP,IAAI,CAACX,WAAW,GAAGY,eAAe,CAACE,MAAM,GACzC,IAAI,CAACd,WAAW,GAAGW,IAAI,CAACG,MAAO;MACpCH,IAAI,IAAI,IAAI,GACP,IAAI,CAACI,aAAa,GAAGH,eAAe,CAACI,QAAQ,GAC7C,IAAI,CAACD,aAAa,GAAGJ,IAAI,CAACK,QAAS;IAC1C,CAAC,CAAC;IACF,IAAI,IAAI,CAAC+D,SAAS,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACsZ,kBAAkB,CAAC,IAAI,CAACtZ,SAAS,CAAC;IACzC;IAEA,IAAI,CAACuZ,UAAU,GAAG,IAAI9Y,MAAM,CAACsP,SAAS,CAACC,KAAK,CAC1C3O,QAAQ,CAAC4O,cAAc,CAAC,mBAAmB,CAAC,CAC7C;IACD,IAAI,CAACuJ,sBAAsB,EAAE;IAC7B,IAAI,CAAChe,WAAW,CAACgB,IAAI,CAACf,oBAAoB,CAAC;EAC7C;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;EAEA8d,kBAAkBA,CAACtZ,SAAc;IAC/B,MAAM1B,KAAK,GAAG;MACZ0B,SAAS,EAAEA,SAAS;MACpBjE,MAAM,EAAE,IAAI,CAACd;KACd;IACD,MAAMwe,sBAAsB,GAAG,IAAI,CAAC9e,QAAQ,CAAC+e,wBAAwB,CAACpb,KAAK,CAAC,CAAC3C,SAAS,CACnFC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAAC0b,aAAa,GAAG7c,IAAI,CAACA,IAAI;QAE9B,IAAI,CAACsd,SAAS,GAAG,IAAI,CAACT,aAAa,CAACphB,aAAa,CAACoG,KAAK,CAAC,GAAG,CAAC;QAC5D;QACA,IAAI,IAAI,CAACgb,aAAa,CAACkB,gBAAgB,EAAE;UACvC,IAAI,CAACjB,UAAU,GACb,IAAI,CAAC/d,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACsb,aAAa,CAACkB,gBAAgB;QACtE;QACA,IAAI,CAACP,OAAO,GACV,IAAI,CAACX,aAAa,CAAChiB,kBAAkB,IAAI,SAAS,GAC9C,eAAe,GACf,WAAW;QACjB,IAAI,CAACmjB,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAAChf,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACid,sBAAsB,CAAC;EAC/C;EAEAI,YAAYA,CAAA;IACV,MAAMC,SAAS,GAAa,EAAE;IAC9B,KAAK,MAAMC,KAAK,IAAI,IAAI,CAACb,SAAS,EAAE;MAClCY,SAAS,CAACtd,IAAI,CAAC,IAAI,CAAC7B,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAG4c,KAAK,CAACC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC5E;IACA,OAAOF,SAAS;EAClB;EAEAtB,eAAeA,CAAA;IACb,IAAI,IAAI,CAACU,SAAS,IAAI,IAAI,CAACA,SAAS,CAACrgB,MAAM,GAAG,CAAC,EAAE;MAC/C,OAAO,IAAI,CAAC8B,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC+b,SAAS,CAAC,CAAC,CAAC,CAACc,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;IAC/E;IACA,OAAO,EAAE;EACX;EAEAC,qBAAqBA,CAAC3jB,EAAO;IAC3B,IAAI,CAACijB,UAAU,CAACra,IAAI,EAAE;IACtB,IAAI,CAACc,SAAS,GAAG1J,EAAE;EACrB;EAEA4jB,sBAAsBA,CAAA;IACpB,IAAI,CAACX,UAAU,CAACna,IAAI,EAAE;EACxB;EAEAU,YAAYA,CAACxJ,EAAO;IAClB,MAAM+I,WAAW,GAAG,IAAI,CAACtE,aAAa,CAACuE,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAAC1E,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAACY,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACrF,MAAM,CAACsF,OAAO,CAAC;QAClBrC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAExI,mEAAU,CAACyI;OACtB,CAAC;MACF,IAAI,CAACpD,OAAO,CAAC2E,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAACtD,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,MAAMuC,KAAK,GAAG;QACZ0B,SAAS,EAAE,IAAI,CAACyY,aAAa,CAACniB,EAAE;QAChCyF,MAAM,EAAE,IAAI,CAACd,WAAW;QACxB4E,WAAW,EAAEvK,mCAAM,EAAE,CAAC6K,MAAM,CAAC,sBAAsB,CAAC;QACpDC,MAAM,EAAE,KAAK;QACbC,KAAK,EAAE;OACR;MACD,MAAM8Z,gBAAgB,GAAG,IAAI,CAACxf,QAAQ,CAACmF,YAAY,CAACxB,KAAK,CAAC,CAAC3C,SAAS,CACjEC,IAAS,IAAI;QACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;YAAE1C,MAAM,EAAE,SAAS;YAAEC,OAAO,EAAElC,IAAI,CAACA;UAAI,CAAE,CAAC;UAC9D4E,UAAU,CAAC,MAAK;YACd,IAAI,CAAC0Z,sBAAsB,EAAE;YAC7B,IAAI,CAACrf,OAAO,CAAC2E,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;UAClC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAI,CAAC5E,MAAM,CAACgD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;YACrBC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;QACJ;MACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;QAChBC,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;QACpBC,QAAQ,EAAExI,mEAAU,CAACyI;OACtB,CAAC,CACL;MACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAAC2d,gBAAgB,CAAC;IACzC;EACF;EAEAC,WAAWA,CAACC,WAAgB;IAC1B,MAAMhb,WAAW,GAAG,IAAI,CAACtE,aAAa,CAACuE,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAAC1E,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAACY,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACrF,MAAM,CAACsF,OAAO,CAAC;QAClBrC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAExI,mEAAU,CAACyI;OACtB,CAAC;MACF,IAAI,CAACpD,OAAO,CAAC2E,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAACtD,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,MAAMuC,KAAK,GAAG;QACZ0B,SAAS,EAAE,IAAI,CAACyY,aAAa,CAACniB,EAAE;QAChCyF,MAAM,EAAE,IAAI,CAACd,WAAW;QACxBqf,kBAAkB,EAAED,WAAW;QAC/B1B,WAAW,EAAErjB,mCAAM,EAAE,CAAC6K,MAAM,CAAC,sBAAsB;OACpD;MACD,MAAMoa,uBAAuB,GAAG,IAAI,CAAC5f,QAAQ,CAAC6f,iBAAiB,CAAClc,KAAK,CAAC,CAAC3C,SAAS,CAC7EC,IAAS,IAAI;QACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;YAClB1C,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAElC,IAAI,CAACA,IAAI;YAClBoC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;UACFwC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,MAAM;UACL,IAAI,CAAC/F,MAAM,CAACgD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;YACrBC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;QACJ;MACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;QAChBC,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;QACpBC,QAAQ,EAAExI,mEAAU,CAACyI;OACtB,CAAC,CACL;MACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAAC+d,uBAAuB,CAAC;IAChD;EACF;EAEAX,qBAAqBA,CAAA;IACnB,MAAMW,uBAAuB,GAAG,IAAI,CAAC5f,QAAQ,CAAC8f,6BAA6B,CAAC,IAAI,CAAChC,aAAa,CAACniB,EAAE,CAAC,CAACqF,SAAS,CACzGC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACsc,kBAAkB,GAAGzd,IAAI,CAACA,IAAI;QAEnC,IAAI,CAACyd,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACrc,GAAG,CAAEC,CAAC,IAAI;UAC1D,OAAO;YACL3G,EAAE,EAAE2G,CAAC,CAAC3G,EAAE;YACRsiB,kBAAkB,EAAE3b,CAAC,CAAC2b,kBAAkB;YACxCD,WAAW,EAAE1b,CAAC,CAAC0b,WAAW,GACtB,IAAI,CAACM,SAAS,CAACyB,SAAS,CACtBzd,CAAC,CAAC0b,WAAW,EACb,yBAAyB,CAC1B,GACD,EAAE;YACN3Y,SAAS,EAAE/C,CAAC,CAAC+C,SAAS;YACtBjE,MAAM,EAAEkB,CAAC,CAAClB,MAAM;YAChBmF,YAAY,EAAEjE,CAAC,CAACiE,YAAY;YAC5BjB,SAAS,EAAEhD,CAAC,CAACgD,SAAS,GAClB,IAAI,CAACtF,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAGF,CAAC,CAACgD,SAAS,GAC1C;WACL;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACrF,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAAC+d,uBAAuB,CAAC;EAChD;EAEAI,mBAAmBA,CAAC3a,SAAc;IAChC,MAAMX,WAAW,GAAG,IAAI,CAACtE,aAAa,CAACuE,YAAY,EAAE;IACrD,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACE,QAAQ,IAAI,MAAM,EAAE;MACzD,IAAI,CAAC1E,OAAO,CAAC2E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIH,WAAW,CAACY,SAAS,IAAI,EAAE,EAAE;MACtC,IAAI,CAACrF,MAAM,CAACsF,OAAO,CAAC;QAClBrC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,kCAAkC;QAC3CE,QAAQ,EAAExI,mEAAU,CAACyI;OACtB,CAAC;MACF,IAAI,CAACpD,OAAO,CAAC2E,QAAQ,CAAC,CAAC,eAAeH,WAAW,CAACtD,MAAM,EAAE,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAACT,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;MAC9C,MAAMgD,KAAK,GAAG;QACZ0B,SAAS,EAAEA,SAAS;QACpBjE,MAAM,EAAE,IAAI,CAACd;OACd;MACD,IAAI,IAAI,CAACK,gBAAgB,EAAE;QACzB,MAAMsf,4BAA4B,GAAG,IAAI,CAACjgB,QAAQ,CAACkgB,mBAAmB,CAACvc,KAAK,CAAC,CAAC3C,SAAS,CAAEC,IAAS,IAAI;UACpG,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;YACpB,IAAI,CAACuc,kBAAkB,CAACtZ,SAAS,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAACpF,MAAM,CAACgD,KAAK,CAAC;cAChBC,MAAM,EAAE,OAAO;cACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;cACrBC,QAAQ,EAAExI,mEAAU,CAACyI;aACtB,CAAC;UACJ;QACF,CAAC,CAAC;QACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACoe,4BAA4B,CAAC;MACrD,CAAC,MAAM;QACL,MAAME,+BAA+B,GAAG,IAAI,CAACngB,QAAQ,CAACogB,sBAAsB,CAACzc,KAAK,CAAC,CAAC3C,SAAS,CAAEC,IAAS,IAAI;UAC1G,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;YACpB,IAAI,CAACuc,kBAAkB,CAACtZ,SAAS,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAACpF,MAAM,CAACgD,KAAK,CAAC;cAChBC,MAAM,EAAE,OAAO;cACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;cACrBC,QAAQ,EAAExI,mEAAU,CAACyI;aACtB,CAAC;UACJ;QACF,CAAC,CAAC;QACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACse,+BAA+B,CAAC;MACxD;IACF;EACF;EAEAtB,sBAAsBA,CAAA;IACpB,MAAMlb,KAAK,GAAG;MACZ0B,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBjE,MAAM,EAAE,IAAI,CAACd;KACd;IACD,MAAM+f,sBAAsB,GAAG,IAAI,CAACrgB,QAAQ,CAACwe,mBAAmB,CAAC7a,KAAK,CAAC,CAAC3C,SAAS,CAAEC,IAAS,IAAI;MAC9F,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACoc,mBAAmB,GAAGvd,IAAI,CAACA,IAAI;QACpC,IAAI,CAACud,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACnc,GAAG,CAAEC,CAAC,IAAI;UAC5D,OAAO;YACL3G,EAAE,EAAE2G,CAAC,CAAC3G,EAAE;YACR2kB,QAAQ,EAAEhe,CAAC,CAACge,QAAQ;YACpBlf,MAAM,EAAEkB,CAAC,CAAClB,MAAM;YAChB8O,QAAQ,EAAE5N,CAAC,CAAC4N,QAAQ;YACpB5K,SAAS,EAAEhD,CAAC,CAACgD,SAAS,GAClB,IAAI,CAACtF,QAAQ,CAACwC,QAAQ,GAAG,GAAG,GAAGF,CAAC,CAACgD,SAAS,GAC1C;WACL;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACrF,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACwe,sBAAsB,CAAC;EAC/C;;;uCApUW/mB,4BAA4B,EAAAlB,+DAAA,CAAAW,kFAAA,GAAAX,+DAAA,CAAA6O,4DAAA,GAAA7O,+DAAA,CAAA+O,2DAAA,GAAA/O,+DAAA,CAAA+O,mDAAA,GAAA/O,+DAAA,CAAAiP,sDAAA,GAAAjP,+DAAA,CAAAmP,+DAAA;IAAA;EAAA;;;YAA5BjO,4BAA4B;MAAArB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAA+nB,sCAAA7nB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCtBzCN,uDAAA,cAAoE;UACpEA,4DAAA,UAAK;UAEHA,uDADA,iBAAyB,4BACsB;UACjDA,0DAAA,EAAM;UAGAA,4DAFN,aAAuB,aACmB,aACd;UACpBA,wDAAA,IAAAooB,2CAAA,iBAAqE;UAMvEpoB,0DAAA,EAAM;UAEJA,4DADF,aAA8B,WACT;UAAAA,oDAAA,IAA8B;UAAAA,0DAAA,EAAI;UACnDA,4DAAA,YAAkB;UAACA,oDAAA,IAAoC;UAAAA,0DAAA,EAAI;UAGnDA,4DAFJ,cAAkB,eACK,eACI;UAEnBA,wDADA,KAAAqoB,0CAAA,gBAAmE,KAAAC,0CAAA,gBACA;UAE3EtoB,0DADI,EAAM,EACJ;UAEFA,4DADF,eAA+B,cACP;UACpBA,uDAAA,eAAqC;UAAAA,oDAAA,eACrC;UAAAA,4DAAA,gBAA0C;UAAAA,oDAAA,IAA6B;UAAAA,0DAAA,EAAO;UAACA,uDAAA,UAAK;UAAAA,4DAAA,gBAA+B;UAAAA,oDAAA,kBAAU;UAC/HA,0DAD+H,EAAO,EAChI;UAUNA,wDATA,KAAAuoB,4CAAA,mBAAgE,KAAAC,4CAAA,mBASA;UAKpExoB,0DAAA,EAAM;UACNA,uDAAA,aAA8F;UAG1DA,4DAFpC,eAAiB,cACW,kBACQ,gBAAsD;UAAhDA,wDAAA,mBAAAyoB,6DAAA;YAAAzoB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAqnB,mBAAA,CAAArnB,GAAA,CAAAmlB,aAAA,CAAAniB,EAAA,CAAqC;UAAA,EAAC;UAACvD,uDAAA,eAAgH;UAAAA,0DAAA,EAAO;UAAAA,4DAAA,gBAAsB;UAAAA,oDAAA,wBAAgB;UACjPA,0DADiP,EAAO,EAAS,EAC3P;UAE2BA,4DADhC,cAAsB,kBACU,YAAM;UAAAA,uDAAA,eAA6C;UAAAA,0DAAA,EAAO;UAAAA,4DAAA,gBAAsB;UAAAA,oDAAA,iCAAyB;UAE/IA,0DAF+I,EAAO,EAAS,EACnJ,EACN;UACNA,4DAAA,eAAyB;UAKtBA,uDAJA,gBAAgC,gBACA,gBACA,gBACA,gBACA;UACpCA,0DAAA,EAAM;UAGFA,4DAFJ,eAAiB,eACkB,eACT;UAAAA,uDAAA,eAA6C;UAAAA,0DAAA,EAAM;UAChDA,4DAAzB,eAAyB,gBAAyC;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAO;UAAAA,uDAAA,UAAK;UAAAA,4DAAA,YAAM;UAAAA,oDAAA,IAA0B;UACpHA,0DADoH,EAAO,EAAM,EAC3H;UAEJA,4DADF,eAAiC,eACT;UAAAA,uDAAA,eAA4C;UAAAA,0DAAA,EAAM;UAC/CA,4DAAzB,eAAyB,gBAAyC;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAO;UAAAA,uDAAA,UAAK;UAAAA,4DAAA,YAAM;UAAAA,oDAAA,IAAkC;UAC7HA,0DAD6H,EAAO,EAAM,EACpI;UAEJA,4DADF,eAAiC,eACT;UAAAA,uDAAA,eAA6C;UAAAA,0DAAA,EAAM;UAChDA,4DAAzB,eAAyB,gBAAwC;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAO;UAAAA,uDAAA,UAAK;UAAAA,4DAAA,YAAM;UAAAA,oDAAA,IAAmD;;UAAAA,uDAAA,UAAK;UAAAA,oDAAA,IAAmD;;UACpMA,0DADoM,EAAO,EAAM,EAC3M;UAEJA,4DADF,eAAiC,eACT;UAAAA,uDAAA,eAAqD;UAAAA,0DAAA,EAAM;UACxDA,4DAAzB,eAAyB,gBAAyC;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAO;UAAAA,uDAAA,UAAK;UAAAA,4DAAA,YAAM;UAAAA,oDAAA,IAAyC;UAE7IA,0DAF6I,EAAO,EAAM,EAClJ,EACF;UAEJA,4DADF,eAAoH,kBACiC;UAAlDA,wDAAA,mBAAA0oB,+DAAA;YAAA1oB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAA2mB,qBAAA,CAAA3mB,GAAA,CAAAmlB,aAAA,CAAAniB,EAAA,CAAuC;UAAA,EAAC;UAACvD,4DAAA,gBAAoB;UACrKA,oDAAA,IAAkB;UAAAA,uDAAA,aAAqE;UAKvGA,0DALuG,EAAO,EAAS,EACrG,EACD,EAEP,EACJ;UAMIA,4DALV,eAAwD,eAChC,eACO,kBACK,eACL,cACG;UAAAA,oDAAA,qBAAY;UAAAA,0DAAA,EAAI;UACxCA,4DAAA,cAAyB;UACvBA,oDAAA,kPACA;UAAKA,uDAAL,WAAK,WAAK;UACVA,oDAAA,gOACA;UAAKA,uDAAL,WAAK,WAAK;UACVA,oDAAA,ucACF;UAAAA,0DAAA,EAAI;UACJA,4DAAA,cAAwB;UAAAA,oDAAA,kBAAS;UAAAA,0DAAA,EAAI;UACrCA,4DAAA,cAAyB;UACvBA,oDAAA,wcACA;UAAKA,uDAAL,WAAK,WAAK;UACVA,oDAAA,iWACF;UAAAA,0DAAA,EAAI;UAEJA,wDADA,MAAA2oB,2CAAA,gBAAwD,MAAAC,6CAAA,kBACW;UAGrE5oB,0DAAA,EAAM;UACNA,4DAAA,gBAA4B;UAAAA,oDAAA,KAA2C;UAAAA,0DAAA,EAAM;UAC7EA,4DAAA,gBAAwB;UACtBA,uDAAA,wBAAkG;UAEhGA,4DADF,gBAAkB,mBACiE;UAAzCA,wDAAA,mBAAA6oB,gEAAA;YAAA7oB,2DAAA,CAAAgD,GAAA;YAAA,MAAA8lB,cAAA,GAAA9oB,yDAAA;YAAA,OAAAA,yDAAA,CAASO,GAAA,CAAA8mB,WAAA,CAAAyB,cAAA,CAAAvd,KAAA,CAA8B;UAAA,EAAC;UAACvL,4DAAA,iBAAoB;UAAAA,oDAAA,qBAAY;UACnHA,0DADmH,EAAO,EAAS,EAC7H;UACNA,4DAAA,gBAA8G;UAC1GA,wDAAA,MAAA+oB,6CAAA,mBAAiI;UAe7I/oB,0DAJQ,EAAM,EACF,EACC,EACT,EACE;UAIAA,4DAHN,gBAAsB,gBACc,gBACT,cAC0D;UAAAA,oDAAA,oBAAW;UAAAA,0DAAA,EAAI;UAC9FA,uDAAA,cAAyB;UACzBA,4DAAA,cAA0B;UAAAA,oDAAA,eAAM;UAAAA,4DAAA,iBAA+B;UAAAA,oDAAA,KAAkC;UAAOA,0DAAP,EAAO,EAAI;UAC5GA,uDAAA,cAAyB;UACzBA,4DAAA,cAA0B;UAAAA,oDAAA,aAAI;UAAAA,4DAAA,iBAA+B;UAAAA,oDAAA,2BAAkB;UAAOA,0DAAP,EAAO,EAAI;UAC1FA,uDAAA,cAAyB;UACCA,4DAA1B,cAA0B,aAAM;UAAAA,oDAAA,eAAM;UAAAA,0DAAA,EAAO;UAC3CA,4DAAA,iBAAgD;UAK9CA,uDAJA,iBAAwC,iBACA,iBACA,iBACA,iBACR;UAClCA,0DAAA,EAAO;UAAAA,oDAAA,oCAET;UAEJA,0DAFI,EAAI,EACA,EACF;UAIFA,4DAFJ,gBAAkC,gBACT,cAC0D;UAAAA,oDAAA,0BAAiB;UAAAA,0DAAA,EAAI;UACpGA,uDAAA,cAAyB;UACzBA,4DAAA,gBAAiB;UACfA,wDAAA,MAAAgpB,sDAAA,2BAAsD;UAclEhpB,0DALU,EAAM,EACH,EACD,EACF,EACF,EACF;UACNA,4DAAA,gBAA6B;UAC3BA,uDAAA,cAA8E;UAE1EA,4DADJ,gBAAc,cACkH;UAC1HA,oDAAA,0BACF;UAAAA,0DAAA,EAAI;UAIEA,4DAHN,gBAAuE,gBAC/C,gBAC+B,gBACW;UACtDA,uDAAA,gBAA6C;UAC7CA,4DAAA,gBAA4B;UAAAA,uDAAA,cAAgC;UAAAA,oDAAA,sBAAa;UAAAA,0DAAA,EAAM;UAC/EA,4DAAA,gBAA8B;UAAAA,uDAAA,gBAA+C;UAAAA,0DAAA,EAAM;UACnFA,4DAAA,gBAA6B;UAAAA,uDAAA,cAAmC;UAAAA,0DAAA,EAAM;UACtEA,4DAAA,gBAAsB;UAAAA,oDAAA,oBAAW;UACvCA,0DADuC,EAAM,EACvC;UAEFA,4DADJ,gBAAuB,aACA;UAAAA,oDAAA,oEACS;UAAAA,0DAAA,EAAI;UAChCA,4DAAA,cAAmB;UAACA,oDAAA,gHAAsG;UAAAA,0DAAA,EAAI;UAE5HA,4DADF,gBAAuC,gBACD;UAClCA,oDAAA,sBACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,gBAAuE;UAKrEA,uDAJA,iBAAwC,iBACA,iBACA,iBACR,iBACA;UAEpCA,0DADE,EAAM,EACF;UAOAA,4DANN,YAAK,gBAIkB,gBACgB,cACJ;UAAAA,oDAAA,4BAAmB;UAGtDA,0DAHsD,EAAI,EAChD,EACF,EACF;UAEFA,4DADJ,gBAA8B,gBACH;UACrBA,uDAAA,gBAAqF;UAAAA,oDAAA,gBACrF;UAAAA,4DAAA,iBAAyD;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAACA,uDAAA,WAAK;UAAAA,4DAAA,iBAA+B;UAAAA,oDAAA,4BAAmB;UAGrIA,0DAHqI,EAAO,EAC9H,EACJ,EACJ;UACNA,uDAAA,cAAoD;UAETA,4DAD3C,gBAAuD,mBACZ,iBAAqB;UAAAA,oDAAA,qBAAY;UAAAA,uDAAA,cAAqE;UAElJA,0DAFkJ,EAAO,EAAS,EAC3J,EACD;UAELA,4DADF,gBAAmD,gBACW;UACtDA,uDAAA,gBAA+C;UAC/CA,4DAAA,gBAA4B;UAAAA,uDAAA,cAAgC;UAAAA,oDAAA,wBAAe;UAAAA,0DAAA,EAAM;UACjFA,4DAAA,gBAA8B;UAAAA,uDAAA,gBAA+C;UAAAA,0DAAA,EAAM;UACnFA,4DAAA,gBAA6B;UAAAA,uDAAA,cAAmC;UAAAA,0DAAA,EAAM;UACtEA,4DAAA,gBAAsB;UAAAA,oDAAA,oBAAW;UACvCA,0DADuC,EAAM,EACvC;UAEFA,4DADJ,gBAAuB,aACA;UAAAA,oDAAA,8CAEnB;UAAAA,0DAAA,EAAI;UACJA,4DAAA,cAAmB;UAACA,oDAAA,gHAAsG;UAAAA,0DAAA,EAAI;UAE5HA,4DADF,gBAAuC,gBACD;UAClCA,oDAAA,wBACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,gBAAuE;UAKrEA,uDAJA,iBAAwC,iBACA,iBACA,iBACR,iBACA;UAEpCA,0DADE,EAAM,EACF;UAOAA,4DANN,YAAK,gBAIkB,gBACgB,cACJ;UAAAA,oDAAA,2BAAkB;UAGrDA,0DAHqD,EAAI,EAC/C,EACF,EACF;UAEFA,4DADJ,gBAA8B,eACJ;UACpBA,uDAAA,gBAA4E;UAAAA,oDAAA,gBAC5E;UAAAA,4DAAA,iBAAyD;UAAAA,oDAAA,WAAE;UAAAA,0DAAA,EAAO;UAACA,uDAAA,WAAK;UAAAA,4DAAA,iBAA+B;UAAAA,oDAAA,kBAAS;UAClHA,0DADkH,EAAO,EACnH;UACNA,4DAAA,eAAsB;UACpBA,uDAAA,iBAA2F;UAAAA,oDAAA,gBAC3F;UACEA,4DADF,iBAA0C,iBACiD;UACvFA,uDAAA,iBAA0I;UAE9IA,0DADE,EAAM,EACD;UAACA,uDAAA,WAAK;UAAAA,4DAAA,kBAA2C;UAAAA,oDAAA,sBAAa;UAG/EA,0DAH+E,EAAO,EACxE,EACJ,EACJ;UACNA,uDAAA,cAAoD;UAETA,4DAD3C,gBAAuD,mBACZ,iBAAqB;UAAAA,oDAAA,qBAAY;UAAAA,uDAAA,cAAqE;UAElJA,0DAFkJ,EAAO,EAAS,EAC3J,EACD;UAELA,4DADD,gBAAmD,gBACU;UACtDA,uDAAA,iBAAmD;UACnDA,4DAAA,gBAA4B;UAAAA,uDAAA,cAAgC;UAAAA,oDAAA,oBAAW;UAAAA,0DAAA,EAAM;UAC7EA,4DAAA,gBAA8B;UAAAA,uDAAA,gBAA+C;UAAAA,0DAAA,EAAM;UACnFA,4DAAA,gBAA6B;UAAAA,uDAAA,cAAmC;UAAAA,0DAAA,EAAM;UACtEA,4DAAA,gBAAsB;UAAAA,oDAAA,oBAAW;UACvCA,0DADuC,EAAM,EACvC;UAEFA,4DADJ,gBAAuB,aACA;UAAAA,oDAAA,gDAEnB;UAAAA,0DAAA,EAAI;UACJA,4DAAA,cAAmB;UAACA,oDAAA,gHAAsG;UAAAA,0DAAA,EAAI;UAE5HA,4DADF,gBAAuC,gBACD;UAClCA,oDAAA,wBACF;UAAAA,0DAAA,EAAM;UACNA,4DAAA,gBAAuE;UAKrEA,uDAJA,iBAAwC,iBACA,iBACA,iBACR,iBACA;UAEpCA,0DADE,EAAM,EACF;UAOFA,4DANJ,YAAK,gBAIgB,gBACgB,cACJ;UAAAA,oDAAA,2BAAkB;UAGnDA,0DAHmD,EAAI,EAC/C,EACF,EACA;UAEJA,4DADF,gBAA8B,eACN;UACpBA,uDAAA,gBAA4E;UAAAA,oDAAA,gBAC5E;UAAAA,4DAAA,iBAAyD;UAAAA,oDAAA,WAAE;UAAAA,0DAAA,EAAO;UAACA,uDAAA,WAAK;UAAAA,4DAAA,iBAA+B;UAAAA,oDAAA,kBAAS;UAClHA,0DADkH,EAAO,EACnH;UACNA,4DAAA,eAAsB;UACpBA,uDAAA,iBAA2F;UAAAA,oDAAA,gBAC3F;UACEA,4DADF,iBAA0C,iBACiD;UACvFA,uDAAA,iBAA0I;UAE9IA,0DADE,EAAM,EACD;UAACA,uDAAA,WAAK;UAAAA,4DAAA,kBAA2C;UAAAA,oDAAA,sBAAa;UAE3EA,0DAF2E,EAAO,EACxE,EACJ;UACRA,uDAAA,cAAoD;UAETA,4DAD3C,gBAAuD,mBACZ,iBAAqB;UAAAA,oDAAA,qBAAY;UAAAA,uDAAA,cAAqE;UAM3JA,0DAN2J,EAAO,EAAS,EAC3J,EACD,EACP,EACE,EACJ,EACA;UACNA,4DAAA,eAAkB;UAChBA,uDAAA,mBAAyB;UAC3BA,0DAAA,EAAM;UAOEA,4DAJR,iBAA0J,iBAC9G,iBACb,iBACC,gBACuB;UAAAA,oDAAA,uBAAc;UAAAA,0DAAA,EAAK;UAClEA,4DAAA,oBAAmH;UAAnCA,wDAAA,mBAAAipB,gEAAA;YAAAjpB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAA4mB,sBAAA,EAAwB;UAAA,EAAC;UAEpHnnB,0DADE,EAAS,EACL;UACNA,4DAAA,iBAAwB;UACtBA,uDAAA,mBAA8B;UAC7BA,4DAAA,WAAI;UAAAA,oDAAA,qDAA4C;UACnDA,0DADmD,EAAK,EAClD;UAEJA,4DADF,iBAA0B,oBACwE;UAAnCA,wDAAA,mBAAAkpB,gEAAA;YAAAlpB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAA4mB,sBAAA,EAAwB;UAAA,EAAC;UAACnnB,4DAAA,kBAAsB;UAACA,oDAAA,gBAAM;UAAQA,0DAAR,EAAO,EAAU;UAC9IA,4DAAA,oBAA0E;UAAlCA,wDAAA,mBAAAmpB,gEAAA;YAAAnpB,2DAAA,CAAAgD,GAAA;YAAA,OAAAhD,yDAAA,CAASO,GAAA,CAAAwM,YAAA,CAAAxM,GAAA,CAAA0M,SAAA,CAAuB;UAAA,EAAC;UAACjN,4DAAA,kBAAsB;UAAAA,oDAAA,cAAK;UAI7GA,0DAJ6G,EAAO,EAAS,EACjH,EACF,EACF,EACF,EA9LqB;;;UAxKOA,uDAAA,GAAuC;UAAvCA,wDAAA,SAAAO,GAAA,CAAA4lB,SAAA,IAAA5lB,GAAA,CAAA4lB,SAAA,CAAArgB,MAAA,KAAuC;UAQhD9F,uDAAA,GAA8B;UAA9BA,+DAAA,CAAAO,GAAA,CAAAmlB,aAAA,CAAAhhB,YAAA,CAA8B;UAC5B1E,uDAAA,GAAoC;UAApCA,gEAAA,MAAAO,GAAA,CAAAmlB,aAAA,CAAAxe,kBAAA,KAAoC;UAIjBlH,uDAAA,GAAuC;UAAvCA,wDAAA,SAAAO,GAAA,CAAAmlB,aAAA,CAAA0D,WAAA,WAAuC;UACvCppB,uDAAA,EAAuC;UAAvCA,wDAAA,SAAAO,GAAA,CAAAmlB,aAAA,CAAA0D,WAAA,WAAuC;UAMzBppB,uDAAA,GAA6B;UAA7BA,+DAAA,CAAAO,GAAA,CAAAmlB,aAAA,CAAAxgB,WAAA,CAA6B;UAElDlF,uDAAA,GAAuC;UAAvCA,wDAAA,SAAAO,GAAA,CAAAmlB,aAAA,CAAA0D,WAAA,WAAuC;UASvCppB,uDAAA,EAAuC;UAAvCA,wDAAA,SAAAO,GAAA,CAAAmlB,aAAA,CAAA0D,WAAA,WAAuC;UASkDppB,uDAAA,GAAuE;UAAvEA,mEAAA,QAAAO,GAAA,CAAAmlB,aAAA,CAAA2D,sBAAA,UAAA9oB,GAAA,CAAA6H,OAAA,GAAA7H,GAAA,CAAA8H,QAAA,EAAArI,2DAAA,CAAuE;UAgB/FA,uDAAA,IAA0B;UAA1BA,+DAAA,CAAAO,GAAA,CAAAmlB,aAAA,CAAA1e,QAAA,CAA0B;UAIzBhH,uDAAA,GAAkC;UAAlCA,+DAAA,CAAAO,GAAA,CAAAmlB,aAAA,CAAAjhB,gBAAA,CAAkC;UAIpCzE,uDAAA,GAAmD;UAAnDA,gEAAA,SAAAA,yDAAA,SAAAO,GAAA,CAAAmlB,aAAA,CAAA1gB,SAAA,oBAAmD;UAAKhF,uDAAA,GAAmD;UAAnDA,gEAAA,WAAAA,yDAAA,SAAAO,GAAA,CAAAmlB,aAAA,CAAAzgB,OAAA,oBAAmD;UAIlGjF,uDAAA,IAAyC;UAAzCA,+DAAA,CAAAO,GAAA,CAAAmlB,aAAA,CAAAze,uBAAA,CAAyC;UAInGjH,uDAAA,GAAwD;UAAxDA,wDAAA,aAAAO,GAAA,CAAAmlB,aAAA,CAAAhiB,kBAAA,cAAwD;UAC9F1D,uDAAA,GAAkB;UAAlBA,gEAAA,MAAAO,GAAA,CAAA8lB,OAAA,YAAkB;UAyBCrmB,uDAAA,IAA6B;UAA7BA,wDAAA,SAAAO,GAAA,CAAAolB,UAAA,IAAAjG,SAAA,CAA6B;UAClB1f,uDAAA,EAA6B;UAA7BA,wDAAA,SAAAO,GAAA,CAAAolB,UAAA,IAAAjG,SAAA,CAA6B;UAIvC1f,uDAAA,GAA2C;UAA3CA,+DAAA,CAAAO,GAAA,CAAAmlB,aAAA,CAAA9gB,yBAAA,CAA2C;UAOyC5E,uDAAA,GAAqB;UAArBA,wDAAA,YAAAO,GAAA,CAAA+lB,kBAAA,CAAqB;UAqBtEtmB,uDAAA,IAAkC;UAAlCA,+DAAA,CAAAO,GAAA,CAAAmlB,aAAA,CAAAve,gBAAA,CAAkC;UAsBhFnH,uDAAA,IAA4B;UAA5BA,wDAAA,SAAAO,GAAA,CAAA6lB,mBAAA,CAA4B;;;qBD7I3CrkB,qEAAe,EAAEyjB,2DAAU,EAAAtV,6DAAA,EAAAA,gEAAA,EAAE7N,0DAAY,EAAA4M,qDAAA,EAAAA,kDAAA,EAAAA,sDAAA,EAAEuJ,qGAAyB,EAAE9V,qEAAe;MAAA7B,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;AEpBxC;AAEgC;AAI3B;AAEA;AAED;;;;;;;;;;ICqBzCb,4DADA,SAAgC,SAC5B;IAAAA,oDAAA,GAAoB;IAAAA,0DAAA,EAAK;IAC7BA,4DAAA,SAAI;IAAAA,oDAAA,GAA6C;;IAAAA,0DAAA,EAAK;IACtDA,4DAAA,SAAI;IAAAA,oDAAA,GAAc;IAAAA,0DAAA,EAAK;IACvBA,4DAAA,SAAI;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAK;IAEvBA,4DADF,cAA+B,kBACwE;IAA5CA,wDAAA,mBAAAwpB,sFAAA;MAAA,MAAAvmB,OAAA,GAAAjD,2DAAA,CAAAgD,GAAA,EAAAG,SAAA;MAAA,MAAAC,MAAA,GAAApD,2DAAA;MAAA,OAAAA,yDAAA,CAASoD,MAAA,CAAAqmB,wBAAA,CAAAxmB,OAAA,CAAAM,EAAA,CAAiC;IAAA,EAAC;IAClGvD,uDAAA,aAA2B;IAC7BA,0DAAA,EAAS;IACTA,4DAAA,kBAA4G;IAA/CA,wDAAA,mBAAA0pB,sFAAA;MAAA,MAAAzmB,OAAA,GAAAjD,2DAAA,CAAAgD,GAAA,EAAAG,SAAA;MAAA,MAAAC,MAAA,GAAApD,2DAAA;MAAA,OAAAA,yDAAA,CAASoD,MAAA,CAAAumB,2BAAA,CAAA1mB,OAAA,CAAAM,EAAA,CAAoC;IAAA,EAAC;IACzGvD,uDAAA,aAA6B;IAGnCA,0DAFI,EAAS,EACN,EACF;;;;IAZCA,uDAAA,GAAoB;IAApBA,+DAAA,CAAAiD,OAAA,CAAA2mB,WAAA,CAAoB;IACpB5pB,uDAAA,GAA6C;IAA7CA,+DAAA,CAAAA,yDAAA,OAAAiD,OAAA,CAAA4mB,eAAA,gBAA6C;IAC7C7pB,uDAAA,GAAc;IAAdA,+DAAA,CAAAiD,OAAA,CAAA6mB,KAAA,CAAc;IACd9pB,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAiD,OAAA,CAAA8mB,OAAA,CAAgB;;;;;IAW4D/pB,4DADlF,SAAgC,aACkD,QAAG;IAAAA,oDAAA,qBAAc;IACnGA,0DADmG,EAAI,EAAM,EACxG;;;;;IAjBLA,qEAAA,GAA2C;IAe3CA,wDAdE,IAAAgqB,4DAAA,kBAAgC,IAAAC,4DAAA,iBAcF;;;;;IAdTjqB,uDAAA,EAAS;IAATA,wDAAA,YAAAkqB,SAAA,CAAS;IAc3BlqB,uDAAA,EAAyB;IAAzBA,wDAAA,SAAAkqB,SAAA,CAAApkB,MAAA,OAAyB;;;;;;IA6BlC9F,4DADA,aAA4D,SACxD;IAAAA,oDAAA,GAAoB;IAAAA,0DAAA,EAAK;IAC7BA,4DAAA,SAAI;IAAAA,oDAAA,GAAmC;;IAAAA,0DAAA,EAAK;IAC5CA,4DAAA,SAAI;IAAAA,oDAAA,GAAe;IAAAA,0DAAA,EAAK;IAEtBA,4DADF,aAA+B,iBACwE;IAA5CA,wDAAA,mBAAAmqB,qFAAA;MAAA,MAAA7Q,OAAA,GAAAtZ,2DAAA,CAAAoqB,GAAA,EAAAjnB,SAAA;MAAA,MAAAC,MAAA,GAAApD,2DAAA;MAAA,OAAAA,yDAAA,CAASoD,MAAA,CAAAinB,wBAAA,CAAA/Q,OAAA,CAAA/V,EAAA,CAAiC;IAAA,EAAC;IAClGvD,uDAAA,aAA2B;IAC7BA,0DAAA,EAAS;IACTA,4DAAA,kBAA4G;IAA/CA,wDAAA,mBAAAsqB,sFAAA;MAAA,MAAAhR,OAAA,GAAAtZ,2DAAA,CAAAoqB,GAAA,EAAAjnB,SAAA;MAAA,MAAAC,MAAA,GAAApD,2DAAA;MAAA,OAAAA,yDAAA,CAASoD,MAAA,CAAAumB,2BAAA,CAAArQ,OAAA,CAAA/V,EAAA,CAAoC;IAAA,EAAC;IACzGvD,uDAAA,aAA6B;IAGnCA,0DAFI,EAAS,EACN,EACF;;;;IAXCA,uDAAA,GAAoB;IAApBA,+DAAA,CAAAsZ,OAAA,CAAAsQ,WAAA,CAAoB;IACpB5pB,uDAAA,GAAmC;IAAnCA,+DAAA,CAAAA,yDAAA,OAAAsZ,OAAA,CAAAiR,IAAA,gBAAmC;IACnCvqB,uDAAA,GAAe;IAAfA,+DAAA,CAAAsZ,OAAA,CAAAkR,MAAA,CAAe;;;;;IAW6DxqB,4DADlF,SAAgC,aACkD,QAAG;IAAAA,oDAAA,qBAAc;IACnGA,0DADmG,EAAI,EAAM,EACxG;;;;;IAhBLA,qEAAA,GAA2C;IAc3CA,wDAbE,IAAAyqB,4DAAA,kBAA4D,IAAAC,4DAAA,iBAa9B;;;;;IAbT1qB,uDAAA,EAAS;IAATA,wDAAA,YAAA2qB,SAAA,CAAS;IAa3B3qB,uDAAA,EAAyB;IAAzBA,wDAAA,SAAA2qB,SAAA,CAAA7kB,MAAA,OAAyB;;;;;IA2B5B9F,4DAAA,iBAA8D;IAAAA,oDAAA,GAAa;IAAAA,0DAAA,EAAS;;;;IAA3CA,wDAAA,UAAA4qB,OAAA,CAAArf,KAAA,CAAoB;IAACvL,uDAAA,EAAa;IAAbA,+DAAA,CAAA4qB,OAAA,CAAAzR,IAAA,CAAa;;;;;IAE7EnZ,4DAAA,eAA8I;IAC5IA,oDAAA,4BACF;IAAAA,0DAAA,EAAO;;;;;IAKPA,4DAAA,eAA0J;IACxJA,oDAAA,yBACF;IAAAA,0DAAA,EAAO;;;;;IAMLA,4DAAA,cAAqI;IACnIA,oDAAA,0BACF;IAAAA,0DAAA,EAAM;;;;;IAKNA,4DAAA,eAA0I;IACxIA,oDAAA,4BACF;IAAAA,0DAAA,EAAO;;;;;IAMTA,4DAAA,eAA0I;IACxIA,oDAAA,4BACF;IAAAA,0DAAA,EAAO;;;;;IA4BLA,4DAAA,iBAA8D;IAAAA,oDAAA,GAAa;IAAAA,0DAAA,EAAS;;;;IAA3CA,wDAAA,UAAA6qB,OAAA,CAAAtf,KAAA,CAAoB;IAACvL,uDAAA,EAAa;IAAbA,+DAAA,CAAA6qB,OAAA,CAAA1R,IAAA,CAAa;;;;;IAE7EnZ,4DAAA,eAA8I;IAC5IA,oDAAA,4BACF;IAAAA,0DAAA,EAAO;;;;;IAQPA,4DAAA,eAAwI;IACtIA,oDAAA,2BACF;IAAAA,0DAAA,EAAO;;;;;IAKPA,4DAAA,eAAoI;IAClIA,oDAAA,yBACF;IAAAA,0DAAA,EAAO;;;;;IAKPA,4DAAA,eAA0I;IACxIA,oDAAA,4BACF;IAAAA,0DAAA,EAAO;;;ADtLb,MAAOwB,8BAA8B;EAezC7B,YACUiI,QAAsC,EACtC0T,aAA0B,EAC1BzT,MAAsB,EACtBwJ,GAAgB,EAChB6U,SAAmB;IAJnB,KAAAte,QAAQ,GAARA,QAAQ;IACR,KAAA0T,aAAa,GAAbA,aAAa;IACb,KAAAzT,MAAM,GAANA,MAAM;IACN,KAAAwJ,GAAG,GAAHA,GAAG;IACH,KAAA6U,SAAS,GAATA,SAAS;IAPX,KAAAzd,WAAW,GAAmB,EAAE;EAQrC;EAEHzG,QAAQA,CAAA;IACN,IAAI,CAAC8oB,wBAAwB,GAAG,IAAIpd,MAAM,CAACsP,SAAS,CAACC,KAAK,CACxD3O,QAAQ,CAAC4O,cAAc,CAAC,wBAAwB,CAAC,CAClD;IACD,IAAI,CAAC6N,uBAAuB,GAAG,IAAIrd,MAAM,CAACsP,SAAS,CAACC,KAAK,CACvD3O,QAAQ,CAAC4O,cAAc,CAAC,wBAAwB,CAAC,CAClD;IACD,IAAI,CAAC8N,WAAW,GAAG,IAAItd,MAAM,CAACsP,SAAS,CAACC,KAAK,CAC3C3O,QAAQ,CAAC4O,cAAc,CAAC,yBAAyB,CAAC,CACnD;IACD,IAAI,CAAC+N,6BAA6B,EAAE;IACpC,IAAI,CAACC,6BAA6B,EAAE;IACpC,MAAMxiB,oBAAoB,GAAG,IAAI,CAAC4S,aAAa,CAAC3S,cAAc,EAAE,CAACC,SAAS,CAAEC,IAAS,IAAI;MACvF,IAAI,CAACyT,WAAW,GAAG,IAAI,CAAChB,aAAa,CAACvS,aAAa,EAAE;MACrDF,IAAI,IAAI,IAAI,GACP,IAAI,CAACX,WAAW,GAAG,IAAI,CAACoU,WAAW,CAACtT,MAAM,GAC1C,IAAI,CAACd,WAAW,GAAGW,IAAI,CAACG,MAAO;IACtC,CAAC,CAAC;IACF,IAAI,CAACP,WAAW,CAACgB,IAAI,CAACf,oBAAoB,CAAC;IAC3C,IAAI,CAACyiB,wBAAwB,EAAE;IAC/B,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEA1hB,WAAWA,CAAA;IACT,IAAI,CAACjB,WAAW,CAACkB,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnB,WAAW,EAAE,CAAC;EACpD;EAEA4iB,0BAA0BA,CAAA;IACxB,IAAI,CAACP,wBAAwB,CAAC3e,IAAI,EAAE;IACpC,IAAI,CAACmf,gBAAgB,EAAE;EACzB;EAEAC,2BAA2BA,CAAA;IACzB,IAAI,CAACT,wBAAwB,CAACze,IAAI,EAAE;IACpCqB,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;EAC1B;EAEA4d,0BAA0BA,CAAA;IACxB,IAAI,CAACT,uBAAuB,CAAC5e,IAAI,EAAE;IACnC,IAAI,CAACmf,gBAAgB,EAAE;EACzB;EAEAG,2BAA2BA,CAAA;IACzB,IAAI,CAACV,uBAAuB,CAAC1e,IAAI,EAAE;IACnCqB,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;EAC1B;EAEA+b,2BAA2BA,CAACpmB,EAAO;IACjC,IAAI,CAACynB,WAAW,CAAC7e,IAAI,EAAE;IACvB,IAAI,CAACuf,OAAO,GAAGnoB,EAAE;EACnB;EAEAooB,4BAA4BA,CAAA;IAC1B,IAAI,CAACX,WAAW,CAAC3e,IAAI,EAAE;EACzB;EAEAif,gBAAgBA,CAAA;IACd,MAAMM,4BAA4B,GAAG,IAAI,CAAChkB,QAAQ,CAACikB,uBAAuB,CAAC,IAAI,CAAC3jB,WAAW,CAAC,CAACU,SAAS,CACnGC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAAChE,WAAW,GAAG6C,IAAI,CAACA,IAAI;MAC9B,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACmiB,4BAA4B,CAAC;EACrD;EAEA;EAEAX,6BAA6BA,CAAA;IAC3B,IAAI,CAACa,qBAAqB,GAAG,IAAI,CAACza,GAAG,CAACG,KAAK,CAAC;MAC1CjO,EAAE,EAAE,CAAC,CAAC,CAAC;MACP0J,SAAS,EAAE,CAAC,IAAI,EAAE+D,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MAC5DmY,eAAe,EAAE,CAAC,IAAI,EAAE7Y,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MAClEoY,KAAK,EAAE,CAAC,IAAI,EAAE9Y,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MACxDqY,OAAO,EAAE,CAAC,IAAI,EAAE/Y,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MAC1D1G,OAAO,EAAE,CAAC,IAAI,EAAEgG,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC;KAC1D,CAAC;EACJ;EAEAyZ,wBAAwBA,CAAA;IACtB,MAAMY,0BAA0B,GAAG,IAAI,CAACnkB,QAAQ,CAACujB,wBAAwB,CAAC,IAAI,CAACjjB,WAAW,CAAC,CAACU,SAAS,CAClGC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACgiB,SAAS,GAAGnjB,IAAI,CAACA,IAAI;MAC5B,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACsiB,0BAA0B,CAAC;EACnD;EAEAtC,wBAAwBA,CAAClmB,EAAO;IAC9B,MAAMwoB,0BAA0B,GAAG,IAAI,CAACnkB,QAAQ,CAAC6hB,wBAAwB,CAAClmB,EAAE,CAAC,CAACqF,SAAS,CACpFC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACyV,QAAQ,GAAG5W,IAAI,CAACA,IAAI;QACzB,MAAMojB,UAAU,GAAG,IAAI,CAAC/F,SAAS,CAACyB,SAAS,CACzC,IAAI,CAAClI,QAAQ,CAACoK,eAAe,EAC7B,YAAY,CACb;QACD,IAAI,CAACpK,QAAQ,CAACoK,eAAe,GAAGoC,UAAU;QAC1C,IAAI,CAACH,qBAAqB,CAACI,UAAU,CAAC,IAAI,CAACzM,QAAQ,CAAC;QACpD,IAAI,CAAC4L,0BAA0B,EAAE;MACnC,CAAC,MAAM;QACL,IAAI,CAACxjB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACsiB,0BAA0B,CAAC;EACnD;EAEAI,wBAAwBA,CAAA;IACtB,MAAM5gB,KAAK,GAAG,IAAI,CAACugB,qBAAqB,CAACvgB,KAAK;IAC9CA,KAAK,CAACvC,MAAM,GAAG,IAAI,CAACd,WAAW;IAC/B,IAAIqD,KAAK,CAAChI,EAAE,IAAI,CAAC,IAAIgI,KAAK,CAAChI,EAAE,IAAI,IAAI,EAAE;MACrC,IAAI,CAAC6oB,uBAAuB,CAAC7gB,KAAK,CAAC;IACrC,CAAC,MAAM;MACL,IAAI,CAAC8gB,uBAAuB,CAAC9gB,KAAK,CAAC;IACrC;EACF;EAEA6gB,uBAAuBA,CAAC7gB,KAAU;IAChC,IAAI,IAAI,CAACugB,qBAAqB,CAAC/Z,KAAK,EAAE;MACpC,MAAMga,0BAA0B,GAAG,IAAI,CAACnkB,QAAQ,CAAC0kB,oBAAoB,CAAC/gB,KAAK,CAAC,CAAC3C,SAAS,CAAEC,IAAS,IAAI;QACnG,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;YAClB1C,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAElC,IAAI,CAACA,IAAI;YAClBoC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;UACFuC,UAAU,CAAC,MAAK;YACd,IAAI,CAACqe,qBAAqB,CAACS,KAAK,EAAE;YAClC,IAAI,CAAChB,2BAA2B,EAAE;YAClC7d,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;UAC1B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAI,CAAC/F,MAAM,CAACgD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;YACrBC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;QACJ;MACF,CAAC,CAAC;MACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACsiB,0BAA0B,CAAC;IACnD,CAAC,MAAM;MACL3Q,qEAAY,CAACiF,qBAAqB,CAAC,IAAI,CAACyL,qBAAqB,CAAC;IAChE;EACF;EAEAO,uBAAuBA,CAAC9gB,KAAU;IAChC,IAAI,IAAI,CAACugB,qBAAqB,CAAC/Z,KAAK,EAAE;MACpC,MAAMga,0BAA0B,GAAG,IAAI,CAACnkB,QAAQ,CAACykB,uBAAuB,CAAC9gB,KAAK,CAAC,CAAC3C,SAAS,CAAEC,IAAS,IAAI;QACtG,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;YAClB1C,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAElC,IAAI,CAACA,IAAI;YAClBoC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;UACFuC,UAAU,CAAC,MAAK;YACd,IAAI,CAACqe,qBAAqB,CAACS,KAAK,EAAE;YAClC,IAAI,CAAChB,2BAA2B,EAAE;YAClC7d,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;UAC1B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAI,CAAC/F,MAAM,CAACgD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;YACrBC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;QACJ;MACF,CAAC,CAAC;MACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACsiB,0BAA0B,CAAC;IACnD,CAAC,MAAM;MACL3Q,qEAAY,CAACiF,qBAAqB,CAAC,IAAI,CAACyL,qBAAqB,CAAC;IAChE;EACF;EAEAU,uBAAuBA,CAAA;IACrB,MAAMT,0BAA0B,GAAG,IAAI,CAACnkB,QAAQ,CAAC4kB,uBAAuB,CAAC,IAAI,CAACd,OAAO,CAAC,CAAC9iB,SAAS,CAC7FC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;UAClB1C,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAElC,IAAI,CAACA,IAAI;UAClBoC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;QACFuC,UAAU,CAAC,MAAK;UACd,IAAI,CAACke,4BAA4B,EAAE;UACnCje,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,IAAI,CAAC/F,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACsiB,0BAA0B,CAAC;EACnD;EAEA;EACAb,6BAA6BA,CAAA;IAC3B,IAAI,CAACuB,qBAAqB,GAAG,IAAI,CAACpb,GAAG,CAACG,KAAK,CAAC;MAC1CjO,EAAE,EAAE,CAAC,CAAC,CAAC;MACP0J,SAAS,EAAE,CAAC,IAAI,EAAE+D,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MAC5D6Y,IAAI,EAAE,CAAC,IAAI,EAAEvZ,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MACvD8Y,MAAM,EAAE,CAAC,IAAI,EAAExZ,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC,CAAC;MACzD1G,OAAO,EAAE,CAAC,IAAI,EAAEgG,sDAAU,CAACS,OAAO,CAAC,CAACT,sDAAU,CAACU,QAAQ,CAAC,CAAC;KAC1D,CAAC;EACJ;EAEA0Z,wBAAwBA,CAAA;IACtB,MAAMsB,0BAA0B,GAAG,IAAI,CAAC9kB,QAAQ,CAACwjB,wBAAwB,CAAC,IAAI,CAACljB,WAAW,CAAC,CAACU,SAAS,CAClGC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAAC2iB,SAAS,GAAG9jB,IAAI,CAACA,IAAI;MAC5B,CAAC,MAAM;QACL,IAAI,CAAChB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACijB,0BAA0B,CAAC;EACnD;EAEArC,wBAAwBA,CAAC9mB,EAAO;IAC9B,MAAMmpB,0BAA0B,GAAG,IAAI,CAAC9kB,QAAQ,CAACyiB,wBAAwB,CAAC9mB,EAAE,CAAC,CAACqF,SAAS,CACpFC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACyV,QAAQ,GAAG5W,IAAI,CAACA,IAAI;QACzB,MAAMojB,UAAU,GAAG,IAAI,CAAC/F,SAAS,CAACyB,SAAS,CACzC,IAAI,CAAClI,QAAQ,CAAC8K,IAAI,EAClB,YAAY,CACb;QACD,IAAI,CAAC9K,QAAQ,CAAC8K,IAAI,GAAG0B,UAAU;QAC/B,IAAI,CAACQ,qBAAqB,CAACP,UAAU,CAAC,IAAI,CAACzM,QAAQ,CAAC;QACpD,IAAI,CAAC+L,0BAA0B,EAAE;MACnC,CAAC,MAAM;QACL,IAAI,CAAC3jB,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACijB,0BAA0B,CAAC;EACnD;EAEAE,wBAAwBA,CAAA;IACtB,MAAMrhB,KAAK,GAAG,IAAI,CAACkhB,qBAAqB,CAAClhB,KAAK;IAC9CA,KAAK,CAACvC,MAAM,GAAG,IAAI,CAACd,WAAW;IAC/B,IAAIqD,KAAK,CAAChI,EAAE,IAAI,CAAC,IAAIgI,KAAK,CAAChI,EAAE,IAAI,IAAI,EAAE;MACrC,IAAI,CAACspB,uBAAuB,CAACthB,KAAK,CAAC;IACrC,CAAC,MAAM;MACL,IAAI,CAACuhB,uBAAuB,CAACvhB,KAAK,CAAC;IACrC;EACF;EAEAshB,uBAAuBA,CAACthB,KAAU;IAChC,IAAI,IAAI,CAACkhB,qBAAqB,CAAC1a,KAAK,EAAE;MACpC,MAAM2a,0BAA0B,GAAG,IAAI,CAAC9kB,QAAQ,CAACmlB,oBAAoB,CAACxhB,KAAK,CAAC,CAAC3C,SAAS,CAAEC,IAAS,IAAI;QACnG,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;YAClB1C,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAElC,IAAI,CAACA,IAAI;YAClBoC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;UACFuC,UAAU,CAAC,MAAK;YACd,IAAI,CAACgf,qBAAqB,CAACF,KAAK,EAAE;YAClC,IAAI,CAACd,2BAA2B,EAAE;YAClC/d,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;UAC1B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAI,CAAC/F,MAAM,CAACgD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;YACrBC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;QACJ;MACF,CAAC,CAAC;MACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACijB,0BAA0B,CAAC;IACnD,CAAC,MAAM;MACLtR,qEAAY,CAACiF,qBAAqB,CAAC,IAAI,CAACoM,qBAAqB,CAAC;IAChE;EACF;EAEAK,uBAAuBA,CAACvhB,KAAU;IAChC,IAAI,IAAI,CAACkhB,qBAAqB,CAAC1a,KAAK,EAAE;MACpC,MAAM2a,0BAA0B,GAAG,IAAI,CAAC9kB,QAAQ,CAACklB,uBAAuB,CAACvhB,KAAK,CAAC,CAAC3C,SAAS,CAAEC,IAAS,IAAI;QACtG,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;UACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;YAClB1C,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAElC,IAAI,CAACA,IAAI;YAClBoC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;UACFuC,UAAU,CAAC,MAAK;YACd,IAAI,CAACgf,qBAAqB,CAACF,KAAK,EAAE;YAClC,IAAI,CAACd,2BAA2B,EAAE;YAClC/d,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;UAC1B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAI,CAAC/F,MAAM,CAACgD,KAAK,CAAC;YAChBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;YACrBC,QAAQ,EAAExI,mEAAU,CAACyI;WACtB,CAAC;QACJ;MACF,CAAC,CAAC;MACF,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACijB,0BAA0B,CAAC;IACnD,CAAC,MAAM;MACLtR,qEAAY,CAACiF,qBAAqB,CAAC,IAAI,CAACoM,qBAAqB,CAAC;IAChE;EACF;EAEAO,uBAAuBA,CAAA;IACrB,MAAMN,0BAA0B,GAAG,IAAI,CAAC9kB,QAAQ,CAAColB,uBAAuB,CAAC,IAAI,CAACtB,OAAO,CAAC,CAAC9iB,SAAS,CAC7FC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAACmB,MAAM,IAAI,CAAC,EAAE;QACpB,IAAI,CAACnC,MAAM,CAAC2F,OAAO,CAAC;UAClB1C,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAElC,IAAI,CAACA,IAAI;UAClBoC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;QACFuC,UAAU,CAAC,MAAK;UACd,IAAI,CAACke,4BAA4B,EAAE;UACnCje,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,IAAI,CAAC/F,MAAM,CAACgD,KAAK,CAAC;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAElC,IAAI,CAACmC,OAAO;UACrBC,QAAQ,EAAExI,mEAAU,CAACyI;SACtB,CAAC;MACJ;IACF,CAAC,EACA2C,GAAG,IACF,IAAI,CAAChG,MAAM,CAACgD,KAAK,CAAC;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE8C,GAAG,CAAC7C,OAAO;MACpBC,QAAQ,EAAExI,mEAAU,CAACyI;KACtB,CAAC,CACL;IACD,IAAI,CAACzC,WAAW,CAACgB,IAAI,CAACijB,0BAA0B,CAAC;EACnD;;;uCAxaWlrB,8BAA8B,EAAAxB,+DAAA,CAAAW,kGAAA,GAAAX,+DAAA,CAAA6O,+DAAA,GAAA7O,+DAAA,CAAA+O,4DAAA,GAAA/O,+DAAA,CAAAiP,uDAAA,GAAAjP,+DAAA,CAAAmP,qDAAA;IAAA;EAAA;;;YAA9B3N,8BAA8B;MAAA3B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,iEAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA8B,MAAA;MAAA7B,QAAA,WAAA8sB,wCAAA5sB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpB3CN,4DAAA,UAAK;UAEHA,uDADA,iBAAyB,YACc;UACzCA,0DAAA,EAAM;UAEJA,4DADF,aAAwD,aACjC;UAAAA,oDAAA,6BAAsB;UAAAA,0DAAA,EAAM;UAIvCA,4DAHV,aAAiB,aACO,aACS,WACD;UACpBA,oDAAA,4BACA;UACEA,4DADF,eAA6C,iBAC4B;UAAvCA,wDAAA,mBAAAmtB,iEAAA;YAAA,OAAS5sB,GAAA,CAAA8qB,0BAAA,EAA4B;UAAA,EAAC;UACpErrB,uDAAA,YAA0B;UAAAA,oDAAA,YAC5B;UAEJA,0DAFI,EAAS,EACJ,EACL;UAKIA,4DAJR,eAAwC,aAE7B,UACD,cACwB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAK;UACtCA,4DAAA,cAA0B;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAK;UACnCA,4DAAA,cAAyB;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAK;UACnCA,4DAAA,cAAyB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAK;UACrCA,uDAAA,cAAiD;UAErDA,0DADE,EAAK,EACC;UACRA,4DAAA,aAAO;UACLA,wDAAA,KAAAotB,uDAAA,2BAA2C;UAsBzDptB,0DAHY,EAAQ,EACN,EACN,EACF;UAGFA,4DAFJ,cAAsB,eACS,YACL;UAAAA,oDAAA,2BACpB;UACEA,4DADF,eAA6C,iBAC4B;UAAvCA,wDAAA,mBAAAqtB,iEAAA;YAAA,OAAS9sB,GAAA,CAAAirB,0BAAA,EAA4B;UAAA,EAAC;UACpExrB,uDAAA,YAA0B;UAAAA,oDAAA,YAC5B;UAEJA,0DAFI,EAAS,EACJ,EACL;UAIEA,4DAHN,eAAwC,aAC/B,UACD,cACwB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAK;UACtCA,4DAAA,cAA0B;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAK;UACnCA,4DAAA,cAA0B;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAK;UACrCA,uDAAA,cAAiD;UAErDA,0DADE,EAAK,EACC;UACRA,4DAAA,aAAO;UACLA,wDAAA,KAAAstB,uDAAA,2BAA2C;UAuBvDttB,0DALU,EAAQ,EACN,EACA,EACF,EACF,EACF;UAOEA,4DAJR,eAA+J,eAC1G,eACtB,eACC,cACuB;UAAAA,oDAAA,mDAA2C;UAAAA,0DAAA,EAAK;UAC/FA,4DAAA,kBAAwH;UAAxCA,wDAAA,mBAAAutB,iEAAA;YAAA,OAAShtB,GAAA,CAAAgrB,2BAAA,EAA6B;UAAA,EAAC;UAEzHvrB,0DADE,EAAS,EACL;UAGFA,4DAFJ,eAAwB,gBACoB,WACnC;UACHA,uDAAA,iBAA0C;UAE1CA,4DADF,eAAwB,iBACQ;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAQ;UAC7CA,4DAAA,kBAAsL;UACpLA,wDAAA,KAAAwtB,iDAAA,qBAA8D;UAChExtB,0DAAA,EAAS;UACTA,wDAAA,KAAAytB,+CAAA,mBAA8I;UAGhJztB,0DAAA,EAAM;UAEJA,4DADF,eAA6B,iBACG;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAQ;UAC1CA,uDAAA,iBAA4O;UAC5OA,wDAAA,KAAA0tB,+CAAA,mBAA0J;UAG5J1tB,0DAAA,EAAM;UAGFA,4DAFJ,eAAsB,eACa,iBACD;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAQ;UAC3CA,uDAAA,iBAAqN;UACrNA,wDAAA,KAAA2tB,8CAAA,kBAAqI;UAGvI3tB,0DAAA,EAAM;UAEJA,4DADF,eAAiC,iBACD;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAQ;UAC7CA,uDAAA,iBAA4N;UAC5NA,wDAAA,KAAA4tB,+CAAA,mBAA0I;UAI9I5tB,0DADA,EAAM,EACA;UAEJA,4DADF,eAA6B,iBACG;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAQ;UAC7CA,uDAAA,oBAAsO;UACtOA,wDAAA,KAAA6tB,+CAAA,mBAA0I;UAMhJ7tB,0DAHI,EAAM,EACA,EACD,EACH;UAEJA,4DADF,eAA0B,kBAC6E;UAAxCA,wDAAA,mBAAA8tB,iEAAA;YAAA,OAASvtB,GAAA,CAAAgrB,2BAAA,EAA6B;UAAA,EAAC;UAACvrB,4DAAA,gBAAqB;UAACA,oDAAA,eAAM;UAAQA,0DAAR,EAAO,EAAU;UAClJA,4DAAA,kBAA6E;UAArCA,wDAAA,mBAAA+tB,iEAAA;YAAA,OAASxtB,GAAA,CAAA4rB,wBAAA,EAA0B;UAAA,EAAC;UAACnsB,4DAAA,gBAAyB;UAAAA,oDAAA,cAAM;UAIpHA,0DAJoH,EAAO,EAAS,EACxH,EACF,EACF,EACF;UAMEA,4DAJR,eAA+J,eAC1G,eACtB,eACC,cACuB;UAAAA,oDAAA,kDAA0C;UAAAA,0DAAA,EAAK;UAC9FA,4DAAA,mBAAwH;UAAxCA,wDAAA,mBAAAguB,kEAAA;YAAA,OAASztB,GAAA,CAAAkrB,2BAAA,EAA6B;UAAA,EAAC;UAEzHzrB,0DADE,EAAS,EACL;UAGJA,4DAFF,gBAAwB,iBACoB,eACzB;UACfA,uDAAA,kBAA0C;UAExCA,4DADF,gBAAwB,kBACQ;UAAAA,oDAAA,gBAAO;UAAAA,0DAAA,EAAQ;UAC7CA,4DAAA,mBAAoL;UAClLA,wDAAA,MAAAiuB,kDAAA,qBAA8D;UAChEjuB,0DAAA,EAAS;UACTA,wDAAA,MAAAkuB,gDAAA,mBAA8I;UAGhJluB,0DAAA,EAAM;UAEJA,4DADF,gBAA6B,kBACG;UAAAA,oDAAA,gBAAO;UAAAA,0DAAA,EAAQ;UAE3CA,4DADF,mBAA2K,mBACvJ;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAS;UAC9BA,4DAAA,mBAAkB;UAAAA,oDAAA,WAAE;UACtBA,0DADsB,EAAS,EACtB;UACTA,wDAAA,MAAAmuB,gDAAA,mBAAwI;UAG1InuB,0DAAA,EAAM;UAEJA,4DADF,gBAA6B,kBACG;UAAAA,oDAAA,aAAI;UAAAA,0DAAA,EAAQ;UAC1CA,uDAAA,kBAA2M;UAC3MA,wDAAA,MAAAouB,gDAAA,mBAAoI;UAGtIpuB,0DAAA,EAAM;UAEJA,4DADF,gBAA6B,kBACG;UAAAA,oDAAA,gBAAO;UAAAA,0DAAA,EAAQ;UAC7CA,uDAAA,qBAAsO;UACtOA,wDAAA,MAAAquB,gDAAA,mBAA0I;UAMhJruB,0DAHI,EAAM,EACF,EACC,EACH;UAEJA,4DADF,gBAA0B,mBAC6E;UAAxCA,wDAAA,mBAAAsuB,kEAAA;YAAA,OAAS/tB,GAAA,CAAAkrB,2BAAA,EAA6B;UAAA,EAAC;UAACzrB,4DAAA,iBAAqB;UAACA,oDAAA,gBAAM;UAAQA,0DAAR,EAAO,EAAU;UAClJA,4DAAA,mBAA6E;UAArCA,wDAAA,mBAAAuuB,kEAAA;YAAA,OAAShuB,GAAA,CAAAqsB,wBAAA,EAA0B;UAAA,EAAC;UAAC5sB,4DAAA,iBAAyB;UAAAA,oDAAA,eAAM;UAIpHA,0DAJoH,EAAO,EAAS,EACxH,EACF,EACF,EACF;UAKEA,4DAJR,gBAAgK,gBACpH,gBACb,gBACC,eACuB;UAAAA,oDAAA,uBAAc;UAAAA,0DAAA,EAAK;UAClEA,4DAAA,mBAAyH;UAAzCA,wDAAA,mBAAAwuB,kEAAA;YAAA,OAASjuB,GAAA,CAAAorB,4BAAA,EAA8B;UAAA,EAAC;UAE1H3rB,0DADE,EAAS,EACL;UACNA,4DAAA,gBAAwB;UACtBA,uDAAA,kBAA8B;UAC7BA,4DAAA,WAAI;UAAAA,oDAAA,mDAA0C;UACjDA,0DADiD,EAAK,EAChD;UAEJA,4DADF,gBAA0B,mBAC8E;UAAzCA,wDAAA,mBAAAyuB,kEAAA;YAAA,OAASluB,GAAA,CAAAorB,4BAAA,EAA8B;UAAA,EAAC;UAAC3rB,4DAAA,iBAAqB;UAACA,oDAAA,gBAAM;UAAQA,0DAAR,EAAO,EAAU;UACnJA,4DAAA,mBAA6E;UAArCA,wDAAA,mBAAA0uB,kEAAA;YAAA,OAAWnuB,GAAA,CAAAisB,uBAAA,EAAyB;UAAA;UAACxsB,4DAAA,iBAAqB;UAAAA,oDAAA,eAAM;UAIhHA,0DAJgH,EAAO,EAAS,EACpH,EACF,EACF,EACF;;;UA3M2BA,uDAAA,IAAiB;UAAjBA,wDAAA,SAAAO,GAAA,CAAAyrB,SAAA,CAAiB;UA0CvBhsB,uDAAA,IAAiB;UAAjBA,wDAAA,SAAAO,GAAA,CAAAosB,SAAA,CAAiB;UAmC9B3sB,uDAAA,GAAmC;UAAnCA,wDAAA,cAAAO,GAAA,CAAAurB,qBAAA,CAAmC;UAKqB9rB,uDAAA,GAA2H;UAA3HA,yDAAA,UAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,cAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,0BAA2H;UAC1JpR,uDAAA,EAAc;UAAdA,wDAAA,YAAAO,GAAA,CAAAyF,WAAA,CAAc;UAEdhG,uDAAA,EAAiH;UAAjHA,wDAAA,SAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,cAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,0BAAiH;UAMxCpR,uDAAA,GAAuI;UAAvIA,yDAAA,UAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,oBAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,gCAAuI;UAChNpR,uDAAA,EAA6H;UAA7HA,wDAAA,SAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,oBAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,gCAA6H;UAOtDpR,uDAAA,GAAmH;UAAnHA,yDAAA,UAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,UAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,sBAAmH;UACzLpR,uDAAA,EAAyG;UAAzGA,wDAAA,SAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,UAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,sBAAyG;UAM/BpR,uDAAA,GAAuH;UAAvHA,yDAAA,UAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,YAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,wBAAuH;UAChMpR,uDAAA,EAA6G;UAA7GA,wDAAA,SAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,YAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,wBAA6G;UAOvCpR,uDAAA,GAAuH;UAAvHA,yDAAA,UAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,YAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,wBAAuH;UAC/LpR,uDAAA,EAA6G;UAA7GA,wDAAA,SAAAO,GAAA,CAAAurB,qBAAA,CAAAnH,QAAA,YAAAC,KAAA,IAAArkB,GAAA,CAAAurB,qBAAA,CAAA1a,QAAA,wBAA6G;UAwBtIpR,uDAAA,IAAmC;UAAnCA,wDAAA,cAAAO,GAAA,CAAAksB,qBAAA,CAAmC;UAKmBzsB,uDAAA,GAA2H;UAA3HA,yDAAA,UAAAO,GAAA,CAAAksB,qBAAA,CAAA9H,QAAA,cAAAC,KAAA,IAAArkB,GAAA,CAAAksB,qBAAA,CAAArb,QAAA,0BAA2H;UACxJpR,uDAAA,EAAc;UAAdA,wDAAA,YAAAO,GAAA,CAAAyF,WAAA,CAAc;UAEdhG,uDAAA,EAAiH;UAAjHA,wDAAA,SAAAO,GAAA,CAAAksB,qBAAA,CAAA9H,QAAA,cAAAC,KAAA,IAAArkB,GAAA,CAAAksB,qBAAA,CAAArb,QAAA,0BAAiH;UAMvFpR,uDAAA,GAAqH;UAArHA,yDAAA,UAAAO,GAAA,CAAAksB,qBAAA,CAAA9H,QAAA,WAAAC,KAAA,IAAArkB,GAAA,CAAAksB,qBAAA,CAAArb,QAAA,uBAAqH;UAI/IpR,uDAAA,GAA2G;UAA3GA,wDAAA,SAAAO,GAAA,CAAAksB,qBAAA,CAAA9H,QAAA,WAAAC,KAAA,IAAArkB,GAAA,CAAAksB,qBAAA,CAAArb,QAAA,uBAA2G;UAM7CpR,uDAAA,GAAiH;UAAjHA,yDAAA,UAAAO,GAAA,CAAAksB,qBAAA,CAAA9H,QAAA,SAAAC,KAAA,IAAArkB,GAAA,CAAAksB,qBAAA,CAAArb,QAAA,qBAAiH;UAC/KpR,uDAAA,EAAuG;UAAvGA,wDAAA,SAAAO,GAAA,CAAAksB,qBAAA,CAAA9H,QAAA,SAAAC,KAAA,IAAArkB,GAAA,CAAAksB,qBAAA,CAAArb,QAAA,qBAAuG;UAM/BpR,uDAAA,GAAuH;UAAvHA,yDAAA,UAAAO,GAAA,CAAAksB,qBAAA,CAAA9H,QAAA,YAAAC,KAAA,IAAArkB,GAAA,CAAAksB,qBAAA,CAAArb,QAAA,wBAAuH;UAC/LpR,uDAAA,EAA6G;UAA7GA,wDAAA,SAAAO,GAAA,CAAAksB,qBAAA,CAAA9H,QAAA,YAAAC,KAAA,IAAArkB,GAAA,CAAAksB,qBAAA,CAAArb,QAAA,wBAA6G;;;qBDtLxIL,+DAAmB,EAAA9B,4DAAA,EAAAA,0DAAA,EAAAA,sEAAA,EAAAA,gEAAA,EAAAA,sEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,8DAAA,EAAAA,2DAAA,EAAEvM,qEAAe,EAAEL,yDAAY,EAAA8M,oDAAA,EAAAA,iDAAA,EAAAA,qDAAA;MAAAtO,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;AElBC;AAExD,MAAMguB,YAAY,GAAGD,kEAAW,CAACE,UAAU;AAC3C,MAAMC,cAAc,GAAGH,kEAAW,CAACE,UAAU;AAE7C,MAAMrsB,UAAU,GAAG;EACxBqsB,UAAU,EAAE,GAAGD,YAAY,MAAM;EACjCG,YAAY,EAAED,cAAc;EAC5BE,QAAQ,EAAE,cAAc;EACxBC,eAAe,EAAE,EAAE;EACnBhkB,aAAa,EAAE;CAChB;;;;;;;;;;;;;;;ACXM,MAAMikB,aAAa,GAAG;EAC3BC,IAAI,EAAE;IACJC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,iBAAiB;IAC3BC,eAAe,EAAE,uBAAuB;IACxCC,cAAc,EAAE,sBAAsB;IACtCC,eAAe,EAAE,uBAAuB;IACxCC,cAAc,EAAE,oBAAoB;IACpCC,WAAW,EAAE,mBAAmB;IAChCC,gBAAgB,EAAE,iCAAiC;IACnDC,mBAAmB,EAAE,+BAA+B;IACpDC,oBAAoB,EAAE;GACvB;EACDC,OAAO,EAAE;IACPC,IAAI,EAAE,sBAAsB;IAC5BC,MAAM,EAAE,4BAA4B;IACpCC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,wBAAwB;IAChCC,MAAM,EAAE,wBAAwB;IAChCC,UAAU,EAAE,8BAA8B;IAC1CC,UAAU,EAAE,8BAA8B;IAC1CC,gBAAgB,EAAE,iCAAiC;IACnDC,mBAAmB,EAAE,oCAAoC;IACzDC,kBAAkB,EAAE;GACrB;EACDC,cAAc,EAAE;IACdV,IAAI,EAAE,sCAAsC;IAC5CW,WAAW,EAAE,kCAAkC;IAC/CV,MAAM,EAAE,yCAAyC;IACjDW,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,kCAAkC;IAC/CC,YAAY,EAAE,8CAA8C;IAC5DC,YAAY,EAAE,oCAAoC;IAClDC,eAAe,EAAE,uCAAuC;IACxDC,MAAM,EAAE,8BAA8B;IACtCC,iBAAiB,EAAE,oCAAoC;IACvDC,aAAa,EAAE,4BAA4B;IAC3CC,WAAW,EAAE,sCAAsC;IACnDC,aAAa,EAAE;GAChB;EACDC,MAAM,EAAE;IACNC,YAAY,EAAE,qBAAqB;IACnCC,SAAS,EAAE,kBAAkB;IAC7BC,YAAY,EAAE,qBAAqB;IACnCC,UAAU,EAAE,mBAAmB;IAC/BC,cAAc,EAAE,sBAAsB;IACtCC,cAAc,EAAE,sBAAsB;IACtCC,kBAAkB,EAAE,0BAA0B;IAC9CC,oBAAoB,EAAE,4BAA4B;IAClDC,iBAAiB,EAAE,yBAAyB;IAC5CC,kBAAkB,EAAE,0BAA0B;IAC9CC,kBAAkB,EAAE;GACrB;EACDC,SAAS,EAAE;IACTC,cAAc,EAAE,iDAAiD;IACjEC,eAAe,EAAE,qDAAqD;IACtEC,SAAS,EAAE,6CAA6C;IACxDC,YAAY,EAAE,gDAAgD;IAC9DC,YAAY,EAAE,gDAAgD;IAC9DC,cAAc,EAAE,iDAAiD;IACjEC,eAAe,EAAE,qDAAqD;IACtEC,SAAS,EAAE,6CAA6C;IACxDC,YAAY,EAAE,gDAAgD;IAC9DC,YAAY,EAAE,gDAAgD;IAC9DC,yBAAyB,EAAE;GAC5B;EACDC,SAAS,EAAC;IACRC,SAAS,EAAE,2BAA2B;IACtCC,WAAW,EAAE;GACd;EACDC,aAAa,EAAE;IACbjD,IAAI,EAAE,mCAAmC;IACzCkD,SAAS,EAAE,mCAAmC;IAC9ChD,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,kCAAkC;IAC1CC,MAAM,EAAE;GACT;EACD+C,aAAa,EAAE;IACbnD,IAAI,EAAE,mCAAmC;IACzCkD,SAAS,EAAE,mCAAmC;IAC9ChD,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,kCAAkC;IAC1CC,MAAM,EAAE;;CAEX;;;;;;;;;;;;;;;;;;;;AChFyD;;;;;AAKpD,MAAOtvB,SAAS;EACpBnB,YACUyzB,OAAoB,EACrBC,MAAc,EACdC,MAAsB;IAFrB,KAAAF,OAAO,GAAPA,OAAO;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;EACZ;EACH1xB,WAAWA,CAAA;IACT,IAAI,IAAI,CAACwxB,OAAO,CAACG,UAAU,EAAE,EAAE;MAC7B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,IAAI,CAACD,MAAM,CAACzoB,KAAK,CAAC;QAAEC,MAAM,EAAE,OAAO;QAAEC,OAAO,EAAE,wBAAwB;QAAEE,QAAQ,EAAExI,mEAAU,CAACyI;MAAa,CAAE,CAAC;MAC7G,IAAI,CAACmoB,MAAM,CAAC5mB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MAC/B,OAAO,KAAK;IACd;EACF;;;uCAdW3L,SAAS,EAAAd,sDAAA,CAAAW,+DAAA,GAAAX,sDAAA,CAAA6O,mDAAA,GAAA7O,sDAAA,CAAA+O,4DAAA;IAAA;EAAA;;;aAATjO,SAAS;MAAA2yB,OAAA,EAAT3yB,SAAS,CAAA4yB,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;;;;;;;;;;;;;;;;ACPmC;AAEzC,MAAOvY,YAAY;EAC/B,OAAOiF,qBAAqBA,CAACyT,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACnP,QAAQ,CAAC,CAAChb,OAAO,CAAEsqB,KAAK,IAAI;MAChD,MAAMC,OAAO,GAAGJ,SAAS,CAACliB,GAAG,CAACqiB,KAAK,CAAC;MACpC,IAAIC,OAAO,YAAYN,uDAAW,EAAE;QAClCM,OAAO,CAACC,WAAW,CAAC;UAClBC,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,MAAM,IAAIF,OAAO,YAAYL,qDAAS,EAAE;QACvC,IAAI,CAACxT,qBAAqB,CAAC6T,OAAO,CAAC;MACrC;IACF,CAAC,CAAC;EACJ;;;;;;;;;;;;;;;;;;;;;;;;ACP2B;AACiC;AAIJ;;;;;AAGpD,MAAOM,gBAAgB;EAC3B70B,YACSyzB,OAAoB,EACnBqB,KAAa,EACbnB,MAAsB;IAFvB,KAAAF,OAAO,GAAPA,OAAO;IACN,KAAAqB,KAAK,GAALA,KAAK;IACL,KAAAnB,MAAM,GAANA,MAAM;EACb;EAEHoB,SAASA,CAACC,OAA6B,EAAEva,IAAiB;IACxD,MAAMwa,OAAO,GAAG,IAAI,CAACxB,OAAO,CAACyB,QAAQ,EAAE;IAEvC,IAAID,OAAO,EAAE;MACXD,OAAO,GAAGA,OAAO,CAACG,KAAK,CAAC;QACtBC,UAAU,EAAE;UAAEC,aAAa,EAAE,UAAUJ,OAAO;QAAE;OACjD,CAAC;IACJ;IACA,OAAOxa,IAAI,CAAC6a,MAAM,CAACN,OAAO,CAAC,CAAC5U,IAAI,CAC9BuU,gDAAU,CAAEzmB,GAAQ,IAAI;MACtB,IAAIA,GAAG,YAAYwmB,mEAAiB,EAAE;QACpC,IAAIxmB,GAAG,CAACR,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAACimB,MAAM,CAACzoB,KAAK,CAAC;YAAEC,MAAM,EAAE,OAAO;YAAEC,OAAO,EAAE,sCAAsC;YAAEE,QAAQ,EAAExI,mEAAU,CAACyI;UAAa,CAAE,CAAC;UAC3H,IAAI,CAACkoB,OAAO,CAAC3b,SAAS,EAAE;UACxB,IAAI,CAACgd,KAAK,CAAChoB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QACjC;MACF;MACA,OAAO8nB,gDAAU,CAAC,MAAM,IAAIW,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACH;;;uCA3BWV,gBAAgB,EAAAx0B,sDAAA,CAAAW,+DAAA,GAAAX,sDAAA,CAAA6O,mDAAA,GAAA7O,sDAAA,CAAA+O,4DAAA;IAAA;EAAA;;;aAAhBylB,gBAAgB;MAAAf,OAAA,EAAhBe,gBAAgB,CAAAd;IAAA;EAAA;;;;;;;;;;;;;;;;;;ACTvB,MAAO9wB,UAAU;EACrB+kB,SAASA,CAACpc,KAAY,EAAE4pB,IAAU;IAChC,IAAI,CAAC5pB,KAAK,EAAE,OAAO,IAAI;IACvB,IAAI,CAAC4pB,IAAI,EAAE,OAAO5pB,KAAK;IAEvB4pB,IAAI,GAAGA,IAAI,CAACxpB,WAAW,EAAE;IAEzB,OAAOJ,KAAK,CAAC6pB,MAAM,CAAElnB,IAAS,IAAI;MAChC,OAAOmnB,IAAI,CAACC,SAAS,CAACpnB,IAAI,CAAC,CAACvC,WAAW,EAAE,CAAC4pB,QAAQ,CAACJ,IAAI,CAAC;IAC1D,CAAC,CAAC;EACJ;;;uCAVWvyB,UAAU;IAAA;EAAA;;;;YAAVA,UAAU;MAAA4yB,IAAA;MAAA11B,UAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;ACJiC;AACF;AACK;AAEA;;;AAKrD,MAAOsP,WAAW;EAUtBzP,YAAmBg2B,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IATvB,KAAAC,MAAM,GAAGnzB,mEAAU,CAACqsB,UAAU;IAC9B,KAAA1kB,QAAQ,GAAG3H,mEAAU,CAACusB,YAAY;IAElC,KAAA6G,WAAW,GAAyB,IAAIJ,iDAAe,CAAC,IAAI,CAAC;IAC7D,KAAAK,eAAe,GAAyB,IAAIL,iDAAe,CAAC,IAAI,CAAC;IAGjE,KAAAM,gBAAgB,GAAG,IAAIL,gEAAgB,EAAE;IAGvC,IAAI,CAACM,WAAW,GAAG,IAAI,CAACzpB,YAAY,EAAE;EACxC;EAEA4I,YAAYA,CAAC8gB,IAAU;IACrB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CACnB,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACE,QAAQ,EAAE,EAC9C2G,IAAI,EACJ;MAAEE,YAAY,EAAE;IAAM,CAAE,CACzB;EACH;EAEAC,WAAWA,CAAC7yB,EAAU;IACpB,OAAO,IAAI,CAACoyB,IAAI,CAAC/jB,GAAG,CAClB,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACM,cAAc,IAAInsB,EAAE,EAAE,CAC3D;EACH;EAEA8yB,UAAUA,CAACxtB,IAAc;IACvB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CACnB,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACO,WAAW,EAAE,EACjD9mB,IAAI,CACL;EACH;EAEAkK,SAASA,CAACujB,SAAwB;IAChC,OAAO,IAAI,CAACX,IAAI,CAACO,IAAI,CACnB,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACC,KAAK,EAAE,EAC3C;MACEkH,YAAY,EAAED,SAAS,CAAC,CAAC,CAAC;MAC1BE,QAAQ,EAAEF,SAAS,CAAC,CAAC;KACtB,EACD;MAAEH,YAAY,EAAE;IAAM,CAAE,CACzB;EACH;EAEAjkB,wBAAwBA,CAACrJ,IAAS;IAChC,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CACnB,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACG,eAAe,EAAE,EACrD1mB,IAAI,CACL;EACH;EAEA6N,aAAaA,CAAC7N,IAAS;IACrB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CACnB,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACI,cAAc,EAAE,EACpD3mB,IAAI,EACJ;MAAEstB,YAAY,EAAE;IAAM,CAAE,CACzB;EACH;EAEArV,cAAcA,CAACjY,IAAS;IACtB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CACnB,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACK,eAAe,EAAE,EACrD5mB,IAAI,CACL;EACH;EAEAgsB,QAAQA,CAAA;IACN,OAAO4B,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAC7C;EAEAtjB,QAAQA,CAACujB,KAAa;IACpBF,YAAY,CAACG,OAAO,CAAC,cAAc,EAAED,KAAK,CAAC;EAC7C;EAEApD,UAAUA,CAAA;IACR,OAAOkD,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,GAAG,KAAK;EAC5D;EAEAjf,SAASA,CAAA;IACPgf,YAAY,CAACI,UAAU,CAAC,cAAc,CAAC;EACzC;EACOluB,cAAcA,CAAA;IACnB,OAAO,IAAI,CAACktB,WAAW,CAACiB,YAAY,EAAE;EACxC;EAEOxjB,cAAcA,CAACsE,UAAe;IACnC,OAAO,IAAI,CAACie,WAAW,CAACzb,IAAI,CAACxC,UAAU,CAAC;EAC1C;EAEArL,YAAYA,CAAA;IACV,MAAMoqB,KAAK,GAAG,IAAI,CAAC9B,QAAQ,EAAE;IAC7B,OAAO,IAAI,CAACkB,gBAAgB,CAACgB,WAAW,CAACJ,KAAK,CAAC;EACjD;EAEAK,eAAeA,CAAA;IACb,IAAI,IAAI,CAAChB,WAAW,EAAE,OAAO,IAAI,CAACA,WAAW,CAAC9sB,QAAQ;EACxD;EAEOH,aAAaA,CAAA;IAClB,IAAI,IAAI,CAACitB,WAAW,EAAE,OAAO,IAAI,CAACA,WAAW;EAC/C;;;uCAtGW5mB,WAAW,EAAApP,sDAAA,CAAAW,4DAAA;IAAA;EAAA;;;aAAXyO,WAAW;MAAAqkB,OAAA,EAAXrkB,WAAW,CAAAskB,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;ACL2C;AACL;;;AAMpD,MAAO/kB,oBAAoB;EAC/BjP,YAAoBg2B,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IACxB,KAAAC,MAAM,GAAG,GAAGhH,kEAAW,CAACE,UAAU,MAAM;IACxC,KAAA1kB,QAAQ,GAAGwkB,kEAAW,CAACE,UAAU;EAFM;EAIvC;EACA9oB,WAAWA,CAACgD,MAAW;IACrB,OAAO,IAAI,CAAC2sB,IAAI,CAAC/jB,GAAG,CAAY,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACV,IAAI,IAAIhnB,MAAM,EAAE,CAAC;EACjG;EAEAgD,iBAAiBA,CAACnD,IAAS;IACzB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACC,WAAW,EAAE,EAAE9nB,IAAI,CAAC;EAC1F;EAEA8d,wBAAwBA,CAAC9d,IAAS;IAChC,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACT,MAAM,GAAG,EAAEpnB,IAAI,CAAC;EACtF;EAEAkE,YAAYA,CAAClE,IAAS;IACpB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACE,KAAK,EAAE,EAAE/nB,IAAI,CAAC;EACpF;EAEA;EACA4e,iBAAiBA,CAAC5e,IAAS;IACzB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACG,WAAW,EAAE,EAAEhoB,IAAI,CAAC;EAC1F;EAEA6e,6BAA6BA,CAACza,SAAc;IAC1C,OAAO,IAAI,CAAC0oB,IAAI,CAAC/jB,GAAG,CAAQ,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACI,YAAY,IAAI7jB,SAAS,EAAE,CAAC;EACxG;EAEA;EACA6a,mBAAmBA,CAACjf,IAAS;IAC3B,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACK,YAAY,EAAE,EAAEloB,IAAI,CAAC;EAC3F;EAEAmf,sBAAsBA,CAACnf,IAAS;IAC9B,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACM,eAAe,EAAE,EAAEnoB,IAAI,CAAC;EAC9F;EAEA;EACAquB,aAAaA,CAACruB,IAAS;IACrB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACO,MAAM,EAAE,EAAEpoB,IAAI,CAAC;EACrF;EAEA;EACAud,mBAAmBA,CAACvd,IAAS;IAC3B,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACQ,iBAAiB,EAAE,EAAEroB,IAAI,CAAC;EAChG;EAEA;EACAiF,WAAWA,CAAC9E,MAAW;IACrB,OAAO,IAAI,CAAC2sB,IAAI,CAAC/jB,GAAG,CAAQ,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACS,aAAa,IAAInoB,MAAM,EAAE,CAAC;EACtG;EAEAmuB,qBAAqBA,CAACtuB,IAAS;IAC7B,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACU,WAAW,EAAE,EAAEvoB,IAAI,CAAC;EAC1F;;;uCAzDW+F,oBAAoB,EAAA5O,sDAAA,CAAAW,4DAAA;IAAA;EAAA;;;aAApBiO,oBAAoB;MAAA6kB,OAAA,EAApB7kB,oBAAoB,CAAA8kB,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;ACN2C;AAEL;;;AAMpD,MAAOvS,aAAa;EACxBzhB,YAAoBg2B,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IACxB,KAAAC,MAAM,GAAG,GAAGhH,kEAAW,CAACE,UAAU,MAAM;IACxC,KAAA1kB,QAAQ,GAAGwkB,kEAAW,CAACE,UAAU;EAFM;EAIvC;EACAxD,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACqK,IAAI,CAAC/jB,GAAG,CAAY,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACuB,cAAc,CAACW,aAAa,EAAE,CAAC;EAChG;EAEAlT,mBAAmBA,CAAC5a,EAAO;IACzB,OAAO,IAAI,CAACoyB,IAAI,CAAC/jB,GAAG,CAAS,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACU,oBAAoB,IAAIvsB,EAAE,EAAE,CAAC;EAChG;EAEA6c,sBAAsBA,CAACxI,UAAsB;IAC3C,OAAO,IAAI,CAAC+d,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACS,mBAAmB,EAAE,EAAEjY,UAAU,CAAC;EAC9F;EAEA4H,wBAAwBA,CAACxW,MAAW;IAClC,OAAO,IAAI,CAAC2sB,IAAI,CAAC/jB,GAAG,CAAe,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACC,IAAI,CAACQ,gBAAgB,IAAI5mB,MAAM,EAAE,CAAC;EACtG;;;uCApBWoY,aAAa,EAAAphB,sDAAA,CAAAW,4DAAA;IAAA;EAAA;;;aAAbygB,aAAa;MAAAqS,OAAA,EAAbrS,aAAa,CAAAsS,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;ACP8B;AACa;AAEL;;;AAMpD,MAAOzkB,aAAa;EACxBvP,YAAoBg2B,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IACxB,KAAAC,MAAM,GAAG,GAAGhH,kEAAW,CAACE,UAAU,MAAM;IACxC,KAAA1kB,QAAQ,GAAGwkB,kEAAW,CAACE,UAAU;IACjC,KAAAvlB,UAAU,GAAyB,IAAIksB,iDAAe,CAAM,EAAE,CAAC;EAHxB;EAKvC9b,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACgc,IAAI,CAAC/jB,GAAG,CAAC,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACQ,oBAAoB,EAAE,CAAC;EACpF;EAEAlY,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC+b,IAAI,CAAC/jB,GAAG,CAAC,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACS,iBAAiB,EAAE,CAAC;EACjF;EAEAlY,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC8b,IAAI,CAAC/jB,GAAG,CAAC,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACU,kBAAkB,EAAE,CAAC;EAClF;EAEAlY,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC6b,IAAI,CAAC/jB,GAAG,CAAC,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACW,kBAAkB,EAAE,CAAC;EAClF;EAEAnS,WAAWA,CAACjX,IAAS;IACnB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACG,YAAY,EAAE,EAAE5oB,IAAI,CAAC;EACnF;EAEA2S,WAAWA,CAAA;IACT,OAAO,IAAI,CAACma,IAAI,CAAC/jB,GAAG,CAAY,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACC,YAAY,EAAE,CAAC;EACvF;EAEA9V,QAAQA,CAACrW,SAAc;IACrB,OAAO,IAAI,CAACuwB,IAAI,CAAC/jB,GAAG,CAAS,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACE,SAAS,IAAIpsB,SAAS,EAAE,CAAC;EAC9F;EAEAoX,SAASA,CAAC3T,IAAS;IACjB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACI,UAAU,EAAE,EAAE7oB,IAAI,CAAC;EACjF;EAEA;EACAuuB,eAAeA,CAAA;IACb,OAAO,IAAI,CAACzB,IAAI,CAAC/jB,GAAG,CAAY,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACO,kBAAkB,EAAE,CAAC;EAC7F;EAEA;EACAjU,YAAYA,CAAC/U,IAAS;IACpB,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACK,cAAc,EAAE,EAAE9oB,IAAI,CAAC;EACrF;EAEA+T,YAAYA,CAAC5T,MAAW;IACtB,OAAO,IAAI,CAAC2sB,IAAI,CAAC/jB,GAAG,CAAQ,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAACmC,MAAM,CAACM,cAAc,IAAI5oB,MAAM,EAAE,CAAC;EAC/F;;;uCAlDWkG,aAAa,EAAAlP,sDAAA,CAAAW,4DAAA;IAAA;EAAA;;;aAAbuO,aAAa;MAAAukB,OAAA,EAAbvkB,aAAa,CAAAwkB,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;ACJ2C;AACL;;;AAMpD,MAAO1G,4BAA4B;EACvCttB,YAAoBg2B,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IACxB,KAAAC,MAAM,GAAG,GAAGhH,kEAAW,CAACE,UAAU,MAAM;IACxC,KAAA1kB,QAAQ,GAAGwkB,kEAAW,CAACE,UAAU;EAFM;EAIvC;EACA3D,wBAAwBA,CAACkM,MAAW;IAClC,OAAO,IAAI,CAAC1B,IAAI,CAAC/jB,GAAG,CAAsB,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACC,cAAc,IAAIkF,MAAM,EAAE,CAAC;EAChH;EAEA5N,wBAAwBA,CAAClmB,EAAU;IACjC,OAAO,IAAI,CAACoyB,IAAI,CAAC/jB,GAAG,CAAsB,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACE,eAAe,IAAI7uB,EAAE,EAAE,CAAC;EAC7G;EAEAsoB,uBAAuBA,CAACtoB,EAAU;IAChC,OAAO,IAAI,CAACoyB,IAAI,CAAC/jB,GAAG,CAAY,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACW,yBAAyB,IAAItvB,EAAE,EAAE,CAAC;EAC7G;EAEA+oB,oBAAoBA,CAACzjB,IAAuB;IAC1C,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACG,SAAS,EAAE,EAAExpB,IAAI,CAAC;EACnF;EAEAwjB,uBAAuBA,CAACxjB,IAAuB;IAC7C,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACI,YAAY,EAAE,EAAEzpB,IAAI,CAAC;EACtF;EAEA2jB,uBAAuBA,CAACjpB,EAAO;IAC7B,OAAO,IAAI,CAACoyB,IAAI,CAAC2B,MAAM,CAAC,GAAG,IAAI,CAAC1B,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACK,YAAY,IAAIhvB,EAAE,EAAE,CAAC;EACxF;EAEA;EACA6nB,wBAAwBA,CAACiM,MAAW;IAClC,OAAO,IAAI,CAAC1B,IAAI,CAAC/jB,GAAG,CAAsB,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACM,cAAc,IAAI6E,MAAM,EAAE,CAAC;EAChH;EAEAhN,wBAAwBA,CAAC9mB,EAAU;IACjC,OAAO,IAAI,CAACoyB,IAAI,CAAC/jB,GAAG,CAAsB,GAAG,IAAI,CAACgkB,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACO,eAAe,IAAIlvB,EAAE,EAAE,CAAC;EAC7G;EAEAwpB,oBAAoBA,CAAClkB,IAAuB;IAC1C,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACQ,SAAS,EAAE,EAAE7pB,IAAI,CAAC;EACnF;EAEAikB,uBAAuBA,CAACjkB,IAAuB;IAC7C,OAAO,IAAI,CAAC8sB,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACS,YAAY,EAAE,EAAE9pB,IAAI,CAAC;EACtF;EAEAmkB,uBAAuBA,CAACzpB,EAAO;IAC7B,OAAO,IAAI,CAACoyB,IAAI,CAAC2B,MAAM,CAAC,GAAG,IAAI,CAAC1B,MAAM,GAAGzG,mEAAa,CAAC+C,SAAS,CAACU,YAAY,IAAIrvB,EAAE,EAAE,CAAC;EACxF;;;uCAjDW0pB,4BAA4B,EAAAjtB,sDAAA,CAAAW,4DAAA;IAAA;EAAA;;;aAA5BssB,4BAA4B;MAAAwG,OAAA,EAA5BxG,4BAA4B,CAAAyG,IAAA;MAAAC,UAAA,EAF3B;IAAM;EAAA;;;;;;;;;;;;;;;;ACVpB;AACA;AACA;AAEO,MAAM/E,WAAW,GAAG;EACzB2I,UAAU,EAAE,KAAK;EACjBzI,UAAU,EAAE;CACb;AAED;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;;;;AChB2E;AAC+B;AACvD;AACM;AACL;AACM;AACsD;AACnC;AAClC;AACK;AACP;AACC;AACqC;AAE/E,IAAIF,kEAAW,CAAC2I,UAAU,EAAE;EAC1BC,6DAAc,EAAE;AAClB;AAEAC,+EAAoB,CAAC/3B,4DAAY,EAAE;EAC/Bw4B,SAAS,EAAE,CACPR,kEAAmB,CAACC,oEAAa,EAAEV,4DAAU,CAAC,EAC9C;IACEkB,OAAO,EAAEP,mEAAiB;IAC1BQ,QAAQ,EAAE5D,sFAAgB;IAC1B6D,KAAK,EAAE;GACR,EACDX,kEAAmB,CAACM,oDAAY,CAACM,OAAO,EAAE,CAAC,EAC3CZ,kEAAmB,CAACO,yFAAuB,CAAC,EAC5CF,8DAAa,CAACt2B,kDAAM,CAAC,EACrB4O,sDAAQ,EACRwnB,uEAAiB,CAACC,4EAAsB,EAAE,CAAC;CAElD,CAAC,CAACS,KAAK,CAAC1qB,GAAG,IAAI2qB,OAAO,CAAC3tB,KAAK,CAACgD,GAAG,CAAC,CAAC;;;;;;;;;;AChCnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["./src/app/app.component.ts", "./src/app/app.component.html", "./src/app/app.route.ts", "./src/app/main/components/footer/footer.component.ts", "./src/app/main/components/footer/footer.component.html", "./src/app/main/components/home/<USER>", "./src/app/main/components/home/<USER>", "./src/app/main/components/login-register/forgot-password/forgot-password.component.ts", "./src/app/main/components/login-register/forgot-password/forgot-password.component.html", "./src/app/main/components/login-register/login/login.component.ts", "./src/app/main/components/login-register/login/login.component.html", "./src/app/main/components/login-register/register/register.component.ts", "./src/app/main/components/login-register/register/register.component.html", "./src/app/main/components/login-register/reset-password/reset-password.component.ts", "./src/app/main/components/login-register/reset-password/reset-password.component.html", "./src/app/main/components/navbar/navbar.component.ts", "./src/app/main/components/navbar/navbar.component.html", "./src/app/main/components/new-mission/new-mission.component.ts", "./src/app/main/components/new-mission/new-mission.component.html", "./src/app/main/components/privacy-policy/privacy-policy.component.ts", "./src/app/main/components/privacy-policy/privacy-policy.component.html", "./src/app/main/components/searching-sorting/searching-sorting.component.ts", "./src/app/main/components/searching-sorting/searching-sorting.component.html", "./src/app/main/components/user-edit-profile/user-edit-profile.component.ts", "./src/app/main/components/user-edit-profile/user-edit-profile.component.html", "./src/app/main/components/volunteering-mission/volunteering-mission.component.ts", "./src/app/main/components/volunteering-mission/volunteering-mission.component.html", "./src/app/main/components/volunteering-timesheet/volunteering-timesheet.component.ts", "./src/app/main/components/volunteering-timesheet/volunteering-timesheet.component.html", "./src/app/main/configs/environment.config.ts", "./src/app/main/constants/api.constants.ts", "./src/app/main/guards/auth.guard.ts", "./src/app/main/helpers/validate-form.helper.ts", "./src/app/main/interceptors/token.interceptor.ts", "./src/app/main/pipes/search.pipe.ts", "./src/app/main/services/auth.service.ts", "./src/app/main/services/client-mission.service.ts", "./src/app/main/services/client.service.ts", "./src/app/main/services/common.service.ts", "./src/app/main/services/volunteering-timesheet.service.ts", "./src/environments/environment.ts", "./src/main.ts", "./node_modules/moment/locale/ sync ^\\.\\/.*$"], "sourcesContent": ["import { Component, CUSTOM_ELEMENTS_SCHEMA } from \"@angular/core\"\nimport { RouterOutlet } from \"@angular/router\";\nimport { NgToastModule } from \"ng-angular-popup\";\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [RouterOutlet, NgToastModule],\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css'],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\n})\nexport class AppComponent {\n  title = 'FrontEnd';\n}\n", "<div>\n  <router-outlet></router-outlet>\n  <lib-ng-toast></lib-ng-toast>\n</div>\n", "import { type Routes } from \"@angular/router\"\nimport { AuthGuard } from \"./main/guards/auth.guard\"\nimport { HomeComponent } from \"./main/components/home/<USER>\"\nimport { NewMissionComponent } from \"./main/components/new-mission/new-mission.component\"\nimport { PrivacyPolicyComponent } from \"./main/components/privacy-policy/privacy-policy.component\"\nimport { VolunteeringMissionComponent } from \"./main/components/volunteering-mission/volunteering-mission.component\"\nimport { ForgotPasswordComponent } from \"./main/components/login-register/forgot-password/forgot-password.component\"\nimport { LoginComponent } from \"./main/components/login-register/login/login.component\"\nimport { RegisterComponent } from \"./main/components/login-register/register/register.component\"\nimport { ResetPasswordComponent } from \"./main/components/login-register/reset-password/reset-password.component\"\nimport { UserEditProfileComponent } from \"./main/components/user-edit-profile/user-edit-profile.component\"\nimport { VolunteeringTimesheetComponent } from \"./main/components/volunteering-timesheet/volunteering-timesheet.component\"\n\nexport const routes: Routes = [\n  { path: \"\", component: LoginComponent },\n  { path: \"register\", component: RegisterComponent },\n  { path: \"forgotPassword\", component: ForgotPasswordComponent },\n  { path: \"resetPassword\", component: ResetPasswordComponent },\n  { path: \"home\", component: HomeComponent },\n  { path: \"addNewMission\", component: NewMissionComponent },\n  { path: \"volunteeringMission/:missionId\", component: VolunteeringMissionComponent, canActivate: [AuthGuard] },\n  { path: \"userProfile/:userId\", component: UserEditProfileComponent, canActivate: [AuthGuard] },\n  { path: \"privacyPolicy\", component: PrivacyPolicyComponent },\n  { path: \"volunteeringTimesheet\", component: VolunteeringTimesheetComponent, canActivate: [AuthGuard] },\n  { path: \"admin\", loadChildren: () => import(\"./main/components/admin-side/admin-side.route\") },\n]", "import { Component, type OnInit } from \"@angular/core\"\nimport { RouterModule } from \"@angular/router\";\n\n@Component({\n  selector: \"app-footer\",\n  imports: [RouterModule],\n  templateUrl: \"./footer.component.html\",\n  styleUrls: [\"./footer.component.css\"],\n  standalone: true\n})\nexport class FooterComponent implements OnInit {\n  constructor() {}\n\n  ngOnInit(): void {}\n}\n", "<div class=\"container-fluid footer\">\n    <hr/>\n    <a routerLink=\"/privacyPolicy\" style=\"text-decoration: none;cursor:pointer;color: black;\">Privacy Policy</a>\n  </div>\n  ", "import { Component, OnDestroy, type OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { NgToastService } from 'ng-angular-popup';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport dateFormat from 'dateformat';\nimport * as moment from 'moment';\nimport { FormGroup, FormsModule } from '@angular/forms';\nimport { CommonService } from 'src/app/main/services/common.service';\nimport { AuthService } from '../../services/auth.service';\nimport { APP_CONFIG } from '../../configs/environment.config';\nimport { ClientMissionService } from '../../services/client-mission.service';\nimport { FooterComponent } from '../footer/footer.component';\nimport { NavbarComponent } from '../navbar/navbar.component';\nimport { NgxPaginationModule } from 'ngx-pagination';\nimport { SearchPipe } from '../../pipes/search.pipe';\nimport { Mission } from '../../interfaces/common.interface';\nimport { Subscription } from 'rxjs';\n\ndeclare var window: any;\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css'],\n  standalone: true,\n  imports: [CommonModule, FooterComponent, NavbarComponent, NgxPaginationModule, FormsModule, SearchPipe]\n})\nexport class HomeComponent implements OnInit, OnDestroy {\n  missionList: any[] = [];\n  userList: any[] = [];\n  page = 1;\n  missionPerPages = 9;\n  listmissionPerPages = 5;\n  totalMission: any;\n  searchParam: any;\n  loginUserId = 0;\n  loginUserName: any;\n  loginemailAddress: any;\n  missionApplyModal: any;\n  shareOrInviteModal: any;\n  missionData: any;\n  appliedDate: any;\n  missionStatu = false;\n  favImag = 'assets/Img/heart1.png';\n  favImag1 = 'assets/Img/heart11.png';\n  view: 'grid' | 'list' = 'grid';\n  missionFavourite = false;\n  public form: FormGroup;\n  rating3: any;\n  missionid: any;\n  usercheckedlist: any[] = [];\n  private unsubscribe: Subscription[] = [];\n\n  constructor(\n    private _service: ClientMissionService,\n    private _toast: NgToastService,\n    private _router: Router,\n    private _commonservice: CommonService,\n    private _adminservice: AuthService\n  ) {}\n\n  ngOnInit(): void {\n    const currentUserSubscribe = this._adminservice.getCurrentUser().subscribe((data: any) => {\n      const loginUserDetail = this._adminservice.getUserDetail();\n      data == null\n        ? (this.loginUserId = loginUserDetail.userId)\n        : (this.loginUserId = data.userId);\n      data == null\n        ? (this.loginUserName = loginUserDetail.fullName)\n        : (this.loginUserName = data.fullName);\n      data == null\n        ? (this.loginemailAddress = loginUserDetail.emailAddress)\n        : (this.loginemailAddress = data.emailAddress);\n    });\n    this.allMissionList();\n    const searchListSubscribe = this._commonservice.searchList.subscribe((data: any) => {\n      this.searchParam = data;\n    });\n    this.missionData = '';\n    this.unsubscribe.push(currentUserSubscribe, searchListSubscribe);\n  }\n  \n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n\n  onChangeGrid() {\n    this.view = 'grid';\n  }\n\n  onChangeList() {\n    this.view = 'list';\n  }\n\n  allMissionList() {\n    const missionListSubscribe = this._service.missionList(this.loginUserId).subscribe((data: any) => {\n      if (data.result == 1) {\n        this.missionList = data.data;\n        this.missionList = this.missionList.map((x) => {\n          var missionimg = x.missionImages\n            ? this._service.imageUrl + '/' + x.missionImages\n            : 'assets/NoImg.png';\n          this.rating3 = x.rating;\n          return {\n            id: x.id,\n            missionTitle: x.missionTitle,\n            missionDescription: x.missionDescription,\n            countryId: x.countryId,\n            countryName: x.countryName,\n            cityId: x.cityId,\n            cityName: x.cityName,\n            startDate: x.startDate,\n            endDate: x.endDate,\n            totalSheets: x.totalSheets,\n            registrationDeadLine: x.registrationDeadLine,\n            missionThemeId: x.missionThemeId,\n            missionSkillId: x.missionSkillId,\n            missionImages: missionimg.split(',', 1),\n            missionThemeName: x.missionThemeName,\n            missionSkillName: x.missionSkillName,\n            missionStatus: x.missionStatus,\n            missionApplyStatus: x.missionApplyStatus,\n            missionApproveStatus: x.missionApproveStatus,\n            missionDateStatus: x.missionDateStatus,\n            missionDeadLineStatus: x.missionDeadLineStatus,\n          };\n        });\n        this.totalMission = data.data.length;\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n        // this.toastr.error(data.message);\n      }\n    });\n    this.unsubscribe.push(missionListSubscribe);\n  }\n\n  sortingData(e: any) {\n    const selectedValue = e.target.value;\n    if (selectedValue == 'a-z') {\n      this.missionList.sort((a, b) => {\n        var a = a['missionTitle'].toLowerCase(),\n          b = b['missionTitle'].toLowerCase();\n        return a > b ? 1 : a < b ? -1 : 0;\n      });\n    } else {\n      this.missionList.sort((a, b) => {\n        var a = a['missionTitle'].toLowerCase(),\n          b = b['missionTitle'].toLowerCase();\n        return a < b ? 1 : a > b ? -1 : 0;\n      });\n    }\n  }\n  sortingList(e: any) {\n    let selectedVal = e.target.value;\n    selectedVal = selectedVal == '' ? 'null' : selectedVal;\n    const value = {\n      userId: this.loginUserId,\n      sortestValue: selectedVal,\n    };\n    const missionClientSubscribe = this._service.missionClientList(value).subscribe((data: any) => {\n      if (data.result == 1) {\n        this.missionList = data.data;\n        this.missionList = this.missionList.map((x) => {\n          const missionimg = x.missionImages\n            ? this._service.imageUrl + '/' + x.missionImages\n            : 'assets/NoImg.png';\n          return {\n            id: x.id,\n            missionTitle: x.missionTitle,\n            missionDescription: x.missionDescription,\n            countryId: x.countryId,\n            countryName: x.countryName,\n            cityId: x.cityId,\n            cityName: x.cityName,\n            startDate: x.startDate,\n            endDate: x.endDate,\n            totalSheets: x.totalSheets,\n            registrationDeadLine: x.registrationDeadLine,\n            missionThemeId: x.missionThemeId,\n            missionSkillId: x.missionSkillId,\n            missionImages: missionimg.split(',', 1),\n            missionThemeName: x.missionThemeName,\n            missionSkillName: x.missionSkillName,\n            missionStatus: x.missionStatus,\n            missionApplyStatus: x.missionApplyStatus,\n            missionApproveStatus: x.missionApproveStatus,\n            missionDateStatus: x.missionDateStatus,\n            missionDeadLineStatus: x.missionDeadLineStatus,\n          };\n        });\n        this.totalMission = data.data.length;\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n        // this.toastr.error(data.message);\n      }\n    });\n    this.unsubscribe.push(missionClientSubscribe);\n  }\n\n  openMissionApplyModal() {\n    this.missionApplyModal.show();\n  }\n\n  closeMissionApplyModal() {\n    this.missionApplyModal.hide();\n  }\n\n  checkUserLoginOrNot(id: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else {\n      const data = this.missionList.find((v: Mission) => v.id == id);\n      this.missionData = data;\n      const now = new Date();\n      this.appliedDate = dateFormat(now, 'dd/mm/yyyy h:MM:ss TT');\n      this.applyMission();\n    }\n  }\n\n  redirectVolunteering(missionId: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      this._router.navigate([`volunteeringMission/${missionId}`]);\n    }\n  }\n\n  applyMission() {\n    const value = {\n      missionId: this.missionData.id,\n      userId: this.loginUserId,\n      appliedDate: moment().format('yyyy-MM-DDTHH:mm:ssZ'),\n      status: false,\n      sheet: 1,\n    };\n    const applyMissionSubscribe = this._service.applyMission(value).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this._toast.success({ detail: 'SUCCESS', summary: data.data });\n          setTimeout(() => {\n            this.missionData.totalSheets = this.missionData.totalSheets - 1;\n          }, 1000);\n          window.location.reload();\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(applyMissionSubscribe);\n  }\n\n  getUserList() {\n    const userListSubscribe = this._service.getUserList(this.loginUserId).subscribe((data: any) => {\n      if (data.result == 1) {\n        this.userList = data.data;\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(userListSubscribe);\n  }\n\n  getUserCheckedList(isSelected, item) {\n    if (isSelected == true) {\n      this.usercheckedlist.push({\n        id: item.id,\n        userFullName: item.userFullName,\n        emailAddress: item.emailAddress,\n        missionShareUserEmailAddress: this.loginemailAddress,\n        baseUrl: document.location.origin,\n        missionId: this.missionid,\n      });\n    } else {\n      this.usercheckedlist.map((a: any, index: any) => {\n        if (item.id == a.id) {\n          this.usercheckedlist.splice(index, 1);\n        }\n      });\n    }\n  }\n}", "<div>\n<app-navbar></app-navbar>\n</div>\n<div class=\"container\" style=\"width:100%\">\n  <div class=\"sortingdata mt-5\">\n\n  </div>\n  <div class=\"row\">\n    <div class=\"col-sm-6\">\n        <p style=\"font-size: 20px;margin-left: 20px;\">Explore <span><b>{{totalMission}} missions</b></span></p>\n    </div>\n    <div class=\"col-sm-6 row\" style=\"display: flex;justify-content: flex-end;\">\n      <div class=\"col-sm-3\" style=\"margin-left:20%\">\n        <!-- <select class=\"form-select\" (change)=\"SortingData($event)\"> -->\n          <select class=\"form-select\" (change)=\"sortingList($event)\">\n          <option value=\"\">Sort by</option>\n          <option value=\"Newest\">Newest</option>\n          <option value=\"Oldest\">Oldest</option>\n          <option value=\"Lowest available seats\">Lowest available seats</option>\n          <option value=\"Highest available seats\">Highest available seats</option>\n          <option value=\"My favourites\">My favourites</option>\n          <option value=\"Registration deadline\">Registration deadline</option>\n        </select>\n      </div>\n      <div class=\"col-sm-1\" style=\"color:#e8e8e8;margin-right:-2%;\">\n        <div class=\"Ellipse-574\">\n          <div class=\"btn btn-icon btn-clean btn-lg mr-1\" title=\"GridView\" id=\"kt_quick_panel_toggle\" style=\"padding-top: 0px;margin-left:-8px;margin-top: 4px;cursor: pointer;\" (click)=\"onChangeGrid()\">\n            <img src=\"assets/Img/grid.png\">\n        </div>\n      </div>\n      </div>\n      <div class=\"col-sm-2\" title=\"ListView\">\n        <img src=\"assets/Img/list.png\" alt=\"NoImage\" style=\"margin-left:40%;padding-top: 6px;cursor: pointer;\" (click)=\"onChangeList()\">\n      </div>\n    </div>\n  </div>\n  <input type=\"hidden\" [(ngModel)]=\"searchParam\">\n  <div *ngIf=\"view =='grid'\" class=\"row col-sm-12\">\n    <ng-container *ngIf=\"(missionList | search:searchParam | paginate:{itemsPerPage:missionPerPages,currentPage:page,totalItems:totalMission}) as result\">\n      <div class=\"col-sm-4  Rounded-Rectangle-2-copy\" *ngFor=\"let item of result\">\n          <div class=\"card-header\" >\n              <img src=\"{{item.missionImages}}\" alt=\"NoImage\" style=\"width: 420px;height:220px;\" onerror=\"this.src='assets/NoImg.png'\">\n              <div class=\"top-center\" *ngIf=\"item.missionStatus == 'Closed'\">CLOSED</div>\n              <div class=\"top-center\" *ngIf=\"item.missionApplyStatus == 'Applied'\">APPLIED</div>\n              <div class=\"top-center\" *ngIf=\"item.missionApproveStatus === 'Approved'\">APPROVED</div>\n              <div class=\"top-center\" *ngIf=\"item.missionStatus != 'Closed' && item.missionApplyStatus != 'Applied' && item.missionApproveStatus !== 'Approved'\">NEW</div>\n              <div class=\"centered\">{{item.missionThemeName}}</div>\n          </div>\n          <div class=\"card-body\">\n            <p class=\"heading\">{{item.missionTitle}}</p>\n            <p class=\"content\"> {{item.missionOrganisationDetail}}</p>\n            <div class=\"row\" style=\"margin: 14px;\">\n              <div class=\"col-sm-7 contentdetail\">\n                {{item.countryName}}\n              </div>\n            </div>\n            <div class=\"bordert\">\n                <div class=\"text-center data py-3\">\n                  <p style=\"margin-top: -12px\">From {{item.startDate | date: 'dd/MM/yyyy'}} until {{item.endDate | date: 'dd/MM/yyyy'}}</p>\n                </div>\n            </div>\n            <div class=\"SeatDeadLine row\">\n                <div class=\"col-sm-6\">\n                  <i class=\"fa fa-user-circle-o fa-2x\"></i>&nbsp;\n                  <span style=\"font-size: 24px !important;\">{{item.totalSheets}}</span> <br/><span style=\"margin-left:40px\">Seats left</span>\n                </div>\n                <div class=\"col-sm-6\">\n                  <i class=\"fa fa-clock-o fa-2x\"></i>&nbsp;\n                  <span style=\"font-size: 24px !important;\">{{item.registrationDeadLine | date: 'dd/MM/yyyy'}}</span> <br/><span style=\"margin-left:40px\">Deadline</span>\n                </div>\n            </div>\n          </div>\n          <P style=\"border-top: 1px solid rgba(0, 0, 0, 0.06);width:100%\"></P>\n          <div class=\"d-grid card-footer\" style=\"display: flex;justify-content: center;\">\n          <button class=\"btn-login\" type=\"submit\" *ngIf=\"item.missionApplyStatus =='Apply'\" (click)=\"checkUserLoginOrNot(item.id)\" [disabled]=\"item.missionStatus=='Closed'\"><span class=\"Login\">Apply &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n          <button class=\"btn-login\" type=\"submit\" *ngIf=\"item.missionApplyStatus === 'Applied' && item.missionApproveStatus !== 'Approved'\" [disabled]=\"item.missionApplyStatus === 'Applied'\"><span class=\"Login\">Applied</span></button>\n          <button class=\"btn-login\" type=\"submit\" *ngIf=\"item.missionApproveStatus === 'Approved'\" [disabled]=\"item.missionApproveStatus === 'Approved'\"><span class=\"Login\">Approve</span></button>\n        </div>\n           <p style=\"display: none;\">{{item.countryId}}</p>\n\n      </div>\n      <div class=\"col-sm-12 text-center\" *ngIf=\"result.length === 0\">\n          <p class=\"text-secondary\" style=\"font-size:20px\"><b>No mission Found</b></p>\n      </div>\n      <div class=\"mt-8 py-5\" *ngIf=\"result.length != 0\" style=\"display:flex;justify-content: center;\">\n        <pagination-controls previousLabel=\"\" nextLabel=\"\" (pageChange)=\"page = $event\"></pagination-controls>\n      </div>\n    </ng-container>\n  </div>\n  <div class=\"row mt-3\" *ngIf=\"view=='list'\">\n    <ng-container *ngIf=\"(missionList | search:searchParam | paginate:{itemsPerPage:listmissionPerPages,currentPage:page,totalItems:totalMission}) as result\">\n    <div class=\"card ps-md-0 mt-4\" *ngFor=\"let item of result\" style=\"height: 220px;box-shadow: 0px 0px 0.75rem rgba(0,0,0,0.3);\">\n          <div class=\"col-sm-12 row\">\n      <div class=\"col-sm-3\">\n        <img src=\"{{item.missionImages}}\" alt=\"no img\" width=\"100%\" height=\"220px;\" onerror=\"this.src='assets/NoImg.png'\">\n        <div class=\"top-center\" *ngIf=\"item.missionStatus == 'Closed'\">Closed</div>\n        <div class=\"top-center\" *ngIf=\"item.missionApplyStatus == 'Applied'\">APPLIED</div>\n        <div class=\"top-center\" *ngIf=\"item.missionApproveStatus === 'Approved'\">APPROVED</div>\n        <div class=\"top-center\" *ngIf=\"item.missionStatus != 'Closed' && item.missionApplyStatus != 'Applied' && item.missionApproveStatus !== 'Approved'\">NEW</div>\n        <div class=\"list-centered\">{{item.missionThemeName}}</div>\n      </div>\n      <div class=\"col-sm-9\">\n        <div class=\"row col-sm-12\">\n          <div class=\"col-sm-2\">\n            <img src=\"assets/Img/pin1.png\" alt=\"NoImage\">&nbsp;{{item.cityName}}\n          </div>\n          <div class=\"col-sm-4\">\n            <img src=\"assets/Img/web.png\" alt=\"NoImage\">&nbsp;<span>{{item.missionThemeName}}</span>\n          </div>\n          <div class=\"col-sm-4\">\n            <img src=\"assets/Img/organization.png\" alt=\"NoImage\">&nbsp;<span>{{item.missionOrganisationName}}</span>\n          </div>  \n        </div>\n        <div class=\"mt-3\">\n            <div style=\"font-size: 23px;\">\n              {{item.missionTitle}}\n            </div>\n            <div class=\"\">\n              {{item.missionDescription}}\n            </div>\n        </div>\n        <div class=\"row col-sm-12 mt-3\">\n          <div class=\"col-sm-2\">\n            <img src=\"assets/Img/Seats-left.png\" alt=\"NoImage\">&nbsp;\n            <span>{{item.totalSheets}}</span> <br/><span style=\"margin-left:35px\">Seats left</span>\n          </div>\n          <div class=\"col-sm-2\">\n            <img src=\"assets/Img/calender.png\" alt=\"NoImage\">&nbsp;\n             {{item.registrationDeadLine | date: 'dd/MM/yyyy'}} <br/> <span style=\"margin-left:31px;\">Deadline</span>\n          </div>\n          <div class=\"col-sm-3\">\n            <img src=\"assets/Img/calender.png\" alt=\"NoImage\">&nbsp;\n            FROM {{item.startDate | date: 'dd/MM/yyyy'}} <br/> <span style=\"margin-left:31px;\">Until {{item.endDate | date: 'dd/MM/yyyy'}}</span>\n          </div>\n          <div class=\"col-sm-3\">\n            <img src=\"assets/Img/settings.png\" alt=\"NoImage\">&nbsp;Skills\n             <br/> <span style=\"margin-left:21px;word-break: break-all\">{{item.missionSkillName}}</span>\n          </div>\n          <div class=\"col-sm-2\">\n            <!-- <button class=\"btnViewDetail btn-btn-outline\" [disabled]=\"item.missionStatus=='Closed'\" type=\"submit\"><span class=\"ViewDetail\">Apply <i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button> -->\n            <button class=\"btnViewDetail btn-btn-outline\" type=\"submit\" *ngIf=\"item.missionApplyStatus =='Apply'\" (click)=\"checkUserLoginOrNot(item.id)\" [disabled]=\"item.missionStatus=='Closed'\"><span class=\"ViewDetail\">Apply &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n            <button class=\"btn-login\" type=\"submit\" *ngIf=\"item.missionApplyStatus ==='Applied'\" [disabled]=\"item.missionApplyStatus=='Applied'\"><span class=\"Login\">Applied</span></button>\n            <button class=\"btn-login\" type=\"submit\" *ngIf=\"item.missionApproveStatus === 'Approved'\" [disabled]=\"item.missionApproveStatus === 'Approved'\"><span class=\"Login\">Approve</span></button>\n          </div>\n        </div>\n      </div>\n          </div>\n    </div>\n    <div class=\"col-sm-12 text-center\" *ngIf=\"result.length === 0\">\n      <p class=\"text-secondary\" style=\"font-size:20px\"><b>No mission Found</b></p>\n    </div>\n    <div class=\"mt-8 py-5\" *ngIf=\"result.length != 0\" style=\"display:flex;justify-content: center;\">\n      <pagination-controls previousLabel=\"\" nextLabel=\"\" (pageChange)=\"page = $event\"></pagination-controls>\n    </div>\n  </ng-container>\n  </div>\n</div>\n\n\n<div>\n  <app-footer></app-footer>\n</div>\n\n\n\n<div class=\"modal fade\"  id=\"applyMissionModel\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"contactUsModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\" role=\"document\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"contactUsModalLabel\">Apply Mission</h5>\n        <button type=\"button\" class=\"btn-close\" data-dismiss=\"modal\" aria-label=\"Close\" (click)=\"closeMissionApplyModal()\">\n        </button>\n      </div>\n\n      <div class=\"modal-body\">\n        <div class=\"row\">\n          <div class=\"form-group\">\n           <label class=\"col-form-label\"><b>Mission Title :</b></label>\n           <label class=\"col-form-label\" style=\"margin-left:3px;word-wrap: unset;\" >{{missionData.missionTitle}}</label>\n          </div>\n          <div class=\"form-group\">\n            <label class=\"col-form-label\"><b>User Name :</b></label>\n            <label class=\"col-form-label\" style=\"margin-left:3px\">{{loginUserName}}</label>\n           </div>\n           <div class=\"form-group\">\n            <label class=\"col-form-label\"><b>Applied Date :</b></label>\n            <label class=\"col-form-label\" style=\"margin-left:3px\">{{appliedDate}}</label>\n           </div>\n           <div class=\"form-group\">\n            <label class=\"col-form-label\"><b>Sheet :</b></label>\n            <label class=\"col-form-label\" style=\"margin-left:3px\">1</label>\n           </div>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn-Close\" data-dismiss=\"modal\" (click)=\"closeMissionApplyModal()\"><span class=\"Close\"> Cancel</span> </button>\n        <button type=\"submit\" class=\"btnApplyMission\" (click)=\"applyMission()\"><span class=\"ApplyMission\">Submit</span></button>\n      </div>\n\n    </div>\n  </div>\n</div>\n", "import { NgIf } from \"@angular/common\"\nimport { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from \"@angular/core\"\nimport { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from \"@angular/forms\"\nimport { Router, RouterModule } from \"@angular/router\"\nimport { NgToastService } from \"ng-angular-popup\"\nimport { ToastrService } from \"ngx-toastr\"\nimport { Subscription } from \"rxjs\"\nimport { APP_CONFIG } from \"src/app/main/configs/environment.config\"\nimport { AuthService } from \"src/app/main/services/auth.service\"\n\n@Component({\n  selector: \"app-forgot-password\",\n  standalone: true,\n  imports: [ReactiveFormsModule, NgIf, RouterModule],\n  templateUrl: \"./forgot-password.component.html\",\n  styleUrls: [\"./forgot-password.component.css\"],\n})\nexport class ForgotPasswordComponent implements OnInit, OnDestroy {\n  private unsubscribe: Subscription[] = [];\n\n  constructor(\n    private _fb: FormBuilder,\n    private _service: AuthService,\n    private _router: Router,\n    private _toast: NgToastService,\n  ) { }\n  forgotPasswordForm: FormGroup\n  formValid: boolean\n  ngOnInit(): void {\n    this.forgotPassword()\n  }\n  forgotPassword() {\n    this.forgotPasswordForm = this._fb.group({\n      emailAddress: [null, Validators.compose([Validators.required, Validators.email])],\n    })\n  }\n  get emailAddress() {\n    return this.forgotPasswordForm.get(\"emailAddress\") as FormControl\n  }\n\n  onSubmit() {\n    this.formValid = true\n    if (this.forgotPasswordForm.valid) {\n      const addFormValue = this.forgotPasswordForm.value\n      addFormValue.baseUrl = document.location.origin\n\n      const forgotPasswordSubscribe = this._service.forgotPasswordEmailCheck(addFormValue).subscribe((data: any) => {\n        if (!data) {\n          // this.toastr.error('OOPS This email address does not exist');\n          this._toast.error({ detail: \"ERROR\", summary: \"OOPS This email address does not exist\", duration: APP_CONFIG.toastDuration })\n        } else {\n          // this.toastr.success('Reset password mail send successfully. please check your emailtoreset your password');\n          this._toast.success({\n            detail: \"SUCCESS\",\n            summary: \"Password reset link is send to your registred email, Kindly check your mail box\",\n            duration: APP_CONFIG.toastDuration,\n          })\n          setTimeout(() => {\n            this._router.navigate([\"\"])\n          }, 2000)\n        }\n      })\n      this.formValid = false\n      this.unsubscribe.push(forgotPasswordSubscribe);\n    }\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n}\n", "<div class=\"container-fluid ps-md-0\">\n  <div class=\"row g-0\">\n    <div class=\"d-flex col-md-6 col-lg-9 bg-image\">\n      <img src=\"assets/Images/image.png\" alt=\"No Image\">\n      <div class=\"carousel-caption d-md-block\">\n        <p class=\"heading\">Sed ut perspiciatis unde omnis\n          iste natus voluptatem.</p>\n        <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna\n            aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint\n            occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>\n    </div>\n    </div>\n    <div class=\"col-md-6 col-lg-3\">\n      <div class=\"login d-flex align-items-center\">\n        <div class=\"container\" >\n          <div class=\"row\">\n            <div class=\"col-md-9 col-lg-8\" style=\"margin-left: 7%;\">\n                <p class=\"Forgot\">Forgot Password</p>\n                <div class=\"ForgotContent\">\n                    Enter your email address you've using for your account below\n                    and we will send you a password reset link\n                </div>\n                <form [formGroup]=\"forgotPasswordForm\" (ngSubmit)=\"onSubmit()\">\n                <div class=\"form-group\">\n                  <label class=\"col-form-label\">Email Address</label>\n                  <input type=\"text\" class=\"form-control\" formControlName=\"emailAddress\" placeholder=\"Enter your email address..\" autofocus>\n                  <span class=\"text-danger\" *ngIf=\"emailAddress.invalid && (emailAddress.touched || formValid)\">\n                    <span *ngIf=\"emailAddress.hasError('required')\">\n                      Please Enter EmailAddress\n                    </span>\n                    <span *ngIf=\"emailAddress.hasError('email')\">\n                      Please Enter Valid EmailAddress\n                    </span>\n                  </span>\n                </div>\n                <div class=\"d-grid mt-4\">\n                  <button class=\"btn-login\" type=\"submit\"><span class=\"Login\">Reset my Password</span></button>\n                  <div class=\"text-center\">\n                    <p class=\"small\" routerLink='../'>Login</p>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </div>\n          <div style=\"text-align: center;\">\n            <p>Privacy Policy</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { NgIf } from \"@angular/common\"\nimport { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from \"@angular/core\"\nimport { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from \"@angular/forms\"\nimport { Router, RouterModule } from \"@angular/router\"\nimport { NgToastService } from \"ng-angular-popup\"\nimport { Subscription } from \"rxjs\"\nimport { APP_CONFIG } from \"src/app/main/configs/environment.config\"\nimport { AuthService } from \"src/app/main/services/auth.service\"\n\n@Component({\n  selector: \"app-login\",\n  standalone: true,\n  imports: [ReactiveFormsModule, NgIf, RouterModule],\n  templateUrl: \"./login.component.html\",\n  styleUrls: [\"./login.component.css\"],\n})\nexport class LoginComponent implements OnInit, OnDestroy {\n  private unsubscribe: Subscription[] = [];\n  \n  constructor(\n    private _fb: FormBuilder,\n    private _service: AuthService,\n    private _router: Router,\n    private _toast: NgToastService,\n  ) { }\n  loginForm: FormGroup\n  formValid: boolean\n  ngOnInit(): void {\n    this.loginUser()\n  }\n  loginUser() {\n    this.loginForm = this._fb.group({\n      emailAddress: [null, Validators.compose([Validators.required, Validators.email])],\n      password: [null, Validators.compose([Validators.required])],\n    })\n  }\n  get emailAddress() {\n    return this.loginForm.get(\"emailAddress\") as FormControl\n  }\n  get password() {\n    return this.loginForm.get(\"password\") as FormControl\n  }\n  onSubmit() {\n    this.formValid = true\n    if (this.loginForm.valid) {\n      const loginUserSubscribe = this._service\n        .loginUser([this.loginForm.value.emailAddress, this.loginForm.value.password])\n        .subscribe((res: any) => {\n          if (res.result == 1) {\n            if (res.data.message == \"Login Successfully\") {\n              this._service.setToken(res.data.data)\n              const tokenpayload = this._service.decodedToken()\n              this._service.setCurrentUser(tokenpayload)\n\n              this._toast.success({ detail: \"SUCCESS\", summary: res.data.message, duration: APP_CONFIG.toastDuration })\n              if (tokenpayload.userType == \"admin\") {\n                this._router.navigate([\"admin/dashboard\"])\n              } else {\n                this._router.navigate([\"/home\"])\n              }\n            } else {\n              // this.toastr.error(res.data.message);\n              this._toast.error({ detail: \"ERROR\", summary: res.data.message, duration: APP_CONFIG.toastDuration })\n            }\n          } else {\n            // this.toastr.error(res.message);\n            this._toast.error({ detail: \"ERROR\", summary: res.message, duration: APP_CONFIG.toastDuration })\n          }\n        })\n      this.formValid = false\n      this.unsubscribe.push(loginUserSubscribe);\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n}\n", "<div class=\"container-fluid ps-md-0\">\n  <div class=\"row g-0\">\n    <div class=\"d-none d-lg-flex col-md-6 col-lg-8 bg-image position-relative\">\n      <img src=\"assets/Images/image.png\" alt=\"No Image\" class=\"w-100\">\n      <div class=\"carousel-caption d-md-block\">\n        <p class=\"heading\">Sed ut perspiciatis unde omnis\n          iste natus voluptatem.</p>\n        <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna\n            aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint\n            occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>\n      </div>\n    </div>\n    <div class=\"col-md-6 col-lg-4\">\n      <div class=\"login d-flex align-items-center py-3\">\n        <div class=\"container\">\n          <div class=\"row justify-content-center\">\n            <div class=\"col-12 col-sm-10 col-md-11 col-lg-10\">\n              <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n                <div class=\"ForgotContent\">\n\n                </div>\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">Email Address</label>\n                  <input type=\"text\" class=\"form-control\" formControlName=\"emailAddress\" placeholder=\"Email Address\" autofocus>\n                  <span class=\"text-danger\" *ngIf=\"emailAddress.invalid && (emailAddress.touched || formValid)\">\n                    <span *ngIf=\"emailAddress.hasError('required')\">\n                        Please Enter EmailAddress\n                    </span>\n                    <span *ngIf=\"emailAddress.hasError('email')\">\n                      Please Enter Valid EmailAddress\n                    </span>\n                  </span>\n                </div>\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">Password</label>\n                  <input type=\"password\" onpaste=\"return false\" class=\"form-control\" formControlName=\"password\" placeholder=\"Password\">\n                  <span class=\"text-danger\" *ngIf=\"password.invalid && (password.touched || formValid)\">\n                    Please Enter Password\n                  </span>\n                </div>\n                <div class=\"d-grid mt-4 mb-4\">\n                  <button class=\"btn-login\" type=\"submit\"><span class=\"Login\">Login</span></button>\n                  <div class=\"text-center mt-3\">\n                    <p class=\"Lost\">Don't have an account? <span><a routerLink=\"/register\">Register Now</a></span></p>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </div>\n          <div class=\"text-center mb-3\">\n            <a routerLink=\"/privacyPolicy\" class=\"privacy-policy\" style=\"text-decoration: none;cursor:pointer;color: black;\">Privacy Policy</a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>", "import { NgIf } from \"@angular/common\"\nimport { Component, <PERSON><PERSON><PERSON>roy, OnInit } from \"@angular/core\"\nimport {\n  AbstractControl,\n  FormBuilder,\n  FormControl,\n  FormGroup,\n  ReactiveFormsModule,\n  ValidationErrors,\n  Validators,\n} from \"@angular/forms\"\nimport { Router, RouterModule } from \"@angular/router\"\nimport { NgToastService } from \"ng-angular-popup\"\nimport { Subscription } from \"rxjs\"\nimport { APP_CONFIG } from \"src/app/main/configs/environment.config\"\nimport { AuthService } from \"src/app/main/services/auth.service\"\n\n@Component({\n  selector: \"app-register\",\n  standalone: true,\n  imports: [ReactiveFormsModule, NgIf, RouterModule],\n  templateUrl: \"./register.component.html\",\n  styleUrls: [\"./register.component.css\"],\n})\nexport class RegisterComponent implements OnInit, OnDestroy {\n  private unsubscribe: Subscription[] = [];\n  \n  constructor(\n    private _fb: FormBuilder,\n    private _service: AuthService,\n    private _router: Router,\n    private _toast: NgToastService,\n  ) {}\n  registerForm: FormGroup\n  formValid: boolean\n  ngOnInit(): void {\n    this.createRegisterForm()\n  }\n  createRegisterForm() {\n    this.registerForm = this._fb.group(\n      {\n        firstName: [null, Validators.compose([Validators.required])],\n        lastName: [null, Validators.compose([Validators.required])],\n        phoneNumber: [\n          null,\n          Validators.compose([Validators.required, Validators.minLength(10), Validators.maxLength(10)]),\n        ],\n        emailAddress: [null, Validators.compose([Validators.required, Validators.email])],\n        password: [null, Validators.compose([Validators.required, Validators.minLength(5), Validators.maxLength(10)])],\n        confirmPassword: [null, Validators.compose([Validators.required])],\n      },\n      { validator: [this.passwordCompareValidator] },\n    )\n  }\n  passwordCompareValidator(fc: AbstractControl): ValidationErrors | null {\n    return fc.get(\"password\")?.value === fc.get(\"confirmPassword\")?.value ? null : { notmatched: true }\n  }\n  get firstName() {\n    return this.registerForm.get(\"firstName\") as FormControl\n  }\n  get lastName() {\n    return this.registerForm.get(\"lastName\") as FormControl\n  }\n  get phoneNumber() {\n    return this.registerForm.get(\"phoneNumber\") as FormControl\n  }\n  get emailAddress() {\n    return this.registerForm.get(\"emailAddress\") as FormControl\n  }\n  get password() {\n    return this.registerForm.get(\"password\") as FormControl\n  }\n  get confirmPassword() {\n    return this.registerForm.get(\"confirmPassword\") as FormControl\n  }\n  onSubmit() {\n    this.formValid = true\n    if (this.registerForm.valid) {\n      const register = this.registerForm.value\n      register.userType = \"user\"\n      \n      const registerSubscribe = this._service.registerUser(register).subscribe((data: any) => {\n        if (data.result == 1) {\n          //this.toastr.success(data.data);\n          this._toast.success({ detail: \"SUCCESS\", summary: data.data, duration: APP_CONFIG.toastDuration })\n          setTimeout(() => {\n            this._router.navigate([\"/\"])\n          }, 1000)\n        } else {\n          //this.toastr.error(data.message);\n          this._toast.error({ detail: \"ERROR\", summary: data.message, duration: APP_CONFIG.toastDuration })\n        }\n      })\n      this.formValid = false\n      this.unsubscribe.push(registerSubscribe)\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n}\n", "<div class=\"container-fluid ps-md-0\">\n  <div class=\"row g-0\">\n    <!-- Image section - hidden on small screens -->\n    <div class=\"d-none d-md-flex col-md-6 col-lg-8 bg-image position-relative\">\n      <img src=\"assets/Images/image.png\" alt=\"No Image\" class=\"w-100\">\n      <div class=\"carousel-caption\">\n        <p class=\"heading\">Sed ut perspiciatis unde omnis\n          iste natus voluptatem.</p>\n        <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna\n            aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint\n            occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>\n      </div>\n    </div>\n    <!-- Form section -->\n    <div class=\"col-12 col-md-6 col-lg-4\">\n      <div class=\"login d-flex align-items-center py-3\">\n        <div class=\"container\">\n          <div class=\"row justify-content-center\">\n            <div class=\"col-12 col-sm-10 col-md-11 col-lg-10\">\n              <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">First Name</label>\n                  <input type=\"text\" class=\"form-control\" formControlName=\"firstName\" placeholder=\"First Name\" autofocus>\n                  <span class=\"text-danger mb-0\" *ngIf=\"firstName.invalid &&  (firstName.touched || formValid)\">\n                    Please Enter FirstName\n                  </span>\n                </div>\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">Last Name (Surname)</label>\n                  <input type=\"text\" class=\"form-control\" formControlName=\"lastName\" placeholder=\"Last Name\">\n                  <span class=\"text-danger\" *ngIf=\"lastName.invalid &&  (lastName.touched || formValid)\">\n                    Please Enter LastName\n                  </span>\n                </div>\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">Phone Number</label>\n                  <input type=\"text\" class=\"form-control\" formControlName=\"phoneNumber\" placeholder=\"Phone Number\">\n                  <span class=\"text-danger\" *ngIf=\"phoneNumber.invalid &&  (phoneNumber.touched || formValid)\">\n                    <span *ngIf=\"phoneNumber.errors?.['required']\">\n                      Please Enter PhoneNumber\n                    </span>\n                    <span *ngIf=\"phoneNumber.errors?.['minLength']\">\n                      Please Enter Valid PhoneNumber\n                    </span>\n                    <span *ngIf=\"phoneNumber.errors?.['maxLength']\">\n                      Please Enter Valid PhoneNumber\n                    </span>\n                  </span>\n                </div>\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">Email Address</label>\n                  <input type=\"text\" class=\"form-control\" formControlName=\"emailAddress\" placeholder=\"Email Address\">\n                  <span class=\"text-danger\" *ngIf=\"emailAddress.invalid &&  (emailAddress.touched || formValid)\">\n                    <span *ngIf=\"emailAddress.hasError('required')\">\n                      Please Enter EmailAddress\n                    </span>\n                    <span *ngIf=\"emailAddress.hasError('email')\">\n                      Please Enter Valid EmailAddress\n                    </span>\n                  </span>\n                </div>\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">Password</label>\n                  <input type=\"password\" class=\"form-control\" formControlName=\"password\" placeholder=\"Password\">\n                  <span class=\"text-danger\" *ngIf=\"password.invalid && (password.touched || formValid)\">\n                        <span *ngIf=\"password.errors?.['required']\">\n                          Please Enter Password\n                        </span>\n                        <span *ngIf=\"password.errors?.['minLength']\">\n                          Password should not be less than 5 character\n                        </span>\n                        <span *ngIf=\"password.errors?.['maxLength']\">\n                          Password should not be greater than 10 character\n                        </span>\n                  </span>\n                </div>\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">Confirm Password</label>\n                  <input type=\"password\" class=\"form-control\" formControlName=\"confirmPassword\" placeholder=\"Confirm Password\">\n                  <span class=\"text-danger\" *ngIf=\"confirmPassword.invalid && (confirmPassword.touched || formValid)\">\n                      <span *ngIf=\"confirmPassword.hasError('required')\">\n                        Please Enter Confirm Password\n                      </span>\n                  </span>\n                  <span class=\"text-danger\" *ngIf=\"registerForm.hasError('notmatched') && confirmPassword.valid\">\n                      Password and Confirm Password not matched\n                  </span>\n                </div>\n                <div class=\"d-grid mt-4 mb-4\">\n                  <button class=\"btn-login\" type=\"submit\"><span class=\"Login\">Register</span></button>\n                  <div class=\"text-center mt-3\">\n                    <p class=\"Lost\">Already registered? <span><a routerLink=\"/\">Login Now</a></span></p>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </div>\n          <div class=\"text-center mb-3\">\n            <a routerLink=\"/privacyPolicy\" class=\"privacy-policy\" style=\"text-decoration: none;cursor:pointer;color: black;\">Privacy Policy</a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n", "import { NgIf } from \"@angular/common\"\nimport { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit } from \"@angular/core\"\nimport {\n  AbstractControl,\n  FormBuilder,\n  FormControl,\n  FormGroup,\n  ReactiveFormsModule,\n  ValidationErrors,\n  Validators,\n} from \"@angular/forms\"\nimport { ActivatedRoute, Router, RouterModule } from \"@angular/router\"\nimport { NgToastService } from \"ng-angular-popup\"\nimport { Subscription } from \"rxjs\"\nimport { APP_CONFIG } from \"src/app/main/configs/environment.config\"\nimport { AuthService } from \"src/app/main/services/auth.service\"\n\n@Component({\n  selector: \"app-reset-password\",\n  standalone: true,\n  imports: [ReactiveFormsModule, NgIf, RouterModule],\n  templateUrl: \"./reset-password.component.html\",\n  styleUrls: [\"./reset-password.component.css\"],\n})\nexport class ResetPasswordComponent implements OnInit, AfterViewInit, On<PERSON><PERSON>roy {\n  private unsubscribe: Subscription[] = [];\n  \n  constructor(\n    private _fb: FormBuilder,\n    private _service: AuthService,\n    private _router: Router,\n    private _activateRoute: ActivatedRoute,\n    private _toast: NgToastService,\n  ) {}\n  resetForm: FormGroup\n  formValid: boolean\n  userId\n  ngOnInit(): void {\n    this.resetFormCheck()\n  }\n  ngAfterViewInit() {\n    this._activateRoute.queryParams.subscribe((params) => {\n      if (params[\"Uid\"] != null) {\n        this.userId = params[\"Uid\"]\n      } else {\n        this._router.navigate([\"forgotPassword\"])\n        // this.toastr.error('Your Password Reset Link is Expired or Invalid');\n      }\n    })\n  }\n  resetFormCheck() {\n    this.resetForm = this._fb.group(\n      {\n        password: [null, Validators.compose([Validators.required, Validators.minLength(5), Validators.maxLength(10)])],\n        confirmPassword: [null, Validators.compose([Validators.required])],\n      },\n      { validator: [this.passwordCompareValidator] },\n    )\n  }\n  passwordCompareValidator(fc: AbstractControl): ValidationErrors | null {\n    return fc.get(\"password\")?.value === fc.get(\"confirmPassword\")?.value ? null : { notmatched: true }\n  }\n\n  get password() {\n    return this.resetForm.get(\"password\") as FormControl\n  }\n  get confirmPassword() {\n    return this.resetForm.get(\"confirmPassword\") as FormControl\n  }\n\n  onSubmit() {\n    this.formValid = true\n    if (this.resetForm.valid) {\n      const resetFormValue = this.resetForm.value\n      resetFormValue.Uid = this.userId\n      const resetPasswordSubscribe = this._service.resetPassword(resetFormValue).subscribe((data) => {\n        if (data == \"Failure\") {\n          //this.toastr.error('Something went wrong!');\n          this._toast.error({ detail: \"ERROR\", summary: \"Something went wrong!\", duration: APP_CONFIG.toastDuration })\n        } else {\n          //this.toastr.success(\"Password Changed Successfully.\");\n          this._toast.success({ detail: \"SUCCESS\", summary: \"Password Changed Successfully.\", duration: APP_CONFIG.toastDuration })\n          setTimeout(() => {\n            this._router.navigate([\"\"])\n          }, 2000)\n        }\n      })\n      this.formValid = false\n      this.unsubscribe.push(resetPasswordSubscribe)\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n}\n", "<div class=\"container-fluid ps-md-0\">\n  <div class=\"row g-0\">\n    <div class=\"d-flex col-md-6 col-lg-9 bg-image\">\n      <img src=\"assets/Images/image.png\" alt=\"No Image\">\n      <div class=\"carousel-caption d-md-block\">\n        <p class=\"heading\">Sed ut perspiciatis unde omnis\n          iste natus voluptatem.</p>\n        <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna\n            aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint\n            occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>\n    </div>\n    </div>\n    <div class=\"col-md-6 col-lg-3\">\n      <div class=\"login d-flex align-items-center\">\n        <div class=\"container\" >\n          <div class=\"row\">\n            <div class=\"col-md-9 col-lg-8\" style=\"margin-left: 7%;\">\n                <p class=\"Forgot\">New Password</p>\n                <div class=\"ForgotContent\">\n                    Please enter a new password in the fields below.\n                </div>\n                <form [formGroup]=\"resetForm\" (ngSubmit)=\"onSubmit()\">\n                <div class=\"form-group\">\n                  <label class=\"col-form-label\">New Password</label>\n                  <input type=\"password\" formControlName=\"password\" class=\"form-control\" placeholder=\"**********\" autofocus>\n                  <span class=\"text-danger\" *ngIf=\"password.invalid && (password.touched || formValid)\">\n                      <span *ngIf=\"password.errors?.['required']\">\n                        Please Enter Password\n                      </span>\n                      <span *ngIf=\"password.errors?.['minLength']\">\n                        Password should not be less than 5 character\n                      </span>\n                      <span *ngIf=\"password.errors?.['maxLength']\">\n                        Password should not be greater than 10 character\n                      </span>\n                  </span>\n                </div>\n                <div class=\"form-group\">\n                  <label class=\"col-form-label\">Confirm New Password</label>\n                  <input type=\"password\" class=\"form-control\" formControlName=\"confirmPassword\" placeholder=\"**********\">\n                  <span class=\"text-danger\" *ngIf=\"confirmPassword.invalid && (confirmPassword.touched || formValid)\">\n                    <span *ngIf=\"confirmPassword.errors?.['required']\">\n                      Please Enter Confirm Password\n                    </span>\n                  </span>\n                  <span class=\"text-danger\" *ngIf=\"resetForm.hasError('notmatched') && confirmPassword.valid\">\n                    Password and Confirm Password not matched\n                  </span>\n                </div>\n                <div class=\"d-grid mt-5\">\n                  <button class=\"btn-login\" type=\"submit\"><span class=\"Login\">Change Password</span></button>\n                  <div class=\"text-center\">\n                    <p class=\"small\" routerLink='/'>Login</p>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </div>\n          <div style=\"text-align: center;\">\n            <a routerLink=\"/privacyPolicy\" class=\"privacy-policy\" style=\"text-decoration: none;cursor:pointer;color: black;\">Privacy Policy</a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, OnDestroy, type OnInit } from '@angular/core';\nimport { Router, RouterModule } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { BsDropdownModule } from 'ngx-bootstrap/dropdown';\n@Component({\n  selector: 'app-navbar',\n  templateUrl: './navbar.component.html',\n  styleUrls: ['./navbar.component.css'],\n  standalone: true,\n  imports: [CommonModule, FormsModule, RouterModule, BsDropdownModule],\n})\nexport class NavbarComponent implements OnInit, OnDestroy {\n  isLogin = false;\n  userDetail: any;\n  loginUserId: any;\n  userImage: any;\n  userImages: any;\n  private unsubscribe: Subscription[] = [];\n\n  constructor(private _service: AuthService, private _router: Router) {}\n\n  ngOnInit(): void {\n    const currentUserSubscribe = this._service\n      .getCurrentUser()\n      .subscribe((data: any) => {\n        const userName = this._service.getUserDetail();\n        if (userName != null || data != null) {\n          this.isLogin = true;\n          data == null\n            ? (this.userDetail = userName.fullName)\n            : (this.userDetail = data.fullName);\n          data == null\n            ? (this.loginUserId = userName.userId)\n            : (this.loginUserId = data.userId);\n          data == null\n            ? (this.userImage =\n                this._service.imageUrl + '/' + userName.userImage)\n            : (this.userImage = this._service.imageUrl + '/' + data.userImage);\n        }\n      });\n    var tokenDetail = this._service.decodedToken();\n    if (tokenDetail.userType != 'user') {\n      this.isLogin = false;\n    }\n    this.unsubscribe.push(currentUserSubscribe);\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe());\n  }\n\n  redirectLogin() {\n    this._router.navigate(['']);\n  }\n\n  redirectRegister() {\n    this._router.navigate(['register']);\n  }\n\n  loggedOut() {\n    this._service.loggedOut();\n    this.isLogin = false;\n    this._router.navigate(['']);\n  }\n}\n", "<nav class=\"navbar navbar-expand-lg navbar-white bg-white\">\n    <div class=\"container-fluid\" style=\"padding-left: 20%;padding-right: 18%;\">\n      <button class=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarSupportedContent\" aria-controls=\"navbarSupportedContent\" aria-expanded=\"false\" aria-label=\"Toggle navigation\">\n        <span class=\"navbar-toggler-icon\"></span>\n      </button>\n      <div class=\"collapse navbar-collapse\" id=\"navbarSupportedContent\">\n        <ul class=\"navbar-nav me-auto mb-2 mb-lg-0\">\n  \n          <li class=\"nav-item\">\n            <a class=\"nav-link clicked\" routerLink=\"/../home\"><b>Virtual Community Support</b></a>\n          </li>\n          <li class=\"nav-item dropdown\" dropdown>\n            <a dropdownToggle class=\"nav-link dropdown-toggle\">\n              Policy\n            </a>\n            <ul *dropdownMenu class=\"dropdown-menu\" role=\"menu\">\n              <li role=\"menuitem\"><a class=\"dropdown-item\" routerLink=\"/../privacyPolicy\" >Policy</a></li>\n            </ul>\n          </li>\n        </ul>\n        <ul class=\"navbar-nav ms-auto mb-2 mb-lg-0 profile-menu\">\n          <li class=\"nav-item\" *ngIf=\"!isLogin\">\n            <a class=\"nav-link\" style=\"cursor:pointer;\" (click)=\"redirectLogin()\">Login</a>\n          </li>\n          <li class=\"nav-item\" *ngIf=\"!isLogin\">\n            <a class=\"nav-link\" style=\"cursor:pointer;\" (click)=\"redirectRegister()\">Register</a>\n          </li>\n          <li class=\"nav-item dropdown\" dropdown *ngIf=\"isLogin\">\n            <a dropdownToggle class=\"nav-link dropdown-toggle\">\n              <img src=\"{{userImage}}\" onerror=\"this.src='assets/NoUser.png'\" class=\"userImg\" alt=\"No Image\"/>&nbsp;&nbsp;{{userDetail}}\n               <span class=\"caret\"></span>\n            </a>\n            <ul *dropdownMenu class=\"dropdown-menu\" role=\"menu\">\n              <li role=\"menuitem\"><a class=\"dropdown-item\" routerLinkActive='active' routeLinkActiveOptions=\"{exact:true}\" routerLink=\"../userProfile/{{loginUserId}}\" >My Profile</a></li>\n              <li class=\"divider dropdown-divider\"></li>\n              <li role=\"menuitem\"><a class=\"dropdown-item\" routerLinkActive='active' (click)=\"loggedOut()\">Log Out</a></li>\n              <!-- <li role=\"menuitem\" (click)=\"loggedOut()\"><a class=\"dropdown-item\">Log Out</a></li> -->\n            </ul>\n          </li>\n       </ul>\n      </div>\n    </div>\n  </nav>\n  ", "import { Component, type OnInit } from '@angular/core';\nimport { NavbarComponent } from '../navbar/navbar.component';\nimport { CommonModule } from '@angular/common';\nimport { SearchingSortingComponent } from '../searching-sorting/searching-sorting.component';\n\n@Component({\n  selector: 'app-new-mission',\n  templateUrl: './new-mission.component.html',\n  styleUrls: ['./new-mission.component.css'],\n  standalone: true,\n  imports: [NavbarComponent, CommonModule, SearchingSortingComponent]\n})\nexport class NewMissionComponent implements OnInit {\n\n  ngOnInit(): void {}\n  displayStyle = 'none';\n\n  openPopup() {\n    this.displayStyle = 'block';\n  }\n  closePopup() {\n    this.displayStyle = 'none';\n  }\n}", "<div>\n  <app-navbar></app-navbar>\n  <app-searching-sorting></app-searching-sorting>\n  </div>\n  <button\n  style=\"margin: 50px; padding: 10px\"\n  type=\"button\"\n  class=\"btn btn-primary\"\n  (click)=\"openPopup()\">Show Data\n</button>\n  <div class=\"modal\" tabindex=\"-1\" role=\"dialog\" [ngStyle]=\"{'display':displayStyle}\" >\n  <div class=\"modal-dialog\" role=\"document\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h4 class=\"modal-title\">Add New Mission</h4>\n      </div>\n      <div class=\"modal-body\">\n            <div class=\"row\">\n              <div class=\"col-sm-6\">\n                  <label class=\"col-form-label\">Country</label>\n                  <select class=\"form-select\">\n                    <option value=\"\">Select Country</option>\n                  </select>\n              </div>\n              <div class=\"col-sm-6\">\n                <label class=\"col-form-label\">City</label>\n                <select class=\"form-select\">\n                  <option value=\"\">Select City</option>\n                </select>\n              </div>\n            </div>\n            <div class=\"form-group\">\n                <label class=\"col-form-label\">Mission Title</label>\n                <input type=\"text\" class=\"form-control\" placeholder=\"Enter Mission Title\">\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Mission Description</label>\n              <textarea class=\"form-control\" row=\"3\" placeholder=\"Enter your message\"></textarea>\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Mission Organisation Name</label>\n              <input type=\"text\" class=\"form-control\" placeholder=\"Enter mission organisation name\">\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Mission Organisation Detail</label>\n              <textarea class=\"form-control\" row=\"3\" placeholder=\"Enter your message\"></textarea>\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Start Date</label>\n              <input type=\"date\" class=\"form-control\" placeholder=\"Select start date\">\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">End Date</label>\n              <input type=\"date\" class=\"form-control\" placeholder=\"Select end date\">\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Total seats</label>\n              <input type=\"text\" class=\"form-control\" placeholder=\"Enter total seats\">\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Mission registration deadline</label>\n              <input type=\"text\" class=\"form-control\" placeholder=\"Enter mission registration deadline\">\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Mission Theme</label>\n                  <select class=\"form-select\">\n                    <option value=\"\">Select mission theme</option>\n                  </select>\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Mission Skills</label>\n                  <select class=\"form-select\">\n                    <option value=\"\">Select mission skills</option>\n                  </select>\n            </div>\n            <div class=\"form-group\">\n                <label class=\"col-form-label\">Mission Images</label>\n                <div class=\"Neon Neon-theme-dragdropbox\">\n                  <input style=\"z-index: 999; opacity: 0; width: 320px; height: 200px; position: absolute; right: 0px; left: 0px; margin-right: auto; margin-left: auto;\" name=\"files[]\" id=\"filer_input2\" multiple=\"multiple\" type=\"file\">\n                   <div class=\"Neon-input-dragDrop\">\n                     <div class=\"Neon-input-inner\">\n                       <div class=\"Neon-input-icon\">\n                         <i class=\"fa fa-upload\"></i>\n                       </div>\n                       <div class=\"Neon-input-text\">\n                         <h6>Upload image here</h6>\n                       </div>\n                     </div>\n                   </div>\n               </div>\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Mission Documents</label>\n                  <div class=\"Neon Neon-theme-dragdropbox\">\n                     <input style=\"z-index: 999; opacity: 0; width: 320px; height: 200px; position: absolute; right: 0px; left: 0px; margin-right: auto; margin-left: auto;\" name=\"files[]\" id=\"filer_input2\" multiple=\"multiple\" type=\"file\">\n                      <div class=\"Neon-input-dragDrop\">\n                        <div class=\"Neon-input-inner\">\n                          <div class=\"Neon-input-icon\">\n                            <i class=\"fa fa-upload\"></i>\n                          </div>\n                          <div class=\"Neon-input-text\">\n                            <h6>Upload resume here or click</h6>\n                          </div>\n                        </div>\n                      </div>\n                  </div>\n            </div>\n            <div class=\"form-group\">\n              <label class=\"col-form-label\">Mission Avilability</label>\n                  <select class=\"form-select\">\n                    <option value=\"\">Select mission avilability</option>\n                  </select>\n            </div>\n      </div>\n      <div class=\"modal-footer\" style=\"display: block;\">\n        <button type=\"button\" class=\"btnCancel\" (click)=\"closePopup()\"><span class=\"Cancel\"> Cancel</span> </button>\n        <button type=\"button\" class=\"btnSave\"><span class=\"Save\">Save</span></button>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, type OnInit } from '@angular/core';\nimport { NavbarComponent } from '../navbar/navbar.component';\nimport { FooterComponent } from '../footer/footer.component';\n\n@Component({\n  selector: 'app-privacy-policy',\n  templateUrl: './privacy-policy.component.html',\n  styleUrls: ['./privacy-policy.component.css'],\n  standalone: true,\n  imports: [NavbarComponent, FooterComponent]\n})\nexport class PrivacyPolicyComponent implements OnInit {\n  constructor() {}\n\n  ngOnInit(): void {}\n}", "<!-- <div>\n  <app-navbar></app-navbar>\n  <hr style=\"margin: 0rem 0 !important\"/>\n</div>\n<div class=\"container\">\n  <div class=\"row\">\n      Privacy Policy\n  </div>\n</div> -->\n<div>\n  <app-navbar></app-navbar>\n  <hr style=\"margin: 0rem 0 !important\"/>\n</div>\n<div class=\"container\">\n<div class=\"accordion row\" id=\"accordionExample\" style=\"height:100%;width:auto;\">\n  <div class=\"col-sm-3\">\n    <div class=\"accordion-item\">\n      <h2 class=\"accordion-header\" id=\"headingOne\">\n        <button class=\"accordion-button \" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#collapseOne\" aria-expanded=\"false\" aria-controls=\"collapseOne\">\n          Introduction\n        </button>\n      </h2>\n    </div>\n    <div class=\"accordion-item\">\n      <h2 class=\"accordion-header\" id=\"headingTwo\">\n        <button class=\"accordion-button\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#collapseTwo\" aria-expanded=\"false\" aria-controls=\"collapseTwo\">\n          How does use cookies?\n        </button>\n      </h2>\n    </div>\n    <div class=\"accordion-item\">\n      <h2 class=\"accordion-header\" id=\"headingThree\">\n        <button class=\"accordion-button\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#collapseThree\" aria-expanded=\"false\" aria-controls=\"collapseThree\">\n          How to manage your cookie\n          Preferences?\n        </button>\n      </h2>\n      </div>\n  </div>\n  <div class=\"col-sm-9\">\n    <div id=\"collapseOne\" class=\"accordion-collapse collapse show\" aria-labelledby=\"headingOne\" data-bs-parent=\"#accordionExample\">\n      <div class=\"accordion-body\">\n        <h2 class=\"mb-3\">Introduction</h2>\n          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n          <br><br>\n          Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.\n          <br><br>\n          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n\n      </div>\n    </div>\n    <br>\n    <hr><br><br>\n    <div id=\"collapseTwo\" class=\"accordion-collapse collapse show\" aria-labelledby=\"headingTwo\" data-bs-parent=\"#accordionExample\">\n      <div class=\"accordion-body\">\n        <h2 class=\"mb-3\">How does use cookies?</h2>\n        Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.\n        <br><br>\n        <strong>Why Optimy Uses These Technologies?</strong><br>\n        <p style=\"margin-top: 4px;\"> We use these technologies for a number of purposes, such as:</p>\n        <div>\n          <ul>\n            <li>But I must explain to you how all this mistaken idea of denouncing pleasure and praising pain.</li>\n            <li>\n              At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque\n              excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi.</li>\n              <li>On the other hand, we denounce with righteous indignation and dislike men who are so beguiled and demoralized\n              But I must explain to you how all this mistaken idea of denouncing pleasure and praising pain.</li>\n              <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n    <br>\n    <hr>\n    <br><br>\n    <div id=\"collapseThree\" class=\"accordion-collapse collapse show\" aria-labelledby=\"headingThree\" data-bs-parent=\"#accordionExample\">\n      <div class=\"accordion-body\">\n        <h2 class=\"mb-3\">How to manage your cookie preferences?</h2>\n        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n        <br><br>\n        Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur.\n        <br><br>\n        <h4>Subtitle goes here</h4>\n        <div class=\"subtitle mb-5\">\n          <ul>\n            <li>1. But I must explain to you how all this mistaken idea of denouncing pleasure and praising pain.\n              <ul>\n                <li>1.1. On the other hand, we denounce with righteous indignation and dislike men who are so beguiled.</li>\n                <li>1.2. On the other hand, we denounce with righteous indignation and dislike men who are so beguiled.</li>\n              </ul>\n            </li>\n            <li>\n              2. At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque\n              excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi.\n            </li>\n            <li>\n              3. On the other hand, we denounce with righteous indignation and dislike men who are so beguiled and demoralized\n            </li>\n            <li>\n              4. But I must explain to you how all this mistaken idea of denouncing pleasure and praising pain.\n            </li>\n            <li>\n              5. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore\n            </li>\n          </ul>\n        </div>\n        <div class=\"heading mb-4\">\n          <h1>The first level heading - H1</h1>\n          <h2>The second level heading - H2</h2>\n          <h3>The third level heading - H3</h3>\n          <h4>The forth level heading - H4</h4>\n          <h5>The fifth level heading - H5</h5>\n          <h6>The sixth level heading - H6</h6>\n        </div>\n          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n      </div>\n    </div>\n  </div>\n</div>\n</div>\n\n\n  <app-footer></app-footer>\n", "import { Component, OnD<PERSON>roy, type OnInit } from '@angular/core';\nimport { NgToastService } from 'ng-angular-popup';\nimport { CommonService } from '../../services/common.service';\nimport { APP_CONFIG } from '../../configs/environment.config';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-searching-sorting',\n  templateUrl: './searching-sorting.component.html',\n  styleUrls: ['./searching-sorting.component.css'],\n  standalone: true,\n  imports: [CommonModule]\n})\nexport class SearchingSortingComponent implements OnInit, OnDestroy {\n  missionCountryList: any[] = [];\n  missionCityList: any[] = [];\n  missionThemeList: any[] = [];\n  missionSkillList: any[] = [];\n  private unsubscribe: Subscription[] = [];\n  \n  constructor(private _service: CommonService, private _toast: NgToastService) {}\n  \n  ngOnInit(): void {\n    this.getMissionCountryList();\n    this.getMissionCityList();\n    this.getMissionThemeList();\n    this.getMissionSkillList();\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n\n  getMissionCountryList() {\n    const missionCountryListSubscribe = this._service.getMissionCountryList().subscribe((data: any) => {\n      if (data.result == 1) {\n        this.missionCountryList = data.data;\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(missionCountryListSubscribe);\n  }\n\n  getMissionCityList() {\n    const missionCityListSubscribe = this._service.getMissionCityList().subscribe((data: any) => {\n      if (data.result == 1) {\n        this.missionCityList = data.data;\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(missionCityListSubscribe);\n  }\n\n  getMissionThemeList() {\n    const missionThemeListSubscribe = this._service.getMissionThemeList().subscribe((data: any) => {\n      if (data.result == 1) {\n        this.missionThemeList = data.data;\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(missionThemeListSubscribe);\n  }\n\n  getMissionSkillList() {\n    const missionSkillListSubscribe = this._service.getMissionSkillList().subscribe((data: any) => {\n      if (data.result == 1) {\n        this.missionSkillList = data.data;\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(missionSkillListSubscribe);\n  }\n\n  onTextChange(text: any) {\n    this._service.searchList.next(text);\n  }\n\n  onChange(e: any) {\n    this._service.searchList.next(e.target.value);\n  }\n}", "<div class=\"Layer-63-copy row\">\n\n  <div class=\"col-sm-4 pipeborder\">\n    <div class=\"input-field\">\n      <span class=\"fa fa-search p-2\"></span>\n      <input type=\"text\" class=\"form-control\" #txtSearch (keyup)=\"onTextChange(txtSearch.value)\" placeholder=\"Search Mission...\" />\n    </div>\n  </div>\n  <div class=\"col-sm-1 pipeborder\">\n    <select class=\"form-select border-0\" (change)=\"onChange($event)\">\n      <option value=\"\">Country</option>\n      <option *ngFor=\"let item of missionCountryList\" value=\"{{item.text}}\">{{item.text}}</option>\n    </select>\n  </div>\n  <div class=\"col-sm-1 pipeborder\">\n    <select class=\"form-select border-0\" (change)=\"onChange($event)\">\n      <option value=\"\">City</option>\n      <option *ngFor=\"let item of missionCityList\" value=\"{{item.text}}\">{{item.text}}</option>\n    </select>\n  </div>\n  <div class=\"col-sm-1 pipeborder\">\n    <select class=\"form-select border-0\" (change)=\"onChange($event)\">\n      <option value=\"\">Theme</option>\n      <option *ngFor=\"let item of missionThemeList\" value=\"{{item.text}}\">{{item.text}}</option>\n    </select>\n  </div>\n  <div class=\"col-sm-1 pipeborder\">\n    <select class=\"form-select border-0\" (change)=\"onChange($event)\">\n      <option value=\"\">Skills</option>\n      <option *ngFor=\"let item of missionSkillList\" value=\"{{item.text}}\">{{item.text}}</option>\n    </select>\n  </div>\n</div>\n", "import {\n  <PERSON>mpo<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  type OnInit,\n  type QueryList,\n  ViewChildren,\n} from '@angular/core';\nimport {\n  AbstractControl,\n  FormBuilder,\n  FormGroup,\n  FormsModule,\n  NgForm,\n  ReactiveFormsModule,\n  ValidationErrors,\n  Validators,\n} from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport {\n  ActionName,\n  ListBoxComponent,\n  ListBoxModule,\n  ListBoxToolbarConfig,\n} from '@progress/kendo-angular-listbox';\nimport { NgToastService } from 'ng-angular-popup';\nimport { ToastrService } from 'ngx-toastr';\nimport ValidateForm from '../../helpers/validate-form.helper';\n\nimport { ClientService } from '../../services/client.service';\nimport { AuthService } from '../../services/auth.service';\nimport { APP_CONFIG } from '../../configs/environment.config';\nimport { CommonService } from '../../services/common.service';\nimport { ContactUs, ChangePassword } from '../../interfaces/user.interface';\nimport { NavbarComponent } from '../navbar/navbar.component';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\ndeclare var window: any;\n@Component({\n  selector: 'app-user-edit-profile',\n  templateUrl: './user-edit-profile.component.html',\n  styleUrls: ['./user-edit-profile.component.css'],\n  standalone: true,\n  imports: [FormsModule, ListBoxModule, ReactiveFormsModule, NavbarComponent, CommonModule]\n})\nexport class UserEditProfileComponent implements OnInit, OnDestroy {\n  changePasswordModal: any;\n  addyourSkillModal: any;\n  contactUsModal: any;\n  loginUserId: any;\n  loginDetail: any;\n  loginName: any;\n  loginUserDetails: any;\n  countryList: any[] = [];\n  cityList: any[] = [];\n  skillList: any[] = [];\n  skillList1: any[] = [];\n  userSkillList: any[] = [];\n  isFileUpload = false;\n  userImage: any = '';\n  formData = new FormData();\n  userProfileForm: FormGroup;\n  userId: any;\n  editData: any;\n  firstName: any;\n  lastName: any;\n  contactUsForm: any;\n  private unsubscribe: Subscription[] = [];\n\n  constructor(\n    private _commonService: CommonService,\n    private _service: ClientService,\n    private _loginService: AuthService,\n    private _router: Router,\n    private _toast: NgToastService,\n    private _fb: FormBuilder,\n    private _activateRouter: ActivatedRoute\n  ) {\n    this.userId = this._activateRouter.snapshot.paramMap.get('userId');\n  }\n\n  public data: string[] = [\n    'Anthropology',\n    'Archeology',\n    'Astronomy',\n    'Computer Science',\n    'Environmental Science',\n    'History',\n    'Library Sciences',\n    'Mathematics',\n    'Music Theory',\n    'Research',\n    'Administrative Support',\n    'Customer Service',\n    'Data Entry',\n    'Executive Admin',\n    'Office Management',\n    'Office Reception',\n    'Program Management',\n    'Transactions',\n    'Agronomy',\n    'Animal Care / Handling',\n    'Animal Therapy',\n    'Aquarium Maintenance',\n    'Botany',\n    'Environmental Education',\n    'Environmental Policy',\n    'Farming',\n  ];\n\n  public data1: string[] = [\n    'Computer Science',\n    'Data Entry',\n    'Office Management',\n  ];\n\n  public toolbarSettings: ListBoxToolbarConfig = {\n    position: 'right',\n    tools: ['transferTo', 'transferFrom'],\n  };\n\n  ngOnInit(): void {\n    this._loginService.getCurrentUser().subscribe((data: any) => {\n      this.loginDetail = this._loginService.getUserDetail();\n      data == null\n        ? (this.loginUserId = this.loginDetail.userId)\n        : (this.loginUserId = data.userId);\n      data == null\n        ? (this.loginName = this.loginDetail.fullName)\n        : (this.loginName = data.fullName);\n      data == null\n        ? (this.firstName = this.loginDetail.firstName)\n        : (this.firstName = data.firstName);\n      data == null\n        ? (this.lastName = this.loginDetail.lastName)\n        : (this.lastName = data.lastName);\n      data == null\n        ? (this.contactUs.userId = this.loginDetail.userId)\n        : (this.contactUs.userId = data.userId);\n      data == null\n        ? (this.contactUs.name = this.loginDetail.fullName)\n        : (this.contactUs.name = data.fullName);\n      data == null\n        ? (this.contactUs.emailAddress = this.loginDetail.emailAddress)\n        : (this.contactUs.emailAddress = data.emailAddress);\n    });\n\n    this.userFormCheckValid();\n    this.loginUserDetailByUserId(this.loginUserId);\n\n    this.getUserSkill();\n    this.fetchData(this.userId);\n\n    this.getCountryList();\n\n    this.changePasswordModal = new window.bootstrap.Modal(\n      document.getElementById('changePasswordModal')\n    );\n    this.addyourSkillModal = new window.bootstrap.Modal(\n      document.getElementById('addSkillModal')\n    );\n    this.contactUsModal = new window.bootstrap.Modal(\n      document.getElementById('contactUsModal')\n    );\n  }\n  @ViewChildren(ListBoxComponent)\n  private listbox: QueryList<ListBoxComponent>;\n\n  public onSubmitSkillModal(event: ActionName): void {\n    let data = [],\n      data1 = [];\n    if (this.listbox && this.listbox.length) {\n      this.listbox.forEach((item: ListBoxComponent, index: number) => {\n        if (index === 0) {\n          data = item.data;\n        } else {\n          data1 = item.data;\n        }\n      });\n      this.skillList1 = data1;\n    }\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n\n  saveSkill() {\n    const value = {\n      skill: this.skillList1.join(','),\n      userId: this.loginUserId,\n    };\n    const addUserSkillSubscribe = this._commonService.addUserSkill(value).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this._toast.success({ detail: 'SUCCESS', summary: data.data });\n          setTimeout(() => {\n            this.closeAddYourSkillModal();\n          }, 1000);\n        } else {\n          this._toast.error({ detail: 'ERROR', summary: data.message });\n        }\n      },\n      (err) => this._toast.error({ detail: 'ERROR', summary: err.message })\n    );\n    this.unsubscribe.push(addUserSkillSubscribe);\n  }\n\n  getUserSkill() {\n    const userSkillSubscirbe = this._commonService.getUserSkill(this.loginUserId).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          if (data.data.length > 0) {\n            this.userSkillList = data.data;\n            this.userSkillList = this.userSkillList[0].text.split(',');\n          } else {\n            this.userSkillList = this.data1;\n          }\n        } else {\n          this._toast.error({ detail: 'ERROR', summary: data.message });\n        }\n      },\n      (err) => this._toast.error({ detail: 'ERROR', summary: err.message })\n    );\n    this.unsubscribe.push(userSkillSubscirbe);\n  }\n\n  getCountryList() {\n    const countryListSubscribe = this._commonService.countryList().subscribe((data: any) => {\n      if (data.result == 1) {\n        this.countryList = data.data;\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(countryListSubscribe);\n  }\n\n  getCityList(countryId: any) {\n    countryId = countryId.target.value;\n    const cityListSubscribe = this._commonService.cityList(countryId).subscribe((data: any) => {\n      if (data.result == 1) {\n        this.cityList = data.data;\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(cityListSubscribe);\n  }\n\n  loginUserDetailByUserId(id: any) {\n    const userDetailSubscribe = this._service.loginUserDetailById(id).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.loginUserDetails = data.data;\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(userDetailSubscribe);\n  }\n\n  onSelectImage(event: any) {\n    if (event.target.files && event.target.files[0]) {\n      this.formData = new FormData();\n      var reader = new FileReader();\n      reader.readAsDataURL(event.target.files[0]);\n      reader.onload = (e: any) => {\n        this.userImage = e.target.result;\n      };\n\n      for (let i = 0; i < event.target.files.length; i++) {\n        this.formData.append('file', event.target.files[i]);\n        this.formData.append('moduleName', 'UserImage');\n      }\n      this.isFileUpload = true;\n    }\n  }\n\n  userFormCheckValid() {\n    this.userProfileForm = this._fb.group({\n      id: [0],\n      name: [\n        this.firstName,\n        Validators.compose([Validators.required, Validators.maxLength(16)]),\n      ],\n      surname: [\n        this.lastName,\n        Validators.compose([Validators.required, Validators.maxLength(16)]),\n      ],\n      employeeId: [''],\n      manager: [''],\n      title: ['', Validators.compose([Validators.maxLength(255)])],\n      department: ['', Validators.compose([Validators.maxLength(16)])],\n      myProfile: [null, Validators.compose([Validators.required])],\n      whyIVolunteer: [''],\n      countryId: [null, Validators.compose([Validators.required])],\n      cityId: [null, Validators.compose([Validators.required])],\n      avilability: [''],\n      linkdInUrl: [''],\n      mySkills: ['', Validators.compose([Validators.required])],\n      userImage: ['', Validators.compose([Validators.required])],\n      userId: [''],\n    });\n  }\n\n  fetchData(id: any) {\n    const userProfileSubscribe = this._service.getUserProfileDetailById(id).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.editData = data.data;\n          if (this.editData != undefined) {\n            this.userProfileForm = this._fb.group({\n              id: [this.editData.id],\n              name: [\n                this.editData.name,\n                Validators.compose([Validators.required]),\n              ],\n              surname: [\n                this.editData.surname,\n                Validators.compose([Validators.required]),\n              ],\n              employeeId: [this.editData.employeeId],\n              manager: [this.editData.manager],\n              title: [this.editData.title],\n              department: [this.editData.department],\n              myProfile: [\n                this.editData.myProfile,\n                Validators.compose([Validators.required]),\n              ],\n              whyIVolunteer: [this.editData.whyIVolunteer],\n              countryId: [\n                this.editData.countryId,\n                Validators.compose([Validators.required]),\n              ],\n              cityId: [\n                this.editData.cityId,\n                Validators.compose([Validators.required]),\n              ],\n              avilability: [this.editData.avilability],\n              linkdInUrl: [this.editData.linkdInUrl],\n              mySkills: [\n                this.editData.mySkills.split(','),\n                Validators.compose([Validators.required]),\n              ],\n              userImage: [''],\n              userId: [this.editData.userId],\n            });\n            const cityListSubscribe = this._commonService\n              .cityList(this.editData.countryId)\n              .subscribe((data: any) => {\n                this.cityList = data.data;\n              });\n            if (this.editData.userImage) {\n              this.userImage =\n                this._service.imageUrl + '/' + this.editData.userImage;\n            }\n            this.unsubscribe.push(userProfileSubscribe, cityListSubscribe);\n          }\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    \n  }\n\n  async onSubmit() {\n    let imageUrl = '';\n    const formValue = this.userProfileForm.value;\n    formValue.userId = this.userId;\n    if (this.userProfileForm.valid) {\n      if (this.isFileUpload) {\n         await this._commonService\n          .uploadImage(this.formData)\n          .pipe()\n          .toPromise()\n          .then(\n            (res: any) => {\n              if (res.success) {\n                imageUrl = res.data[0];\n              }\n            },\n            (err) => {\n              this._toast.error({\n                detail: 'ERROR',\n                summary: err.message,\n                duration: APP_CONFIG.toastDuration,\n              });\n            }\n          );\n      }\n      if (this.isFileUpload) {\n        formValue.userImage = imageUrl;\n      } else {\n        formValue.userImage = this.editData.userImage;\n      }\n\n      const mySkillLists = formValue.mySkills.join(',');\n      formValue.mySkills = mySkillLists;\n      formValue.status = true;\n      const userProfileUpdateSubscribe = this._service.loginUserProfileUpdate(formValue).subscribe(\n        (res: any) => {\n          if (res.result == 1) {\n            this._toast.success({\n              detail: 'SUCCESS',\n              summary: res.data,\n              duration: APP_CONFIG.toastDuration,\n            });\n            setTimeout(() => {\n              this._router.navigate(['home']);\n            }, 1000);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: res.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        },\n        (err) => {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: err.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      );\n      this.unsubscribe.push(userProfileUpdateSubscribe);\n    } else {\n      ValidateForm.validateAllFormFields(this.userProfileForm);\n    }\n\n  }\n  contactUs: ContactUs;\n  changePass: ChangePassword;\n\n  onSubmitContactUs(form: NgForm) {\n    form.value.userId = this.contactUs.userId;\n    form.value.name = this.contactUs.name;\n    form.value.emailAddress = this.contactUs.emailAddress;\n    const contactUsSubscribe = this._commonService.contactUs(form.value).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data,\n            duration: APP_CONFIG.toastDuration,\n          });\n          setTimeout(() => {\n            form.value.subject = '';\n            form.value.message = '';\n            this.closeContactUsModal();\n          }, 1000);\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(contactUsSubscribe);\n  }\n\n  passwordCompareValidator(fc: AbstractControl): ValidationErrors | null {\n    return fc.get('newPassword')?.value === fc.get('confirmPassword')?.value\n      ? null\n      : { notmatched: true };\n  }\n\n  onSubmitChangePassword(changePasswordForm: NgForm) {\n    const value = changePasswordForm.value;\n    value.userId = this.loginUserId;\n    if (changePasswordForm.valid) {\n      const changePasswordSubscribe = this._loginService.changePassword(value).subscribe(\n        (data: any) => {\n          if (data.result == 1) {\n            this._toast.success({\n              detail: 'SUCCESS',\n              summary: data.data,\n              duration: APP_CONFIG.toastDuration,\n            });\n            setTimeout(() => {\n              this.closeChangePasswordModal();\n              this._loginService.loggedOut();\n              this._router.navigate(['']);\n            }, 1000);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        },\n        (err) =>\n          this._toast.error({\n            detail: 'ERROR',\n            summary: err.message,\n            duration: APP_CONFIG.toastDuration,\n          })\n      );\n      this.unsubscribe.push(changePasswordSubscribe);\n    }\n  }\n\n  onCancel() {\n    this._router.navigate(['/']);\n  }\n\n  openChangePasswordModal() {\n    this.changePasswordModal.show();\n  }\n\n  closeChangePasswordModal() {\n    this.changePasswordModal.hide();\n  }\n\n  openAddYourSkillModal() {\n    this.addyourSkillModal.show();\n    this.data1 = this.userSkillList;\n  }\n\n  closeAddYourSkillModal() {\n    this.addyourSkillModal.hide();\n    window.location.reload();\n  }\n\n  openContactUsModal() {\n    this.contactUsModal.show();\n  }\n\n  closeContactUsModal() {\n    this.contactUsModal.hide();\n  }\n}", "<div>\n  <app-navbar></app-navbar>\n  <hr style=\"margin: 0rem 0 !important\"/>\n</div>\n<div class=\"container-fluid\">\n  <form [formGroup]=\"userProfileForm\">\n<div class=\"row\">\n  <input type=\"hidden\" formControlName=\"id\">\n  <div class=\"col-sm-4 detail\">\n      <img src=\"{{userImage}}\" onerror=\"this.src='assets/NoUser.png'\" alt=\"NoImage\" class=\"userImage\" (click)=\"userImg.click()\" style=\"cursor: pointer;\">\n      <input type=\"file\" #userImg style=\"display: none;\" (change)=\"onSelectImage($event)\" formControlName=\"userImage\">\n      <!-- <span class=\"text-danger\" *ngIf=\"userProfileForm.controls['userImage'].dirty && userProfileForm.hasError('required','userImage')\">Please Select UserImage</span> -->\n      <p class=\"userName\">{{loginName}}</p>\n      <p class=\"changepassword\" (click)=\"openChangePasswordModal()\">Change Password</p>\n  </div>\n  <div class=\"col-sm-8 basicInfo\">\n    <p>Basic Information</p>\n    <hr style=\"width: 100%;margin-bottom: 2%;\"/>\n    <div class=\"row\">\n      <div class=\"form-group row\">\n          <div class=\"col-sm-6\">\n            <label class=\"col-form-label\">Name<span class=\"text-success\">*</span></label>\n            <input type=\"text\" class=\"form-control\" placeholder=\"Enter your name\" formControlName=\"name\">\n            <span class=\"text-danger\" *ngIf=\"userProfileForm.controls['name'].dirty && userProfileForm.hasError('required','name')\">Please Enter Name</span>\n            <span class=\"text-danger\" *ngIf=\"userProfileForm.hasError('maxLength','name')\">Maximum 16 characters are allowed</span>\n          </div>\n          <div class=\"col-sm-6\">\n            <label class=\"col-form-label\">Surname<span class=\"text-success\">*</span></label>\n            <input type=\"text\" class=\"form-control\" placeholder=\"Enter your surname\" formControlName=\"surname\">\n            <span class=\"text-danger\" *ngIf=\"userProfileForm.controls['surname'].dirty && userProfileForm.hasError('required','surname')\">Please Enter SurName</span>\n            <span class=\"text-danger\" *ngIf=\"userProfileForm.hasError('maxLength','surname')\">Maximum 16 characters are allowed</span>\n          </div>\n      </div>\n      <div class=\"form-group row mt-1\">\n        <div class=\"col-sm-6\">\n          <label class=\"col-form-label\">Employee ID</label>\n          <input type=\"text\" class=\"form-control\" placeholder=\"Enter your employee id\" formControlName=\"employeeId\">\n        </div>\n        <div class=\"col-sm-6\">\n          <label class=\"col-form-label\">Manager</label>\n          <input type=\"text\" class=\"form-control\" placeholder=\"Enter your manager details\" formControlName=\"manager\">\n        </div>\n      </div>\n      <div class=\"form-group row mt-1\">\n        <div class=\"col-sm-6\">\n          <label class=\"col-form-label\">Title</label>\n          <input type=\"text\" class=\"form-control\" placeholder=\"Enter your title\" formControlName=\"title\">\n          <span class=\"text-danger\" *ngIf=\"userProfileForm.hasError('maxLength','title')\">Maximum 255 characters are allowed</span>\n        </div>\n        <div class=\"col-sm-6\">\n          <label class=\"col-form-label\">Department</label>\n          <input type=\"text\" class=\"form-control\" placeholder=\"Enter your department\" formControlName=\"department\">\n          <span class=\"text-danger\" *ngIf=\"userProfileForm.hasError('maxLength','department')\">Maximum 16 characters are allowed</span>\n        </div>\n      </div>\n      <div class=\"form-group row mt-1\">\n        <div class=\"col-sm-12\">\n          <label class=\"col-form-label\">My Profile<span class=\"text-success\">*</span></label>\n          <textarea class=\"form-control\" rows=\"5\" placeholder=\"Enter your comments...\" formControlName=\"myProfile\"></textarea>\n          <span class=\"text-danger\" *ngIf=\"userProfileForm.controls['myProfile'].dirty && userProfileForm.hasError('required','myProfile')\">Please Enter MyProfile</span>\n        </div>\n      </div>\n      <div class=\"form-group row mt-1\">\n        <div class=\"col-sm-12\">\n          <label class=\"col-form-label\">Why I Volunteer?</label>\n          <textarea class=\"form-control\" rows=\"5\" placeholder=\"Enter your comments...\" formControlName=\"whyIVolunteer\"></textarea>\n        </div>\n      </div>\n      <div class=\"col-sm-12 basicInfo\">\n        <p>Address Information</p>\n        <hr style=\"width: 100%;margin-bottom: 2%;\"/>\n        <div class=\"row\">\n          <div class=\"form-group row\">\n            <div class=\"col-sm-6\">\n              <label class=\"col-form-label\">Country<span class=\"text-success\">*</span></label>\n              <select class=\"form-select\" (change)=\"getCityList($event)\" formControlName=\"countryId\">\n                <option *ngFor=\"let item of countryList\" value=\"{{item.value}}\">  {{item.text}}</option>\n              </select>\n              <span class=\"text-danger\" *ngIf=\"userProfileForm.controls['countryId'].dirty && userProfileForm.hasError('required','countryId')\">Please Select Country</span>\n            </div>\n            <div class=\"col-sm-6\">\n              <label class=\"col-form-label\">City<span class=\"text-success\">*</span></label>\n              <select class=\"form-select\" formControlName=\"cityId\">\n                <option value=\"\">Select City</option>\n                <option *ngFor=\"let item of cityList\" value=\"{{item.value}}\">{{item.text}}</option>\n              </select>\n              <span class=\"text-danger\" *ngIf=\"userProfileForm.controls['cityId'].dirty && userProfileForm.hasError('required','cityId')\">Please Select City</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"col-sm-12 basicInfo\">\n        <p>Professional Information</p>\n        <hr style=\"width: 100%;margin-bottom: 2%;\"/>\n        <div class=\"row\">\n          <div class=\"form-group row\">\n            <div class=\"col-sm-6\">\n              <label class=\"col-form-label\">Avilability</label>\n              <select class=\"form-select\" formControlName=\"avilability\">\n                <option value=\"\">Select your avilability</option>\n                <option value=\"Payment Availability\">Payment Availability</option>\n                <option value=\"Block Availability\">Block Availability</option>\n                <option value=\"Period Availability\">Period Availability</option>\n                <option value=\"Standards Availability\">Standards Availability</option>\n                <option value=\"Service Availability\">Service Availability</option>\n                <option value=\"Daily\">Daily</option>\n                <option value=\"Weekend\">Weekend</option>\n                <option value=\"Monthly\">Monthly</option>\n                <option value=\"Yearly\">Yearly</option>\n              </select>\n            </div>\n            <div class=\"col-sm-6\">\n              <label class=\"col-form-label\">LinkedIn</label>\n              <input type=\"text\" class=\"form-control\" placeholder=\"Enter linkedin url\" formControlName=\"linkdInUrl\">\n            </div>\n              <input type=\"hidden\" formControlName=\"userId\">\n          </div>\n        </div>\n      </div>\n      <div class=\"col-sm-12 basicInfo\">\n        <p>My Skills</p>\n        <hr style=\"width: 100%;margin-bottom: 2%;\"/>\n        <div class=\"row\">\n          <div class=\"form-group row\">\n            <div class=\"col-sm-12\">\n              <select class=\"form-select\" multiple=\"multiple\" formControlName=\"mySkills\">\n                <option *ngFor=\"let item of userSkillList\" value=\"{{item}}\">{{item}}</option>\n              </select>\n              <span class=\"text-danger\" *ngIf=\"userProfileForm.controls['mySkills'].dirty && userProfileForm.hasError('required','mySkills')\">Please Select Skill</span>\n            </div>\n          </div>\n          <div class=\"form-group row mt-3\">\n            <button class=\"btn-skill\" type=\"button\" (click)=\"openAddYourSkillModal()\"><span class=\"Skill\">Add Skill</span></button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n</form>\n<div class=\"row justify-content-center\">\n  <button class=\"btn-Close\" type=\"button\" (click)=\"onCancel()\"><span class=\"Close\">Cancel</span></button>\n  <button class=\"btn-save\" type=\"button\" (click)=\"onSubmit()\"><span class=\"Save\">Save</span></button>\n</div>\n</div>\n<div class=\"container-fluid footer\">\n  <hr/>\n  <div class=\"row\">\n    <div class=\"col-sm-6\" style=\"display: flex;justify-content: flex-end;cursor: pointer;\"><p>Privacy Policy</p></div>\n    <div class=\"col-sm-6\" style=\"display: flex;justify-content: flex-start;cursor: pointer;\" (click)=\"openContactUsModal()\"><p>Contact us</p></div>\n  </div>\n\n</div>\n\n\n<div class=\"modal fade\" style=\"margin-top: 6%;\" id=\"changePasswordModal\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"exampleModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\" role=\"document\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"exampleModalLabel\">Change Password</h5>\n        <button type=\"button\" class=\"btn-close\" data-dismiss=\"modal\" aria-label=\"Close\" (click)=\"closeChangePasswordModal()\">\n        </button>\n      </div>\n      <form autocomplete=\"off\" #changePasswordForm=\"ngForm\" (ngSubmit)=\"changePasswordForm.form.valid && onSubmitChangePassword(changePasswordForm)\">\n      <div class=\"modal-body\">\n        <div class=\"row\">\n\n          <div class=\"form-group\">\n            <input type=\"password\" class=\"form-control\" placeholder=\"Enter old password\" name=\"oldPassword\" #oldPassword=\"ngModel\" [(ngModel)]=\"changePass.oldPassword\" required>\n            <span class=\"text-danger\" *ngIf=\"oldPassword.invalid && (oldPassword.touched || oldPassword.dirty)\">\n                OldPassword is Required\n            </span>\n          </div>\n          <div class=\"form-group mt-3\">\n            <input type=\"password\" class=\"form-control\" placeholder=\"Enter new password\" name=\"newPassword\" #newPassword=\"ngModel\"  [(ngModel)]=\"changePass.newPassword\" required>\n            <span class=\"text-danger\" *ngIf=\"newPassword.invalid && (newPassword.touched || newPassword.dirty)\">\n              NewPassword is Required\n            </span>\n          </div>\n          <div class=\"form-group mt-3\">\n            <input type=\"password\" class=\"form-control\" placeholder=\"Enter confirm password\" name=\"confirmPassword\" #confirmPassword=\"ngModel\"  [(ngModel)]=\"changePass.confirmPassword\" required>\n            <span class=\"text-danger\" *ngIf=\"confirmPassword.invalid && (confirmPassword.touched || newPassword.dirty)\">\n              ConfirmPassword is Required\n          </span>\n          </div>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btnCancel\" data-dismiss=\"modal\" (click)=\"closeChangePasswordModal()\"><span class=\"Cancel\"> Cancel</span> </button>\n        <button type=\"submit\" class=\"btnChangePassword\"><span class=\"Change\">Change Password</span></button>\n      </div>\n    </form>\n    </div>\n  </div>\n</div>\n\n<div class=\"modal fade\"   id=\"addSkillModal\" tabindex=\"-1\" aria-labelledby=\"addSkillLabel\" aria-hidden=\"true\" role=\"dialog\">\n  <div class=\"modal-dialog modal-lg modal-dialog-centered\" role=\"document\"  >\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"addSkillLabel\">Add your Skills</h5>\n        <button class=\"btn-close\" type=\"button\" data-dismiss=\"modal\" aria-label=\"Close\" (click)=\"closeAddYourSkillModal()\"></button>\n      </div>\n      <div class=\"modal-body text-center\">\n        <kendo-listbox\n             #Listbox1\n             [data]=\"data\"\n            class=\"kendolistdata1\"\n           (actionClick)=\"onSubmitSkillModal($event)\"\n            kendoListBoxDataBinding\n           [toolbar]=\"toolbarSettings\"\n           [connectedWith]=\"Listbox2\">\n       </kendo-listbox>\n\n       <kendo-listbox\n           #Listbox2\n           [data]=\"data1\"\n           class=\"kendolistdata2\"\n           (actionClick)=\"onSubmitSkillModal($event)\"\n           [toolbar]=\"false\">\n        </kendo-listbox>\n      </div>\n      <div class=\"modal-footer\" style=\"display: flex;justify-content: flex-start;\">\n        <button type=\"button\" class=\"btnCancel\" data-dismiss=\"modal\" (click)=\"closeAddYourSkillModal()\"><span class=\"Cancel\"> Cancel</span> </button>\n        <button type=\"button\" class=\"btnSkillSave\" (click)=\"saveSkill()\"><span class=\"SkillSave\">Save</span></button>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n<div class=\"modal fade\"  id=\"contactUsModal\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"contactUsModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\" role=\"document\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"contactUsModalLabel\">Contact Us</h5>\n        <button type=\"button\" class=\"btn-close\" data-dismiss=\"modal\" aria-label=\"Close\" (click)=\"closeContactUsModal()\">\n        </button>\n      </div>\n      <form autocomplete=\"off\" #myForm=\"ngForm\" (ngSubmit)=\"myForm.form.valid && onSubmitContactUs(myForm)\">\n      <div class=\"modal-body\">\n        <div class=\"row\">\n          <input type=\"hidden\" name=\"userId\" #userId=\"ngModel\" [(ngModel)]=\"contactUs.userId\">\n          <div class=\"form-group\">\n            <label class=\"col-form-label\">Name<span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control\"  name=\"name\" #name=\"ngModel\" [(ngModel)]=\"contactUs.name\" disabled>\n          </div>\n          <div class=\"form-group\">\n            <label class=\"col-form-label\">Email Address<span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control\"  name=\"emailAddress\" #emailAddress=\"ngModel\" [(ngModel)]=\"contactUs.emailAddress\" disabled>\n          </div>\n          <div class=\"form-group mt-3\">\n            <label class=\"col-form-label\">Subject</label>\n            <input type=\"text\" class=\"form-control\" placeholder=\"Enter your subject\" name=\"subject\" #subject=\"ngModel\" [(ngModel)]=\"contactUs.subject\" required>\n            <span class=\"text-danger\" *ngIf=\"subject.invalid && (subject.touched || subject.dirty)\">Please Enter Subject</span>\n          </div>\n          <div class=\"form-group mt-3\">\n            <label class=\"col-form-label\">Message</label>\n            <textarea class=\"form-control\" rows=\"3\"placeholder=\"Enter your message..\" name=\"message\" #message=\"ngModel\" [(ngModel)]=\"contactUs.message\" required></textarea>\n            <span class=\"text-danger\" *ngIf=\"message.invalid && (message.touched || message.dirty)\">Please Enter Message</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn-Close\" data-dismiss=\"modal\" (click)=\"closeContactUsModal()\"><span class=\"Close\"> Cancel</span> </button>\n        <button type=\"submit\" class=\"btn-save\"><span class=\"Save\">Save</span></button>\n      </div>\n    </form>\n    </div>\n  </div>\n</div>\n", "import { CommonModule, DatePipe } from '@angular/common';\nimport { Component, OnDestroy, type OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\n\nimport * as moment from 'moment';\nimport { NgToastService } from 'ng-angular-popup';\nimport { AuthService } from '../../services/auth.service';\nimport { APP_CONFIG } from '../../configs/environment.config';\nimport { ClientMissionService } from '../../services/client-mission.service';\nimport { FooterComponent } from '../footer/footer.component';\nimport { TabsModule } from 'ngx-bootstrap/tabs';\nimport { SearchingSortingComponent } from '../searching-sorting/searching-sorting.component';\nimport { NavbarComponent } from '../navbar/navbar.component';\nimport { Subscription } from 'rxjs';\ndeclare var window: any;\n@Component({\n  selector: 'app-volunteering-mission',\n  templateUrl: './volunteering-mission.component.html',\n  styleUrls: ['./volunteering-mission.component.css'],\n  standalone: true,\n  imports: [FooterComponent, TabsModule, CommonModule, SearchingSortingComponent, NavbarComponent]\n})\nexport class VolunteeringMissionComponent implements OnInit, OnDestroy {\n  applyModal: any;\n  missionId: any;\n  missionDetail: any;\n  imageList: any = [];\n  recentVolunteerList: any[] = [];\n  missionDoc: any;\n  loginUserId = 0;\n  loginUserName: any;\n  btnText: any = 'Apply Now';\n  missionCommentList: any[] = [];\n  missionFavourite = false;\n  favImag = 'assets/Img/heart1.png';\n  favImag1 = 'assets/Img/heart11.png';\n  private unsubscribe: Subscription[] = [];\n\n  constructor(\n    private _service: ClientMissionService,\n    private _toast: NgToastService,\n    private _activeRoute: ActivatedRoute,\n    private _router: Router,\n    private _datePipe: DatePipe,\n    private _adminservice: AuthService\n  ) {\n    this.missionId = this._activeRoute.snapshot.paramMap.get('missionId');\n  }\n\n  ngOnInit(): void {\n    const currentUserSubscribe = this._adminservice.getCurrentUser().subscribe((data: any) => {\n      const loginUserDetail = this._adminservice.getUserDetail();\n      data == null\n        ? (this.loginUserId = loginUserDetail.userId)\n        : (this.loginUserId = data.userId);\n      data == null\n        ? (this.loginUserName = loginUserDetail.fullName)\n        : (this.loginUserName = data.fullName);\n    });\n    if (this.missionId != null) {\n      this.fetchMissionDetail(this.missionId);\n    }\n\n    this.applyModal = new window.bootstrap.Modal(\n      document.getElementById('applyMissionModal')\n    );\n    this.getRecentVolunteerList();\n    this.unsubscribe.push(currentUserSubscribe);\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n  \n  fetchMissionDetail(missionId: any) {\n    const value = {\n      missionId: missionId,\n      userId: this.loginUserId,\n    };\n    const missionDetailSubscribe = this._service.missionDetailByMissionId(value).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.missionDetail = data.data;\n\n          this.imageList = this.missionDetail.missionImages.split(',');\n          // Gallery images removed - using simple image display\n          if (this.missionDetail.missionDocuments) {\n            this.missionDoc =\n              this._service.imageUrl + '/' + this.missionDetail.missionDocuments;\n          }\n          this.btnText =\n            this.missionDetail.missionApplyStatus == 'Applied'\n              ? 'Already Apply'\n              : 'Apply Now';\n          this.getMissionCommentList();\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(missionDetailSubscribe);\n  }\n\n  getImageUrls(): string[] {\n    const imageUrls: string[] = [];\n    for (const photo of this.imageList) {\n      imageUrls.push(this._service.imageUrl + '/' + photo.replaceAll('\\\\', '/'));\n    }\n    return imageUrls;\n  }\n\n  getMainImageUrl(): string {\n    if (this.imageList && this.imageList.length > 0) {\n      return this._service.imageUrl + '/' + this.imageList[0].replaceAll('\\\\', '/');\n    }\n    return '';\n  }\n\n  openApplyMissionModal(id: any) {\n    this.applyModal.show();\n    this.missionId = id;\n  }\n\n  closeApplyMissionModal() {\n    this.applyModal.hide();\n  }\n\n  applyMission(id: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        appliedDate: moment().format('yyyy-MM-DDTHH:mm:ssZ'),\n        status: false,\n        sheet: 1,\n      };\n      const missionSubscribe = this._service.applyMission(value).subscribe(\n        (data: any) => {\n          if (data.result == 1) {\n            this._toast.success({ detail: 'SUCCESS', summary: data.data });\n            setTimeout(() => {\n              this.closeApplyMissionModal();\n              this._router.navigate(['/home']);\n            }, 1000);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        },\n        (err) =>\n          this._toast.error({\n            detail: 'ERROR',\n            summary: err.message,\n            duration: APP_CONFIG.toastDuration,\n          })\n      );\n      this.unsubscribe.push(missionSubscribe);\n    }\n  }\n\n  postComment(commentdesc: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      const value = {\n        missionId: this.missionDetail.id,\n        userId: this.loginUserId,\n        CommentDescription: commentdesc,\n        commentDate: moment().format('yyyy-MM-DDTHH:mm:ssZ'),\n      };\n      const missionCommentSubscribe = this._service.addMissionComment(value).subscribe(\n        (data: any) => {\n          if (data.result == 1) {\n            this._toast.success({\n              detail: 'SUCCESS',\n              summary: data.data,\n              duration: APP_CONFIG.toastDuration,\n            });\n            window.location.reload();\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        },\n        (err) =>\n          this._toast.error({\n            detail: 'ERROR',\n            summary: err.message,\n            duration: APP_CONFIG.toastDuration,\n          })\n      );\n      this.unsubscribe.push(missionCommentSubscribe);\n    }\n  }\n\n  getMissionCommentList() {\n    const missionCommentSubscribe = this._service.missionCommentListByMissionId(this.missionDetail.id).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.missionCommentList = data.data;\n\n          this.missionCommentList = this.missionCommentList.map((x) => {\n            return {\n              id: x.id,\n              commentDescription: x.commentDescription,\n              commentDate: x.commentDate\n                ? this._datePipe.transform(\n                    x.commentDate,\n                    'EEEE, MMMM d, y, h:mm a'\n                  )\n                : '',\n              missionId: x.missionId,\n              userId: x.userId,\n              userFullName: x.userFullName,\n              userImage: x.userImage\n                ? this._service.imageUrl + '/' + x.userImage\n                : 'assets/NoImg.png',\n            };\n          });\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(missionCommentSubscribe);\n  }\n  \n  getMissionFavourite(missionId: any) {\n    const tokenDetail = this._adminservice.decodedToken();\n    if (tokenDetail == null || tokenDetail.userType != 'user') {\n      this._router.navigate(['']);\n    } else if (tokenDetail.userImage == '') {\n      this._toast.warning({\n        detail: 'Warning',\n        summary: 'First Fillup User Profile Detail',\n        duration: APP_CONFIG.toastDuration,\n      });\n      this._router.navigate([`userProfile/${tokenDetail.userId}`]);\n    } else {\n      this.missionFavourite = !this.missionFavourite;\n      const value = {\n        missionId: missionId,\n        userId: this.loginUserId,\n      };\n      if (this.missionFavourite) {\n        const addMissionFavouriteSubscribe = this._service.addMissionFavourite(value).subscribe((data: any) => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        });\n        this.unsubscribe.push(addMissionFavouriteSubscribe);\n      } else {\n        const removeMissionFavouriteSubscribe = this._service.removeMissionFavourite(value).subscribe((data: any) => {\n          if (data.result == 1) {\n            this.fetchMissionDetail(missionId);\n          } else {\n            this._toast.error({\n              detail: 'ERROR',\n              summary: data.message,\n              duration: APP_CONFIG.toastDuration,\n            });\n          }\n        });\n        this.unsubscribe.push(removeMissionFavouriteSubscribe);\n      }\n    }\n  }\n\n  getRecentVolunteerList() {\n    const value = {\n      missionId: this.missionId,\n      userId: this.loginUserId,\n    };\n    const volunteerListSubscribe = this._service.recentVolunteerList(value).subscribe((data: any) => {\n      if (data.result == 1) {\n        this.recentVolunteerList = data.data;\n        this.recentVolunteerList = this.recentVolunteerList.map((x) => {\n          return {\n            id: x.id,\n            missioId: x.missioId,\n            userId: x.userId,\n            userName: x.userName,\n            userImage: x.userImage\n              ? this._service.imageUrl + '/' + x.userImage\n              : 'assets/NoImg.png',\n          };\n        });\n      } else {\n        this._toast.error({\n          detail: 'ERROR',\n          summary: data.message,\n          duration: APP_CONFIG.toastDuration,\n        });\n      }\n    });\n    this.unsubscribe.push(volunteerListSubscribe);\n  }\n}", "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n<div>\n  <app-navbar></app-navbar>\n  <app-searching-sorting></app-searching-sorting>\n</div>\n<div class=\"container\">\n  <div class=\"row\" style=\"height: 650px;\">\n      <div class=\"col-sm-6\">\n        <div class=\"image-gallery\" *ngIf=\"imageList && imageList.length > 0\">\n          <img [src]=\"getMainImageUrl()\"\n               alt=\"Mission Image\"\n               class=\"img-fluid main-image\"\n               style=\"width: 100%; height: 465px; object-fit: cover;\">\n        </div>\n      </div>\n      <div class=\"col-sm-6 content\">\n        <p class=\"heading\">{{missionDetail.missionTitle}}</p>\n          <p class=\"detail\"> {{missionDetail.missionDescription}}</p>\n              <div class=\"mt-5\">\n                <div class=\"bordert\">\n                  <div class=\"totalTree\">\n                      <p class=\"totalTreegoal\" *ngIf=\"missionDetail.missionType=='Goal'\">Plant 10,000 Trees</p>\n                      <p class=\"totalTreetime\" *ngIf=\"missionDetail.missionType=='Time'\">FROM {{missionDetail.startDate | date : 'dd/MM/yyyy'}} Until {{missionDetail.endDate | date:'dd/MM/yyyy'}}</p>\n                  </div>\n              </div>\n                <div class=\"SeatDeadLines row\">\n                  <div class=\"col-sm-6\">\n                    <img src=\"assets/Img/Seats-left.png\">&nbsp;\n                    <span style=\"font-size: 24px !important;\">{{missionDetail.totalSheets}}</span> <br/><span style=\"margin-left:40px\">Seats left</span>\n                  </div>\n                  <div class=\"col-sm-6\" *ngIf=\"missionDetail.missionType=='Goal'\">\n                    <img src=\"assets/Img/achieved.png\">&nbsp;\n                    <span style=\"font-size: 24px !important;\">\n                      <div class=\"progress\">\n                        <div class=\"progress-bar bg-warning\" role=\"progressbar\" style=\"width: 50%\" aria-valuenow=\"25\" aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\n                      </div>\n                    </span> <br/><span style=\"margin-left:40px;\">8000 achieved</span>\n                    <br/><br/>\n                  </div>\n                  <div class=\"col-sm-6\" *ngIf=\"missionDetail.missionType=='Time'\">\n                    <img src=\"assets/Img/deadline.png\">&nbsp;\n                    <span style=\"font-size: 24px !important;\">{{missionDetail.registrationDeadLine | date: 'dd/MM/yyyy'}}</span> <br/><span style=\"margin-left:40px\">Deadline</span>\n                    <br/><br/>\n                  </div>\n              </div>\n              <p style=\"width:616px;margin: 20px 100px 0px 61px;border:1px solid #757575;opacity: 0.1;\"></p>\n              <div class=\"row\">\n                    <div class=\"col-sm-6\">\n                       <button class=\"btnMission\"><span (click)=\"getMissionFavourite(missionDetail.id)\"><img style=\"width: 30px;\" src=\"{{missionDetail.missionFavouriteStatus=='0'? favImag : favImag1}}\" alt=\"NoImage\"></span><span class=\"Mission\">Add to Favourite</span></button>\n                    </div>\n                     <div class=\"col-sm-6\">\n                          <button class=\"btnMission\"><span><img src=\"assets/Img/add1.png\" alt=\"NoImage\"></span><span class=\"Mission\">Recommend to a Co-Worker </span></button>\n                     </div>\n               </div>\n               <div class=\"totalRating\">\n                  <span class=\"fa fa-star\"></span>\n                  <span class=\"fa fa-star\"></span>\n                  <span class=\"fa fa-star\"></span>\n                  <span class=\"fa fa-star\"></span>\n                  <span class=\"fa fa-star\"></span>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-sm-3 carddetail\">\n                  <div class=\"top-left\"><img src=\"assets/Img/pin1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-left\"><span style=\" color:#757575 !important;\">City</span><br/><span>{{missionDetail.cityName}}</span></div>\n                </div>\n                <div class=\"col-sm-3 carddetail\">\n                  <div class=\"top-left\"><img src=\"assets/Img/web.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-left\"><span style=\" color:#757575 !important;\">Theme</span><br/><span>{{missionDetail.missionThemeName}}</span></div>\n                </div>\n                <div class=\"col-sm-3 carddetail\">\n                  <div class=\"top-left\"><img src=\"assets/Img/pin1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-left\"><span style=\"color:#757575 !important;\">Date</span><br/><span>From{{missionDetail.startDate | date:'dd/MM/yyyy'}}<br/>Until {{missionDetail.endDate | date:'dd/MM/yyyy'}}</span></div>\n                </div>\n                <div class=\"col-sm-3 carddetail\">\n                  <div class=\"top-left\"><img src=\"assets/Img/organization.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-left\"><span style=\" color:#757575 !important;\">Organization</span><br/><span>{{missionDetail.missionOrganisationName}}</span></div>\n                </div>\n              </div>\n              <div class=\"d-grid card-footer align-items-center\" style=\"display: flex;justify-content: center;margin-left: 20%;\" >\n                <button class=\"btn-login\" type=\"submit\" [disabled]=\"missionDetail.missionApplyStatus=='Applied'\" (click)=\"openApplyMissionModal(missionDetail.id)\"><span class=\"Login\">\n                  {{btnText}} &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n              </div>\n             </div>\n\n      </div>\n  </div>\n  <div class=\"row\" style=\"height: 650px;margin-top: 14%;\">\n    <div class=\"col-sm-8\">\n      <div class=\"tab-panel p-3\">\n        <tabset class=\"member-tabset\">\n          <tab heading=\"Mission\">\n            <p class=\"Introduction\">Introduction</p>\n            <p class=\"contentDetail\">\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n              <br/><br/>\n              Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n              <br/><br/>\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n            </p>\n            <p class=\"Introduction\">Challenge</p>\n            <p class=\"contentDetail\">\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n              <br/><br/>\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n            </p>\n            <p class=\"Introduction\" *ngIf=\"missionDoc != undefined\">Documents</p>\n            <div class=\"documentFile row mt-4\" *ngIf=\"missionDoc != undefined\">\n              <div class=\"col-sm-3 document\"><img src=\"assets/Img/pdf.png\"><span><a href=\"{{missionDoc}}\" style=\"color: #414141;text-decoration: none;\">&nbsp;{{missionDoc}}</a></span></div>\n            </div>\n          </tab>\n          <tab heading=\"Organization\">{{missionDetail.missionOrganisationDetail}}</tab>\n          <tab heading=\"Comments\">\n            <textarea class=\"form-control\" rows=\"4\" #commentdesc placeholder=\"Enter Your Comments\"></textarea>\n            <div class=\"mt-4\">\n              <button class=\"btn-login\" type=\"button\" (click)=\"postComment(commentdesc.value)\"><span class=\"Login\">Post Comment</span></button>\n            </div>\n            <div class=\"mt-4\" style=\"height: 400px !important;overflow-y:auto;max-height: 400px !important;border: none;\">\n                <div class=\"card mt-2 col-sm-11 row\" style=\"height: 100px !important;margin-left: 10px;\" *ngFor=\"let item of missionCommentList\">\n                    <div class=\"col-sm-1\">\n                      <img class=\"userimg\" src=\"{{item.userImage}}\" alt=\"NoImage\" onerror=\"this.src='assets/NoUser.png'\">\n                    </div>\n                    <div class=\"col-sm-10\">\n                        <span style=\"font-size: 16px;\"><b>{{item.userFullName}}</b></span>\n                        <p style=\"font-size: 14px;\">{{item.commentDate }}</p>\n                        <!-- <p style=\"font-size: 14px;\">Monday,Octomber 21,2022,3:35pm</p> -->\n                        <p class=\"commentdisc\">{{item.commentDescription}}</p>\n                    </div>\n                </div>\n            </div>\n          </tab>\n        </tabset>\n  </div>\n    </div>\n    <div class=\"col-sm-4\">\n      <div class=\"card cardInformation\">\n        <div class=\"card-body\">\n          <p class=\"cardBodyDetail\" style=\"font-size: 22px !important;font-weight: 700;\">Information</p>\n          <p class=\"underLine\"></p>\n          <p class=\"cardBodyDetail\">Skills<span style=\"margin-left: 5%;\">{{missionDetail.missionSkillName}}</span></p>\n          <p class=\"underLine\"></p>\n          <p class=\"cardBodyDetail\">Day <span style=\"margin-left: 6%;\">&nbsp;Weekend only</span></p>\n          <p class=\"underLine\"></p>\n          <p class=\"cardBodyDetail\"><span>Rating</span>\n            <span class=\"col-sm-5\" style=\"margin-left: 5%;\">\n              <span class=\"fa fa-star checked\"></span>\n              <span class=\"fa fa-star checked\"></span>\n              <span class=\"fa fa-star checked\"></span>\n              <span class=\"fa fa-star checked\"></span>\n              <span class=\"fa fa-star\"></span>\n            </span>&nbsp;\n           (by 125 volunteers)\n          </p>\n        </div>\n      </div>\n\n      <div class=\"card userInformation\">\n        <div class=\"card-body\">\n          <p class=\"cardBodyDetail\" style=\"font-size: 22px !important;font-weight: 700;\">Recent Volunteers</p>\n          <p class=\"underLine\"></p>\n          <div class=\"row\">\n            <ng-container *ngIf=\"(recentVolunteerList) as result\">\n               <div class=\"col-sm-4 row\" style=\"margin-bottom: 4%;\" *ngFor=\"let item of result\">\n                   <img src=\"{{item.userImage}}\" alt=\"NoImage\" style=\" width: 90px;height: 70px;border-radius: 50%;margin-left: 0%;\">\n                   <p style=\"width: 120px;\">{{item.userName}}</p>\n                </div>\n                <div class=\"col-sm-12 justify-content-center\" *ngIf=\"result.length === 0\">\n                  <p class=\"text-danger\" style=\"font-size:18px;text-align: center;margin-top: 50%;\"><b>No Recent Volunteers Avilable</b></p>\n              </div>\n            </ng-container>\n          </div>\n       </div>\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"container-fluid\">\n  <p style=\"margin-top: 9%;height:1px;border: 1px solid gray;opacity: 0.1;\"></p>\n  <div class=\"\">\n      <p style=\"width:100%;display: flex;justify-content: center;font-size: 22px;font-weight: 400;color: #757575;margin-top: 2%;\">\n        Related Mission\n      </p>\n      <div class=\"container-fluid\" style=\"width: fit-content;height: 676px;\">\n        <div class=\"row mt-5\">\n          <div class=\"col-sm-4 row Rounded-Rectangle-2-copy\">\n            <div class=\"card-header\" style=\"width: 460px;height:220px;\">\n                  <img src=\"assets/Images/1.png\" alt=\"NoImage\">\n                  <div class=\"bottom-leftimg\"><i class=\"fa fa-map-marker\"></i>&nbsp;Toronto</div>\n                  <div class=\"bottom-rightsimg\"><img src=\"assets/Img/heart1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-rightimg\"><i class=\"fa fa-user-plus 3-x\"></i></div>\n                  <div class=\"centered\">Environment</div>\n            </div>\n            <div class=\"card-body\">\n                <p class=\"heading\">Grow Trees – On the path to\n                  environment sustainability</p>\n                <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore...</p>\n                <div class=\"row\" style=\"margin: 14px;\">\n                  <div class=\"col-sm-7 contentdetail\">\n                    CSE Network\n                  </div>\n                  <div class=\"col-sm-5\" style=\"display: flex;justify-content: flex-end;\">\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star\"></span>\n                    <span class=\"fa fa-star\"></span>\n                  </div>\n                </div>\n                <div>\n                  <!-- <div class=\"missionLabel\">\n                    <p>Ongoing Opportunity</p>\n                  </div> -->\n                  <div class=\"bordert\">\n                    <div class=\"text-center data py-3\">\n                      <p style=\"margin-top: -12px\">Ongoing Opportunity</p>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"SeatDeadLine row\">\n                    <div class=\"col-sm-12\">\n                      <img src=\"assets/Img/Already-volunteered.png\" alt=\"NoImage\" style=\"margin-top: -1%;\">&nbsp;\n                      <span style=\"font-size: 24px !important;margin-top: 2%;\">250</span> <br/><span style=\"margin-left:34px\">Already volunteered</span>\n                    </div>\n                </div>\n            </div>\n            <P style=\"border: 1px solid #e8e8e8;width:100%\"></P>\n            <div class=\"d-grid card-footer\"style=\"margin-left:32%\">\n              <button class=\"btn-logins\" type=\"submit\"><span class=\"Logins\">Apply &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n            </div>\n           </div>\n          <div class=\"col-sm-4 row Rounded-Rectangle-2-copy\">\n            <div class=\"card-header\" style=\"width: 460px;height:220px;\">\n                  <img src=\"assets/Img/animal.png\" alt=\"NoImage\">\n                  <div class=\"bottom-leftimg\"><i class=\"fa fa-map-marker\"></i>&nbsp;Cape Town</div>\n                  <div class=\"bottom-rightsimg\"><img src=\"assets/Img/heart1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-rightimg\"><i class=\"fa fa-user-plus 3-x\"></i></div>\n                  <div class=\"centered\">Environment</div>\n            </div>\n            <div class=\"card-body\">\n                <p class=\"heading\">Animal Welfare & save birds\n                  campaign\n                </p>\n                <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore...</p>\n                <div class=\"row\" style=\"margin: 14px;\">\n                  <div class=\"col-sm-7 contentdetail\">\n                    JR Foundation\n                  </div>\n                  <div class=\"col-sm-5\" style=\"display: flex;justify-content: flex-end;\">\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star\"></span>\n                    <span class=\"fa fa-star\"></span>\n                  </div>\n                </div>\n                <div>\n                  <!-- <div class=\"missionLabel\">\n                    <p>Plant 10,000 Trees</p>\n                  </div> -->\n                  <div class=\"bordert\">\n                    <div class=\"text-center data py-3\">\n                      <p style=\"margin-top: -12px\">Plant 10,000 Trees</p>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"SeatDeadLine row\">\n                    <div class=\"col-sm-6\">\n                      <img src=\"assets/Img/Seats-left.png\" alt=\"NoImage\" style=\"margin-top: -1%;\">&nbsp;\n                      <span style=\"font-size: 24px !important;margin-top: 2%;\">10</span> <br/><span style=\"margin-left:34px\">Seat left</span>\n                    </div>\n                    <div class=\"col-sm-6\">\n                      <img src=\"assets/Img/achieved.png\" alt=\"NoImage\" style=\"margin-top: -1%;margin-left: -8%;\">&nbsp;\n                      <span style=\"font-size: 24px !important;\">\n                        <div class=\"progress\" style=\"margin-top: -10% !important;margin-bottom: -16% !important\">\n                          <div class=\"progress-bar bg-warning\" role=\"progressbar\" style=\"width: 75%\" aria-valuenow=\"25\" aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\n                        </div>\n                      </span> <br/><span style=\"margin-left:30px !important;\">8000 achieved</span>\n                    </div>\n                </div>\n            </div>\n            <P style=\"border: 1px solid #e8e8e8;width:100%\"></P>\n            <div class=\"d-grid card-footer\"style=\"margin-left:32%\">\n              <button class=\"btn-logins\" type=\"submit\"><span class=\"Logins\">Apply &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n            </div>\n           </div>\n           <div class=\"col-sm-4 row Rounded-Rectangle-2-copy\">\n            <div class=\"card-header\" style=\"width: 460px;height:220px;\">\n                  <img src=\"assets/Img/Plantation.png\" alt=\"NoImage\">\n                  <div class=\"bottom-leftimg\"><i class=\"fa fa-map-marker\"></i>&nbsp;Paris</div>\n                  <div class=\"bottom-rightsimg\"><img src=\"assets/Img/heart1.png\" alt=\"NoImage\"></div>\n                  <div class=\"bottom-rightimg\"><i class=\"fa fa-user-plus 3-x\"></i></div>\n                  <div class=\"centered\">Environment</div>\n            </div>\n            <div class=\"card-body\">\n                <p class=\"heading\">Plantation and Afforestation\n                  programme\n                </p>\n                <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore...</p>\n                <div class=\"row\" style=\"margin: 14px;\">\n                  <div class=\"col-sm-7 contentdetail\">\n                    Amaze Doctors\n                  </div>\n                  <div class=\"col-sm-5\" style=\"display: flex;justify-content: flex-end;\">\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star checked\"></span>\n                    <span class=\"fa fa-star\"></span>\n                    <span class=\"fa fa-star\"></span>\n                  </div>\n                </div>\n                <div>\n                  <!-- <div class=\"missionLabel\">\n                    <p>Plant 10,000 Trees</p>\n                </div> -->\n                <div class=\"bordert\">\n                  <div class=\"text-center data py-3\">\n                    <p style=\"margin-top: -12px\">Plant 10,000 Trees</p>\n                  </div>\n                </div>\n                </div>\n                <div class=\"SeatDeadLine row\">\n                  <div class=\"col-sm-6\">\n                    <img src=\"assets/Img/Seats-left.png\" alt=\"NoImage\" style=\"margin-top: -1%;\">&nbsp;\n                    <span style=\"font-size: 24px !important;margin-top: 2%;\">10</span> <br/><span style=\"margin-left:34px\">Seat left</span>\n                  </div>\n                  <div class=\"col-sm-6\">\n                    <img src=\"assets/Img/achieved.png\" alt=\"NoImage\" style=\"margin-top: -1%;margin-left: -8%;\">&nbsp;\n                    <span style=\"font-size: 24px !important;\">\n                      <div class=\"progress\" style=\"margin-top: -10% !important;margin-bottom: -16% !important\">\n                        <div class=\"progress-bar bg-warning\" role=\"progressbar\" style=\"width: 75%\" aria-valuenow=\"25\" aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\n                      </div>\n                    </span> <br/><span style=\"margin-left:30px !important;\">8000 achieved</span>\n                  </div>\n              </div>\n            <P style=\"border: 1px solid #e8e8e8;width:100%\"></P>\n            <div class=\"d-grid card-footer\"style=\"margin-left:32%\">\n              <button class=\"btn-logins\" type=\"submit\"><span class=\"Logins\">Apply &nbsp;<i style=\"margin-top: 5px !important;\" class=\"fa fa-arrow-right\"></i></span></button>\n            </div>\n           </div>\n    </div>\n      </div>\n  </div>\n  </div>\n  <div class=\"mt-5\">\n    <app-footer></app-footer>\n  </div>\n\n\n  <div class=\"modal fade\" style=\"margin-top: 8%;\" id=\"applyMissionModal\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"exampleModalLabel\" aria-hidden=\"true\">\n    <div class=\"modal-dialog\" role=\"document\">\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h5 class=\"modal-title\" id=\"exampleModalLabel\">Confirm Delete</h5>\n          <button type=\"button\" class=\"btn-close\" data-dismiss=\"modal\" aria-label=\"Close\" (click)=\"closeApplyMissionModal()\">\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <input type=\"hidden\" value=\"\">\n           <h4>Are you sure you want to apply this mission?</h4>\n        </div>\n        <div class=\"modal-footer\">\n          <button type=\"button\" class=\"btnCancel\" data-dismiss=\"modal\" (click)=\"closeApplyMissionModal()\"><span class=\"bCancel\"> Cancel</span> </button>\n          <button type=\"button\" class=\"btnRemove\" (click)=\"applyMission(missionId)\"><span class=\"bremove\">Apply</span></button>\n        </div>\n      </div>\n    </div>\n  </div>\n", "import { CommonModule, DatePipe } from '@angular/common';\nimport { Component, OnDestroy, type OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { NgToastService } from 'ng-angular-popup';\nimport { ToastrService } from 'ngx-toastr';\nimport ValidateForm from '../../helpers/validate-form.helper';\nimport { AuthService } from '../../services/auth.service';\nimport { APP_CONFIG } from '../../configs/environment.config';\nimport { VolunteeringTimeSheetService } from '../../services/volunteering-timesheet.service';\nimport { NavbarComponent } from '../navbar/navbar.component';\nimport { Subscription } from 'rxjs';\ndeclare var window: any;\n@Component({\n  selector: 'app-volunteering-timesheet',\n  templateUrl: './volunteering-timesheet.component.html',\n  styleUrls: ['./volunteering-timesheet.component.css'],\n  standalone: true,\n  imports: [ReactiveFormsModule, NavbarComponent, CommonModule]\n})\nexport class VolunteeringTimesheetComponent implements OnInit, OnDestroy {\n  volunteeringHourseModals: any;\n  volunteeringGoalsModals: any;\n  deleteModal: any;\n  volunteeringHoursForm: FormGroup;\n  volunteeringGoalsForm: FormGroup;\n  missionList: any;\n  hoursList: any;\n  goalsList: any;\n  editData: any;\n  loginDetail: any;\n  loginUserId: any;\n  hoursId: any;\n  private unsubscribe: Subscription[] = [];\n\n  constructor(\n    private _service: VolunteeringTimeSheetService,\n    private _loginService: AuthService,\n    private _toast: NgToastService,\n    private _fb: FormBuilder,\n    private _datePipe: DatePipe\n  ) {}\n\n  ngOnInit(): void {\n    this.volunteeringHourseModals = new window.bootstrap.Modal(\n      document.getElementById('volunteeringHoursModal')\n    );\n    this.volunteeringGoalsModals = new window.bootstrap.Modal(\n      document.getElementById('volunteeringGoalsModal')\n    );\n    this.deleteModal = new window.bootstrap.Modal(\n      document.getElementById('removeVolunteeringModal')\n    );\n    this.volunteeringHoursFormValidate();\n    this.volunteeringGoalsFormValidate();\n    const currentUserSubscribe = this._loginService.getCurrentUser().subscribe((data: any) => {\n      this.loginDetail = this._loginService.getUserDetail();\n      data == null\n        ? (this.loginUserId = this.loginDetail.userId)\n        : (this.loginUserId = data.userId);\n    });\n    this.unsubscribe.push(currentUserSubscribe);\n    this.getVolunteeringHoursList();\n    this.getVolunteeringGoalsList();\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n\n  openVolunteeringHoursModal() {\n    this.volunteeringHourseModals.show();\n    this.missionTitleList();\n  }\n\n  closeVolunteeringHoursModal() {\n    this.volunteeringHourseModals.hide();\n    window.location.reload();\n  }\n\n  openVolunteeringGoalsModal() {\n    this.volunteeringGoalsModals.show();\n    this.missionTitleList();\n  }\n\n  closeVolunteeringGoalsModal() {\n    this.volunteeringGoalsModals.hide();\n    window.location.reload();\n  }\n  \n  openVolunteeringDeleteModal(id: any) {\n    this.deleteModal.show();\n    this.hoursId = id;\n  }\n\n  closeVolunteeringDeleteModal() {\n    this.deleteModal.hide();\n  }\n\n  missionTitleList() {\n    const volunteeringMissionSubscribe = this._service.volunteeringMissionList(this.loginUserId).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.missionList = data.data;\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(volunteeringMissionSubscribe);\n  }\n\n  //*****************************************Volunteering TimeSheet Hours ************************************************** */\n\n  volunteeringHoursFormValidate() {\n    this.volunteeringHoursForm = this._fb.group({\n      id: [0],\n      missionId: [null, Validators.compose([Validators.required])],\n      dateVolunteered: [null, Validators.compose([Validators.required])],\n      hours: [null, Validators.compose([Validators.required])],\n      minutes: [null, Validators.compose([Validators.required])],\n      message: [null, Validators.compose([Validators.required])],\n    });\n  }\n\n  getVolunteeringHoursList() {\n    const volunteeringHoursSubscribe = this._service.getVolunteeringHoursList(this.loginUserId).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.hoursList = data.data;\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(volunteeringHoursSubscribe);\n  }\n\n  getVolunteeringHoursById(id: any) {\n    const volunteeringHoursSubscribe = this._service.getVolunteeringHoursById(id).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.editData = data.data;\n          const dateformat = this._datePipe.transform(\n            this.editData.dateVolunteered,\n            'yyyy-MM-dd'\n          );\n          this.editData.dateVolunteered = dateformat;\n          this.volunteeringHoursForm.patchValue(this.editData);\n          this.openVolunteeringHoursModal();\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(volunteeringHoursSubscribe);\n  }\n\n  onVolunteringHoursSubmit() {\n    const value = this.volunteeringHoursForm.value;\n    value.userId = this.loginUserId;\n    if (value.id == 0 || value.id == null) {\n      this.insertVolunteeringHours(value);\n    } else {\n      this.updateVolunteeringHours(value);\n    }\n  }\n\n  insertVolunteeringHours(value: any) {\n    if (this.volunteeringHoursForm.valid) {\n      const volunteeringHoursSubscribe = this._service.addVolunteeringHours(value).subscribe((data: any) => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data,\n            duration: APP_CONFIG.toastDuration,\n          });\n          setTimeout(() => {\n            this.volunteeringHoursForm.reset();\n            this.closeVolunteeringHoursModal();\n            window.location.reload();\n          }, 1000);\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      });\n      this.unsubscribe.push(volunteeringHoursSubscribe);\n    } else {\n      ValidateForm.validateAllFormFields(this.volunteeringHoursForm);\n    }\n  }\n\n  updateVolunteeringHours(value: any) {\n    if (this.volunteeringHoursForm.valid) {\n      const volunteeringHoursSubscribe = this._service.updateVolunteeringHours(value).subscribe((data: any) => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data,\n            duration: APP_CONFIG.toastDuration,\n          });\n          setTimeout(() => {\n            this.volunteeringHoursForm.reset();\n            this.closeVolunteeringHoursModal();\n            window.location.reload();\n          }, 1000);\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      });\n      this.unsubscribe.push(volunteeringHoursSubscribe);\n    } else {\n      ValidateForm.validateAllFormFields(this.volunteeringHoursForm);\n    }\n  }\n\n  deleteVolunteeringHours() {\n    const volunteeringHoursSubscribe = this._service.deleteVolunteeringHours(this.hoursId).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data,\n            duration: APP_CONFIG.toastDuration,\n          });\n          setTimeout(() => {\n            this.closeVolunteeringDeleteModal();\n            window.location.reload();\n          }, 1000);\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(volunteeringHoursSubscribe);\n  }\n\n  //*****************************************Volunteering TimeSheet Goals ************************************************** */\n  volunteeringGoalsFormValidate() {\n    this.volunteeringGoalsForm = this._fb.group({\n      id: [0],\n      missionId: [null, Validators.compose([Validators.required])],\n      date: [null, Validators.compose([Validators.required])],\n      action: [null, Validators.compose([Validators.required])],\n      message: [null, Validators.compose([Validators.required])],\n    });\n  }\n\n  getVolunteeringGoalsList() {\n    const volunteeringGoalsSubscribe = this._service.getVolunteeringGoalsList(this.loginUserId).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.goalsList = data.data;\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(volunteeringGoalsSubscribe);\n  }\n\n  getVolunteeringGoalsById(id: any) {\n    const volunteeringGoalsSubscribe = this._service.getVolunteeringGoalsById(id).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this.editData = data.data;\n          const dateformat = this._datePipe.transform(\n            this.editData.date,\n            'yyyy-MM-dd'\n          );\n          this.editData.date = dateformat;\n          this.volunteeringGoalsForm.patchValue(this.editData);\n          this.openVolunteeringGoalsModal();\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(volunteeringGoalsSubscribe);\n  }\n\n  onVolunteringGoalsSubmit() {\n    const value = this.volunteeringGoalsForm.value;\n    value.userId = this.loginUserId;\n    if (value.id == 0 || value.id == null) {\n      this.insertVolunteeringGoals(value);\n    } else {\n      this.updateVolunteeringGoals(value);\n    }\n  }\n\n  insertVolunteeringGoals(value: any) {\n    if (this.volunteeringGoalsForm.valid) {\n      const volunteeringGoalsSubscribe = this._service.addVolunteeringGoals(value).subscribe((data: any) => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data,\n            duration: APP_CONFIG.toastDuration,\n          });\n          setTimeout(() => {\n            this.volunteeringGoalsForm.reset();\n            this.closeVolunteeringGoalsModal();\n            window.location.reload();\n          }, 1000);\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      });\n      this.unsubscribe.push(volunteeringGoalsSubscribe);\n    } else {\n      ValidateForm.validateAllFormFields(this.volunteeringGoalsForm);\n    }\n  }\n\n  updateVolunteeringGoals(value: any) {\n    if (this.volunteeringGoalsForm.valid) {\n      const volunteeringGoalsSubscribe = this._service.updateVolunteeringGoals(value).subscribe((data: any) => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data,\n            duration: APP_CONFIG.toastDuration,\n          });\n          setTimeout(() => {\n            this.volunteeringGoalsForm.reset();\n            this.closeVolunteeringGoalsModal();\n            window.location.reload();\n          }, 1000);\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      });\n      this.unsubscribe.push(volunteeringGoalsSubscribe);\n    } else {\n      ValidateForm.validateAllFormFields(this.volunteeringGoalsForm);\n    }\n  }\n\n  deleteVolunteeringGoals() {\n    const volunteeringGoalsSubscribe = this._service.deleteVolunteeringGoals(this.hoursId).subscribe(\n      (data: any) => {\n        if (data.result == 1) {\n          this._toast.success({\n            detail: 'SUCCESS',\n            summary: data.data,\n            duration: APP_CONFIG.toastDuration,\n          });\n          setTimeout(() => {\n            this.closeVolunteeringDeleteModal();\n            window.location.reload();\n          }, 1000);\n        } else {\n          this._toast.error({\n            detail: 'ERROR',\n            summary: data.message,\n            duration: APP_CONFIG.toastDuration,\n          });\n        }\n      },\n      (err) =>\n        this._toast.error({\n          detail: 'ERROR',\n          summary: err.message,\n          duration: APP_CONFIG.toastDuration,\n        })\n    );\n    this.unsubscribe.push(volunteeringGoalsSubscribe);\n  }\n}", "<div>\n  <app-navbar></app-navbar>\n  <hr style=\"margin: 0rem 0 !important\"/>\n</div>\n<div class=\"container-fluid\" style=\"margin-top: 100px;\">\n  <div class=\"heading\">Volunteering Timesheet</div>\n  <div class=\"row\">\n    <div class=\"col-sm-6\">\n      <div class=\"card card-hours\">\n            <p class=\"card-title\">\n              Volunteering Hours\n              <span style=\"float: right;margin-right: 7%;\">\n                <button class=\"btn btn-outline\" (click)=\"openVolunteeringHoursModal()\">\n                  <i class=\"fa fa-plus\"></i>Add\n                </button>\n              </span>\n            </p>\n            <div class=\"table\" style=\"width: 100%;\">\n\n                <thead>\n                  <tr>\n                    <th style=\"width: 400px;\">Mission</th>\n                    <th style=\"width: 160px;\">Date</th>\n                    <th style=\"width: 30px;\">Hours</th>\n                    <th style=\"width: 40px;\">Minutes</th>\n                    <th style=\"width: 100px;text-align: right;\"></th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <ng-container *ngIf=\"(hoursList)as result\">\n                    <tr *ngFor=\"let item of result\">\n                    <td>{{item.missionName}}</td>\n                    <td>{{item.dateVolunteered | date: 'dd-MM-yyyy'}}</td>\n                    <td>{{item.hours}}</td>\n                    <td>{{item.minutes}}</td>\n                    <td style=\"text-align: right;\">\n                      <button type=\"button\" title=\"Edit hours\" class=\"btnedit\" (click)=\"getVolunteeringHoursById(item.id)\">\n                        <i class=\"fa fa-edit \"></i>\n                      </button>\n                      <button type=\"button\" title=\"Remove hours\" class=\"btndelete\" (click)=\"openVolunteeringDeleteModal(item.id)\">\n                        <i class=\"fa fa-trash-o\"></i>\n                      </button>\n                    </td>\n                  </tr>\n                  <tr *ngIf=\"result.length === 0\">\n                    <td colspan=\"6\" style=\"text-align:center;width:100%;font-size:20px;color: red;\"><b>No Data Found </b> </td>\n                  </tr>\n                  </ng-container>\n                </tbody>\n            </div>\n      </div>\n    </div>\n    <div class=\"col-sm-6\">\n      <div class=\"card card-goals\">\n        <p class=\"card-title\">Volunteering Goals\n          <span style=\"float: right;margin-right: 7%;\">\n            <button class=\"btn btn-outline\" (click)=\"openVolunteeringGoalsModal()\">\n              <i class=\"fa fa-plus\"></i>Add\n            </button>\n          </span>\n        </p>\n        <div class=\"table\" style=\"width: 100%;\">\n          <thead>\n            <tr>\n              <th style=\"width: 550px;\">Mission</th>\n              <th style=\"width: 183px;\">Date</th>\n              <th style=\"width: 120px;\">Action</th>\n              <th style=\"width: 150px;text-align: right;\"></th>\n            </tr>\n          </thead>\n          <tbody>\n            <ng-container *ngIf=\"(goalsList)as result\">\n              <tr *ngFor=\"let item of result\" style=\"margin-bottom: 3px;\">\n              <td>{{item.missionName}}</td>\n              <td>{{item.date | date : 'dd-MM-yyyy'}}</td>\n              <td>{{item.action}}</td>\n              <td style=\"text-align: right;\">\n                <button type=\"button\" class=\"btnedit\" title=\"Edit goals\" (click)=\"getVolunteeringGoalsById(item.id)\">\n                  <i class=\"fa fa-edit \"></i>\n                </button>\n                <button type=\"button\" class=\"btndelete\" title=\"Remove goals\" (click)=\"openVolunteeringDeleteModal(item.id)\">\n                  <i class=\"fa fa-trash-o\"></i>\n                </button>\n              </td>\n            </tr>\n            <tr *ngIf=\"result.length === 0\">\n              <td colspan=\"6\" style=\"text-align:center;width:100%;font-size:20px;color: red;\"><b>No Data Found </b> </td>\n            </tr>\n            </ng-container>\n          </tbody>\n      </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n<div class=\"modal fade\" style=\"margin-top: 5%;\" id=\"volunteeringHoursModal\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"exampleModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-xl\" role=\"document\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"exampleModalLabel\">Please input given below Volunteering Hours</h5>\n        <button type=\"button\" class=\"btn-close\" data-dismiss=\"modal\" aria-label=\"Close\" (click)=\"closeVolunteeringHoursModal()\">\n        </button>\n      </div>\n      <div class=\"modal-body\">\n        <form [formGroup]=\"volunteeringHoursForm\">\n          <div>\n            <input type=\"hidden\" formControlName=\"id\">\n          <div class=\"form-group\">\n            <label class=\"col-form-label\">Mission</label>\n            <select  class=\"form-select\"  formControlName=\"missionId\" [class.error]=\"volunteeringHoursForm.controls['missionId'].dirty && volunteeringHoursForm.hasError('required','missionId')\">\n              <option *ngFor=\"let item of missionList\" [value]=\"item.value\">{{item.text}}</option>\n            </select>\n            <span class=\"text-danger\" *ngIf=\"volunteeringHoursForm.controls['missionId'].dirty && volunteeringHoursForm.hasError('required','missionId')\">\n              Mission is required\n            </span>\n          </div>\n          <div class=\"form-group mt-3\">\n            <label class=\"col-form-label\">Date</label>\n            <input type=\"date\" class=\"form-control\" placeholder=\"Select Date\" formControlName=\"dateVolunteered\" [class.error]=\"volunteeringHoursForm.controls['dateVolunteered'].dirty && volunteeringHoursForm.hasError('required','dateVolunteered')\">\n            <span class=\"text-danger\" *ngIf=\"volunteeringHoursForm.controls['dateVolunteered'].dirty && volunteeringHoursForm.hasError('required','dateVolunteered')\">\n              Date is required\n            </span>\n          </div>\n          <div class=\"row mt-3\">\n            <div class=\"form-group col-sm-6\">\n              <label class=\"col-form-label\">Hours</label>\n              <input type=\"text\" formControlName=\"hours\" class=\"form-control\" placeholder=\"Enter Spent Hours\" [class.error]=\"volunteeringHoursForm.controls['hours'].dirty && volunteeringHoursForm.hasError('required','hours')\" >\n              <div class=\"text-danger\" *ngIf=\"volunteeringHoursForm.controls['hours'].dirty && volunteeringHoursForm.hasError('required','hours')\">\n                Hours is required\n              </div>\n            </div>\n            <div class=\"form-group col-sm-6\">\n              <label class=\"col-form-label\">Minutes</label>\n              <input type=\"text\" class=\"form-control\" placeholder=\"Enter Spent Minutes\" formControlName=\"minutes\" [class.error]=\"volunteeringHoursForm.controls['minutes'].dirty && volunteeringHoursForm.hasError('required','minutes')\">\n              <span class=\"text-danger\" *ngIf=\"volunteeringHoursForm.controls['minutes'].dirty && volunteeringHoursForm.hasError('required','minutes')\">\n                Minutes is required\n              </span>\n          </div>\n          </div>\n          <div class=\"form-group mt-3\">\n            <label class=\"col-form-label\">Message</label>\n            <textarea class=\"form-control\" rows=\"4\" placeholder=\"Enter your message\" formControlName=\"message\" [class.error]=\"volunteeringHoursForm.controls['message'].dirty && volunteeringHoursForm.hasError('required','message')\"></textarea>\n            <span class=\"text-danger\" *ngIf=\"volunteeringHoursForm.controls['message'].dirty && volunteeringHoursForm.hasError('required','message')\">\n              Message is required\n            </span>\n          </div>\n          </div>\n        </form>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btnCancel\" data-dismiss=\"modal\" (click)=\"closeVolunteeringHoursModal()\"><span class=\"Cancel\"> Cancel</span> </button>\n        <button type=\"button\" class=\"btnSubmit\" (click)=\"onVolunteringHoursSubmit()\"><span class=\"SubmitData\">Submit</span></button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<div class=\"modal fade\" style=\"margin-top: 5%;\" id=\"volunteeringGoalsModal\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"exampleModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-xl\" role=\"document\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"exampleModalLabel\">Please input given below Volunteering Goal</h5>\n        <button type=\"button\" class=\"btn-close\" data-dismiss=\"modal\" aria-label=\"Close\" (click)=\"closeVolunteeringGoalsModal()\">\n        </button>\n      </div>\n      <div class=\"modal-body\">\n        <form [formGroup]=\"volunteeringGoalsForm\">\n        <div class=\"row\">\n          <input type=\"hidden\" formControlName=\"id\">\n          <div class=\"form-group\">\n            <label class=\"col-form-label\">Mission</label>\n            <select class=\"form-select\" formControlName=\"missionId\" [class.error]=\"volunteeringGoalsForm.controls['missionId'].dirty && volunteeringGoalsForm.hasError('required','missionId')\">\n              <option *ngFor=\"let item of missionList\" [value]=\"item.value\">{{item.text}}</option>\n            </select>\n            <span class=\"text-danger\" *ngIf=\"volunteeringGoalsForm.controls['missionId'].dirty && volunteeringGoalsForm.hasError('required','missionId')\">\n              Mission is required\n            </span>\n          </div>\n          <div class=\"form-group mt-3\">\n            <label class=\"col-form-label\">Actions</label>\n            <select class=\"form-select\" formControlName=\"action\" [class.error]=\"volunteeringGoalsForm.controls['action'].dirty && volunteeringGoalsForm.hasError('required','action')\">\n              <option value=\"1\">Yes</option>\n              <option value=\"0\">No</option>\n            </select>\n            <span class=\"text-danger\" *ngIf=\"volunteeringGoalsForm.controls['action'].dirty && volunteeringGoalsForm.hasError('required','action')\">\n              Action is required\n            </span>\n          </div>\n          <div class=\"form-group mt-3\">\n            <label class=\"col-form-label\">Date</label>\n            <input type=\"date\" class=\"form-control\" placeholder=\"Select Date\" formControlName=\"date\" [class.error]=\"volunteeringGoalsForm.controls['date'].dirty && volunteeringGoalsForm.hasError('required','date')\">\n            <span class=\"text-danger\" *ngIf=\"volunteeringGoalsForm.controls['date'].dirty && volunteeringGoalsForm.hasError('required','date')\">\n              Date is required\n            </span>\n          </div>\n          <div class=\"form-group mt-3\">\n            <label class=\"col-form-label\">Message</label>\n            <textarea class=\"form-control\" rows=\"4\" placeholder=\"Enter your message\" formControlName=\"message\" [class.error]=\"volunteeringGoalsForm.controls['message'].dirty && volunteeringGoalsForm.hasError('required','message')\"></textarea>\n            <span class=\"text-danger\" *ngIf=\"volunteeringGoalsForm.controls['message'].dirty && volunteeringGoalsForm.hasError('required','message')\">\n              Message is required\n            </span>\n          </div>\n        </div>\n        </form>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btnCancel\" data-dismiss=\"modal\" (click)=\"closeVolunteeringGoalsModal()\"><span class=\"Cancel\"> Cancel</span> </button>\n        <button type=\"button\" class=\"btnSubmit\" (click)=\"onVolunteringGoalsSubmit()\"><span class=\"SubmitData\">Submit</span></button>\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"modal fade\" style=\"margin-top: 8%;\" id=\"removeVolunteeringModal\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"exampleModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\" role=\"document\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"exampleModalLabel\">Confirm Delete</h5>\n        <button type=\"button\" class=\"btn-close\" data-dismiss=\"modal\" aria-label=\"Close\" (click)=\"closeVolunteeringDeleteModal()\">\n        </button>\n      </div>\n      <div class=\"modal-body\">\n        <input type=\"hidden\" value=\"\">\n         <h4>Are you sure you want to delete this item?</h4>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btnCancel\" data-dismiss=\"modal\" (click)=\"closeVolunteeringDeleteModal()\"><span class=\"Cancel\"> Cancel</span> </button>\n        <button type=\"button\" class=\"btnRemove\" (click)=\" deleteVolunteeringHours()\"><span class=\"remove\">Delete</span></button>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { environment } from \"../../../environments/environment\"\n\nexport const API_BASE_URL = environment.apiBaseUrl\nexport const IMAGE_BASE_URL = environment.apiBaseUrl\n\nexport const APP_CONFIG = {\n  apiBaseUrl: `${API_BASE_URL}/api`,\n  imageBaseUrl: IMAGE_BASE_URL,\n  tokenKey: \"access_Token\",\n  defaultPageSize: 10,\n  toastDuration: 3000,\n}\n", "export const API_ENDPOINTS = {\n  AUTH: {\n    LOGIN: \"/Login/LoginUser\",\n    REGISTER: \"/Login/Register\",\n    FORGOT_PASSWORD: \"/Login/ForgotPassword\",\n    RESET_PASSWORD: \"/Login/ResetPassword\",\n    CHANGE_PASSWORD: \"/Login/ChangePassword\",\n    GET_USER_BY_ID: \"/Login/GetUserById\",\n    UPDATE_USER: \"/Login/UpdateUser\",\n    GET_USER_PROFILE: \"/Login/GetUserProfileDetailById\",\n    UPDATE_USER_PROFILE: \"/Login/LoginUserProfileUpdate\",\n    GET_LOGIN_USER_BY_ID: \"/Login/LoginUserDetailById\"\n  },\n  MISSION: {\n    LIST: \"/Mission/MissionList\",\n    DETAIL: \"/Mission/MissionDetailById\",\n    ADD: \"/Mission/AddMission\",\n    UPDATE: \"/Mission/UpdateMission\",\n    DELETE: \"/Mission/DeleteMission\",\n    THEME_LIST: \"/Mission/GetMissionThemeList\",\n    SKILL_LIST: \"/Mission/GetMissionSkillList\",\n    APPLICATION_LIST: \"/Mission/MissionApplicationList\",\n    APPLICATION_APPROVE: \"/Mission/MissionApplicationApprove\",\n    APPLICATION_DELETE: \"/Mission/MissionApplicationDelete\",\n  },\n  CLIENT_MISSION: {\n    LIST: \"/ClientMission/ClientSideMissionList\",\n    CLIENT_LIST: \"/ClientMission/MissionClientList\",\n    DETAIL: \"/ClientMission/MissionDetailByMissionId\",\n    APPLY: \"/ClientMission/ApplyMission\",\n    ADD_COMMENT: \"/ClientMission/AddMissionComment\",\n    COMMENT_LIST: \"/ClientMission/MissionCommentListByMissionId\",\n    ADD_FAVORITE: \"/ClientMission/AddMissionFavourite\",\n    REMOVE_FAVORITE: \"/ClientMission/RemoveMissionFavourite\",\n    RATING: \"/ClientMission/MissionRating\",\n    RECENT_VOLUNTEERS: \"/ClientMission/RecentVolunteerList\",\n    GET_USER_LIST: \"/ClientMission/GetUserList\",\n    SEND_INVITE: \"/ClientMission/SendInviteMissionMail\",\n    MISSION_TITLE: \"/Story/GetMissionTitle\"\n  },\n  COMMON: {\n    COUNTRY_LIST: \"/Common/CountryList\",\n    CITY_LIST: \"/Common/CityList\",\n    UPLOAD_IMAGE: \"/Common/UploadImage\",\n    CONTACT_US: \"/Common/ContactUs\",\n    ADD_USER_SKILL: \"/Common/AddUserSkill\",\n    GET_USER_SKILL: \"/Common/GetUserSkill\",\n    MISSION_TITLE_LIST: \"/Common/MissionTitleList\",\n    MISSION_COUNTRY_LIST: \"/Common/MissionCountryList\",\n    MISSION_CITY_LIST: \"/Common/MissionCityList\",\n    MISSION_THEME_LIST: \"/Common/MissionThemeList\",\n    MISSION_SKILL_LIST: \"/Common/MissionSkillList\",\n  },\n  TIMESHEET: {\n    GET_HOURS_LIST: \"/VolunteeringTimesheet/GetVolunteeringHoursList\",\n    GET_HOURS_BY_ID: \"/VolunteeringTimesheet/GetVolunteeringHoursListById\",\n    ADD_HOURS: \"/VolunteeringTimesheet/AddVolunteeringHours\",\n    UPDATE_HOURS: \"/VolunteeringTimesheet/UpdateVolunteeringHours\",\n    DELETE_HOURS: \"/VolunteeringTimesheet/DeleteVolunteeringHours\",\n    GET_GOALS_LIST: \"/VolunteeringTimesheet/GetVolunteeringGoalsList\",\n    GET_GOALS_BY_ID: \"/VolunteeringTimesheet/GetVolunteeringGoalsListById\",\n    ADD_GOALS: \"/VolunteeringTimesheet/AddVolunteeringGoals\",\n    UPDATE_GOALS: \"/VolunteeringTimesheet/UpdateVolunteeringGoals\",\n    DELETE_GOALS: \"/VolunteeringTimesheet/DeleteVolunteeringGoals\",\n    VOLUNTEERING_MISSION_LIST: \"/VolunteeringTimesheet/VolunteeringMissionList\",\n  },\n  AdminUser:{\n    USER_LIST: \"/AdminUser/UserDetailList\",\n    DELETE_USER: \"/AdminUser/DeleteUser\",\n  },\n  MISSION_THEME: {\n    LIST: \"/MissionTheme/GetMissionThemeList\",\n    GET_BY_ID: \"/MissionTheme/GetMissionThemeById\",\n    ADD: \"/MissionTheme/AddMissionTheme\",\n    UPDATE: \"/MissionTheme/UpdateMissionTheme\",\n    DELETE: \"/MissionTheme/DeleteMissionTheme\"\n  },\n  MISSION_SKILL: {\n    LIST: \"/MissionSkill/GetMissionSkillList\",\n    GET_BY_ID: \"/MissionSkill/GetMissionSkillById\",\n    ADD: \"/MissionSkill/AddMissionSkill\",\n    UPDATE: \"/MissionSkill/UpdateMissionSkill\",\n    DELETE: \"/MissionSkill/DeleteMissionSkill\"\n  }\n}", "import { Injectable } from \"@angular/core\"\nimport { Router } from \"@angular/router\"\nimport { NgToastService } from \"ng-angular-popup\"\nimport { AuthService } from \"../services/auth.service\"\nimport { APP_CONFIG } from \"../configs/environment.config\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class AuthGuard {\n  constructor(\n    private service: AuthService,\n    public router: Router,\n    public toastr: NgToastService,\n  ) {}\n  canActivate(): boolean {\n    if (this.service.isLoggedIn()) {\n      return true\n    } else {\n      this.toastr.error({ detail: \"ERROR\", summary: \"Invalid Client Request\", duration: APP_CONFIG.toastDuration })\n      this.router.navigate([\"admin\"])\n      return false\n    }\n  }\n}\n", "import { FormControl, FormGroup } from \"@angular/forms\"\n\nexport default class ValidateForm {\n  static validateAllFormFields(formGroup: FormGroup) {\n    Object.keys(formGroup.controls).forEach((field) => {\n      const control = formGroup.get(field)\n      if (control instanceof FormControl) {\n        control.markAsDirty({\n          onlySelf: true,\n        })\n      } else if (control instanceof FormGroup) {\n        this.validateAllFormFields(control)\n      }\n    })\n  }\n}\n", "import { Injectable } from \"@angular/core\"\nimport {\n  HttpRequest,\n  HttpHandler,\n  HttpEvent,\n  HttpInterceptor,\n  HttpErrorResponse,\n} from \"@angular/common/http\"\nimport { catchError, type Observable, throwError } from \"rxjs\"\nimport { AuthService } from \"../services/auth.service\"\nimport { Router } from \"@angular/router\"\nimport { NgToastService } from \"ng-angular-popup\"\nimport { APP_CONFIG } from \"../configs/environment.config\"\n\n@Injectable()\nexport class TokenInterceptor implements HttpInterceptor {\n  constructor(\n    public service: AuthService,\n    private route: Router,\n    private toastr: NgToastService,\n  ) {}\n\n  intercept(request: HttpRequest<unknown>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<unknown>> {\n    const myToken = this.service.getToken()\n\n    if (myToken) {\n      request = request.clone({\n        setHeaders: { Authorization: `Bearer ${myToken}` },\n      })\n    }\n    return next.handle(request).pipe(\n      catchError((err: any) => {\n        if (err instanceof HttpErrorResponse) {\n          if (err.status === 401) {\n            this.toastr.error({ detail: \"ERROR\", summary: \"Token is Expired, Please Login Again\", duration: APP_CONFIG.toastDuration })\n            this.service.loggedOut()\n            this.route.navigate([\"/admin\"])\n          }\n        }\n        return throwError(() => new Error(\"Some other error occured\"))\n      }),\n    )\n  }\n}\n", "import { Pipe, type PipeTransform } from \"@angular/core\"\n\n@Pipe({\n  name: \"search\",\n  standalone: true\n})\nexport class SearchPipe implements PipeTransform {\n  transform(value: any[], args?: any): any {\n    if (!value) return null\n    if (!args) return value\n\n    args = args.toLowerCase()\n\n    return value.filter((item: any) => {\n      return JSON.stringify(item).toLowerCase().includes(args)\n    })\n  }\n}\n", "import { HttpClient } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject, type Observable } from 'rxjs';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport { APP_CONFIG } from '../configs/environment.config';\nimport { User } from '../interfaces/user.interface';\nimport { API_ENDPOINTS } from '../constants/api.constants';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class AuthService {\n  apiUrl = APP_CONFIG.apiBaseUrl;\n  imageUrl = APP_CONFIG.imageBaseUrl;\n\n  currentUser: BehaviorSubject<any> = new BehaviorSubject(null);\n  currentUserName: BehaviorSubject<any> = new BehaviorSubject(null);\n  currentUserData: any;\n  public userPayLoad: any;\n  jwthelperService = new JwtHelperService();\n\n  constructor(public http: HttpClient) {\n    this.userPayLoad = this.decodedToken();\n  }\n\n  registerUser(user: User) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.REGISTER}`,\n      user,\n      { responseType: 'json' }\n    );\n  }\n\n  getUserById(id: number): Observable<User[]> {\n    return this.http.get<User[]>(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.GET_USER_BY_ID}/${id}`\n    );\n  }\n\n  updateUser(data: FormData) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.UPDATE_USER}`,\n      data\n    );\n  }\n\n  loginUser(loginInfo: Array<string>) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.LOGIN}`,\n      {\n        EmailAddress: loginInfo[0],\n        Password: loginInfo[1],\n      },\n      { responseType: 'json' }\n    );\n  }\n\n  forgotPasswordEmailCheck(data: any) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.FORGOT_PASSWORD}`,\n      data\n    );\n  }\n\n  resetPassword(data: any) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.RESET_PASSWORD}`,\n      data,\n      { responseType: 'text' }\n    );\n  }\n\n  changePassword(data: any) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.CHANGE_PASSWORD}`,\n      data\n    );\n  }\n\n  getToken() {\n    return localStorage.getItem('access_Token');\n  }\n\n  setToken(token: string) {\n    localStorage.setItem('access_Token', token);\n  }\n\n  isLoggedIn(): boolean {\n    return localStorage.getItem('access_Token') ? true : false;\n  }\n\n  loggedOut() {\n    localStorage.removeItem('access_Token');\n  }\n  public getCurrentUser() {\n    return this.currentUser.asObservable();\n  }\n\n  public setCurrentUser(userDetail: any) {\n    return this.currentUser.next(userDetail);\n  }\n\n  decodedToken() {\n    const token = this.getToken();\n    return this.jwthelperService.decodeToken(token);\n  }\n\n  getUserFullName() {\n    if (this.userPayLoad) return this.userPayLoad.fullName;\n  }\n\n  public getUserDetail() {\n    if (this.userPayLoad) return this.userPayLoad;\n  }\n}\n", "import { HttpClient } from \"@angular/common/http\"\nimport { Injectable } from \"@angular/core\"\nimport { Observable } from \"rxjs\"\n\nimport { environment } from \"../../../environments/environment\"\nimport { API_ENDPOINTS } from \"../constants/api.constants\"\nimport { Mission } from \"../interfaces/common.interface\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class ClientMissionService {\n  constructor(private http: HttpClient) {}\n  apiUrl = `${environment.apiBaseUrl}/api`\n  imageUrl = environment.apiBaseUrl\n\n  //HomePage\n  missionList(userId: any): Observable<Mission[]> {\n    return this.http.get<Mission[]>(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.LIST}/${userId}`)\n  }\n\n  missionClientList(data: any) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.CLIENT_LIST}`, data)\n  }\n\n  missionDetailByMissionId(data: any) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.DETAIL}/`, data)\n  }\n\n  applyMission(data: any) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.APPLY}`, data)\n  }\n\n  //Mission Comment\n  addMissionComment(data: any) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.ADD_COMMENT}`, data)\n  }\n\n  missionCommentListByMissionId(missionId: any): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.COMMENT_LIST}/${missionId}`)\n  }\n\n  //Mission Favourite\n  addMissionFavourite(data: any) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.ADD_FAVORITE}`, data)\n  }\n\n  removeMissionFavourite(data: any) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.REMOVE_FAVORITE}`, data)\n  }\n\n  //Mission Rating\n  missionRating(data: any) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.RATING}`, data)\n  }\n\n  //Mission Recent VolunteerList\n  recentVolunteerList(data: any) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.RECENT_VOLUNTEERS}`, data)\n  }\n\n  //ShareOrInviteMission\n  getUserList(userId: any): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.GET_USER_LIST}/${userId}`)\n  }\n\n  sendInviteMissionMail(data: any) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.SEND_INVITE}`, data)\n  }\n}\n", "import { HttpClient } from \"@angular/common/http\"\nimport { Injectable } from \"@angular/core\"\nimport { Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\nimport { User, UserDetail } from \"../interfaces/user.interface\"\nimport { API_ENDPOINTS } from \"../constants/api.constants\"\nimport { Mission } from \"../interfaces/common.interface\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class ClientService {\n  constructor(private http: HttpClient) {}\n  apiUrl = `${environment.apiBaseUrl}/api`\n  imageUrl = environment.apiBaseUrl\n  \n  //ShareYourStory\n  missionTitleList(): Observable<Mission[]> {\n    return this.http.get<Mission[]>(`${this.apiUrl + API_ENDPOINTS.CLIENT_MISSION.MISSION_TITLE}`)\n  }\n\n  loginUserDetailById(id: any): Observable<User[]> {\n    return this.http.get<User[]>(`${this.apiUrl + API_ENDPOINTS.AUTH.GET_LOGIN_USER_BY_ID}/${id}`)\n  }\n\n  loginUserProfileUpdate(userDetail: UserDetail) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.AUTH.UPDATE_USER_PROFILE}`, userDetail)\n  }\n\n  getUserProfileDetailById(userId: any) {\n    return this.http.get<UserDetail[]>(`${this.apiUrl + API_ENDPOINTS.AUTH.GET_USER_PROFILE}/${userId}`)\n  }\n}\n", "import { HttpClient } from \"@angular/common/http\"\nimport { Injectable } from \"@angular/core\"\nimport { BehaviorSubject, Observable } from \"rxjs\"\nimport { environment } from \"../../../environments/environment\"\n\nimport { API_ENDPOINTS } from \"../constants/api.constants\"\nimport { City, Country, Mission } from \"../interfaces/common.interface\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class CommonService {\n  constructor(private http: HttpClient) {}\n  apiUrl = `${environment.apiBaseUrl}/api`\n  imageUrl = environment.apiBaseUrl\n  searchList: BehaviorSubject<any> = new BehaviorSubject<any>(\"\")\n\n  getMissionCountryList() {\n    return this.http.get(`${this.apiUrl}${API_ENDPOINTS.COMMON.MISSION_COUNTRY_LIST}`)\n  }\n\n  getMissionCityList() {\n    return this.http.get(`${this.apiUrl}${API_ENDPOINTS.COMMON.MISSION_CITY_LIST}`)\n  }\n\n  getMissionThemeList() {\n    return this.http.get(`${this.apiUrl}${API_ENDPOINTS.COMMON.MISSION_THEME_LIST}`)\n  }\n\n  getMissionSkillList() {\n    return this.http.get(`${this.apiUrl}${API_ENDPOINTS.COMMON.MISSION_SKILL_LIST}`)\n  }\n\n  uploadImage(data: any) {\n    return this.http.post(`${this.apiUrl}${API_ENDPOINTS.COMMON.UPLOAD_IMAGE}`, data)\n  }\n\n  countryList(): Observable<Country[]> {\n    return this.http.get<Country[]>(`${this.apiUrl}${API_ENDPOINTS.COMMON.COUNTRY_LIST}`)\n  }\n  \n  cityList(countryId: any): Observable<City[]> {\n    return this.http.get<City[]>(`${this.apiUrl}${API_ENDPOINTS.COMMON.CITY_LIST}/${countryId}`)\n  }\n\n  contactUs(data: any) {\n    return this.http.post(`${this.apiUrl}${API_ENDPOINTS.COMMON.CONTACT_US}`, data)\n  }\n\n  //Volunteering TimeSheet Hours\n  getMissionTitle(): Observable<Mission[]> {\n    return this.http.get<Mission[]>(`${this.apiUrl}${API_ENDPOINTS.COMMON.MISSION_TITLE_LIST}`)\n  }\n  \n  //Add Skill\n  addUserSkill(data: any) {\n    return this.http.post(`${this.apiUrl}${API_ENDPOINTS.COMMON.ADD_USER_SKILL}`, data)\n  }\n\n  getUserSkill(userId: any): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}${API_ENDPOINTS.COMMON.GET_USER_SKILL}/${userId}`)\n  }\n}", "import { HttpClient } from \"@angular/common/http\"\nimport { Injectable } from \"@angular/core\"\nimport { Observable } from \"rxjs\"\n\nimport { VolunteeringGoals, VolunteeringHours } from \"../interfaces/volunteering.interface\"\nimport { environment } from \"../../../environments/environment\"\nimport { API_ENDPOINTS } from \"../constants/api.constants\"\nimport { Mission } from \"../interfaces/common.interface\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class VolunteeringTimeSheetService {\n  constructor(private http: HttpClient) {}\n  apiUrl = `${environment.apiBaseUrl}/api`\n  imageUrl = environment.apiBaseUrl\n\n  //Volunteering Timesheet Hours\n  getVolunteeringHoursList(userid: any): Observable<VolunteeringHours[]> {\n    return this.http.get<VolunteeringHours[]>(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.GET_HOURS_LIST}/${userid}`)\n  }\n\n  getVolunteeringHoursById(id: number): Observable<VolunteeringHours[]> {\n    return this.http.get<VolunteeringHours[]>(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.GET_HOURS_BY_ID}/${id}`)\n  }\n\n  volunteeringMissionList(id: number): Observable<Mission[]> {\n    return this.http.get<Mission[]>(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.VOLUNTEERING_MISSION_LIST}/${id}`)\n  }\n\n  addVolunteeringHours(data: VolunteeringHours) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.ADD_HOURS}`, data)\n  }\n\n  updateVolunteeringHours(data: VolunteeringHours) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.UPDATE_HOURS}`, data)\n  }\n\n  deleteVolunteeringHours(id: any) {\n    return this.http.delete(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.DELETE_HOURS}/${id}`)\n  }\n\n  //Volunteering Timesheet Goals\n  getVolunteeringGoalsList(userid: any): Observable<VolunteeringGoals[]> {\n    return this.http.get<VolunteeringGoals[]>(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.GET_GOALS_LIST}/${userid}`)\n  }\n\n  getVolunteeringGoalsById(id: number): Observable<VolunteeringGoals[]> {\n    return this.http.get<VolunteeringGoals[]>(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.GET_GOALS_BY_ID}/${id}`)\n  }\n\n  addVolunteeringGoals(data: VolunteeringGoals) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.ADD_GOALS}`, data)\n  }\n\n  updateVolunteeringGoals(data: VolunteeringGoals) {\n    return this.http.post(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.UPDATE_GOALS}`, data)\n  }\n\n  deleteVolunteeringGoals(id: any) {\n    return this.http.delete(`${this.apiUrl + API_ENDPOINTS.TIMESHEET.DELETE_GOALS}/${id}`)\n  }\n}\n", "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\n\nexport const environment = {\n  production: false,\n  apiBaseUrl: 'http://localhost:56577',\n};\n\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.\n", "import { enableProdMode, provideZoneChangeDetection } from '@angular/core';\nimport { bootstrapApplication, provideClientHydration, withEventReplay } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { environment } from './environments/environment';\nimport { importProvidersFrom } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { TokenInterceptor } from './app/main/interceptors/token.interceptor';\nimport { DatePipe } from '@angular/common';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app/app.route';\nimport { ToastrModule } from 'ngx-toastr';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n\nif (environment.production) {\n  enableProdMode();\n}\n\nbootstrapApplication(AppComponent, {\n    providers: [\n        importProvidersFrom(BrowserModule, HttpClient),\n        {\n          provide: HTTP_INTERCEPTORS,\n          useClass: TokenInterceptor,\n          multi: true,\n        },\n        importProvidersFrom(ToastrModule.forRoot()),\n        importProvidersFrom(BrowserAnimationsModule),\n        provideRouter(routes),\n        DatePipe,\n        provideHttpClient(withInterceptorsFromDi()),\n    ]\n}).catch(err => console.error(err));", "var map = {\n\t\"./af\": 5637,\n\t\"./af.js\": 5637,\n\t\"./ar\": 6777,\n\t\"./ar-dz\": 4508,\n\t\"./ar-dz.js\": 4508,\n\t\"./ar-kw\": 7504,\n\t\"./ar-kw.js\": 7504,\n\t\"./ar-ly\": 5373,\n\t\"./ar-ly.js\": 5373,\n\t\"./ar-ma\": 2412,\n\t\"./ar-ma.js\": 2412,\n\t\"./ar-ps\": 8823,\n\t\"./ar-ps.js\": 8823,\n\t\"./ar-sa\": 6670,\n\t\"./ar-sa.js\": 6670,\n\t\"./ar-tn\": 6448,\n\t\"./ar-tn.js\": 6448,\n\t\"./ar.js\": 6777,\n\t\"./az\": 3009,\n\t\"./az.js\": 3009,\n\t\"./be\": 8299,\n\t\"./be.js\": 8299,\n\t\"./bg\": 4685,\n\t\"./bg.js\": 4685,\n\t\"./bm\": 1171,\n\t\"./bm.js\": 1171,\n\t\"./bn\": 3590,\n\t\"./bn-bd\": 5841,\n\t\"./bn-bd.js\": 5841,\n\t\"./bn.js\": 3590,\n\t\"./bo\": 4309,\n\t\"./bo.js\": 4309,\n\t\"./br\": 4130,\n\t\"./br.js\": 4130,\n\t\"./bs\": 8033,\n\t\"./bs.js\": 8033,\n\t\"./ca\": 5294,\n\t\"./ca.js\": 5294,\n\t\"./cs\": 3028,\n\t\"./cs.js\": 3028,\n\t\"./cv\": 5807,\n\t\"./cv.js\": 5807,\n\t\"./cy\": 342,\n\t\"./cy.js\": 342,\n\t\"./da\": 8269,\n\t\"./da.js\": 8269,\n\t\"./de\": 1489,\n\t\"./de-at\": 2123,\n\t\"./de-at.js\": 2123,\n\t\"./de-ch\": 7757,\n\t\"./de-ch.js\": 7757,\n\t\"./de.js\": 1489,\n\t\"./dv\": 8152,\n\t\"./dv.js\": 8152,\n\t\"./el\": 7687,\n\t\"./el.js\": 7687,\n\t\"./en-au\": 6668,\n\t\"./en-au.js\": 6668,\n\t\"./en-ca\": 6798,\n\t\"./en-ca.js\": 6798,\n\t\"./en-gb\": 3615,\n\t\"./en-gb.js\": 3615,\n\t\"./en-ie\": 1364,\n\t\"./en-ie.js\": 1364,\n\t\"./en-il\": 9907,\n\t\"./en-il.js\": 9907,\n\t\"./en-in\": 533,\n\t\"./en-in.js\": 533,\n\t\"./en-nz\": 3190,\n\t\"./en-nz.js\": 3190,\n\t\"./en-sg\": 1096,\n\t\"./en-sg.js\": 1096,\n\t\"./eo\": 3962,\n\t\"./eo.js\": 3962,\n\t\"./es\": 7726,\n\t\"./es-do\": 5010,\n\t\"./es-do.js\": 5010,\n\t\"./es-mx\": 3654,\n\t\"./es-mx.js\": 3654,\n\t\"./es-us\": 9043,\n\t\"./es-us.js\": 9043,\n\t\"./es.js\": 7726,\n\t\"./et\": 5343,\n\t\"./et.js\": 5343,\n\t\"./eu\": 728,\n\t\"./eu.js\": 728,\n\t\"./fa\": 787,\n\t\"./fa.js\": 787,\n\t\"./fi\": 1771,\n\t\"./fi.js\": 1771,\n\t\"./fil\": 5335,\n\t\"./fil.js\": 5335,\n\t\"./fo\": 9761,\n\t\"./fo.js\": 9761,\n\t\"./fr\": 1670,\n\t\"./fr-ca\": 8991,\n\t\"./fr-ca.js\": 8991,\n\t\"./fr-ch\": 7280,\n\t\"./fr-ch.js\": 7280,\n\t\"./fr.js\": 1670,\n\t\"./fy\": 4203,\n\t\"./fy.js\": 4203,\n\t\"./ga\": 9858,\n\t\"./ga.js\": 9858,\n\t\"./gd\": 8605,\n\t\"./gd.js\": 8605,\n\t\"./gl\": 7365,\n\t\"./gl.js\": 7365,\n\t\"./gom-deva\": 3896,\n\t\"./gom-deva.js\": 3896,\n\t\"./gom-latn\": 5587,\n\t\"./gom-latn.js\": 5587,\n\t\"./gu\": 7950,\n\t\"./gu.js\": 7950,\n\t\"./he\": 2029,\n\t\"./he.js\": 2029,\n\t\"./hi\": 1897,\n\t\"./hi.js\": 1897,\n\t\"./hr\": 9816,\n\t\"./hr.js\": 9816,\n\t\"./hu\": 2253,\n\t\"./hu.js\": 2253,\n\t\"./hy-am\": 8196,\n\t\"./hy-am.js\": 8196,\n\t\"./id\": 1307,\n\t\"./id.js\": 1307,\n\t\"./is\": 5474,\n\t\"./is.js\": 5474,\n\t\"./it\": 3099,\n\t\"./it-ch\": 8188,\n\t\"./it-ch.js\": 8188,\n\t\"./it.js\": 3099,\n\t\"./ja\": 9127,\n\t\"./ja.js\": 9127,\n\t\"./jv\": 182,\n\t\"./jv.js\": 182,\n\t\"./ka\": 758,\n\t\"./ka.js\": 758,\n\t\"./kk\": 3444,\n\t\"./kk.js\": 3444,\n\t\"./km\": 2034,\n\t\"./km.js\": 2034,\n\t\"./kn\": 6223,\n\t\"./kn.js\": 6223,\n\t\"./ko\": 3064,\n\t\"./ko.js\": 3064,\n\t\"./ku\": 8714,\n\t\"./ku-kmr\": 961,\n\t\"./ku-kmr.js\": 961,\n\t\"./ku.js\": 8714,\n\t\"./ky\": 2062,\n\t\"./ky.js\": 2062,\n\t\"./lb\": 4796,\n\t\"./lb.js\": 4796,\n\t\"./lo\": 9279,\n\t\"./lo.js\": 9279,\n\t\"./lt\": 106,\n\t\"./lt.js\": 106,\n\t\"./lv\": 1840,\n\t\"./lv.js\": 1840,\n\t\"./me\": 2240,\n\t\"./me.js\": 2240,\n\t\"./mi\": 3588,\n\t\"./mi.js\": 3588,\n\t\"./mk\": 5518,\n\t\"./mk.js\": 5518,\n\t\"./ml\": 7823,\n\t\"./ml.js\": 7823,\n\t\"./mn\": 8657,\n\t\"./mn.js\": 8657,\n\t\"./mr\": 1285,\n\t\"./mr.js\": 1285,\n\t\"./ms\": 3014,\n\t\"./ms-my\": 6253,\n\t\"./ms-my.js\": 6253,\n\t\"./ms.js\": 3014,\n\t\"./mt\": 167,\n\t\"./mt.js\": 167,\n\t\"./my\": 7940,\n\t\"./my.js\": 7940,\n\t\"./nb\": 14,\n\t\"./nb.js\": 14,\n\t\"./ne\": 9023,\n\t\"./ne.js\": 9023,\n\t\"./nl\": 4208,\n\t\"./nl-be\": 1412,\n\t\"./nl-be.js\": 1412,\n\t\"./nl.js\": 4208,\n\t\"./nn\": 1354,\n\t\"./nn.js\": 1354,\n\t\"./oc-lnc\": 870,\n\t\"./oc-lnc.js\": 870,\n\t\"./pa-in\": 389,\n\t\"./pa-in.js\": 389,\n\t\"./pl\": 7342,\n\t\"./pl.js\": 7342,\n\t\"./pt\": 4774,\n\t\"./pt-br\": 3003,\n\t\"./pt-br.js\": 3003,\n\t\"./pt.js\": 4774,\n\t\"./ro\": 5333,\n\t\"./ro.js\": 5333,\n\t\"./ru\": 3451,\n\t\"./ru.js\": 3451,\n\t\"./sd\": 3921,\n\t\"./sd.js\": 3921,\n\t\"./se\": 9682,\n\t\"./se.js\": 9682,\n\t\"./si\": 582,\n\t\"./si.js\": 582,\n\t\"./sk\": 4348,\n\t\"./sk.js\": 4348,\n\t\"./sl\": 5337,\n\t\"./sl.js\": 5337,\n\t\"./sq\": 9358,\n\t\"./sq.js\": 9358,\n\t\"./sr\": 683,\n\t\"./sr-cyrl\": 9382,\n\t\"./sr-cyrl.js\": 9382,\n\t\"./sr.js\": 683,\n\t\"./ss\": 1156,\n\t\"./ss.js\": 1156,\n\t\"./sv\": 9855,\n\t\"./sv.js\": 9855,\n\t\"./sw\": 8536,\n\t\"./sw.js\": 8536,\n\t\"./ta\": 7754,\n\t\"./ta.js\": 7754,\n\t\"./te\": 7809,\n\t\"./te.js\": 7809,\n\t\"./tet\": 1297,\n\t\"./tet.js\": 1297,\n\t\"./tg\": 2527,\n\t\"./tg.js\": 2527,\n\t\"./th\": 5862,\n\t\"./th.js\": 5862,\n\t\"./tk\": 9331,\n\t\"./tk.js\": 9331,\n\t\"./tl-ph\": 4387,\n\t\"./tl-ph.js\": 4387,\n\t\"./tlh\": 3592,\n\t\"./tlh.js\": 3592,\n\t\"./tr\": 9732,\n\t\"./tr.js\": 9732,\n\t\"./tzl\": 9570,\n\t\"./tzl.js\": 9570,\n\t\"./tzm\": 3553,\n\t\"./tzm-latn\": 7699,\n\t\"./tzm-latn.js\": 7699,\n\t\"./tzm.js\": 3553,\n\t\"./ug-cn\": 5674,\n\t\"./ug-cn.js\": 5674,\n\t\"./uk\": 9974,\n\t\"./uk.js\": 9974,\n\t\"./ur\": 5773,\n\t\"./ur.js\": 5773,\n\t\"./uz\": 357,\n\t\"./uz-latn\": 7135,\n\t\"./uz-latn.js\": 7135,\n\t\"./uz.js\": 357,\n\t\"./vi\": 43,\n\t\"./vi.js\": 43,\n\t\"./x-pseudo\": 767,\n\t\"./x-pseudo.js\": 767,\n\t\"./yo\": 150,\n\t\"./yo.js\": 150,\n\t\"./zh-cn\": 1828,\n\t\"./zh-cn.js\": 1828,\n\t\"./zh-hk\": 6644,\n\t\"./zh-hk.js\": 6644,\n\t\"./zh-mo\": 9305,\n\t\"./zh-mo.js\": 9305,\n\t\"./zh-tw\": 1860,\n\t\"./zh-tw.js\": 1860\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 5358;"], "names": ["RouterOutlet", "NgToastModule", "AppComponent", "constructor", "title", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "i1", "NgToastComponent", "styles", "<PERSON><PERSON><PERSON><PERSON>", "HomeComponent", "NewMissionComponent", "PrivacyPolicyComponent", "VolunteeringMissionComponent", "ForgotPasswordComponent", "LoginComponent", "RegisterComponent", "ResetPasswordComponent", "UserEditProfileComponent", "VolunteeringTimesheetComponent", "routes", "path", "component", "canActivate", "loadChildren", "RouterModule", "FooterComponent", "ngOnInit", "consts", "FooterComponent_Template", "ɵɵtext", "RouterLink", "CommonModule", "dateFormat", "moment", "FormsModule", "APP_CONFIG", "NavbarComponent", "NgxPaginationModule", "SearchPipe", "ɵɵlistener", "HomeComponent_div_35_ng_container_1_div_1_button_43_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "item_r2", "ɵɵnextContext", "$implicit", "ctx_r2", "ɵɵresetView", "checkUserLoginOrNot", "id", "ɵɵproperty", "missionStatus", "missionApplyStatus", "missionApproveStatus", "ɵɵtemplate", "HomeComponent_div_35_ng_container_1_div_1_div_3_Template", "HomeComponent_div_35_ng_container_1_div_1_div_4_Template", "HomeComponent_div_35_ng_container_1_div_1_div_5_Template", "HomeComponent_div_35_ng_container_1_div_1_div_6_Template", "HomeComponent_div_35_ng_container_1_div_1_button_43_Template", "HomeComponent_div_35_ng_container_1_div_1_button_44_Template", "HomeComponent_div_35_ng_container_1_div_1_button_45_Template", "ɵɵadvance", "ɵɵpropertyInterpolate", "missionImages", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "missionThemeName", "missionTitle", "ɵɵtextInterpolate1", "missionOrganisationDetail", "countryName", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "startDate", "endDate", "totalSheets", "registrationDeadLine", "countryId", "HomeComponent_div_35_ng_container_1_div_3_Template_pagination_controls_pageChange_1_listener", "$event", "_r4", "page", "ɵɵelementContainerStart", "HomeComponent_div_35_ng_container_1_div_1_Template", "HomeComponent_div_35_ng_container_1_div_2_Template", "HomeComponent_div_35_ng_container_1_div_3_Template", "result_r5", "length", "HomeComponent_div_35_ng_container_1_Template", "missionList", "searchParam", "ɵɵpureFunction3", "_c0", "missionPerPages", "totalMission", "HomeComponent_div_36_ng_container_1_div_1_button_61_Template_button_click_0_listener", "_r6", "item_r7", "HomeComponent_div_36_ng_container_1_div_1_div_4_Template", "HomeComponent_div_36_ng_container_1_div_1_div_5_Template", "HomeComponent_div_36_ng_container_1_div_1_div_6_Template", "HomeComponent_div_36_ng_container_1_div_1_div_7_Template", "HomeComponent_div_36_ng_container_1_div_1_button_61_Template", "HomeComponent_div_36_ng_container_1_div_1_button_62_Template", "HomeComponent_div_36_ng_container_1_div_1_button_63_Template", "cityName", "missionOrganisationName", "missionDescription", "missionSkillName", "HomeComponent_div_36_ng_container_1_div_3_Template_pagination_controls_pageChange_1_listener", "_r8", "HomeComponent_div_36_ng_container_1_div_1_Template", "HomeComponent_div_36_ng_container_1_div_2_Template", "HomeComponent_div_36_ng_container_1_div_3_Template", "result_r9", "HomeComponent_div_36_ng_container_1_Template", "listmissionPerPages", "_service", "_toast", "_router", "_commonservice", "_adminservice", "userList", "loginUserId", "missionStatu", "favImag", "favImag1", "view", "missionFavourite", "usercheckedlist", "unsubscribe", "currentUserSubscribe", "getCurrentUser", "subscribe", "data", "loginUserDetail", "getUserDetail", "userId", "loginUserName", "fullName", "loginemailAddress", "emailAddress", "allMissionList", "searchListSubscribe", "searchList", "missionData", "push", "ngOnDestroy", "for<PERSON>ach", "sb", "onChangeGrid", "onChangeList", "missionListSubscribe", "result", "map", "x", "missionimg", "imageUrl", "rating3", "rating", "cityId", "missionThemeId", "missionSkillId", "split", "missionDateStatus", "missionDeadLineStatus", "error", "detail", "summary", "message", "duration", "toastDuration", "sortingData", "e", "selected<PERSON><PERSON><PERSON>", "target", "value", "sort", "a", "b", "toLowerCase", "sortingList", "selected<PERSON><PERSON>", "sortestValue", "missionClientSubscribe", "missionClientList", "openMissionApplyModal", "missionApplyModal", "show", "closeMissionApplyModal", "hide", "tokenDetail", "decodedToken", "userType", "navigate", "find", "v", "now", "Date", "appliedDate", "applyMission", "redirectVolunteering", "missionId", "userImage", "warning", "format", "status", "sheet", "applyMissionSubscribe", "success", "setTimeout", "window", "location", "reload", "err", "getUserList", "userListSubscribe", "getUserCheckedList", "isSelected", "item", "userFullName", "missionShareUserEmailAddress", "baseUrl", "document", "origin", "missionid", "index", "splice", "ɵɵdirectiveInject", "ClientMissionService", "i2", "NgToastService", "i3", "Router", "i4", "CommonService", "i5", "AuthService", "HomeComponent_Template", "HomeComponent_Template_select_change_13_listener", "HomeComponent_Template_div_click_30_listener", "HomeComponent_Template_img_click_33_listener", "ɵɵtwoWayListener", "HomeComponent_Template_input_ngModelChange_34_listener", "ɵɵtwoWayBindingSet", "HomeComponent_div_35_Template", "HomeComponent_div_36_Template", "HomeComponent_Template_button_click_45_listener", "HomeComponent_Template_button_click_73_listener", "HomeComponent_Template_button_click_76_listener", "ɵɵtwoWayProperty", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i7", "PaginatePipe", "PaginationControlsComponent", "i8", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "NgControlStatus", "NgModel", "ReactiveFormsModule", "Validators", "ForgotPasswordComponent_span_23_span_1_Template", "ForgotPasswordComponent_span_23_span_2_Template", "ctx_r0", "<PERSON><PERSON><PERSON><PERSON>", "_fb", "forgotPassword", "forgotPasswordForm", "group", "compose", "required", "email", "get", "onSubmit", "formValid", "valid", "addFormValue", "forgotPasswordSubscribe", "forgotPasswordEmailCheck", "FormBuilder", "ForgotPasswordComponent_Template", "ForgotPasswordComponent_Template_form_ngSubmit_18_listener", "ForgotPasswordComponent_span_23_Template", "invalid", "touched", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "LoginComponent_span_20_span_1_Template", "LoginComponent_span_20_span_2_Template", "loginUser", "loginForm", "password", "loginUserSubscribe", "res", "setToken", "tokenpayload", "setCurrentUser", "LoginComponent_Template", "LoginComponent_Template_form_ngSubmit_14_listener", "LoginComponent_span_20_Template", "LoginComponent_span_25_Template", "RegisterComponent_span_29_span_1_Template", "RegisterComponent_span_29_span_2_Template", "RegisterComponent_span_29_span_3_Template", "phoneNumber", "errors", "RegisterComponent_span_34_span_1_Template", "RegisterComponent_span_34_span_2_Template", "RegisterComponent_span_39_span_1_Template", "RegisterComponent_span_39_span_2_Template", "RegisterComponent_span_39_span_3_Template", "RegisterComponent_span_44_span_1_Template", "confirmPassword", "createRegisterForm", "registerForm", "firstName", "lastName", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "validator", "passwordCompareValidator", "fc", "notmatched", "register", "registerSubscribe", "registerUser", "RegisterComponent_Template", "RegisterComponent_Template_form_ngSubmit_14_listener", "RegisterComponent_span_19_Template", "RegisterComponent_span_24_Template", "RegisterComponent_span_29_Template", "RegisterComponent_span_34_Template", "RegisterComponent_span_39_Template", "RegisterComponent_span_44_Template", "RegisterComponent_span_45_Template", "ResetPasswordComponent_span_23_span_1_Template", "ResetPasswordComponent_span_23_span_2_Template", "ResetPasswordComponent_span_23_span_3_Template", "ResetPasswordComponent_span_28_span_1_Template", "_activateRoute", "resetForm<PERSON>heck", "ngAfterViewInit", "queryParams", "params", "resetForm", "resetFormValue", "<PERSON><PERSON>", "resetPasswordSubscribe", "resetPassword", "ActivatedRoute", "ResetPasswordComponent_Template", "ResetPasswordComponent_Template_form_ngSubmit_18_listener", "ResetPasswordComponent_span_23_Template", "ResetPasswordComponent_span_28_Template", "ResetPasswordComponent_span_29_Template", "BsDropdownModule", "NavbarComponent_li_15_Template_a_click_1_listener", "ctx_r1", "redirectLogin", "NavbarComponent_li_16_Template_a_click_1_listener", "_r3", "redirectRegister", "NavbarComponent_li_17_ul_5_Template_a_click_6_listener", "loggedOut", "ɵɵpropertyInterpolate1", "NavbarComponent_li_17_ul_5_Template", "userDetail", "is<PERSON>ogin", "userName", "NavbarComponent_Template", "NavbarComponent_ul_13_Template", "NavbarComponent_li_15_Template", "NavbarComponent_li_16_Template", "NavbarComponent_li_17_Template", "RouterLinkActive", "BsDropdownMenuDirective", "BsDropdownToggleDirective", "BsDropdownDirective", "SearchingSortingComponent", "displayStyle", "openPopup", "closePopup", "NewMissionComponent_Template", "NewMissionComponent_Template_button_click_3_listener", "NewMissionComponent_Template_button_click_100_listener", "ɵɵpureFunction1", "NgStyle", "PrivacyPolicyComponent_Template", "item_r3", "text", "item_r4", "item_r5", "item_r6", "missionCountryList", "missionCityList", "missionThemeList", "missionSkillList", "getMissionCountryList", "getMissionCityList", "getMissionThemeList", "getMissionSkillList", "missionCountryListSubscribe", "missionCityListSubscribe", "missionThemeListSubscribe", "missionSkillListSubscribe", "onTextChange", "next", "onChange", "SearchingSortingComponent_Template", "SearchingSortingComponent_Template_input_keyup_4_listener", "txtSearch_r2", "ɵɵreference", "SearchingSortingComponent_Template_select_change_7_listener", "SearchingSortingComponent_option_10_Template", "SearchingSortingComponent_Template_select_change_12_listener", "SearchingSortingComponent_option_15_Template", "SearchingSortingComponent_Template_select_change_17_listener", "SearchingSortingComponent_option_20_Template", "SearchingSortingComponent_Template_select_change_22_listener", "SearchingSortingComponent_option_25_Template", "ListBoxComponent", "ListBoxModule", "ValidateForm", "_commonService", "_loginService", "_activateRouter", "countryList", "cityList", "skillList", "skillList1", "userSkillList", "isFileUpload", "formData", "FormData", "data1", "toolbarSettings", "position", "tools", "snapshot", "paramMap", "loginDetail", "loginName", "contactUs", "name", "userFormCheckValid", "loginUserDetailByUserId", "getUserSkill", "fetchData", "getCountryList", "changePasswordModal", "bootstrap", "Modal", "getElementById", "addyourSkillModal", "contactUsModal", "onSubmitSkillModal", "event", "listbox", "saveSkill", "skill", "join", "addUserSkillSubscribe", "addUserSkill", "closeAddYourSkillModal", "userSkillSubscirbe", "countryListSubscribe", "getCityList", "cityListSubscribe", "userDetailSubscribe", "loginUserDetailById", "loginUserDetails", "onSelectImage", "files", "reader", "FileReader", "readAsDataURL", "onload", "i", "append", "userProfileForm", "surname", "employeeId", "manager", "department", "myProfile", "whyIVolunteer", "avilability", "linkdInUrl", "mySkills", "userProfileSubscribe", "getUserProfileDetailById", "editData", "undefined", "_this", "_asyncToGenerator", "formValue", "uploadImage", "pipe", "to<PERSON>romise", "then", "mySkillLists", "userProfileUpdateSubscribe", "loginUserProfileUpdate", "validate<PERSON>ll<PERSON>orm<PERSON><PERSON>s", "onSubmitContactUs", "form", "contactUsSubscribe", "subject", "closeContactUsModal", "onSubmitChangePassword", "changePasswordForm", "changePasswordSubscribe", "changePassword", "closeChangePasswordModal", "onCancel", "openChangePasswordModal", "openAddYourSkillModal", "openContactUsModal", "ClientService", "viewQuery", "UserEditProfileComponent_Query", "UserEditProfileComponent_Template_img_click_8_listener", "userImg_r2", "click", "UserEditProfileComponent_Template_input_change_9_listener", "UserEditProfileComponent_Template_p_click_13_listener", "UserEditProfileComponent_span_27_Template", "UserEditProfileComponent_span_28_Template", "UserEditProfileComponent_span_35_Template", "UserEditProfileComponent_span_36_Template", "UserEditProfileComponent_span_51_Template", "UserEditProfileComponent_span_56_Template", "UserEditProfileComponent_span_64_Template", "UserEditProfileComponent_Template_select_change_81_listener", "UserEditProfileComponent_option_82_Template", "UserEditProfileComponent_span_83_Template", "UserEditProfileComponent_option_92_Template", "UserEditProfileComponent_span_93_Template", "UserEditProfileComponent_option_137_Template", "UserEditProfileComponent_span_138_Template", "UserEditProfileComponent_Template_button_click_140_listener", "UserEditProfileComponent_Template_button_click_144_listener", "UserEditProfileComponent_Template_button_click_147_listener", "UserEditProfileComponent_Template_div_click_156_listener", "UserEditProfileComponent_Template_button_click_165_listener", "UserEditProfileComponent_Template_form_ngSubmit_166_listener", "changePasswordForm_r6", "UserEditProfileComponent_Template_input_ngModelChange_171_listener", "changePass", "oldPassword", "UserEditProfileComponent_span_173_Template", "UserEditProfileComponent_Template_input_ngModelChange_175_listener", "newPassword", "UserEditProfileComponent_span_177_Template", "UserEditProfileComponent_Template_input_ngModelChange_179_listener", "UserEditProfileComponent_span_181_Template", "UserEditProfileComponent_Template_button_click_183_listener", "UserEditProfileComponent_Template_button_click_195_listener", "UserEditProfileComponent_Template_kendo_listbox_actionClick_197_listener", "UserEditProfileComponent_Template_kendo_listbox_actionClick_199_listener", "UserEditProfileComponent_Template_button_click_202_listener", "UserEditProfileComponent_Template_button_click_205_listener", "UserEditProfileComponent_Template_button_click_214_listener", "UserEditProfileComponent_Template_form_ngSubmit_215_listener", "myForm_r7", "UserEditProfileComponent_Template_input_ngModelChange_219_listener", "UserEditProfileComponent_Template_input_ngModelChange_226_listener", "UserEditProfileComponent_Template_input_ngModelChange_233_listener", "UserEditProfileComponent_Template_input_ngModelChange_238_listener", "UserEditProfileComponent_span_240_Template", "UserEditProfileComponent_Template_textarea_ngModelChange_244_listener", "UserEditProfileComponent_span_246_Template", "UserEditProfileComponent_Template_button_click_248_listener", "controls", "dirty", "oldPassword_r8", "newPassword_r9", "confirmPassword_r10", "Listbox2_r11", "subject_r12", "message_r13", "SelectControlValueAccessor", "SelectMultipleControlValueAccessor", "RequiredValidator", "NgForm", "DataBindingDirective", "TabsModule", "getMainImageUrl", "missionDetail", "missionDoc", "commentDate", "commentDescription", "VolunteeringMissionComponent_ng_container_162_div_1_Template", "VolunteeringMissionComponent_ng_container_162_div_2_Template", "result_r6", "_activeRoute", "_datePipe", "imageList", "recentVolunteerList", "btnText", "missionCommentList", "fetchMissionDetail", "applyModal", "getRecentVolunteerList", "missionDetailSubscribe", "missionDetailByMissionId", "missionDocuments", "getMissionCommentList", "getImageUrls", "imageUrls", "photo", "replaceAll", "openApplyMissionModal", "closeApplyMissionModal", "missionSubscribe", "postComment", "commentdesc", "CommentDescription", "missionCommentSubscribe", "addMissionComment", "missionCommentListByMissionId", "transform", "getMissionFavourite", "addMissionFavouriteSubscribe", "addMissionFavourite", "removeMissionFavouriteSubscribe", "removeMissionFavourite", "volunteerListSubscribe", "missioId", "VolunteeringMissionComponent_Template", "VolunteeringMissionComponent_div_7_Template", "VolunteeringMissionComponent_p_16_Template", "VolunteeringMissionComponent_p_17_Template", "VolunteeringMissionComponent_div_27_Template", "VolunteeringMissionComponent_div_28_Template", "VolunteeringMissionComponent_Template_span_click_33_listener", "VolunteeringMissionComponent_Template_button_click_91_listener", "VolunteeringMissionComponent_p_117_Template", "VolunteeringMissionComponent_div_118_Template", "VolunteeringMissionComponent_Template_button_click_125_listener", "commentdesc_r3", "VolunteeringMissionComponent_div_129_Template", "VolunteeringMissionComponent_ng_container_162_Template", "VolunteeringMissionComponent_Template_button_click_334_listener", "VolunteeringMissionComponent_Template_button_click_340_listener", "VolunteeringMissionComponent_Template_button_click_343_listener", "missionType", "missionFavouriteStatus", "TabDirective", "TabsetComponent", "VolunteeringTimesheetComponent_ng_container_28_tr_1_Template_button_click_11_listener", "getVolunteeringHoursById", "VolunteeringTimesheetComponent_ng_container_28_tr_1_Template_button_click_13_listener", "openVolunteeringDeleteModal", "missionName", "dateVolunteered", "hours", "minutes", "VolunteeringTimesheetComponent_ng_container_28_tr_1_Template", "VolunteeringTimesheetComponent_ng_container_28_tr_2_Template", "result_r4", "VolunteeringTimesheetComponent_ng_container_48_tr_1_Template_button_click_9_listener", "_r5", "getVolunteeringGoalsById", "VolunteeringTimesheetComponent_ng_container_48_tr_1_Template_button_click_11_listener", "date", "action", "VolunteeringTimesheetComponent_ng_container_48_tr_1_Template", "VolunteeringTimesheetComponent_ng_container_48_tr_2_Template", "result_r7", "item_r8", "item_r9", "volunteeringHourseModals", "volunteeringGoalsModals", "deleteModal", "volunteeringHoursFormValidate", "volunteeringGoalsFormValidate", "getVolunteeringHoursList", "getVolunteeringGoalsList", "openVolunteeringHoursModal", "missionTitleList", "closeVolunteeringHoursModal", "openVolunteeringGoalsModal", "closeVolunteeringGoalsModal", "hoursId", "closeVolunteeringDeleteModal", "volunteeringMissionSubscribe", "volunteeringMissionList", "volunteeringHoursForm", "volunteeringHoursSubscribe", "hoursList", "dateformat", "patchValue", "onVolunteringHoursSubmit", "insertVolunteeringHours", "updateVolunteeringHours", "addVolunteeringHours", "reset", "deleteVolunteeringHours", "volunteeringGoalsForm", "volunteeringGoalsSubscribe", "goalsList", "onVolunteringGoalsSubmit", "insertVolunteeringGoals", "updateVolunteeringGoals", "addVolunteeringGoals", "deleteVolunteeringGoals", "VolunteeringTimeSheetService", "VolunteeringTimesheetComponent_Template", "VolunteeringTimesheetComponent_Template_button_click_12_listener", "VolunteeringTimesheetComponent_ng_container_28_Template", "VolunteeringTimesheetComponent_Template_button_click_34_listener", "VolunteeringTimesheetComponent_ng_container_48_Template", "VolunteeringTimesheetComponent_Template_button_click_55_listener", "VolunteeringTimesheetComponent_option_64_Template", "VolunteeringTimesheetComponent_span_65_Template", "VolunteeringTimesheetComponent_span_70_Template", "VolunteeringTimesheetComponent_div_76_Template", "VolunteeringTimesheetComponent_span_81_Template", "VolunteeringTimesheetComponent_span_86_Template", "VolunteeringTimesheetComponent_Template_button_click_88_listener", "VolunteeringTimesheetComponent_Template_button_click_91_listener", "VolunteeringTimesheetComponent_Template_button_click_100_listener", "VolunteeringTimesheetComponent_option_109_Template", "VolunteeringTimesheetComponent_span_110_Template", "VolunteeringTimesheetComponent_span_119_Template", "VolunteeringTimesheetComponent_span_124_Template", "VolunteeringTimesheetComponent_span_129_Template", "VolunteeringTimesheetComponent_Template_button_click_131_listener", "VolunteeringTimesheetComponent_Template_button_click_134_listener", "VolunteeringTimesheetComponent_Template_button_click_143_listener", "VolunteeringTimesheetComponent_Template_button_click_149_listener", "VolunteeringTimesheetComponent_Template_button_click_152_listener", "ɵɵclassProp", "environment", "API_BASE_URL", "apiBaseUrl", "IMAGE_BASE_URL", "imageBaseUrl", "<PERSON><PERSON><PERSON>", "defaultPageSize", "API_ENDPOINTS", "AUTH", "LOGIN", "REGISTER", "FORGOT_PASSWORD", "RESET_PASSWORD", "CHANGE_PASSWORD", "GET_USER_BY_ID", "UPDATE_USER", "GET_USER_PROFILE", "UPDATE_USER_PROFILE", "GET_LOGIN_USER_BY_ID", "MISSION", "LIST", "DETAIL", "ADD", "UPDATE", "DELETE", "THEME_LIST", "SKILL_LIST", "APPLICATION_LIST", "APPLICATION_APPROVE", "APPLICATION_DELETE", "CLIENT_MISSION", "CLIENT_LIST", "APPLY", "ADD_COMMENT", "COMMENT_LIST", "ADD_FAVORITE", "REMOVE_FAVORITE", "RATING", "RECENT_VOLUNTEERS", "GET_USER_LIST", "SEND_INVITE", "MISSION_TITLE", "COMMON", "COUNTRY_LIST", "CITY_LIST", "UPLOAD_IMAGE", "CONTACT_US", "ADD_USER_SKILL", "GET_USER_SKILL", "MISSION_TITLE_LIST", "MISSION_COUNTRY_LIST", "MISSION_CITY_LIST", "MISSION_THEME_LIST", "MISSION_SKILL_LIST", "TIMESHEET", "GET_HOURS_LIST", "GET_HOURS_BY_ID", "ADD_HOURS", "UPDATE_HOURS", "DELETE_HOURS", "GET_GOALS_LIST", "GET_GOALS_BY_ID", "ADD_GOALS", "UPDATE_GOALS", "DELETE_GOALS", "VOLUNTEERING_MISSION_LIST", "AdminUser", "USER_LIST", "DELETE_USER", "MISSION_THEME", "GET_BY_ID", "MISSION_SKILL", "service", "router", "toastr", "isLoggedIn", "ɵɵinject", "factory", "ɵfac", "providedIn", "FormControl", "FormGroup", "formGroup", "Object", "keys", "field", "control", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlySelf", "HttpErrorResponse", "catchError", "throwError", "TokenInterceptor", "route", "intercept", "request", "myToken", "getToken", "clone", "setHeaders", "Authorization", "handle", "Error", "args", "filter", "JSON", "stringify", "includes", "pure", "BehaviorSubject", "JwtHelperService", "http", "apiUrl", "currentUser", "currentUserName", "jwthelperService", "userPayLoad", "user", "post", "responseType", "getUserById", "updateUser", "loginInfo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Password", "localStorage", "getItem", "token", "setItem", "removeItem", "asObservable", "decodeToken", "getUserFullName", "HttpClient", "missionRating", "sendInviteMissionMail", "getMissionTitle", "userid", "delete", "production", "enableProdMode", "bootstrapApplication", "importProvidersFrom", "BrowserModule", "HTTP_INTERCEPTORS", "provideHttpClient", "withInterceptorsFromDi", "provideRouter", "ToastrModule", "BrowserAnimationsModule", "providers", "provide", "useClass", "multi", "forRoot", "catch", "console"], "sourceRoot": "webpack:///", "x_google_ignoreList": [42]}