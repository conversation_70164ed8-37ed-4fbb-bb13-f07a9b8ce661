{"ast": null, "code": "import { NgIf } from \"@angular/common\";\nimport { ReactiveFormsModule, Validators } from \"@angular/forms\";\nimport { RouterModule } from \"@angular/router\";\nimport { APP_CONFIG } from \"src/app/main/configs/environment.config\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/main/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"ng-angular-popup\";\nfunction LoginComponent_span_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Please Enter EmailAddress \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Please Enter Valid EmailAddress \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtemplate(1, LoginComponent_span_20_span_1_Template, 2, 0, \"span\", 28)(2, LoginComponent_span_20_span_2_Template, 2, 0, \"span\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.emailAddress.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.emailAddress.hasError(\"email\"));\n  }\n}\nfunction LoginComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1, \" Please Enter Password \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(_fb, _service, _router, _toast) {\n    this._fb = _fb;\n    this._service = _service;\n    this._router = _router;\n    this._toast = _toast;\n    this.unsubscribe = [];\n  }\n  ngOnInit() {\n    this.loginUser();\n  }\n  loginUser() {\n    this.loginForm = this._fb.group({\n      emailAddress: [null, Validators.compose([Validators.required, Validators.email])],\n      password: [null, Validators.compose([Validators.required])]\n    });\n  }\n  get emailAddress() {\n    return this.loginForm.get(\"emailAddress\");\n  }\n  get password() {\n    return this.loginForm.get(\"password\");\n  }\n  onSubmit() {\n    this.formValid = true;\n    if (this.loginForm.valid) {\n      const loginUserSubscribe = this._service.loginUser([this.loginForm.value.emailAddress, this.loginForm.value.password]).subscribe(res => {\n        if (res.result == 1) {\n          if (res.data.message == \"Login Successfully\") {\n            this._service.setToken(res.data.data);\n            const tokenpayload = this._service.decodedToken();\n            this._service.setCurrentUser(tokenpayload);\n            this._toast.success({\n              detail: \"SUCCESS\",\n              summary: res.data.message,\n              duration: APP_CONFIG.toastDuration\n            });\n            if (tokenpayload.userType == \"admin\") {\n              this._router.navigate([\"admin/dashboard\"]);\n            } else {\n              this._router.navigate([\"/home\"]);\n            }\n          } else {\n            // this.toastr.error(res.data.message);\n            this._toast.error({\n              detail: \"ERROR\",\n              summary: res.data.message,\n              duration: APP_CONFIG.toastDuration\n            });\n          }\n        } else {\n          // this.toastr.error(res.message);\n          this._toast.error({\n            detail: \"ERROR\",\n            summary: res.message,\n            duration: APP_CONFIG.toastDuration\n          });\n        }\n      });\n      this.formValid = false;\n      this.unsubscribe.push(loginUserSubscribe);\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach(sb => sb.unsubscribe());\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.NgToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 39,\n      vars: 3,\n      consts: [[1, \"container-fluid\", \"ps-md-0\"], [1, \"row\", \"g-0\"], [1, \"d-none\", \"d-lg-flex\", \"col-md-6\", \"col-lg-8\", \"bg-image\", \"position-relative\"], [\"src\", \"assets/Images/image.png\", \"alt\", \"No Image\", 1, \"w-100\"], [1, \"carousel-caption\", \"d-md-block\"], [1, \"heading\"], [1, \"content\"], [1, \"col-md-6\", \"col-lg-4\"], [1, \"login\", \"d-flex\", \"align-items-center\", \"py-3\"], [1, \"container\"], [1, \"row\", \"justify-content-center\"], [1, \"col-12\", \"col-sm-10\", \"col-md-11\", \"col-lg-10\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"ForgotContent\"], [1, \"form-group\", \"mb-3\"], [1, \"col-form-label\"], [\"type\", \"text\", \"formControlName\", \"emailAddress\", \"placeholder\", \"Email Address\", \"autofocus\", \"\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"type\", \"password\", \"onpaste\", \"return false\", \"formControlName\", \"password\", \"placeholder\", \"Password\", 1, \"form-control\"], [1, \"d-grid\", \"mt-4\", \"mb-4\"], [\"type\", \"submit\", 1, \"btn-login\"], [1, \"Login\"], [1, \"text-center\", \"mt-3\"], [1, \"Lost\"], [\"routerLink\", \"/register\"], [1, \"text-center\", \"mb-3\"], [\"routerLink\", \"/privacyPolicy\", 1, \"privacy-policy\", 2, \"text-decoration\", \"none\", \"cursor\", \"pointer\", \"color\", \"black\"], [1, \"text-danger\"], [4, \"ngIf\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"p\", 5);\n          i0.ɵɵtext(6, \"Sed ut perspiciatis unde omnis iste natus voluptatem.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"form\", 12);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_14_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelement(15, \"div\", 13);\n          i0.ɵɵelementStart(16, \"div\", 14)(17, \"label\", 15);\n          i0.ɵɵtext(18, \"Email Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 16);\n          i0.ɵɵtemplate(20, LoginComponent_span_20_Template, 3, 2, \"span\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 14)(22, \"label\", 15);\n          i0.ɵɵtext(23, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 18);\n          i0.ɵɵtemplate(25, LoginComponent_span_25_Template, 2, 0, \"span\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"button\", 20)(28, \"span\", 21);\n          i0.ɵɵtext(29, \"Login\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 22)(31, \"p\", 23);\n          i0.ɵɵtext(32, \"Don't have an account? \");\n          i0.ɵɵelementStart(33, \"span\")(34, \"a\", 24);\n          i0.ɵɵtext(35, \"Register Now\");\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵelementStart(36, \"div\", 25)(37, \"a\", 26);\n          i0.ɵɵtext(38, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.emailAddress.invalid && (ctx.emailAddress.touched || ctx.formValid));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.password.invalid && (ctx.password.touched || ctx.formValid));\n        }\n      },\n      dependencies: [ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, NgIf, RouterModule, i3.RouterLink],\n      styles: [\".login[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n}\\n\\n.bg-image[_ngcontent-%COMP%] {\\n  background-size: cover;\\n  background-position: center;\\n}\\n\\n.col-form-label[_ngcontent-%COMP%] {\\n  height: 14px;\\n  font-size: 16px;\\n  font-weight: 400;\\n  text-align: left;\\n  color: #414141;\\n  margin-bottom: 8px;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  padding: 12px 15px;\\n  font-size: 16px;\\n  font-weight: 400;\\n  border-radius: 3px;\\n  box-shadow: 0 0 10px 0 rgba(43, 100, 177, 0.12);\\n  border: solid 1px #2b64b1;\\n  background-color: #fff;\\n}\\n\\n.btn-login[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 48px;\\n  border-radius: 24px;\\n  border: solid 2px #f88634;\\n  background-color: white;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-login[_ngcontent-%COMP%]:hover {\\n  background-color: #f88634;\\n}\\n\\n.btn-login[_ngcontent-%COMP%]:hover   .Login[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.Login[_ngcontent-%COMP%] {\\n  font-family: NotoSans, sans-serif;\\n  font-size: 20px;\\n  font-weight: normal;\\n  color: #f88634;\\n}\\n\\n.Lost[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 400;\\n  text-align: center;\\n  color: #414141;\\n  cursor: pointer;\\n  margin: 0;\\n}\\n\\n.privacy-policy[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #414141;\\n  margin-bottom: 0;\\n}\\n\\n.ForgotContent[_ngcontent-%COMP%] {\\n  margin: 10px 7px 36px 5px;\\n  font-size: 16px;\\n  font-weight: 400;\\n  text-align: center;\\n  color: #414141;\\n}\\n\\n.heading[_ngcontent-%COMP%] {\\n  font-family: NotoSans, sans-serif;\\n  font-size: 40px;\\n  font-weight: normal;\\n  color: #fff;\\n  margin-bottom: 20px;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  font-family: NotoSans, sans-serif;\\n  font-size: 16px;\\n  font-weight: 300;\\n  line-height: 1.75;\\n  letter-spacing: normal;\\n  text-align: left;\\n  color: #fff;\\n}\\n\\n.carousel-caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: fit-content;\\n  color: #fff;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  left: 10%;\\n  right: 10%;\\n  text-align: left;\\n}\\n\\n\\n\\n@media (max-width: 991.98px) {\\n  .heading[_ngcontent-%COMP%] {\\n    font-size: 32px;\\n  }\\n\\n  .content[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n@media (max-width: 767.98px) {\\n  .login[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n\\n  .col-form-label[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n\\n  .form-control[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n    padding: 10px;\\n  }\\n\\n  .btn-login[_ngcontent-%COMP%] {\\n    height: 44px;\\n  }\\n\\n  .Login[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n}\\n\\n@media (max-width: 575.98px) {\\n  .col-form-label[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n\\n  .form-control[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    padding: 8px;\\n  }\\n\\n  .btn-login[_ngcontent-%COMP%] {\\n    height: 40px;\\n  }\\n\\n  .Login[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n\\n  .Lost[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n.btn-login[_ngcontent-%COMP%] {\\n  height: 40px;\\n}\\n\\n.Login[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.Lost[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NgIf", "ReactiveFormsModule", "Validators", "RouterModule", "APP_CONFIG", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LoginComponent_span_20_span_1_Template", "LoginComponent_span_20_span_2_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "emailAddress", "<PERSON><PERSON><PERSON><PERSON>", "LoginComponent", "constructor", "_fb", "_service", "_router", "_toast", "unsubscribe", "ngOnInit", "loginUser", "loginForm", "group", "compose", "required", "email", "password", "get", "onSubmit", "formValid", "valid", "loginUserSubscribe", "value", "subscribe", "res", "result", "data", "message", "setToken", "tokenpayload", "decodedToken", "setCurrentUser", "success", "detail", "summary", "duration", "toastDuration", "userType", "navigate", "error", "push", "ngOnDestroy", "for<PERSON>ach", "sb", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "NgToastService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_14_listener", "LoginComponent_span_20_Template", "LoginComponent_span_25_Template", "invalid", "touched", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\Downloads\\ci_platfrom_app-develop\\ci_platfrom_app-develop\\src\\app\\main\\components\\login-register\\login\\login.component.ts", "C:\\Users\\<USER>\\Downloads\\ci_platfrom_app-develop\\ci_platfrom_app-develop\\src\\app\\main\\components\\login-register\\login\\login.component.html"], "sourcesContent": ["import { NgIf } from \"@angular/common\"\nimport { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from \"@angular/core\"\nimport { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from \"@angular/forms\"\nimport { Router, RouterModule } from \"@angular/router\"\nimport { NgToastService } from \"ng-angular-popup\"\nimport { Subscription } from \"rxjs\"\nimport { APP_CONFIG } from \"src/app/main/configs/environment.config\"\nimport { AuthService } from \"src/app/main/services/auth.service\"\n\n@Component({\n  selector: \"app-login\",\n  standalone: true,\n  imports: [ReactiveFormsModule, NgIf, RouterModule],\n  templateUrl: \"./login.component.html\",\n  styleUrls: [\"./login.component.css\"],\n})\nexport class LoginComponent implements OnInit, OnDestroy {\n  private unsubscribe: Subscription[] = [];\n  \n  constructor(\n    private _fb: FormBuilder,\n    private _service: AuthService,\n    private _router: Router,\n    private _toast: NgToastService,\n  ) { }\n  loginForm: FormGroup\n  formValid: boolean\n  ngOnInit(): void {\n    this.loginUser()\n  }\n  loginUser() {\n    this.loginForm = this._fb.group({\n      emailAddress: [null, Validators.compose([Validators.required, Validators.email])],\n      password: [null, Validators.compose([Validators.required])],\n    })\n  }\n  get emailAddress() {\n    return this.loginForm.get(\"emailAddress\") as FormControl\n  }\n  get password() {\n    return this.loginForm.get(\"password\") as FormControl\n  }\n  onSubmit() {\n    this.formValid = true\n    if (this.loginForm.valid) {\n      const loginUserSubscribe = this._service\n        .loginUser([this.loginForm.value.emailAddress, this.loginForm.value.password])\n        .subscribe((res: any) => {\n          if (res.result == 1) {\n            if (res.data.message == \"Login Successfully\") {\n              this._service.setToken(res.data.data)\n              const tokenpayload = this._service.decodedToken()\n              this._service.setCurrentUser(tokenpayload)\n\n              this._toast.success({ detail: \"SUCCESS\", summary: res.data.message, duration: APP_CONFIG.toastDuration })\n              if (tokenpayload.userType == \"admin\") {\n                this._router.navigate([\"admin/dashboard\"])\n              } else {\n                this._router.navigate([\"/home\"])\n              }\n            } else {\n              // this.toastr.error(res.data.message);\n              this._toast.error({ detail: \"ERROR\", summary: res.data.message, duration: APP_CONFIG.toastDuration })\n            }\n          } else {\n            // this.toastr.error(res.message);\n            this._toast.error({ detail: \"ERROR\", summary: res.message, duration: APP_CONFIG.toastDuration })\n          }\n        })\n      this.formValid = false\n      this.unsubscribe.push(loginUserSubscribe);\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n}\n", "<div class=\"container-fluid ps-md-0\">\n  <div class=\"row g-0\">\n    <div class=\"d-none d-lg-flex col-md-6 col-lg-8 bg-image position-relative\">\n      <img src=\"assets/Images/image.png\" alt=\"No Image\" class=\"w-100\">\n      <div class=\"carousel-caption d-md-block\">\n        <p class=\"heading\">Sed ut perspiciatis unde omnis\n          iste natus voluptatem.</p>\n        <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna\n            aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint\n            occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>\n      </div>\n    </div>\n    <div class=\"col-md-6 col-lg-4\">\n      <div class=\"login d-flex align-items-center py-3\">\n        <div class=\"container\">\n          <div class=\"row justify-content-center\">\n            <div class=\"col-12 col-sm-10 col-md-11 col-lg-10\">\n              <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n                <div class=\"ForgotContent\">\n\n                </div>\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">Email Address</label>\n                  <input type=\"text\" class=\"form-control\" formControlName=\"emailAddress\" placeholder=\"Email Address\" autofocus>\n                  <span class=\"text-danger\" *ngIf=\"emailAddress.invalid && (emailAddress.touched || formValid)\">\n                    <span *ngIf=\"emailAddress.hasError('required')\">\n                        Please Enter EmailAddress\n                    </span>\n                    <span *ngIf=\"emailAddress.hasError('email')\">\n                      Please Enter Valid EmailAddress\n                    </span>\n                  </span>\n                </div>\n                <div class=\"form-group mb-3\">\n                  <label class=\"col-form-label\">Password</label>\n                  <input type=\"password\" onpaste=\"return false\" class=\"form-control\" formControlName=\"password\" placeholder=\"Password\">\n                  <span class=\"text-danger\" *ngIf=\"password.invalid && (password.touched || formValid)\">\n                    Please Enter Password\n                  </span>\n                </div>\n                <div class=\"d-grid mt-4 mb-4\">\n                  <button class=\"btn-login\" type=\"submit\"><span class=\"Login\">Login</span></button>\n                  <div class=\"text-center mt-3\">\n                    <p class=\"Lost\">Don't have an account? <span><a routerLink=\"/register\">Register Now</a></span></p>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </div>\n          <div class=\"text-center mb-3\">\n            <a routerLink=\"/privacyPolicy\" class=\"privacy-policy\" style=\"text-decoration: none;cursor:pointer;color: black;\">Privacy Policy</a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>"], "mappings": "AAAA,SAASA,IAAI,QAAQ,iBAAiB;AAEtC,SAA8CC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AACrG,SAAiBC,YAAY,QAAQ,iBAAiB;AAGtD,SAASC,UAAU,QAAQ,yCAAyC;;;;;;;;ICoBhDC,EAAA,CAAAC,cAAA,WAAgD;IAC5CD,EAAA,CAAAE,MAAA,kCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,WAA6C;IAC3CD,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IANTH,EAAA,CAAAC,cAAA,eAA8F;IAI5FD,EAHA,CAAAI,UAAA,IAAAC,sCAAA,mBAAgD,IAAAC,sCAAA,mBAGH;IAG/CN,EAAA,CAAAG,YAAA,EAAO;;;;IANEH,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,YAAA,CAAAC,QAAA,aAAuC;IAGvCX,EAAA,CAAAO,SAAA,EAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,YAAA,CAAAC,QAAA,UAAoC;;;;;IAQ7CX,EAAA,CAAAC,cAAA,eAAsF;IACpFD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADvBzB,OAAM,MAAOS,cAAc;EAGzBC,YACUC,GAAgB,EAChBC,QAAqB,EACrBC,OAAe,EACfC,MAAsB;IAHtB,KAAAH,GAAG,GAAHA,GAAG;IACH,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IANR,KAAAC,WAAW,GAAmB,EAAE;EAOpC;EAGJC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;EAClB;EACAA,SAASA,CAAA;IACP,IAAI,CAACC,SAAS,GAAG,IAAI,CAACP,GAAG,CAACQ,KAAK,CAAC;MAC9BZ,YAAY,EAAE,CAAC,IAAI,EAAEb,UAAU,CAAC0B,OAAO,CAAC,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,KAAK,CAAC,CAAC,CAAC;MACjFC,QAAQ,EAAE,CAAC,IAAI,EAAE7B,UAAU,CAAC0B,OAAO,CAAC,CAAC1B,UAAU,CAAC2B,QAAQ,CAAC,CAAC;KAC3D,CAAC;EACJ;EACA,IAAId,YAAYA,CAAA;IACd,OAAO,IAAI,CAACW,SAAS,CAACM,GAAG,CAAC,cAAc,CAAgB;EAC1D;EACA,IAAID,QAAQA,CAAA;IACV,OAAO,IAAI,CAACL,SAAS,CAACM,GAAG,CAAC,UAAU,CAAgB;EACtD;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACR,SAAS,CAACS,KAAK,EAAE;MACxB,MAAMC,kBAAkB,GAAG,IAAI,CAAChB,QAAQ,CACrCK,SAAS,CAAC,CAAC,IAAI,CAACC,SAAS,CAACW,KAAK,CAACtB,YAAY,EAAE,IAAI,CAACW,SAAS,CAACW,KAAK,CAACN,QAAQ,CAAC,CAAC,CAC7EO,SAAS,CAAEC,GAAQ,IAAI;QACtB,IAAIA,GAAG,CAACC,MAAM,IAAI,CAAC,EAAE;UACnB,IAAID,GAAG,CAACE,IAAI,CAACC,OAAO,IAAI,oBAAoB,EAAE;YAC5C,IAAI,CAACtB,QAAQ,CAACuB,QAAQ,CAACJ,GAAG,CAACE,IAAI,CAACA,IAAI,CAAC;YACrC,MAAMG,YAAY,GAAG,IAAI,CAACxB,QAAQ,CAACyB,YAAY,EAAE;YACjD,IAAI,CAACzB,QAAQ,CAAC0B,cAAc,CAACF,YAAY,CAAC;YAE1C,IAAI,CAACtB,MAAM,CAACyB,OAAO,CAAC;cAAEC,MAAM,EAAE,SAAS;cAAEC,OAAO,EAAEV,GAAG,CAACE,IAAI,CAACC,OAAO;cAAEQ,QAAQ,EAAE9C,UAAU,CAAC+C;YAAa,CAAE,CAAC;YACzG,IAAIP,YAAY,CAACQ,QAAQ,IAAI,OAAO,EAAE;cACpC,IAAI,CAAC/B,OAAO,CAACgC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;YAC5C,CAAC,MAAM;cACL,IAAI,CAAChC,OAAO,CAACgC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YAClC;UACF,CAAC,MAAM;YACL;YACA,IAAI,CAAC/B,MAAM,CAACgC,KAAK,CAAC;cAAEN,MAAM,EAAE,OAAO;cAAEC,OAAO,EAAEV,GAAG,CAACE,IAAI,CAACC,OAAO;cAAEQ,QAAQ,EAAE9C,UAAU,CAAC+C;YAAa,CAAE,CAAC;UACvG;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAAC7B,MAAM,CAACgC,KAAK,CAAC;YAAEN,MAAM,EAAE,OAAO;YAAEC,OAAO,EAAEV,GAAG,CAACG,OAAO;YAAEQ,QAAQ,EAAE9C,UAAU,CAAC+C;UAAa,CAAE,CAAC;QAClG;MACF,CAAC,CAAC;MACJ,IAAI,CAACjB,SAAS,GAAG,KAAK;MACtB,IAAI,CAACX,WAAW,CAACgC,IAAI,CAACnB,kBAAkB,CAAC;IAC3C;EACF;EACAoB,WAAWA,CAAA;IACT,IAAI,CAACjC,WAAW,CAACkC,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACnC,WAAW,EAAE,CAAC;EACpD;;;uCA3DWN,cAAc,EAAAZ,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxD,EAAA,CAAAsD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1D,EAAA,CAAAsD,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA5D,EAAA,CAAAsD,iBAAA,CAAAO,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAdlD,cAAc;MAAAmD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjE,EAAA,CAAAkE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdvBxE,EAFJ,CAAAC,cAAA,aAAqC,aACd,aACwD;UACzED,EAAA,CAAA0E,SAAA,aAAgE;UAE9D1E,EADF,CAAAC,cAAA,aAAyC,WACpB;UAAAD,EAAA,CAAAE,MAAA,4DACK;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5BH,EAAA,CAAAC,cAAA,WAAmB;UAACD,EAAA,CAAAE,MAAA,qcAG+E;UAEvGF,EAFuG,CAAAG,YAAA,EAAI,EACnG,EACF;UAMIH,EALV,CAAAC,cAAA,aAA+B,cACqB,cACzB,eACmB,eACY,gBACM;UAAxBD,EAAA,CAAA2E,UAAA,sBAAAC,kDAAA;YAAA,OAAYH,GAAA,CAAA7C,QAAA,EAAU;UAAA,EAAC;UACnD5B,EAAA,CAAA0E,SAAA,eAEM;UAEJ1E,EADF,CAAAC,cAAA,eAA6B,iBACG;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAA0E,SAAA,iBAA6G;UAC7G1E,EAAA,CAAAI,UAAA,KAAAyE,+BAAA,mBAA8F;UAQhG7E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA6B,iBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9CH,EAAA,CAAA0E,SAAA,iBAAqH;UACrH1E,EAAA,CAAAI,UAAA,KAAA0E,+BAAA,mBAAsF;UAGxF9E,EAAA,CAAAG,YAAA,EAAM;UAEoCH,EAD1C,CAAAC,cAAA,eAA8B,kBACY,gBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAS;UAE/EH,EADF,CAAAC,cAAA,eAA8B,aACZ;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAMF,EAAN,CAAAC,cAAA,YAAM,aAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAK7FF,EAL6F,CAAAG,YAAA,EAAI,EAAO,EAAI,EAC9F,EACF,EACD,EACH,EACF;UAEJH,EADF,CAAAC,cAAA,eAA8B,aACqF;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAM3IF,EAN2I,CAAAG,YAAA,EAAI,EAC/H,EACF,EACF,EACF,EACF,EACF;;;UAvCcH,EAAA,CAAAO,SAAA,IAAuB;UAAvBP,EAAA,CAAAQ,UAAA,cAAAiE,GAAA,CAAApD,SAAA,CAAuB;UAOErB,EAAA,CAAAO,SAAA,GAAiE;UAAjEP,EAAA,CAAAQ,UAAA,SAAAiE,GAAA,CAAA/D,YAAA,CAAAqE,OAAA,KAAAN,GAAA,CAAA/D,YAAA,CAAAsE,OAAA,IAAAP,GAAA,CAAA5C,SAAA,EAAiE;UAYjE7B,EAAA,CAAAO,SAAA,GAAyD;UAAzDP,EAAA,CAAAQ,UAAA,SAAAiE,GAAA,CAAA/C,QAAA,CAAAqD,OAAA,KAAAN,GAAA,CAAA/C,QAAA,CAAAsD,OAAA,IAAAP,GAAA,CAAA5C,SAAA,EAAyD;;;qBDzB1FjC,mBAAmB,EAAA2D,EAAA,CAAA0B,aAAA,EAAA1B,EAAA,CAAA2B,oBAAA,EAAA3B,EAAA,CAAA4B,eAAA,EAAA5B,EAAA,CAAA6B,oBAAA,EAAA7B,EAAA,CAAA8B,kBAAA,EAAA9B,EAAA,CAAA+B,eAAA,EAAE3F,IAAI,EAAEG,YAAY,EAAA6D,EAAA,CAAA4B,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}