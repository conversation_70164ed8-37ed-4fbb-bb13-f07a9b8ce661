{"ast": null, "code": "import dateFormat from \"dateformat\";\nimport { SidebarComponent } from \"../sidebar/sidebar.component\";\nimport { HeaderComponent } from \"../header/header.component\";\nimport * as i0 from \"@angular/core\";\nexport class DashboardComponent {\n  constructor() {\n    setInterval(() => {\n      const now = new Date();\n      this.data = dateFormat(now, \"dddd mmmm dS,yyyy, h:MM:ss TT\");\n    }, 1);\n  }\n  ngOnInit() {}\n  static {\n    this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 0,\n      consts: [[1, \"container-fluid\"], [1, \"content\"], [1, \"info\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-sidebar\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵelement(3, \"app-header\");\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"h3\");\n          i0.ɵɵtext(6, \"Dashboard\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [SidebarComponent, HeaderComponent],\n      styles: [\"*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  list-style: none;\\n  text-decoration: none;\\n  overflow: hidden;\\n}\\n.container-fluid[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.container-fluid[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-left: 300px;\\n}\\n\\n.container-fluid[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%] {\\n  margin: 20px;\\n  color: #717171;\\n  line-height: 25px;\\n  text-align: justify;\\n}\\n.container-fluid[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n.container-fluid[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%]   .userLabel[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 400;\\n  margin-top: 30px;\\n  padding-bottom: 12px;\\n  border-bottom: 2px solid #e0e4e5;\\n}\\n.btnAdd[_ngcontent-%COMP%] {\\n  width: 112px;\\n  height: 50px;\\n  margin: 10px 0px 0px 0px;\\n  padding: 10px 25px 17px 22px;\\n  border-radius: 24px;\\n  border: solid 2px #f88634;\\n  background-color: #fff;\\n}\\n.btnAddIcon[_ngcontent-%COMP%] {\\n  width: 14px;\\n  height: 14px;\\n  margin: 0 9px 0 0;\\n  padding: 0 1px 1px 0;\\n  color: #f88634;\\n}\\n.add[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 12px;\\n  margin: 2px 0 0 0px;\\n  font-family: Myriad Pro;\\n  font-size: 17px;\\n  text-align: left;\\n  color: #f88634;\\n}\\n.searchBox[_ngcontent-%COMP%] {\\n  width: 400px;\\n  height: 48px;\\n  margin: 10px 722px 0px 0px;\\n  padding: 16px 317px 16px 0px;\\n  border-radius: 3px;\\n  box-shadow: 1px 0.3px 3px 0 rgba(0, 0, 0, 0.05);\\n  border: solid 1px #d9d9d9;\\n  background-color: #fff;\\n}\\n.icon[_ngcontent-%COMP%] {\\n  padding-left: 35px;\\n  padding-right: 3px;\\n  background: url('search.png') no-repeat;\\n  background-size: 17px;\\n  background-position: 3% 55%;\\n}\\n\\n.tableData[_ngcontent-%COMP%] {\\n  height: 711px;\\n  margin-top: 0%;\\n  border: solid 1px #d9d9d9;\\n  background-color: #fff;\\n}\\n.tableData[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  height: 80px;\\n  margin: 3px 0px 500px 0px;\\n  padding: 36px 20px 22px 18px;\\n  background-color: #f8f9fc;\\n}\\n.btnedit[_ngcontent-%COMP%] {\\n  background-color: white;\\n  color: #f88634;\\n  cursor: pointer;\\n  border: none;\\n  font-size: 22px;\\n  margin-right: 15px;\\n}\\n.btndelete[_ngcontent-%COMP%] {\\n  background-color: white;\\n  color: #414141;\\n  cursor: pointer;\\n  border: none;\\n  font-size: 22px;\\n  margin-right: 15px;\\n}\\ntd[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-bottom: 1px solid #e0e4e5;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["dateFormat", "SidebarComponent", "HeaderComponent", "DashboardComponent", "constructor", "setInterval", "now", "Date", "data", "ngOnInit", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "styles"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\components\\admin-side\\dashboard\\dashboard.component.ts", "B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\components\\admin-side\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, type OnInit } from \"@angular/core\"\nimport dateFormat from \"dateformat\"\nimport { SidebarComponent } from \"../sidebar/sidebar.component\"\nimport { HeaderComponent } from \"../header/header.component\"\n\n@Component({\n  selector: \"app-dashboard\",\n  standalone: true,\n  imports: [SidebarComponent, HeaderComponent],\n  templateUrl: \"./dashboard.component.html\",\n  styleUrls: [\"./dashboard.component.css\"],\n})\nexport class DashboardComponent implements OnInit {\n  data: any\n  constructor() {\n    setInterval(() => {\n      const now = new Date()\n      this.data = dateFormat(now, \"dddd mmmm dS,yyyy, h:MM:ss TT\")\n    }, 1)\n  }\n\n  ngOnInit(): void {}\n}\n", "<div class=\"container-fluid\">\n <app-sidebar></app-sidebar>\n  <div class=\"content\">\n   <app-header></app-header>\n     <div class=\"info\">\n          <h3>Dashboard</h3>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,OAAOA,UAAU,MAAM,YAAY;AACnC,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,eAAe,QAAQ,4BAA4B;;AAS5D,OAAM,MAAOC,kBAAkB;EAE7BC,YAAA;IACEC,WAAW,CAAC,MAAK;MACf,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;MACtB,IAAI,CAACC,IAAI,GAAGR,UAAU,CAACM,GAAG,EAAE,+BAA+B,CAAC;IAC9D,CAAC,EAAE,CAAC,CAAC;EACP;EAEAG,QAAQA,CAAA,GAAU;;;uCATPN,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAO,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ/BP,EAAA,CAAAS,cAAA,aAA6B;UAC5BT,EAAA,CAAAU,SAAA,kBAA2B;UAC1BV,EAAA,CAAAS,cAAA,aAAqB;UACpBT,EAAA,CAAAU,SAAA,iBAAyB;UAElBV,EADL,CAAAS,cAAA,aAAkB,SACT;UAAAT,EAAA,CAAAW,MAAA,gBAAS;UAGvBX,EAHuB,CAAAY,YAAA,EAAK,EAClB,EACF,EACF;;;qBDAMxB,gBAAgB,EAAEC,eAAe;MAAAwB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}