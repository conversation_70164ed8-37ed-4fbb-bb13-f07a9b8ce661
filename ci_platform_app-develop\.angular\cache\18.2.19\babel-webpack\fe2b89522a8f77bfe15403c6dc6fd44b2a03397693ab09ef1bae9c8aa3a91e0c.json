{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport { APP_CONFIG } from '../configs/environment.config';\nimport { API_ENDPOINTS } from '../constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = APP_CONFIG.apiBaseUrl;\n    this.imageUrl = APP_CONFIG.imageBaseUrl;\n    this.currentUser = new BehaviorSubject(null);\n    this.currentUserName = new BehaviorSubject(null);\n    this.jwthelperService = new JwtHelperService();\n    this.userPayLoad = this.decodedToken();\n  }\n  registerUser(user) {\n    return this.http.post(`${this.apiUrl}${API_ENDPOINTS.AUTH.REGISTER}`, user, {\n      responseType: 'json'\n    });\n  }\n  getUserById(id) {\n    return this.http.get(`${this.apiUrl}${API_ENDPOINTS.AUTH.GET_USER_BY_ID}/${id}`);\n  }\n  updateUser(data) {\n    return this.http.post(`${this.apiUrl}${API_ENDPOINTS.AUTH.UPDATE_USER}`, data);\n  }\n  loginUser(loginInfo) {\n    return this.http.post(`${this.apiUrl}${API_ENDPOINTS.AUTH.LOGIN}`, {\n      EmailAddress: loginInfo[0],\n      Password: loginInfo[1]\n    }, {\n      responseType: 'json'\n    });\n  }\n  forgotPasswordEmailCheck(data) {\n    return this.http.post(`${this.apiUrl}${API_ENDPOINTS.AUTH.FORGOT_PASSWORD}`, data);\n  }\n  resetPassword(data) {\n    return this.http.post(`${this.apiUrl}${API_ENDPOINTS.AUTH.RESET_PASSWORD}`, data, {\n      responseType: 'text'\n    });\n  }\n  changePassword(data) {\n    return this.http.post(`${this.apiUrl}${API_ENDPOINTS.AUTH.CHANGE_PASSWORD}`, data);\n  }\n  getToken() {\n    return localStorage.getItem('access_Token');\n  }\n  setToken(token) {\n    localStorage.setItem('access_Token', token);\n  }\n  isLoggedIn() {\n    return localStorage.getItem('access_Token') ? true : false;\n  }\n  loggedOut() {\n    localStorage.removeItem('access_Token');\n  }\n  getCurrentUser() {\n    return this.currentUser.asObservable();\n  }\n  setCurrentUser(userDetail) {\n    return this.currentUser.next(userDetail);\n  }\n  decodedToken() {\n    const token = this.getToken();\n    return this.jwthelperService.decodeToken(token);\n  }\n  getUserFullName() {\n    if (this.userPayLoad) return this.userPayLoad.fullName;\n  }\n  getUserDetail() {\n    if (this.userPayLoad) return this.userPayLoad;\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "JwtHelperService", "APP_CONFIG", "API_ENDPOINTS", "AuthService", "constructor", "http", "apiUrl", "apiBaseUrl", "imageUrl", "imageBaseUrl", "currentUser", "currentUserName", "jwthelperService", "userPayLoad", "decodedToken", "registerUser", "user", "post", "AUTH", "REGISTER", "responseType", "getUserById", "id", "get", "GET_USER_BY_ID", "updateUser", "data", "UPDATE_USER", "loginUser", "loginInfo", "LOGIN", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Password", "forgotPasswordEmailCheck", "FORGOT_PASSWORD", "resetPassword", "RESET_PASSWORD", "changePassword", "CHANGE_PASSWORD", "getToken", "localStorage", "getItem", "setToken", "token", "setItem", "isLoggedIn", "loggedOut", "removeItem", "getCurrentUser", "asObservable", "setCurrentUser", "userDetail", "next", "decodeToken", "getUserFullName", "fullName", "getUserDetail", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\services\\auth.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject, type Observable } from 'rxjs';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport { APP_CONFIG } from '../configs/environment.config';\nimport { User } from '../interfaces/user.interface';\nimport { API_ENDPOINTS } from '../constants/api.constants';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class AuthService {\n  apiUrl = APP_CONFIG.apiBaseUrl;\n  imageUrl = APP_CONFIG.imageBaseUrl;\n\n  currentUser: BehaviorSubject<any> = new BehaviorSubject(null);\n  currentUserName: BehaviorSubject<any> = new BehaviorSubject(null);\n  currentUserData: any;\n  public userPayLoad: any;\n  jwthelperService = new JwtHelperService();\n\n  constructor(public http: HttpClient) {\n    this.userPayLoad = this.decodedToken();\n  }\n\n  registerUser(user: User) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.REGISTER}`,\n      user,\n      { responseType: 'json' }\n    );\n  }\n\n  getUserById(id: number): Observable<User[]> {\n    return this.http.get<User[]>(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.GET_USER_BY_ID}/${id}`\n    );\n  }\n\n  updateUser(data: FormData) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.UPDATE_USER}`,\n      data\n    );\n  }\n\n  loginUser(loginInfo: Array<string>) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.LOGIN}`,\n      {\n        EmailAddress: loginInfo[0],\n        Password: loginInfo[1],\n      },\n      { responseType: 'json' }\n    );\n  }\n\n  forgotPasswordEmailCheck(data: any) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.FORGOT_PASSWORD}`,\n      data\n    );\n  }\n\n  resetPassword(data: any) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.RESET_PASSWORD}`,\n      data,\n      { responseType: 'text' }\n    );\n  }\n\n  changePassword(data: any) {\n    return this.http.post(\n      `${this.apiUrl}${API_ENDPOINTS.AUTH.CHANGE_PASSWORD}`,\n      data\n    );\n  }\n\n  getToken() {\n    return localStorage.getItem('access_Token');\n  }\n\n  setToken(token: string) {\n    localStorage.setItem('access_Token', token);\n  }\n\n  isLoggedIn(): boolean {\n    return localStorage.getItem('access_Token') ? true : false;\n  }\n\n  loggedOut() {\n    localStorage.removeItem('access_Token');\n  }\n  public getCurrentUser() {\n    return this.currentUser.asObservable();\n  }\n\n  public setCurrentUser(userDetail: any) {\n    return this.currentUser.next(userDetail);\n  }\n\n  decodedToken() {\n    const token = this.getToken();\n    return this.jwthelperService.decodeToken(token);\n  }\n\n  getUserFullName() {\n    if (this.userPayLoad) return this.userPayLoad.fullName;\n  }\n\n  public getUserDetail() {\n    if (this.userPayLoad) return this.userPayLoad;\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,QAAyB,MAAM;AACvD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,UAAU,QAAQ,+BAA+B;AAE1D,SAASC,aAAa,QAAQ,4BAA4B;;;AAK1D,OAAM,MAAOC,WAAW;EAUtBC,YAAmBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IATvB,KAAAC,MAAM,GAAGL,UAAU,CAACM,UAAU;IAC9B,KAAAC,QAAQ,GAAGP,UAAU,CAACQ,YAAY;IAElC,KAAAC,WAAW,GAAyB,IAAIX,eAAe,CAAC,IAAI,CAAC;IAC7D,KAAAY,eAAe,GAAyB,IAAIZ,eAAe,CAAC,IAAI,CAAC;IAGjE,KAAAa,gBAAgB,GAAG,IAAIZ,gBAAgB,EAAE;IAGvC,IAAI,CAACa,WAAW,GAAG,IAAI,CAACC,YAAY,EAAE;EACxC;EAEAC,YAAYA,CAACC,IAAU;IACrB,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CACnB,GAAG,IAAI,CAACX,MAAM,GAAGJ,aAAa,CAACgB,IAAI,CAACC,QAAQ,EAAE,EAC9CH,IAAI,EACJ;MAAEI,YAAY,EAAE;IAAM,CAAE,CACzB;EACH;EAEAC,WAAWA,CAACC,EAAU;IACpB,OAAO,IAAI,CAACjB,IAAI,CAACkB,GAAG,CAClB,GAAG,IAAI,CAACjB,MAAM,GAAGJ,aAAa,CAACgB,IAAI,CAACM,cAAc,IAAIF,EAAE,EAAE,CAC3D;EACH;EAEAG,UAAUA,CAACC,IAAc;IACvB,OAAO,IAAI,CAACrB,IAAI,CAACY,IAAI,CACnB,GAAG,IAAI,CAACX,MAAM,GAAGJ,aAAa,CAACgB,IAAI,CAACS,WAAW,EAAE,EACjDD,IAAI,CACL;EACH;EAEAE,SAASA,CAACC,SAAwB;IAChC,OAAO,IAAI,CAACxB,IAAI,CAACY,IAAI,CACnB,GAAG,IAAI,CAACX,MAAM,GAAGJ,aAAa,CAACgB,IAAI,CAACY,KAAK,EAAE,EAC3C;MACEC,YAAY,EAAEF,SAAS,CAAC,CAAC,CAAC;MAC1BG,QAAQ,EAAEH,SAAS,CAAC,CAAC;KACtB,EACD;MAAET,YAAY,EAAE;IAAM,CAAE,CACzB;EACH;EAEAa,wBAAwBA,CAACP,IAAS;IAChC,OAAO,IAAI,CAACrB,IAAI,CAACY,IAAI,CACnB,GAAG,IAAI,CAACX,MAAM,GAAGJ,aAAa,CAACgB,IAAI,CAACgB,eAAe,EAAE,EACrDR,IAAI,CACL;EACH;EAEAS,aAAaA,CAACT,IAAS;IACrB,OAAO,IAAI,CAACrB,IAAI,CAACY,IAAI,CACnB,GAAG,IAAI,CAACX,MAAM,GAAGJ,aAAa,CAACgB,IAAI,CAACkB,cAAc,EAAE,EACpDV,IAAI,EACJ;MAAEN,YAAY,EAAE;IAAM,CAAE,CACzB;EACH;EAEAiB,cAAcA,CAACX,IAAS;IACtB,OAAO,IAAI,CAACrB,IAAI,CAACY,IAAI,CACnB,GAAG,IAAI,CAACX,MAAM,GAAGJ,aAAa,CAACgB,IAAI,CAACoB,eAAe,EAAE,EACrDZ,IAAI,CACL;EACH;EAEAa,QAAQA,CAAA;IACN,OAAOC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAC7C;EAEAC,QAAQA,CAACC,KAAa;IACpBH,YAAY,CAACI,OAAO,CAAC,cAAc,EAAED,KAAK,CAAC;EAC7C;EAEAE,UAAUA,CAAA;IACR,OAAOL,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,GAAG,KAAK;EAC5D;EAEAK,SAASA,CAAA;IACPN,YAAY,CAACO,UAAU,CAAC,cAAc,CAAC;EACzC;EACOC,cAAcA,CAAA;IACnB,OAAO,IAAI,CAACtC,WAAW,CAACuC,YAAY,EAAE;EACxC;EAEOC,cAAcA,CAACC,UAAe;IACnC,OAAO,IAAI,CAACzC,WAAW,CAAC0C,IAAI,CAACD,UAAU,CAAC;EAC1C;EAEArC,YAAYA,CAAA;IACV,MAAM6B,KAAK,GAAG,IAAI,CAACJ,QAAQ,EAAE;IAC7B,OAAO,IAAI,CAAC3B,gBAAgB,CAACyC,WAAW,CAACV,KAAK,CAAC;EACjD;EAEAW,eAAeA,CAAA;IACb,IAAI,IAAI,CAACzC,WAAW,EAAE,OAAO,IAAI,CAACA,WAAW,CAAC0C,QAAQ;EACxD;EAEOC,aAAaA,CAAA;IAClB,IAAI,IAAI,CAAC3C,WAAW,EAAE,OAAO,IAAI,CAACA,WAAW;EAC/C;;;uCAtGWV,WAAW,EAAAsD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXzD,WAAW;MAAA0D,OAAA,EAAX1D,WAAW,CAAA2D,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}