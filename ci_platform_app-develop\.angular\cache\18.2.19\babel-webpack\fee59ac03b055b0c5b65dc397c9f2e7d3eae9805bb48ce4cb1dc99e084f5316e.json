{"ast": null, "code": "import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function onErrorResumeNext(...sources) {\n  const nextSources = argsOrArgArray(sources);\n  return operate((source, subscriber) => {\n    const remaining = [source, ...nextSources];\n    const subscribeNext = () => {\n      if (!subscriber.closed) {\n        if (remaining.length > 0) {\n          let nextSource;\n          try {\n            nextSource = innerFrom(remaining.shift());\n          } catch (err) {\n            subscribeNext();\n            return;\n          }\n          const innerSub = createOperatorSubscriber(subscriber, undefined, noop, noop);\n          nextSource.subscribe(innerSub);\n          innerSub.add(subscribeNext);\n        } else {\n          subscriber.complete();\n        }\n      }\n    };\n    subscribeNext();\n  });\n}", "map": {"version": 3, "names": ["operate", "innerFrom", "argsOrArgArray", "createOperatorSubscriber", "noop", "onErrorResumeNext", "sources", "nextSources", "source", "subscriber", "remaining", "subscribeNext", "closed", "length", "nextSource", "shift", "err", "innerSub", "undefined", "subscribe", "add", "complete"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/rxjs/dist/esm/internal/operators/onErrorResumeNext.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function onErrorResumeNext(...sources) {\n    const nextSources = argsOrArgArray(sources);\n    return operate((source, subscriber) => {\n        const remaining = [source, ...nextSources];\n        const subscribeNext = () => {\n            if (!subscriber.closed) {\n                if (remaining.length > 0) {\n                    let nextSource;\n                    try {\n                        nextSource = innerFrom(remaining.shift());\n                    }\n                    catch (err) {\n                        subscribeNext();\n                        return;\n                    }\n                    const innerSub = createOperatorSubscriber(subscriber, undefined, noop, noop);\n                    nextSource.subscribe(innerSub);\n                    innerSub.add(subscribeNext);\n                }\n                else {\n                    subscriber.complete();\n                }\n            }\n        };\n        subscribeNext();\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAO,SAASC,iBAAiBA,CAAC,GAAGC,OAAO,EAAE;EAC1C,MAAMC,WAAW,GAAGL,cAAc,CAACI,OAAO,CAAC;EAC3C,OAAON,OAAO,CAAC,CAACQ,MAAM,EAAEC,UAAU,KAAK;IACnC,MAAMC,SAAS,GAAG,CAACF,MAAM,EAAE,GAAGD,WAAW,CAAC;IAC1C,MAAMI,aAAa,GAAGA,CAAA,KAAM;MACxB,IAAI,CAACF,UAAU,CAACG,MAAM,EAAE;QACpB,IAAIF,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;UACtB,IAAIC,UAAU;UACd,IAAI;YACAA,UAAU,GAAGb,SAAS,CAACS,SAAS,CAACK,KAAK,CAAC,CAAC,CAAC;UAC7C,CAAC,CACD,OAAOC,GAAG,EAAE;YACRL,aAAa,CAAC,CAAC;YACf;UACJ;UACA,MAAMM,QAAQ,GAAGd,wBAAwB,CAACM,UAAU,EAAES,SAAS,EAAEd,IAAI,EAAEA,IAAI,CAAC;UAC5EU,UAAU,CAACK,SAAS,CAACF,QAAQ,CAAC;UAC9BA,QAAQ,CAACG,GAAG,CAACT,aAAa,CAAC;QAC/B,CAAC,MACI;UACDF,UAAU,CAACY,QAAQ,CAAC,CAAC;QACzB;MACJ;IACJ,CAAC;IACDV,aAAa,CAAC,CAAC;EACnB,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}