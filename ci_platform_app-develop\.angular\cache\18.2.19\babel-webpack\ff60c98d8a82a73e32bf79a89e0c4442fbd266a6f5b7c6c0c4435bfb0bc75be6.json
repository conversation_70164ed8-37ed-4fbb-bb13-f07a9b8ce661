{"ast": null, "code": "export const API_ENDPOINTS = {\n  AUTH: {\n    LOGIN: \"/Login/LoginUser\",\n    REGISTER: \"/Login/Register\",\n    FORGOT_PASSWORD: \"/Login/ForgotPassword\",\n    RESET_PASSWORD: \"/Login/ResetPassword\",\n    CHANGE_PASSWORD: \"/Login/ChangePassword\",\n    GET_USER_BY_ID: \"/Login/GetUserById\",\n    UPDATE_USER: \"/Login/UpdateUser\",\n    GET_USER_PROFILE: \"/Login/GetUserProfileDetailById\",\n    UPDATE_USER_PROFILE: \"/Login/LoginUserProfileUpdate\",\n    GET_LOGIN_USER_BY_ID: \"/Login/LoginUserDetailById\"\n  },\n  MISSION: {\n    LIST: \"/Mission/MissionList\",\n    DETAIL: \"/Mission/MissionDetailById\",\n    ADD: \"/Mission/AddMission\",\n    UPDATE: \"/Mission/UpdateMission\",\n    DELETE: \"/Mission/DeleteMission\",\n    THEME_LIST: \"/Mission/GetMissionThemeList\",\n    SKILL_LIST: \"/Mission/GetMissionSkillList\",\n    APPLICATION_LIST: \"/Mission/MissionApplicationList\",\n    APPLICATION_APPROVE: \"/Mission/MissionApplicationApprove\",\n    APPLICATION_DELETE: \"/Mission/MissionApplicationDelete\"\n  },\n  CLIENT_MISSION: {\n    LIST: \"/ClientMission/ClientSideMissionList\",\n    CLIENT_LIST: \"/ClientMission/MissionClientList\",\n    DETAIL: \"/ClientMission/MissionDetailByMissionId\",\n    APPLY: \"/ClientMission/ApplyMission\",\n    ADD_COMMENT: \"/ClientMission/AddMissionComment\",\n    COMMENT_LIST: \"/ClientMission/MissionCommentListByMissionId\",\n    ADD_FAVORITE: \"/ClientMission/AddMissionFavourite\",\n    REMOVE_FAVORITE: \"/ClientMission/RemoveMissionFavourite\",\n    RATING: \"/ClientMission/MissionRating\",\n    RECENT_VOLUNTEERS: \"/ClientMission/RecentVolunteerList\",\n    GET_USER_LIST: \"/ClientMission/GetUserList\",\n    SEND_INVITE: \"/ClientMission/SendInviteMissionMail\",\n    MISSION_TITLE: \"/Story/GetMissionTitle\"\n  },\n  COMMON: {\n    COUNTRY_LIST: \"/Common/CountryList\",\n    CITY_LIST: \"/Common/CityList\",\n    UPLOAD_IMAGE: \"/Common/UploadImage\",\n    CONTACT_US: \"/Common/ContactUs\",\n    ADD_USER_SKILL: \"/Common/AddUserSkill\",\n    GET_USER_SKILL: \"/Common/GetUserSkill\",\n    MISSION_TITLE_LIST: \"/Common/MissionTitleList\",\n    MISSION_COUNTRY_LIST: \"/Common/MissionCountryList\",\n    MISSION_CITY_LIST: \"/Common/MissionCityList\",\n    MISSION_THEME_LIST: \"/Common/MissionThemeList\",\n    MISSION_SKILL_LIST: \"/Common/MissionSkillList\"\n  },\n  TIMESHEET: {\n    GET_HOURS_LIST: \"/VolunteeringTimesheet/GetVolunteeringHoursList\",\n    GET_HOURS_BY_ID: \"/VolunteeringTimesheet/GetVolunteeringHoursListById\",\n    ADD_HOURS: \"/VolunteeringTimesheet/AddVolunteeringHours\",\n    UPDATE_HOURS: \"/VolunteeringTimesheet/UpdateVolunteeringHours\",\n    DELETE_HOURS: \"/VolunteeringTimesheet/DeleteVolunteeringHours\",\n    GET_GOALS_LIST: \"/VolunteeringTimesheet/GetVolunteeringGoalsList\",\n    GET_GOALS_BY_ID: \"/VolunteeringTimesheet/GetVolunteeringGoalsListById\",\n    ADD_GOALS: \"/VolunteeringTimesheet/AddVolunteeringGoals\",\n    UPDATE_GOALS: \"/VolunteeringTimesheet/UpdateVolunteeringGoals\",\n    DELETE_GOALS: \"/VolunteeringTimesheet/DeleteVolunteeringGoals\",\n    VOLUNTEERING_MISSION_LIST: \"/VolunteeringTimesheet/VolunteeringMissionList\"\n  },\n  AdminUser: {\n    USER_LIST: \"/AdminUser/UserDetailList\",\n    DELETE_USER: \"/AdminUser/DeleteUser\"\n  },\n  MISSION_THEME: {\n    LIST: \"/MissionTheme/GetMissionThemeList\",\n    GET_BY_ID: \"/MissionTheme/GetMissionThemeById\",\n    ADD: \"/MissionTheme/AddMissionTheme\",\n    UPDATE: \"/MissionTheme/UpdateMissionTheme\",\n    DELETE: \"/MissionTheme/DeleteMissionTheme\"\n  },\n  MISSION_SKILL: {\n    LIST: \"/MissionSkill/GetMissionSkillList\",\n    GET_BY_ID: \"/MissionSkill/GetMissionSkillById\",\n    ADD: \"/MissionSkill/AddMissionSkill\",\n    UPDATE: \"/MissionSkill/UpdateMissionSkill\",\n    DELETE: \"/MissionSkill/DeleteMissionSkill\"\n  }\n};", "map": {"version": 3, "names": ["API_ENDPOINTS", "AUTH", "LOGIN", "REGISTER", "FORGOT_PASSWORD", "RESET_PASSWORD", "CHANGE_PASSWORD", "GET_USER_BY_ID", "UPDATE_USER", "GET_USER_PROFILE", "UPDATE_USER_PROFILE", "GET_LOGIN_USER_BY_ID", "MISSION", "LIST", "DETAIL", "ADD", "UPDATE", "DELETE", "THEME_LIST", "SKILL_LIST", "APPLICATION_LIST", "APPLICATION_APPROVE", "APPLICATION_DELETE", "CLIENT_MISSION", "CLIENT_LIST", "APPLY", "ADD_COMMENT", "COMMENT_LIST", "ADD_FAVORITE", "REMOVE_FAVORITE", "RATING", "RECENT_VOLUNTEERS", "GET_USER_LIST", "SEND_INVITE", "MISSION_TITLE", "COMMON", "COUNTRY_LIST", "CITY_LIST", "UPLOAD_IMAGE", "CONTACT_US", "ADD_USER_SKILL", "GET_USER_SKILL", "MISSION_TITLE_LIST", "MISSION_COUNTRY_LIST", "MISSION_CITY_LIST", "MISSION_THEME_LIST", "MISSION_SKILL_LIST", "TIMESHEET", "GET_HOURS_LIST", "GET_HOURS_BY_ID", "ADD_HOURS", "UPDATE_HOURS", "DELETE_HOURS", "GET_GOALS_LIST", "GET_GOALS_BY_ID", "ADD_GOALS", "UPDATE_GOALS", "DELETE_GOALS", "VOLUNTEERING_MISSION_LIST", "AdminUser", "USER_LIST", "DELETE_USER", "MISSION_THEME", "GET_BY_ID", "MISSION_SKILL"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\constants\\api.constants.ts"], "sourcesContent": ["export const API_ENDPOINTS = {\n  AUTH: {\n    LOGIN: \"/Login/LoginUser\",\n    REGISTER: \"/Login/Register\",\n    FORGOT_PASSWORD: \"/Login/ForgotPassword\",\n    RESET_PASSWORD: \"/Login/ResetPassword\",\n    CHANGE_PASSWORD: \"/Login/ChangePassword\",\n    GET_USER_BY_ID: \"/Login/GetUserById\",\n    UPDATE_USER: \"/Login/UpdateUser\",\n    GET_USER_PROFILE: \"/Login/GetUserProfileDetailById\",\n    UPDATE_USER_PROFILE: \"/Login/LoginUserProfileUpdate\",\n    GET_LOGIN_USER_BY_ID: \"/Login/LoginUserDetailById\"\n  },\n  MISSION: {\n    LIST: \"/Mission/MissionList\",\n    DETAIL: \"/Mission/MissionDetailById\",\n    ADD: \"/Mission/AddMission\",\n    UPDATE: \"/Mission/UpdateMission\",\n    DELETE: \"/Mission/DeleteMission\",\n    THEME_LIST: \"/Mission/GetMissionThemeList\",\n    SKILL_LIST: \"/Mission/GetMissionSkillList\",\n    APPLICATION_LIST: \"/Mission/MissionApplicationList\",\n    APPLICATION_APPROVE: \"/Mission/MissionApplicationApprove\",\n    APPLICATION_DELETE: \"/Mission/MissionApplicationDelete\",\n  },\n  CLIENT_MISSION: {\n    LIST: \"/ClientMission/ClientSideMissionList\",\n    CLIENT_LIST: \"/ClientMission/MissionClientList\",\n    DETAIL: \"/ClientMission/MissionDetailByMissionId\",\n    APPLY: \"/ClientMission/ApplyMission\",\n    ADD_COMMENT: \"/ClientMission/AddMissionComment\",\n    COMMENT_LIST: \"/ClientMission/MissionCommentListByMissionId\",\n    ADD_FAVORITE: \"/ClientMission/AddMissionFavourite\",\n    REMOVE_FAVORITE: \"/ClientMission/RemoveMissionFavourite\",\n    RATING: \"/ClientMission/MissionRating\",\n    RECENT_VOLUNTEERS: \"/ClientMission/RecentVolunteerList\",\n    GET_USER_LIST: \"/ClientMission/GetUserList\",\n    SEND_INVITE: \"/ClientMission/SendInviteMissionMail\",\n    MISSION_TITLE: \"/Story/GetMissionTitle\"\n  },\n  COMMON: {\n    COUNTRY_LIST: \"/Common/CountryList\",\n    CITY_LIST: \"/Common/CityList\",\n    UPLOAD_IMAGE: \"/Common/UploadImage\",\n    CONTACT_US: \"/Common/ContactUs\",\n    ADD_USER_SKILL: \"/Common/AddUserSkill\",\n    GET_USER_SKILL: \"/Common/GetUserSkill\",\n    MISSION_TITLE_LIST: \"/Common/MissionTitleList\",\n    MISSION_COUNTRY_LIST: \"/Common/MissionCountryList\",\n    MISSION_CITY_LIST: \"/Common/MissionCityList\",\n    MISSION_THEME_LIST: \"/Common/MissionThemeList\",\n    MISSION_SKILL_LIST: \"/Common/MissionSkillList\",\n  },\n  TIMESHEET: {\n    GET_HOURS_LIST: \"/VolunteeringTimesheet/GetVolunteeringHoursList\",\n    GET_HOURS_BY_ID: \"/VolunteeringTimesheet/GetVolunteeringHoursListById\",\n    ADD_HOURS: \"/VolunteeringTimesheet/AddVolunteeringHours\",\n    UPDATE_HOURS: \"/VolunteeringTimesheet/UpdateVolunteeringHours\",\n    DELETE_HOURS: \"/VolunteeringTimesheet/DeleteVolunteeringHours\",\n    GET_GOALS_LIST: \"/VolunteeringTimesheet/GetVolunteeringGoalsList\",\n    GET_GOALS_BY_ID: \"/VolunteeringTimesheet/GetVolunteeringGoalsListById\",\n    ADD_GOALS: \"/VolunteeringTimesheet/AddVolunteeringGoals\",\n    UPDATE_GOALS: \"/VolunteeringTimesheet/UpdateVolunteeringGoals\",\n    DELETE_GOALS: \"/VolunteeringTimesheet/DeleteVolunteeringGoals\",\n    VOLUNTEERING_MISSION_LIST: \"/VolunteeringTimesheet/VolunteeringMissionList\",\n  },\n  AdminUser:{\n    USER_LIST: \"/AdminUser/UserDetailList\",\n    DELETE_USER: \"/AdminUser/DeleteUser\",\n  },\n  MISSION_THEME: {\n    LIST: \"/MissionTheme/GetMissionThemeList\",\n    GET_BY_ID: \"/MissionTheme/GetMissionThemeById\",\n    ADD: \"/MissionTheme/AddMissionTheme\",\n    UPDATE: \"/MissionTheme/UpdateMissionTheme\",\n    DELETE: \"/MissionTheme/DeleteMissionTheme\"\n  },\n  MISSION_SKILL: {\n    LIST: \"/MissionSkill/GetMissionSkillList\",\n    GET_BY_ID: \"/MissionSkill/GetMissionSkillById\",\n    ADD: \"/MissionSkill/AddMissionSkill\",\n    UPDATE: \"/MissionSkill/UpdateMissionSkill\",\n    DELETE: \"/MissionSkill/DeleteMissionSkill\"\n  }\n}"], "mappings": "AAAA,OAAO,MAAMA,aAAa,GAAG;EAC3BC,IAAI,EAAE;IACJC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,iBAAiB;IAC3BC,eAAe,EAAE,uBAAuB;IACxCC,cAAc,EAAE,sBAAsB;IACtCC,eAAe,EAAE,uBAAuB;IACxCC,cAAc,EAAE,oBAAoB;IACpCC,WAAW,EAAE,mBAAmB;IAChCC,gBAAgB,EAAE,iCAAiC;IACnDC,mBAAmB,EAAE,+BAA+B;IACpDC,oBAAoB,EAAE;GACvB;EACDC,OAAO,EAAE;IACPC,IAAI,EAAE,sBAAsB;IAC5BC,MAAM,EAAE,4BAA4B;IACpCC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,wBAAwB;IAChCC,MAAM,EAAE,wBAAwB;IAChCC,UAAU,EAAE,8BAA8B;IAC1CC,UAAU,EAAE,8BAA8B;IAC1CC,gBAAgB,EAAE,iCAAiC;IACnDC,mBAAmB,EAAE,oCAAoC;IACzDC,kBAAkB,EAAE;GACrB;EACDC,cAAc,EAAE;IACdV,IAAI,EAAE,sCAAsC;IAC5CW,WAAW,EAAE,kCAAkC;IAC/CV,MAAM,EAAE,yCAAyC;IACjDW,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,kCAAkC;IAC/CC,YAAY,EAAE,8CAA8C;IAC5DC,YAAY,EAAE,oCAAoC;IAClDC,eAAe,EAAE,uCAAuC;IACxDC,MAAM,EAAE,8BAA8B;IACtCC,iBAAiB,EAAE,oCAAoC;IACvDC,aAAa,EAAE,4BAA4B;IAC3CC,WAAW,EAAE,sCAAsC;IACnDC,aAAa,EAAE;GAChB;EACDC,MAAM,EAAE;IACNC,YAAY,EAAE,qBAAqB;IACnCC,SAAS,EAAE,kBAAkB;IAC7BC,YAAY,EAAE,qBAAqB;IACnCC,UAAU,EAAE,mBAAmB;IAC/BC,cAAc,EAAE,sBAAsB;IACtCC,cAAc,EAAE,sBAAsB;IACtCC,kBAAkB,EAAE,0BAA0B;IAC9CC,oBAAoB,EAAE,4BAA4B;IAClDC,iBAAiB,EAAE,yBAAyB;IAC5CC,kBAAkB,EAAE,0BAA0B;IAC9CC,kBAAkB,EAAE;GACrB;EACDC,SAAS,EAAE;IACTC,cAAc,EAAE,iDAAiD;IACjEC,eAAe,EAAE,qDAAqD;IACtEC,SAAS,EAAE,6CAA6C;IACxDC,YAAY,EAAE,gDAAgD;IAC9DC,YAAY,EAAE,gDAAgD;IAC9DC,cAAc,EAAE,iDAAiD;IACjEC,eAAe,EAAE,qDAAqD;IACtEC,SAAS,EAAE,6CAA6C;IACxDC,YAAY,EAAE,gDAAgD;IAC9DC,YAAY,EAAE,gDAAgD;IAC9DC,yBAAyB,EAAE;GAC5B;EACDC,SAAS,EAAC;IACRC,SAAS,EAAE,2BAA2B;IACtCC,WAAW,EAAE;GACd;EACDC,aAAa,EAAE;IACbjD,IAAI,EAAE,mCAAmC;IACzCkD,SAAS,EAAE,mCAAmC;IAC9ChD,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,kCAAkC;IAC1CC,MAAM,EAAE;GACT;EACD+C,aAAa,EAAE;IACbnD,IAAI,EAAE,mCAAmC;IACzCkD,SAAS,EAAE,mCAAmC;IAC9ChD,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,kCAAkC;IAC1CC,MAAM,EAAE;;CAEX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}