{"ast": null, "code": "import { NgIf } from \"@angular/common\";\nimport { ReactiveFormsModule, Validators } from \"@angular/forms\";\nimport { RouterModule } from \"@angular/router\";\nimport { APP_CONFIG } from \"src/app/main/configs/environment.config\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/main/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"ng-angular-popup\";\nfunction ForgotPasswordComponent_span_23_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Please Enter EmailAddress \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_span_23_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Please Enter Valid EmailAddress \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtemplate(1, ForgotPasswordComponent_span_23_span_1_Template, 2, 0, \"span\", 26)(2, ForgotPasswordComponent_span_23_span_2_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.emailAddress.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.emailAddress.hasError(\"email\"));\n  }\n}\nexport class ForgotPasswordComponent {\n  constructor(_fb, _service, _router, _toast) {\n    this._fb = _fb;\n    this._service = _service;\n    this._router = _router;\n    this._toast = _toast;\n    this.unsubscribe = [];\n  }\n  ngOnInit() {\n    this.forgotPassword();\n  }\n  forgotPassword() {\n    this.forgotPasswordForm = this._fb.group({\n      emailAddress: [null, Validators.compose([Validators.required, Validators.email])]\n    });\n  }\n  get emailAddress() {\n    return this.forgotPasswordForm.get(\"emailAddress\");\n  }\n  onSubmit() {\n    this.formValid = true;\n    if (this.forgotPasswordForm.valid) {\n      const addFormValue = this.forgotPasswordForm.value;\n      addFormValue.baseUrl = document.location.origin;\n      const forgotPasswordSubscribe = this._service.forgotPasswordEmailCheck(addFormValue).subscribe(data => {\n        if (!data) {\n          // this.toastr.error('OOPS This email address does not exist');\n          this._toast.error({\n            detail: \"ERROR\",\n            summary: \"OOPS This email address does not exist\",\n            duration: APP_CONFIG.toastDuration\n          });\n        } else {\n          // this.toastr.success('Reset password mail send successfully. please check your emailtoreset your password');\n          this._toast.success({\n            detail: \"SUCCESS\",\n            summary: \"Password reset link is send to your registred email, Kindly check your mail box\",\n            duration: APP_CONFIG.toastDuration\n          });\n          setTimeout(() => {\n            this._router.navigate([\"\"]);\n          }, 2000);\n        }\n      });\n      this.formValid = false;\n      this.unsubscribe.push(forgotPasswordSubscribe);\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach(sb => sb.unsubscribe());\n  }\n  static {\n    this.ɵfac = function ForgotPasswordComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.NgToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotPasswordComponent,\n      selectors: [[\"app-forgot-password\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 34,\n      vars: 2,\n      consts: [[1, \"container-fluid\", \"ps-md-0\"], [1, \"row\", \"g-0\"], [1, \"d-flex\", \"col-md-6\", \"col-lg-9\", \"bg-image\"], [\"src\", \"assets/Images/image.png\", \"alt\", \"No Image\"], [1, \"carousel-caption\", \"d-md-block\"], [1, \"heading\"], [1, \"content\"], [1, \"col-md-6\", \"col-lg-3\"], [1, \"login\", \"d-flex\", \"align-items-center\"], [1, \"container\"], [1, \"row\"], [1, \"col-md-9\", \"col-lg-8\", 2, \"margin-left\", \"7%\"], [1, \"Forgot\"], [1, \"ForgotContent\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-group\"], [1, \"col-form-label\"], [\"type\", \"text\", \"formControlName\", \"emailAddress\", \"placeholder\", \"Enter your email address..\", \"autofocus\", \"\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"d-grid\", \"mt-4\"], [\"type\", \"submit\", 1, \"btn-login\"], [1, \"Login\"], [1, \"text-center\"], [\"routerLink\", \"../\", 1, \"small\"], [2, \"text-align\", \"center\"], [1, \"text-danger\"], [4, \"ngIf\"]],\n      template: function ForgotPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"p\", 5);\n          i0.ɵɵtext(6, \"Sed ut perspiciatis unde omnis iste natus voluptatem.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"p\", 12);\n          i0.ɵɵtext(15, \"Forgot Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 13);\n          i0.ɵɵtext(17, \" Enter your email address you've using for your account below and we will send you a password reset link \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"form\", 14);\n          i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_Template_form_ngSubmit_18_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(19, \"div\", 15)(20, \"label\", 16);\n          i0.ɵɵtext(21, \"Email Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 17);\n          i0.ɵɵtemplate(23, ForgotPasswordComponent_span_23_Template, 3, 2, \"span\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 19)(25, \"button\", 20)(26, \"span\", 21);\n          i0.ɵɵtext(27, \"Reset my Password\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 22)(29, \"p\", 23);\n          i0.ɵɵtext(30, \"Login\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(31, \"div\", 24)(32, \"p\");\n          i0.ɵɵtext(33, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"formGroup\", ctx.forgotPasswordForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.emailAddress.invalid && (ctx.emailAddress.touched || ctx.formValid));\n        }\n      },\n      dependencies: [ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, NgIf, RouterModule, i3.RouterLink],\n      styles: [\".login[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  padding-top: 200px;\\n}\\n\\n.bg-image[_ngcontent-%COMP%] {\\n  background-size: cover;\\n  background-position: center;\\n}\\n\\n.col-form-label[_ngcontent-%COMP%] {\\n  width: 201px;\\n  height: 14px;\\n  margin: 0 36px 12px 0;\\n  font-family: NotoSans;\\n  font-size: 18px;\\n  font-weight: 300;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n  letter-spacing: normal;\\n  text-align: left;\\n  color: #414141;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 401px;\\n  height: 56px;\\n  \\n\\n\\n  border-radius: 3px;\\n  box-shadow: 0 0 10px 0 rgba(43, 100, 177, 0.12);\\n  border: solid 1px #2b64b1;\\n  background-color: #fff;\\n}\\n\\n.btn-login[_ngcontent-%COMP%] {\\n  width: 401px;\\n  height: 48px;\\n  border-radius: 24px;\\n  border: solid 2px #f88634;\\n  background-color: white;\\n}\\n\\n.Login[_ngcontent-%COMP%] {\\n  width: 43px;\\n  height: 18px;\\n  font-family: NotoSans;\\n  font-size: 22px;\\n  font-weight: normal;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n  letter-spacing: normal;\\n  text-align: left;\\n  color: #f88634;\\n}\\n\\n.small[_ngcontent-%COMP%] {\\n  width: 325px;\\n  height: 12px;\\n  margin: 12px 68px 234px 27px;\\n  font-size: 16px;\\n  font-weight: 400;\\n  color: #414141;\\n  cursor: pointer;\\n}\\n\\n.heading[_ngcontent-%COMP%] {\\n  width: 615px;\\n  height: 95px;\\n  margin: 0 231px 29px 2px;\\n  font-family: NotoSans;\\n  font-size: 43px;\\n  font-weight: normal;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n  letter-spacing: normal;\\n  text-align: left;\\n  color: #fff;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  width: 648px;\\n  height: 101px;\\n  margin: 29px 0 54px;\\n  font-family: NotoSans;\\n  font-size: 16px;\\n  font-weight: 300;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: 1.75;\\n  letter-spacing: normal;\\n  text-align: left;\\n  color: #fff;\\n}\\n\\n.Forgot[_ngcontent-%COMP%] {\\n  width: 191px;\\n  height: 22px;\\n  margin: 0 115px 10px 120px;\\n  font-size: 25px;\\n  font-weight: 400;\\n  color: #414141;\\n}\\n\\n.ForgotContent[_ngcontent-%COMP%] {\\n  width: 389px;\\n  height: 32px;\\n  margin: 10px 7px 36px 5px;\\n  font-size: 16px;\\n  font-weight: 400;\\n\\n  text-align: center;\\n  color: #414141;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NgIf", "ReactiveFormsModule", "Validators", "RouterModule", "APP_CONFIG", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ForgotPasswordComponent_span_23_span_1_Template", "ForgotPasswordComponent_span_23_span_2_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "emailAddress", "<PERSON><PERSON><PERSON><PERSON>", "ForgotPasswordComponent", "constructor", "_fb", "_service", "_router", "_toast", "unsubscribe", "ngOnInit", "forgotPassword", "forgotPasswordForm", "group", "compose", "required", "email", "get", "onSubmit", "formValid", "valid", "addFormValue", "value", "baseUrl", "document", "location", "origin", "forgotPasswordSubscribe", "forgotPasswordEmailCheck", "subscribe", "data", "error", "detail", "summary", "duration", "toastDuration", "success", "setTimeout", "navigate", "push", "ngOnDestroy", "for<PERSON>ach", "sb", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "NgToastService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ForgotPasswordComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "ForgotPasswordComponent_Template_form_ngSubmit_18_listener", "ForgotPasswordComponent_span_23_Template", "invalid", "touched", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "RouterLink", "styles"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\components\\login-register\\forgot-password\\forgot-password.component.ts", "B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\components\\login-register\\forgot-password\\forgot-password.component.html"], "sourcesContent": ["import { NgIf } from \"@angular/common\"\nimport { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from \"@angular/core\"\nimport { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from \"@angular/forms\"\nimport { Router, RouterModule } from \"@angular/router\"\nimport { NgToastService } from \"ng-angular-popup\"\nimport { ToastrService } from \"ngx-toastr\"\nimport { Subscription } from \"rxjs\"\nimport { APP_CONFIG } from \"src/app/main/configs/environment.config\"\nimport { AuthService } from \"src/app/main/services/auth.service\"\n\n@Component({\n  selector: \"app-forgot-password\",\n  standalone: true,\n  imports: [ReactiveFormsModule, NgIf, RouterModule],\n  templateUrl: \"./forgot-password.component.html\",\n  styleUrls: [\"./forgot-password.component.css\"],\n})\nexport class ForgotPasswordComponent implements OnInit, OnDestroy {\n  private unsubscribe: Subscription[] = [];\n\n  constructor(\n    private _fb: FormBuilder,\n    private _service: AuthService,\n    private _router: Router,\n    private _toast: NgToastService,\n  ) { }\n  forgotPasswordForm: FormGroup\n  formValid: boolean\n  ngOnInit(): void {\n    this.forgotPassword()\n  }\n  forgotPassword() {\n    this.forgotPasswordForm = this._fb.group({\n      emailAddress: [null, Validators.compose([Validators.required, Validators.email])],\n    })\n  }\n  get emailAddress() {\n    return this.forgotPasswordForm.get(\"emailAddress\") as FormControl\n  }\n\n  onSubmit() {\n    this.formValid = true\n    if (this.forgotPasswordForm.valid) {\n      const addFormValue = this.forgotPasswordForm.value\n      addFormValue.baseUrl = document.location.origin\n\n      const forgotPasswordSubscribe = this._service.forgotPasswordEmailCheck(addFormValue).subscribe((data: any) => {\n        if (!data) {\n          // this.toastr.error('OOPS This email address does not exist');\n          this._toast.error({ detail: \"ERROR\", summary: \"OOPS This email address does not exist\", duration: APP_CONFIG.toastDuration })\n        } else {\n          // this.toastr.success('Reset password mail send successfully. please check your emailtoreset your password');\n          this._toast.success({\n            detail: \"SUCCESS\",\n            summary: \"Password reset link is send to your registred email, Kindly check your mail box\",\n            duration: APP_CONFIG.toastDuration,\n          })\n          setTimeout(() => {\n            this._router.navigate([\"\"])\n          }, 2000)\n        }\n      })\n      this.formValid = false\n      this.unsubscribe.push(forgotPasswordSubscribe);\n    }\n  }\n\n  ngOnDestroy() {\n    this.unsubscribe.forEach((sb) => sb.unsubscribe())\n  }\n}\n", "<div class=\"container-fluid ps-md-0\">\n  <div class=\"row g-0\">\n    <div class=\"d-flex col-md-6 col-lg-9 bg-image\">\n      <img src=\"assets/Images/image.png\" alt=\"No Image\">\n      <div class=\"carousel-caption d-md-block\">\n        <p class=\"heading\">Sed ut perspiciatis unde omnis\n          iste natus voluptatem.</p>\n        <p class=\"content\"> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna\n            aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint\n            occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>\n    </div>\n    </div>\n    <div class=\"col-md-6 col-lg-3\">\n      <div class=\"login d-flex align-items-center\">\n        <div class=\"container\" >\n          <div class=\"row\">\n            <div class=\"col-md-9 col-lg-8\" style=\"margin-left: 7%;\">\n                <p class=\"Forgot\">Forgot Password</p>\n                <div class=\"ForgotContent\">\n                    Enter your email address you've using for your account below\n                    and we will send you a password reset link\n                </div>\n                <form [formGroup]=\"forgotPasswordForm\" (ngSubmit)=\"onSubmit()\">\n                <div class=\"form-group\">\n                  <label class=\"col-form-label\">Email Address</label>\n                  <input type=\"text\" class=\"form-control\" formControlName=\"emailAddress\" placeholder=\"Enter your email address..\" autofocus>\n                  <span class=\"text-danger\" *ngIf=\"emailAddress.invalid && (emailAddress.touched || formValid)\">\n                    <span *ngIf=\"emailAddress.hasError('required')\">\n                      Please Enter EmailAddress\n                    </span>\n                    <span *ngIf=\"emailAddress.hasError('email')\">\n                      Please Enter Valid EmailAddress\n                    </span>\n                  </span>\n                </div>\n                <div class=\"d-grid mt-4\">\n                  <button class=\"btn-login\" type=\"submit\"><span class=\"Login\">Reset my Password</span></button>\n                  <div class=\"text-center\">\n                    <p class=\"small\" routerLink='../'>Login</p>\n                  </div>\n                </div>\n              </form>\n            </div>\n          </div>\n          <div style=\"text-align: center;\">\n            <p>Privacy Policy</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,iBAAiB;AAEtC,SAA8CC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AACrG,SAAiBC,YAAY,QAAQ,iBAAiB;AAItD,SAASC,UAAU,QAAQ,yCAAyC;;;;;;;;ICqBhDC,EAAA,CAAAC,cAAA,WAAgD;IAC9CD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,WAA6C;IAC3CD,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IANTH,EAAA,CAAAC,cAAA,eAA8F;IAI5FD,EAHA,CAAAI,UAAA,IAAAC,+CAAA,mBAAgD,IAAAC,+CAAA,mBAGH;IAG/CN,EAAA,CAAAG,YAAA,EAAO;;;;IANEH,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,YAAA,CAAAC,QAAA,aAAuC;IAGvCX,EAAA,CAAAO,SAAA,EAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,YAAA,CAAAC,QAAA,UAAoC;;;ADd/D,OAAM,MAAOC,uBAAuB;EAGlCC,YACUC,GAAgB,EAChBC,QAAqB,EACrBC,OAAe,EACfC,MAAsB;IAHtB,KAAAH,GAAG,GAAHA,GAAG;IACH,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IANR,KAAAC,WAAW,GAAmB,EAAE;EAOpC;EAGJC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EACAA,cAAcA,CAAA;IACZ,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACP,GAAG,CAACQ,KAAK,CAAC;MACvCZ,YAAY,EAAE,CAAC,IAAI,EAAEb,UAAU,CAAC0B,OAAO,CAAC,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,KAAK,CAAC,CAAC;KACjF,CAAC;EACJ;EACA,IAAIf,YAAYA,CAAA;IACd,OAAO,IAAI,CAACW,kBAAkB,CAACK,GAAG,CAAC,cAAc,CAAgB;EACnE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACP,kBAAkB,CAACQ,KAAK,EAAE;MACjC,MAAMC,YAAY,GAAG,IAAI,CAACT,kBAAkB,CAACU,KAAK;MAClDD,YAAY,CAACE,OAAO,GAAGC,QAAQ,CAACC,QAAQ,CAACC,MAAM;MAE/C,MAAMC,uBAAuB,GAAG,IAAI,CAACrB,QAAQ,CAACsB,wBAAwB,CAACP,YAAY,CAAC,CAACQ,SAAS,CAAEC,IAAS,IAAI;QAC3G,IAAI,CAACA,IAAI,EAAE;UACT;UACA,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAAC;YAAEC,MAAM,EAAE,OAAO;YAAEC,OAAO,EAAE,wCAAwC;YAAEC,QAAQ,EAAE5C,UAAU,CAAC6C;UAAa,CAAE,CAAC;QAC/H,CAAC,MAAM;UACL;UACA,IAAI,CAAC3B,MAAM,CAAC4B,OAAO,CAAC;YAClBJ,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAE,iFAAiF;YAC1FC,QAAQ,EAAE5C,UAAU,CAAC6C;WACtB,CAAC;UACFE,UAAU,CAAC,MAAK;YACd,IAAI,CAAC9B,OAAO,CAAC+B,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;UAC7B,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,CAAC;MACF,IAAI,CAACnB,SAAS,GAAG,KAAK;MACtB,IAAI,CAACV,WAAW,CAAC8B,IAAI,CAACZ,uBAAuB,CAAC;IAChD;EACF;EAEAa,WAAWA,CAAA;IACT,IAAI,CAAC/B,WAAW,CAACgC,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACjC,WAAW,EAAE,CAAC;EACpD;;;uCApDWN,uBAAuB,EAAAZ,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtD,EAAA,CAAAoD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxD,EAAA,CAAAoD,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA1D,EAAA,CAAAoD,iBAAA,CAAAO,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAvBhD,uBAAuB;MAAAiD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/D,EAAA,CAAAgE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfhCtE,EAFJ,CAAAC,cAAA,aAAqC,aACd,aAC4B;UAC7CD,EAAA,CAAAwE,SAAA,aAAkD;UAEhDxE,EADF,CAAAC,cAAA,aAAyC,WACpB;UAAAD,EAAA,CAAAE,MAAA,4DACK;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5BH,EAAA,CAAAC,cAAA,WAAmB;UAACD,EAAA,CAAAE,MAAA,qcAG+E;UAEvGF,EAFuG,CAAAG,YAAA,EAAI,EACrG,EACA;UAMMH,EALZ,CAAAC,cAAA,aAA+B,cACgB,cACnB,eACL,eACyC,aAClC;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACrCH,EAAA,CAAAC,cAAA,eAA2B;UACvBD,EAAA,CAAAE,MAAA,iHAEJ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA+D;UAAxBD,EAAA,CAAAyE,UAAA,sBAAAC,2DAAA;YAAA,OAAYH,GAAA,CAAA5C,QAAA,EAAU;UAAA,EAAC;UAE5D3B,EADF,CAAAC,cAAA,eAAwB,iBACQ;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAwE,SAAA,iBAA0H;UAC1HxE,EAAA,CAAAI,UAAA,KAAAuE,wCAAA,mBAA8F;UAQhG3E,EAAA,CAAAG,YAAA,EAAM;UAEoCH,EAD1C,CAAAC,cAAA,eAAyB,kBACiB,gBAAoB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAS;UAE3FH,EADF,CAAAC,cAAA,eAAyB,aACW;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAKjDF,EALiD,CAAAG,YAAA,EAAI,EACvC,EACF,EACD,EACH,EACF;UAEJH,EADF,CAAAC,cAAA,eAAiC,SAC5B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAM7BF,EAN6B,CAAAG,YAAA,EAAI,EACjB,EACF,EACF,EACF,EACF,EACF;;;UA7BgBH,EAAA,CAAAO,SAAA,IAAgC;UAAhCP,EAAA,CAAAQ,UAAA,cAAA+D,GAAA,CAAAlD,kBAAA,CAAgC;UAITrB,EAAA,CAAAO,SAAA,GAAiE;UAAjEP,EAAA,CAAAQ,UAAA,SAAA+D,GAAA,CAAA7D,YAAA,CAAAkE,OAAA,KAAAL,GAAA,CAAA7D,YAAA,CAAAmE,OAAA,IAAAN,GAAA,CAAA3C,SAAA,EAAiE;;;qBDdlGhC,mBAAmB,EAAAyD,EAAA,CAAAyB,aAAA,EAAAzB,EAAA,CAAA0B,oBAAA,EAAA1B,EAAA,CAAA2B,eAAA,EAAA3B,EAAA,CAAA4B,oBAAA,EAAA5B,EAAA,CAAA6B,kBAAA,EAAA7B,EAAA,CAAA8B,eAAA,EAAExF,IAAI,EAAEG,YAAY,EAAA2D,EAAA,CAAA2B,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}