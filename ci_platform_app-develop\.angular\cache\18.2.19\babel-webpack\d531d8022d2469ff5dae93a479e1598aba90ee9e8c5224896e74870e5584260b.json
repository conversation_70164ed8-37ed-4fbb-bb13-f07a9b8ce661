{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class FooterComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function FooterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"app-footer\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"container-fluid\", \"footer\"], [\"routerLink\", \"/privacyPolicy\", 2, \"text-decoration\", \"none\", \"cursor\", \"pointer\", \"color\", \"black\"]],\n      template: function FooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"hr\");\n          i0.ɵɵelementStart(2, \"a\", 1);\n          i0.ɵɵtext(3, \"Privacy Policy\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [RouterModule, i1.RouterLink],\n      styles: [\".footer[_ngcontent-%COMP%] {\\n    bottom: 0;\\n    padding-top: 30px;\\n    padding-bottom: 20px;\\n    left: 0;\\n    text-align: center;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImZvb3Rlci5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0lBQ0ksU0FBUztJQUNULGlCQUFpQjtJQUNqQixvQkFBb0I7SUFDcEIsT0FBTztJQUNQLGtCQUFrQjtFQUNwQiIsImZpbGUiOiJmb290ZXIuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi5mb290ZXIge1xuICAgIGJvdHRvbTogMDtcbiAgICBwYWRkaW5nLXRvcDogMzBweDtcbiAgICBwYWRkaW5nLWJvdHRvbTogMjBweDtcbiAgICBsZWZ0OiAwO1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgfVxuICAiXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbWFpbi9jb21wb25lbnRzL2Zvb3Rlci9mb290ZXIuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtJQUNJLFNBQVM7SUFDVCxpQkFBaUI7SUFDakIsb0JBQW9CO0lBQ3BCLE9BQU87SUFDUCxrQkFBa0I7RUFDcEI7O0FBRUYsNGRBQTRkIiwic291cmNlc0NvbnRlbnQiOlsiLmZvb3RlciB7XG4gICAgYm90dG9tOiAwO1xuICAgIHBhZGRpbmctdG9wOiAzMHB4O1xuICAgIHBhZGRpbmctYm90dG9tOiAyMHB4O1xuICAgIGxlZnQ6IDA7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterModule", "FooterComponent", "constructor", "ngOnInit", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "i1", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\Downloads\\ci_platfrom_app-develop\\ci_platfrom_app-develop\\src\\app\\main\\components\\footer\\footer.component.ts", "C:\\Users\\<USER>\\Downloads\\ci_platfrom_app-develop\\ci_platfrom_app-develop\\src\\app\\main\\components\\footer\\footer.component.html"], "sourcesContent": ["import { Component, type OnInit } from \"@angular/core\"\nimport { RouterModule } from \"@angular/router\";\n\n@Component({\n  selector: \"app-footer\",\n  imports: [RouterModule],\n  templateUrl: \"./footer.component.html\",\n  styleUrls: [\"./footer.component.css\"],\n  standalone: true\n})\nexport class FooterComponent implements OnInit {\n  constructor() {}\n\n  ngOnInit(): void {}\n}\n", "<div class=\"container-fluid footer\">\n    <hr/>\n    <a routerLink=\"/privacyPolicy\" style=\"text-decoration: none;cursor:pointer;color: black;\">Privacy Policy</a>\n  </div>\n  "], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;AAS9C,OAAM,MAAOC,eAAe;EAC1BC,YAAA,GAAe;EAEfC,QAAQA,CAAA,GAAU;;;uCAHPF,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BP,EAAA,CAAAS,cAAA,aAAoC;UAChCT,EAAA,CAAAU,SAAA,SAAK;UACLV,EAAA,CAAAS,cAAA,WAA0F;UAAAT,EAAA,CAAAW,MAAA,qBAAc;UAC1GX,EAD0G,CAAAY,YAAA,EAAI,EACxG;;;qBDEInB,YAAY,EAAAoB,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}