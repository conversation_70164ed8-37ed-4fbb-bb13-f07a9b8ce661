{"ast": null, "code": "/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵ$localize } from '@angular/localize';\nexport { ɵ$localize as $localize } from '@angular/localize';\n\n// Attach $localize to the global context, as a side-effect of this module.\nglobalThis.$localize = ɵ$localize;", "map": {"version": 3, "names": ["ɵ$localize", "$localize", "globalThis"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/@angular/localize/fesm2022/init.mjs"], "sourcesContent": ["/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵ$localize } from '@angular/localize';\nexport { ɵ$localize as $localize } from '@angular/localize';\n\n// Attach $localize to the global context, as a side-effect of this module.\nglobalThis.$localize = ɵ$localize;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,SAASA,UAAU,IAAIC,SAAS,QAAQ,mBAAmB;;AAE3D;AACAC,UAAU,CAACD,SAAS,GAAGD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}