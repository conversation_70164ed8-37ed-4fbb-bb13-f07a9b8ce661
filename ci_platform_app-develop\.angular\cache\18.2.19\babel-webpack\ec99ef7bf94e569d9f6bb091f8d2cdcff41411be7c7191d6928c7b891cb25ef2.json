{"ast": null, "code": "import elementScrollPosition from './element-scroll-position';\nimport parentScrollPosition from './parent-scroll-position';\nexport default (offsetParentElement, element) =>\n// eslint-disable-line no-arrow-condition\noffsetParentElement ? elementScrollPosition(offsetParentElement) : parentScrollPosition(element);", "map": {"version": 3, "names": ["elementScrollPosition", "parentScrollPosition", "offsetParentElement", "element"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/offset-parent-scroll-position.js"], "sourcesContent": ["import elementScrollPosition from './element-scroll-position';\nimport parentScrollPosition from './parent-scroll-position';\n\nexport default (offsetParentElement, element) => ( // eslint-disable-line no-arrow-condition\n    offsetParentElement ? elementScrollPosition(offsetParentElement) : parentScrollPosition(element)\n);\n"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,2BAA2B;AAC7D,OAAOC,oBAAoB,MAAM,0BAA0B;AAE3D,eAAe,CAACC,mBAAmB,EAAEC,OAAO;AAAO;AAC/CD,mBAAmB,GAAGF,qBAAqB,CAACE,mBAAmB,CAAC,GAAGD,oBAAoB,CAACE,OAAO,CAClG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}