{"ast": null, "code": "export default {\n  \"fit\": \"fit\",\n  \"flip\": \"flip\",\n  \"none\": \"none\"\n};", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/collision.js"], "sourcesContent": ["export default {\n    \"fit\": \"fit\",\n    \"flip\": \"flip\",\n    \"none\": \"none\"\n};\n"], "mappings": "AAAA,eAAe;EACX,KAAK,EAAE,KAAK;EACZ,MAAM,EAAE,MAAM;EACd,MAAM,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}