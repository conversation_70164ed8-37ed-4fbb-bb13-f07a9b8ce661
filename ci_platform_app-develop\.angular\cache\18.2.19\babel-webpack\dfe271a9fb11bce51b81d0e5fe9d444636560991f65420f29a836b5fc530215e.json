{"ast": null, "code": "export { detectMobileOS, detectDesktopBrowser, browser, mobileOS, touch, msPointers, pointers, touchEnabled } from './support';\nexport { getter } from './accessors/getter';\nexport { setter } from './accessors/setter';", "map": {"version": 3, "names": ["detectMobileOS", "detectDesktopBrowser", "browser", "mobileOS", "touch", "msPointers", "pointers", "touchEnabled", "getter", "setter"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-common/dist/es2015/main.js"], "sourcesContent": ["export { detectMobileOS, detectDesktopBrowser, browser, mobileOS, touch, msPointers, pointers, touchEnabled } from './support';\nexport { getter } from './accessors/getter';\nexport { setter } from './accessors/setter';\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,oBAAoB,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,WAAW;AAC9H,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,MAAM,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}