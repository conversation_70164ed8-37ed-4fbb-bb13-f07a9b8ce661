{"ast": null, "code": "import { APP_CONFIG } from \"../configs/environment.config\";\nimport { API_ENDPOINTS } from \"../constants/api.constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"ngx-toastr\";\nimport * as i3 from \"@angular/router\";\nexport class AdminService {\n  constructor(http, toastr, router) {\n    this.http = http;\n    this.toastr = toastr;\n    this.router = router;\n    this.apiUrl = APP_CONFIG.apiBaseUrl;\n    this.imageUrl = APP_CONFIG.imageBaseUrl;\n  }\n  //User\n  userList() {\n    return this.http.get(`${this.apiUrl}${API_ENDPOINTS.AdminUser.USER_LIST}`);\n  }\n  deleteUser(userId) {\n    return this.http.delete(`${this.apiUrl}${API_ENDPOINTS.AdminUser.DELETE_USER}/${userId}`);\n  }\n  static {\n    this.ɵfac = function AdminService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.ToastrService), i0.ɵɵinject(i3.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdminService,\n      factory: AdminService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["APP_CONFIG", "API_ENDPOINTS", "AdminService", "constructor", "http", "toastr", "router", "apiUrl", "apiBaseUrl", "imageUrl", "imageBaseUrl", "userList", "get", "AdminUser", "USER_LIST", "deleteUser", "userId", "delete", "DELETE_USER", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "ToastrService", "i3", "Router", "factory", "ɵfac", "providedIn"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\services\\admin.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { HttpClient } from \"@angular/common/http\"\nimport { Observable } from \"rxjs\"\nimport { ToastrService } from \"ngx-toastr\"\nimport { Router } from \"@angular/router\"\nimport { APP_CONFIG } from \"../configs/environment.config\"\nimport { API_ENDPOINTS } from \"../constants/api.constants\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class AdminService {\n  constructor(\n    public http: HttpClient,\n    public toastr: ToastrService,\n    public router: Router,\n  ) { }\n\n  apiUrl = APP_CONFIG.apiBaseUrl\n  imageUrl = APP_CONFIG.imageBaseUrl\n\n  //User\n  userList(): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}${API_ENDPOINTS.AdminUser.USER_LIST}`)\n  }\n  \n  deleteUser(userId: any) {\n    return this.http.delete(`${this.apiUrl}${API_ENDPOINTS.AdminUser.DELETE_USER}/${userId}`)\n  }\n}\n\n"], "mappings": "AAKA,SAASA,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,aAAa,QAAQ,4BAA4B;;;;;AAK1D,OAAM,MAAOC,YAAY;EACvBC,YACSC,IAAgB,EAChBC,MAAqB,EACrBC,MAAc;IAFd,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IAGf,KAAAC,MAAM,GAAGP,UAAU,CAACQ,UAAU;IAC9B,KAAAC,QAAQ,GAAGT,UAAU,CAACU,YAAY;EAH9B;EAKJ;EACAC,QAAQA,CAAA;IACN,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAAQ,GAAG,IAAI,CAACL,MAAM,GAAGN,aAAa,CAACY,SAAS,CAACC,SAAS,EAAE,CAAC;EACnF;EAEAC,UAAUA,CAACC,MAAW;IACpB,OAAO,IAAI,CAACZ,IAAI,CAACa,MAAM,CAAC,GAAG,IAAI,CAACV,MAAM,GAAGN,aAAa,CAACY,SAAS,CAACK,WAAW,IAAIF,MAAM,EAAE,CAAC;EAC3F;;;uCAjBWd,YAAY,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAZxB,YAAY;MAAAyB,OAAA,EAAZzB,YAAY,CAAA0B,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}