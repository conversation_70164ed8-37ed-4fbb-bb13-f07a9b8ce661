{"ast": null, "code": "/**-----------------------------------------------------------------------------------------\n* Copyright © 2021 Progress Software Corporation. All rights reserved.\n* Licensed under commercial license. See LICENSE.md in the project root for more information\n*-------------------------------------------------------------------------------------------*/\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Injectable, Directive, Input, HostBinding, HostListener, isDevMode, Component, ContentChild, Output, NgModule } from '@angular/core';\nimport { validatePackage } from '@progress/kendo-licensing';\nimport { Subscription } from 'rxjs';\nimport { getter } from '@progress/kendo-common';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from '@progress/kendo-angular-buttons';\nimport { ButtonsModule } from '@progress/kendo-angular-buttons';\nimport { isChanged } from '@progress/kendo-angular-common';\n\n/**\n * @hidden\n */\nconst _c0 = (a0, a1) => ({\n  templateRef: a0,\n  $implicit: a1\n});\nfunction ListBoxComponent_div_0_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ListBoxComponent_div_0_li_2_Template_button_click_1_listener() {\n      const tool_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.performAction(tool_r2.name));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tool_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"icon\", tool_r2.icon);\n  }\n}\nfunction ListBoxComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"ul\", 7);\n    i0.ɵɵtemplate(2, ListBoxComponent_div_0_li_2_Template, 2, 1, \"li\", 8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedTools);\n  }\n}\nfunction ListBoxComponent_li_5_1_ng_template_0_Template(rf, ctx) {}\nfunction ListBoxComponent_li_5_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ListBoxComponent_li_5_1_ng_template_0_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"templateContext\", i0.ɵɵpureFunction2(1, _c0, ctx_r2.itemTemplate.templateRef, item_r4));\n  }\n}\nfunction ListBoxComponent_li_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getText(item_r4));\n  }\n}\nfunction ListBoxComponent_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 10);\n    i0.ɵɵtemplate(1, ListBoxComponent_li_5_1_Template, 1, 4, null, 11)(2, ListBoxComponent_li_5_ng_template_2_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const defaultItemTemplate_r6 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"k-disabled\", ctx_r2.itemDisabled(item_r4));\n    i0.ɵɵproperty(\"index\", i_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.itemTemplate)(\"ngIfElse\", defaultItemTemplate_r6);\n  }\n}\nconst packageMetadata = {\n  name: '@progress/kendo-angular-listbox',\n  productName: 'Kendo UI for Angular',\n  productCodes: ['KENDOUIANGULAR', 'KENDOUICOMPLETE'],\n  publishDate: **********,\n  version: '',\n  licensingDocsUrl: 'https://www.telerik.com/kendo-angular-ui/my-license/?utm_medium=product&utm_source=kendoangular&utm_campaign=kendo-ui-angular-purchase-license-keys-warning'\n};\n\n/**\n * @hidden\n */\nclass ListBoxSelectionService {\n  constructor() {\n    this.onSelect = new EventEmitter();\n    this.selectedIndex = null;\n  }\n  select(index) {\n    this.selectedIndex = index;\n    this.onSelect.next({\n      index: this.selectedIndex\n    });\n  }\n  isSelected(index) {\n    return index === this.selectedIndex;\n  }\n  clearSelection() {\n    this.selectedIndex = null;\n  }\n}\nListBoxSelectionService.ɵfac = function ListBoxSelectionService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ListBoxSelectionService)();\n};\nListBoxSelectionService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ListBoxSelectionService,\n  factory: ListBoxSelectionService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ListBoxSelectionService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Renders the ListBox item content. To define the item template, nest an `<ng-template>` tag\n * with the `kendoListBoxItemTemplate` directive inside the `<kendo-listbox>` tag. The template context is\n * set to the current data item.\n *\n * @example\n * ```ts\n * _@Component({\n * selector: 'my-app',\n * template: `\n *   <kendo-listbox [data]=\"listBoxItems\">\n *     <ng-template kendoListBoxItemTemplate let-dataItem>\n *       <span>{{ dataItem }} item</span>\n *     </ng-template>\n *   </kendo-listbox>\n * `\n * })\n * ```\n */\nclass ItemTemplateDirective {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n}\nItemTemplateDirective.ɵfac = function ItemTemplateDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ItemTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\nItemTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ItemTemplateDirective,\n  selectors: [[\"\", \"kendoListBoxItemTemplate\", \"\"]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ItemTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[kendoListBoxItemTemplate]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n\n/**\n * @hidden\n */\nconst DEFAULT_TOOLBAR_POSITION = 'right';\n/**\n * @hidden\n */\nconst allTools = [{\n  name: 'moveUp',\n  label: 'Move Up',\n  icon: 'arrow-n'\n}, {\n  name: 'moveDown',\n  label: 'Move Down',\n  icon: 'arrow-s'\n}, {\n  name: 'transferTo',\n  label: 'Transfer From',\n  icon: 'arrow-w'\n}, {\n  name: 'transferFrom',\n  label: 'Transfer To',\n  icon: 'arrow-e'\n}, {\n  name: 'transferAllTo',\n  label: 'Transfer All To',\n  icon: 'arrow-double-60-right'\n}, {\n  name: 'transferAllFrom',\n  label: 'Transfer All From',\n  icon: 'arrow-double-60-left'\n}, {\n  name: 'remove',\n  label: 'Remove',\n  icon: 'x'\n}];\n/**\n * @hidden\n */\nconst sizeClassMap = {\n  small: 'sm',\n  medium: 'md',\n  large: 'lg'\n};\n/**\n * @hidden\n */\nconst toolbarClasses = {\n  left: 'k-listbox-toolbar-left',\n  right: 'k-listbox-toolbar-right',\n  top: 'k-listbox-toolbar-top',\n  bottom: 'k-listbox-toolbar-bottom'\n};\n\n/**\n * @hidden\n */\nconst isPresent = value => value !== null && value !== undefined;\n/**\n * @hidden\n */\nconst isObject = value => isPresent(value) && typeof value === 'object';\n/**\n * @hidden\n */\nconst fieldAccessor = (dataItem, field) => {\n  if (!isPresent(dataItem)) {\n    return null;\n  }\n  if (!isPresent(field) || !isObject(dataItem)) {\n    return dataItem;\n  }\n  // creates a field accessor supporting nested fields processing\n  const valueFrom = getter(field);\n  return valueFrom(dataItem);\n};\n/**\n * @hidden\n */\nconst defaultItemDisabled = () => false;\n/**\n * @hidden\n */\nconst getTools = names => {\n  return names.map(tool => allTools.find(meta => meta.name === tool));\n};\n\n/**\n * @hidden\n */\nclass ItemSelectableDirective {\n  constructor(selectionService) {\n    this.selectionService = selectionService;\n  }\n  get selectedClassName() {\n    return this.selectionService.isSelected(this.index);\n  }\n  onClick(event) {\n    event.stopPropagation();\n    this.selectionService.select(this.index);\n  }\n}\nItemSelectableDirective.ɵfac = function ItemSelectableDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ItemSelectableDirective)(i0.ɵɵdirectiveInject(ListBoxSelectionService));\n};\nItemSelectableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ItemSelectableDirective,\n  selectors: [[\"\", \"kendoListBoxItemSelectable\", \"\"]],\n  hostVars: 2,\n  hostBindings: function ItemSelectableDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function ItemSelectableDirective_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"k-selected\", ctx.selectedClassName);\n    }\n  },\n  inputs: {\n    index: \"index\"\n  }\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ItemSelectableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[kendoListBoxItemSelectable]'\n    }]\n  }], function () {\n    return [{\n      type: ListBoxSelectionService\n    }];\n  }, {\n    index: [{\n      type: Input\n    }],\n    selectedClassName: [{\n      type: HostBinding,\n      args: ['class.k-selected']\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\n\n/**\n * Represents the [Kendo UI ListBox component for Angular]({% slug overview_listbox %}).\n */\nclass ListBoxComponent {\n  constructor(selectionService, renderer, hostElement) {\n    this.selectionService = selectionService;\n    this.renderer = renderer;\n    this.hostElement = hostElement;\n    /**\n     * @hidden\n     */\n    this.listboxClassName = true;\n    /**\n     * The data which will be displayed by the ListBox.\n     */\n    this.data = [];\n    /**\n     * Sets the size of the component.\n     *\n     * The possible values are:\n     * - `'small'`\n     * - `'medium'` (default)\n     * - `'large'`\n     */\n    this.size = 'medium';\n    /**\n     * A function which determines if a specific item is disabled.\n     */\n    this.itemDisabled = defaultItemDisabled;\n    /**\n     * Fires when the user selects a different ListBox item. Also fires when a node is moved, since that also changes its index.\n     */\n    this.selectionChange = new EventEmitter();\n    /**\n     * Fires when the user clicks a ListBox item.\n     */\n    this.actionClick = new EventEmitter();\n    /**\n     * @hidden\n     */\n    this.selectedTools = allTools;\n    this.sub = new Subscription();\n    validatePackage(packageMetadata);\n    this.setToolbarClass(DEFAULT_TOOLBAR_POSITION);\n    this.sub.add(this.selectionService.onSelect.subscribe(e => {\n      this.selectionChange.next(e);\n    }));\n  }\n  /**\n   * Sets whether a toolbar should be displayed with the ListBox, as well as what tools and position should be used.\n   */\n  set toolbar(config) {\n    let position = DEFAULT_TOOLBAR_POSITION;\n    if (typeof config === 'boolean') {\n      this.selectedTools = config ? allTools : [];\n    } else {\n      this.selectedTools = config.tools ? getTools(config.tools) : allTools;\n      if (config.position) {\n        position = config.position;\n      }\n    }\n    this.setToolbarClass(position);\n  }\n  /**\n   * @hidden\n   */\n  get listClasses() {\n    return `k-list k-list-${sizeClassMap[this.size]}`;\n  }\n  /**\n   * @hidden\n   */\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n  }\n  /**\n   * @hidden\n   */\n  performAction(actionName) {\n    this.actionClick.next(actionName);\n  }\n  /**\n   * Programmatically selects a ListBox node.\n   */\n  selectItem(index) {\n    this.selectionService.selectedIndex = index;\n  }\n  /**\n   * Programmatically clears the ListBox selection.\n   */\n  clearSelection() {\n    this.selectionService.clearSelection();\n  }\n  /**\n   * The index of the currently selected item in the ListBox.\n   */\n  get selectedIndex() {\n    return this.selectionService.selectedIndex;\n  }\n  /**\n   * @hidden\n   */\n  getText(dataItem) {\n    if (typeof dataItem !== 'string' && !this.textField && isDevMode()) {\n      throw new Error('Missing textField input. When passing an array of objects as data, please set the textField input of the ListBox accordingly.');\n    }\n    return fieldAccessor(dataItem, this.textField);\n  }\n  setToolbarClass(pos) {\n    Object.keys(toolbarClasses).forEach(className => {\n      if (pos === className) {\n        this.renderer.addClass(this.hostElement.nativeElement, toolbarClasses[className]);\n      } else {\n        this.renderer.removeClass(this.hostElement.nativeElement, toolbarClasses[className]);\n      }\n    });\n  }\n}\nListBoxComponent.ɵfac = function ListBoxComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ListBoxComponent)(i0.ɵɵdirectiveInject(ListBoxSelectionService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nListBoxComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ListBoxComponent,\n  selectors: [[\"kendo-listbox\"]],\n  contentQueries: function ListBoxComponent_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, ItemTemplateDirective, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemTemplate = _t.first);\n    }\n  },\n  hostVars: 2,\n  hostBindings: function ListBoxComponent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"k-listbox\", ctx.listboxClassName);\n    }\n  },\n  inputs: {\n    textField: \"textField\",\n    data: \"data\",\n    size: \"size\",\n    toolbar: \"toolbar\",\n    itemDisabled: \"itemDisabled\"\n  },\n  outputs: {\n    selectionChange: \"selectionChange\",\n    actionClick: \"actionClick\"\n  },\n  features: [i0.ɵɵProvidersFeature([ListBoxSelectionService])],\n  decls: 6,\n  vars: 5,\n  consts: [[\"defaultItemTemplate\", \"\"], [\"class\", \"k-listbox-toolbar\", 4, \"ngIf\"], [1, \"k-list-scroller\", \"k-selectable\"], [1, \"k-list-content\"], [1, \"k-list-ul\"], [\"class\", \"k-list-item\", \"kendoListBoxItemSelectable\", \"\", 3, \"index\", \"k-disabled\", 4, \"ngFor\", \"ngForOf\"], [1, \"k-listbox-toolbar\"], [1, \"k-reset\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoButton\", \"\", \"role\", \"button\", 3, \"click\", \"icon\"], [\"kendoListBoxItemSelectable\", \"\", 1, \"k-list-item\", 3, \"index\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"templateContext\"], [1, \"k-list-item-text\"]],\n  template: function ListBoxComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, ListBoxComponent_div_0_Template, 3, 1, \"div\", 1);\n      i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\")(3, \"div\", 3)(4, \"ul\", 4);\n      i0.ɵɵtemplate(5, ListBoxComponent_li_5_Template, 4, 5, \"li\", 5);\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedTools.length > 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassMap(ctx.listClasses);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngForOf\", ctx.data);\n    }\n  },\n  dependencies: [i2.NgIf, i2.NgForOf, i3.ButtonDirective, ItemSelectableDirective, i3.TemplateContextDirective],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ListBoxComponent, [{\n    type: Component,\n    args: [{\n      selector: 'kendo-listbox',\n      providers: [ListBoxSelectionService],\n      template: `\n            <div class=\"k-listbox-toolbar\" *ngIf=\"selectedTools.length > 0\">\n                <ul class=\"k-reset\">\n                    <li *ngFor=\"let tool of selectedTools\">\n                        <button kendoButton [icon]=\"tool.icon\" (click)=\"performAction(tool.name)\" role=\"button\"></button>\n                    </li>\n\n                    <!-- react moving items has a smoother removal of the style: https://www.telerik.com/kendo-react-ui/components/listbox/ -->\n                </ul>\n            </div>\n            <div class=\"k-list-scroller k-selectable\">\n                <div class=\"{{ listClasses }}\">\n                    <div class=\"k-list-content\">\n                        <ul class=\"k-list-ul\">\n                            <li\n                                class=\"k-list-item\"\n                                *ngFor=\"let item of data; let i = index;\"\n                                kendoListBoxItemSelectable\n                                [index]=\"i\"\n                                [class.k-disabled]=\"itemDisabled(item)\"\n                            >\n                                <ng-template *ngIf=\"itemTemplate; else defaultItemTemplate\"\n                                    [templateContext]=\"{\n                                        templateRef: itemTemplate.templateRef,\n                                        $implicit: item\n                                    }\">\n                                </ng-template>\n                                <ng-template #defaultItemTemplate>\n                                    <span class=\"k-list-item-text\">{{ getText(item) }}</span>\n                                </ng-template>\n                            </li>\n                        </ul>\n                    </div>\n                </div>\n            </div>\n    `\n    }]\n  }], function () {\n    return [{\n      type: ListBoxSelectionService\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    listboxClassName: [{\n      type: HostBinding,\n      args: ['class.k-listbox']\n    }],\n    itemTemplate: [{\n      type: ContentChild,\n      args: [ItemTemplateDirective, {\n        static: false\n      }]\n    }],\n    textField: [{\n      type: Input\n    }],\n    data: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    toolbar: [{\n      type: Input\n    }],\n    itemDisabled: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    actionClick: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * A directive which manages the functoinality of the ListBox tools out of the box, and modifies the provided data accordingly.\n */\nclass DataBindingDirective {\n  constructor(listbox) {\n    this.listbox = listbox;\n    this.actionClickSub = new Subscription();\n    this.selectedBoxSub = new Subscription();\n    this.selectedBox = this.listbox;\n    this.actionClickSub.add(this.listbox.actionClick.subscribe(actionName => {\n      switch (actionName) {\n        case 'moveUp':\n          {\n            this.moveVertically('up');\n            break;\n          }\n        case 'moveDown':\n          {\n            this.moveVertically('down');\n            break;\n          }\n        case 'transferTo':\n          {\n            this.transferItem(this.connectedWith, this.listbox);\n            break;\n          }\n        case 'transferFrom':\n          {\n            this.transferItem(this.listbox, this.connectedWith);\n            break;\n          }\n        case 'transferAllTo':\n          {\n            this.transferAll(this.listbox, this.connectedWith);\n            break;\n          }\n        case 'transferAllFrom':\n          {\n            this.transferAll(this.connectedWith, this.listbox);\n            break;\n          }\n        case 'remove':\n          {\n            this.removeItem();\n            break;\n          }\n        default:\n          {\n            break;\n          }\n      }\n    }));\n  }\n  /**\n   * @hidden\n   */\n  ngOnChanges(changes) {\n    if (isChanged('connectedWith', changes, false)) {\n      if (!changes.connectedWith.firstChange) {\n        this.selectedBoxSub.unsubscribe();\n        this.selectedBoxSub = new Subscription();\n      }\n      this.selectedBoxSub.add(this.listbox.selectionChange.subscribe(() => {\n        this.selectedBox = this.listbox;\n        this.connectedWith.clearSelection();\n      }));\n      this.selectedBoxSub.add(this.connectedWith.selectionChange.subscribe(() => {\n        this.selectedBox = this.connectedWith;\n        this.listbox.clearSelection();\n      }));\n    }\n  }\n  /**\n   * @hidden\n   */\n  ngOnDestroy() {\n    if (this.actionClickSub) {\n      this.actionClickSub.unsubscribe();\n      this.actionClickSub = null;\n    }\n    if (this.selectedBoxSub) {\n      this.selectedBoxSub.unsubscribe();\n      this.selectedBoxSub = null;\n    }\n  }\n  moveVertically(dir) {\n    const index = this.selectedBox.selectedIndex;\n    if (!isPresent(index)) {\n      return;\n    }\n    const topReached = dir === 'up' && index <= 0;\n    const bottomReached = dir === 'down' && index >= this.selectedBox.data.length - 1;\n    if (topReached || bottomReached) {\n      return;\n    }\n    const newIndex = dir === 'up' ? index - 1 : index + 1;\n    // ES6 Destructuring swap\n    [this.selectedBox.data[newIndex], this.selectedBox.data[index]] = [this.selectedBox.data[index], this.selectedBox.data[newIndex]];\n    this.selectedBox.selectionService.select(newIndex);\n  }\n  removeItem() {\n    const index = this.selectedBox.selectedIndex;\n    if (!isPresent(index)) {\n      return;\n    }\n    this.selectedBox.data.splice(index, 1);\n    this.selectedBox.selectionService.clearSelection();\n  }\n  transferItem(source, target) {\n    const item = source && source.data[source.selectedIndex];\n    if (!item || !target || !source) {\n      return;\n    }\n    target.data.push(item);\n    source.data.splice(source.selectedIndex, 1);\n    source.clearSelection();\n    target.selectItem(target.data.length - 1);\n    this.selectedBox = target;\n  }\n  transferAll(source, target) {\n    if (!target || !source) {\n      return;\n    }\n    target.data.splice(target.data.length, 0, ...source.data.splice(0, source.data.length));\n    target.selectItem(target.data.length - 1);\n    this.selectedBox = target;\n  }\n}\nDataBindingDirective.ɵfac = function DataBindingDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DataBindingDirective)(i0.ɵɵdirectiveInject(ListBoxComponent));\n};\nDataBindingDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DataBindingDirective,\n  selectors: [[\"\", \"kendoListBoxDataBinding\", \"\"]],\n  inputs: {\n    connectedWith: \"connectedWith\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataBindingDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[kendoListBoxDataBinding]'\n    }]\n  }], function () {\n    return [{\n      type: ListBoxComponent\n    }];\n  }, {\n    connectedWith: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Represents the [NgModule](https://angular.io/api/core/NgModule) definition for the ListBox component.\n */\nclass ListBoxModule {}\nListBoxModule.ɵfac = function ListBoxModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ListBoxModule)();\n};\nListBoxModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ListBoxModule\n});\nListBoxModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[ButtonsModule, CommonModule]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ListBoxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ButtonsModule, CommonModule],\n      declarations: [ListBoxComponent, ItemTemplateDirective, ItemSelectableDirective, DataBindingDirective],\n      exports: [ListBoxComponent, ItemTemplateDirective, ItemSelectableDirective, DataBindingDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DataBindingDirective, ItemSelectableDirective, ItemTemplateDirective, ListBoxComponent, ListBoxModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Injectable", "Directive", "Input", "HostBinding", "HostListener", "isDevMode", "Component", "ContentChild", "Output", "NgModule", "validatePackage", "Subscription", "getter", "i2", "CommonModule", "i3", "ButtonsModule", "isChanged", "_c0", "a0", "a1", "templateRef", "$implicit", "ListBoxComponent_div_0_li_2_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "ListBoxComponent_div_0_li_2_Template_button_click_1_listener", "tool_r2", "ɵɵrestoreView", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "performAction", "name", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "icon", "ListBoxComponent_div_0_Template", "ɵɵtemplate", "selectedTools", "ListBoxComponent_li_5_1_ng_template_0_Template", "ListBoxComponent_li_5_1_Template", "item_r4", "ɵɵpureFunction2", "itemTemplate", "ListBoxComponent_li_5_ng_template_2_Template", "ɵɵtext", "ɵɵtextInterpolate", "getText", "ListBoxComponent_li_5_Template", "ɵɵtemplateRefExtractor", "i_r5", "index", "defaultItemTemplate_r6", "ɵɵreference", "ɵɵclassProp", "itemDisabled", "packageMetadata", "productName", "productCodes", "publishDate", "version", "licensingDocsUrl", "ListBoxSelectionService", "constructor", "onSelect", "selectedIndex", "select", "next", "isSelected", "clearSelection", "ɵfac", "ListBoxSelectionService_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "ItemTemplateDirective", "ItemTemplateDirective_Factory", "ɵɵdirectiveInject", "TemplateRef", "ɵdir", "ɵɵdefineDirective", "selectors", "args", "selector", "DEFAULT_TOOLBAR_POSITION", "allTools", "label", "sizeClassMap", "small", "medium", "large", "toolbarClasses", "left", "right", "top", "bottom", "isPresent", "value", "undefined", "isObject", "fieldAccessor", "dataItem", "field", "valueFrom", "defaultItemDisabled", "getTools", "names", "map", "tool", "find", "meta", "ItemSelectableDirective", "selectionService", "selectedClassName", "onClick", "event", "stopPropagation", "ItemSelectableDirective_Factory", "hostVars", "hostBindings", "ItemSelectableDirective_HostBindings", "ItemSelectableDirective_click_HostBindingHandler", "$event", "inputs", "ListBoxComponent", "renderer", "hostElement", "listboxClassName", "data", "size", "selectionChange", "actionClick", "sub", "setToolbarClass", "add", "subscribe", "e", "toolbar", "config", "position", "tools", "listClasses", "ngOnDestroy", "unsubscribe", "actionName", "selectItem", "textField", "Error", "pos", "Object", "keys", "for<PERSON>ach", "className", "addClass", "nativeElement", "removeClass", "ListBoxComponent_Factory", "Renderer2", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "ListBoxComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "ListBoxComponent_HostBindings", "outputs", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "ListBoxComponent_Template", "length", "ɵɵclassMap", "dependencies", "NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ButtonDirective", "TemplateContextDirective", "encapsulation", "providers", "static", "DataBindingDirective", "listbox", "actionClickSub", "selectedBoxSub", "selectedBox", "moveVertically", "transferItem", "connectedWith", "transferAll", "removeItem", "ngOnChanges", "changes", "firstChange", "dir", "topReached", "bottomReached", "newIndex", "splice", "source", "target", "item", "push", "DataBindingDirective_Factory", "ɵɵNgOnChangesFeature", "ListBoxModule", "ListBoxModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/@progress/kendo-angular-listbox/fesm2015/kendo-angular-listbox.js"], "sourcesContent": ["/**-----------------------------------------------------------------------------------------\n* Copyright © 2021 Progress Software Corporation. All rights reserved.\n* Licensed under commercial license. See LICENSE.md in the project root for more information\n*-------------------------------------------------------------------------------------------*/\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Injectable, Directive, Input, HostBinding, HostListener, isDevMode, Component, ContentChild, Output, NgModule } from '@angular/core';\nimport { validatePackage } from '@progress/kendo-licensing';\nimport { Subscription } from 'rxjs';\nimport { getter } from '@progress/kendo-common';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from '@progress/kendo-angular-buttons';\nimport { ButtonsModule } from '@progress/kendo-angular-buttons';\nimport { isChanged } from '@progress/kendo-angular-common';\n\n/**\n * @hidden\n */\nconst packageMetadata = {\n    name: '@progress/kendo-angular-listbox',\n    productName: 'Kendo UI for Angular',\n    productCodes: ['KENDOUIANGULAR', 'KENDOUICOMPLETE'],\n    publishDate: **********,\n    version: '',\n    licensingDocsUrl: 'https://www.telerik.com/kendo-angular-ui/my-license/?utm_medium=product&utm_source=kendoangular&utm_campaign=kendo-ui-angular-purchase-license-keys-warning'\n};\n\n/**\n * @hidden\n */\nclass ListBoxSelectionService {\n    constructor() {\n        this.onSelect = new EventEmitter();\n        this.selectedIndex = null;\n    }\n    select(index) {\n        this.selectedIndex = index;\n        this.onSelect.next({ index: this.selectedIndex });\n    }\n    isSelected(index) {\n        return index === this.selectedIndex;\n    }\n    clearSelection() {\n        this.selectedIndex = null;\n    }\n}\nListBoxSelectionService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ListBoxSelectionService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nListBoxSelectionService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ListBoxSelectionService });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ListBoxSelectionService, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * Renders the ListBox item content. To define the item template, nest an `<ng-template>` tag\n * with the `kendoListBoxItemTemplate` directive inside the `<kendo-listbox>` tag. The template context is\n * set to the current data item.\n *\n * @example\n * ```ts\n * _@Component({\n * selector: 'my-app',\n * template: `\n *   <kendo-listbox [data]=\"listBoxItems\">\n *     <ng-template kendoListBoxItemTemplate let-dataItem>\n *       <span>{{ dataItem }} item</span>\n *     </ng-template>\n *   </kendo-listbox>\n * `\n * })\n * ```\n */\nclass ItemTemplateDirective {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n    }\n}\nItemTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ItemTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nItemTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.16\", type: ItemTemplateDirective, selector: \"[kendoListBoxItemTemplate]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ItemTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[kendoListBoxItemTemplate]'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n\n/**\n * @hidden\n */\nconst DEFAULT_TOOLBAR_POSITION = 'right';\n/**\n * @hidden\n */\nconst allTools = [\n    {\n        name: 'moveUp',\n        label: 'Move Up',\n        icon: 'arrow-n'\n    },\n    {\n        name: 'moveDown',\n        label: 'Move Down',\n        icon: 'arrow-s'\n    },\n    {\n        name: 'transferTo',\n        label: 'Transfer From',\n        icon: 'arrow-w'\n    },\n    {\n        name: 'transferFrom',\n        label: 'Transfer To',\n        icon: 'arrow-e'\n    },\n    {\n        name: 'transferAllTo',\n        label: 'Transfer All To',\n        icon: 'arrow-double-60-right'\n    },\n    {\n        name: 'transferAllFrom',\n        label: 'Transfer All From',\n        icon: 'arrow-double-60-left'\n    },\n    {\n        name: 'remove',\n        label: 'Remove',\n        icon: 'x'\n    }\n];\n/**\n * @hidden\n */\nconst sizeClassMap = {\n    small: 'sm',\n    medium: 'md',\n    large: 'lg'\n};\n/**\n * @hidden\n */\nconst toolbarClasses = {\n    left: 'k-listbox-toolbar-left',\n    right: 'k-listbox-toolbar-right',\n    top: 'k-listbox-toolbar-top',\n    bottom: 'k-listbox-toolbar-bottom'\n};\n\n/**\n * @hidden\n */\nconst isPresent = (value) => value !== null && value !== undefined;\n/**\n * @hidden\n */\nconst isObject = (value) => isPresent(value) && typeof value === 'object';\n/**\n * @hidden\n */\nconst fieldAccessor = (dataItem, field) => {\n    if (!isPresent(dataItem)) {\n        return null;\n    }\n    if (!isPresent(field) || !isObject(dataItem)) {\n        return dataItem;\n    }\n    // creates a field accessor supporting nested fields processing\n    const valueFrom = getter(field);\n    return valueFrom(dataItem);\n};\n/**\n * @hidden\n */\nconst defaultItemDisabled = () => false;\n/**\n * @hidden\n */\nconst getTools = (names) => {\n    return names.map(tool => allTools.find(meta => meta.name === tool));\n};\n\n/**\n * @hidden\n */\nclass ItemSelectableDirective {\n    constructor(selectionService) {\n        this.selectionService = selectionService;\n    }\n    get selectedClassName() {\n        return this.selectionService.isSelected(this.index);\n    }\n    onClick(event) {\n        event.stopPropagation();\n        this.selectionService.select(this.index);\n    }\n}\nItemSelectableDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ItemSelectableDirective, deps: [{ token: ListBoxSelectionService }], target: i0.ɵɵFactoryTarget.Directive });\nItemSelectableDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.16\", type: ItemSelectableDirective, selector: \"[kendoListBoxItemSelectable]\", inputs: { index: \"index\" }, host: { listeners: { \"click\": \"onClick($event)\" }, properties: { \"class.k-selected\": \"this.selectedClassName\" } }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ItemSelectableDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[kendoListBoxItemSelectable]'\n                }]\n        }], ctorParameters: function () { return [{ type: ListBoxSelectionService }]; }, propDecorators: { index: [{\n                type: Input\n            }], selectedClassName: [{\n                type: HostBinding,\n                args: ['class.k-selected']\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\n\n/**\n * Represents the [Kendo UI ListBox component for Angular]({% slug overview_listbox %}).\n */\nclass ListBoxComponent {\n    constructor(selectionService, renderer, hostElement) {\n        this.selectionService = selectionService;\n        this.renderer = renderer;\n        this.hostElement = hostElement;\n        /**\n         * @hidden\n         */\n        this.listboxClassName = true;\n        /**\n         * The data which will be displayed by the ListBox.\n         */\n        this.data = [];\n        /**\n         * Sets the size of the component.\n         *\n         * The possible values are:\n         * - `'small'`\n         * - `'medium'` (default)\n         * - `'large'`\n         */\n        this.size = 'medium';\n        /**\n         * A function which determines if a specific item is disabled.\n         */\n        this.itemDisabled = defaultItemDisabled;\n        /**\n         * Fires when the user selects a different ListBox item. Also fires when a node is moved, since that also changes its index.\n         */\n        this.selectionChange = new EventEmitter();\n        /**\n         * Fires when the user clicks a ListBox item.\n         */\n        this.actionClick = new EventEmitter();\n        /**\n         * @hidden\n         */\n        this.selectedTools = allTools;\n        this.sub = new Subscription();\n        validatePackage(packageMetadata);\n        this.setToolbarClass(DEFAULT_TOOLBAR_POSITION);\n        this.sub.add(this.selectionService.onSelect.subscribe((e) => {\n            this.selectionChange.next(e);\n        }));\n    }\n    /**\n     * Sets whether a toolbar should be displayed with the ListBox, as well as what tools and position should be used.\n     */\n    set toolbar(config) {\n        let position = DEFAULT_TOOLBAR_POSITION;\n        if (typeof config === 'boolean') {\n            this.selectedTools = config ? allTools : [];\n        }\n        else {\n            this.selectedTools = config.tools ? getTools(config.tools) : allTools;\n            if (config.position) {\n                position = config.position;\n            }\n        }\n        this.setToolbarClass(position);\n    }\n    /**\n     * @hidden\n     */\n    get listClasses() {\n        return `k-list k-list-${sizeClassMap[this.size]}`;\n    }\n    /**\n     * @hidden\n     */\n    ngOnDestroy() {\n        this.sub.unsubscribe();\n    }\n    /**\n     * @hidden\n     */\n    performAction(actionName) {\n        this.actionClick.next(actionName);\n    }\n    /**\n     * Programmatically selects a ListBox node.\n     */\n    selectItem(index) {\n        this.selectionService.selectedIndex = index;\n    }\n    /**\n     * Programmatically clears the ListBox selection.\n     */\n    clearSelection() {\n        this.selectionService.clearSelection();\n    }\n    /**\n     * The index of the currently selected item in the ListBox.\n     */\n    get selectedIndex() {\n        return this.selectionService.selectedIndex;\n    }\n    /**\n     * @hidden\n     */\n    getText(dataItem) {\n        if (typeof dataItem !== 'string' && !this.textField && isDevMode()) {\n            throw new Error('Missing textField input. When passing an array of objects as data, please set the textField input of the ListBox accordingly.');\n        }\n        return fieldAccessor(dataItem, this.textField);\n    }\n    setToolbarClass(pos) {\n        Object.keys(toolbarClasses).forEach((className) => {\n            if (pos === className) {\n                this.renderer.addClass(this.hostElement.nativeElement, toolbarClasses[className]);\n            }\n            else {\n                this.renderer.removeClass(this.hostElement.nativeElement, toolbarClasses[className]);\n            }\n        });\n    }\n}\nListBoxComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ListBoxComponent, deps: [{ token: ListBoxSelectionService }, { token: i0.Renderer2 }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nListBoxComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.2.16\", type: ListBoxComponent, selector: \"kendo-listbox\", inputs: { textField: \"textField\", data: \"data\", size: \"size\", toolbar: \"toolbar\", itemDisabled: \"itemDisabled\" }, outputs: { selectionChange: \"selectionChange\", actionClick: \"actionClick\" }, host: { properties: { \"class.k-listbox\": \"this.listboxClassName\" } }, providers: [ListBoxSelectionService], queries: [{ propertyName: \"itemTemplate\", first: true, predicate: ItemTemplateDirective, descendants: true }], ngImport: i0, template: `\n            <div class=\"k-listbox-toolbar\" *ngIf=\"selectedTools.length > 0\">\n                <ul class=\"k-reset\">\n                    <li *ngFor=\"let tool of selectedTools\">\n                        <button kendoButton [icon]=\"tool.icon\" (click)=\"performAction(tool.name)\" role=\"button\"></button>\n                    </li>\n\n                    <!-- react moving items has a smoother removal of the style: https://www.telerik.com/kendo-react-ui/components/listbox/ -->\n                </ul>\n            </div>\n            <div class=\"k-list-scroller k-selectable\">\n                <div class=\"{{ listClasses }}\">\n                    <div class=\"k-list-content\">\n                        <ul class=\"k-list-ul\">\n                            <li\n                                class=\"k-list-item\"\n                                *ngFor=\"let item of data; let i = index;\"\n                                kendoListBoxItemSelectable\n                                [index]=\"i\"\n                                [class.k-disabled]=\"itemDisabled(item)\"\n                            >\n                                <ng-template *ngIf=\"itemTemplate; else defaultItemTemplate\"\n                                    [templateContext]=\"{\n                                        templateRef: itemTemplate.templateRef,\n                                        $implicit: item\n                                    }\">\n                                </ng-template>\n                                <ng-template #defaultItemTemplate>\n                                    <span class=\"k-list-item-text\">{{ getText(item) }}</span>\n                                </ng-template>\n                            </li>\n                        </ul>\n                    </div>\n                </div>\n            </div>\n    `, isInline: true, directives: [{ type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { type: i3.ButtonDirective, selector: \"button[kendoButton], span[kendoButton]\", inputs: [\"toggleable\", \"togglable\", \"selected\", \"tabIndex\", \"icon\", \"iconClass\", \"imageUrl\", \"disabled\", \"size\", \"rounded\", \"fillMode\", \"themeColor\", \"role\", \"primary\", \"look\"], outputs: [\"selectedChange\", \"click\"], exportAs: [\"kendoButton\"] }, { type: ItemSelectableDirective, selector: \"[kendoListBoxItemSelectable]\", inputs: [\"index\"] }, { type: i3.TemplateContextDirective, selector: \"[templateContext]\", inputs: [\"templateContext\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ListBoxComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'kendo-listbox',\n                    providers: [ListBoxSelectionService],\n                    template: `\n            <div class=\"k-listbox-toolbar\" *ngIf=\"selectedTools.length > 0\">\n                <ul class=\"k-reset\">\n                    <li *ngFor=\"let tool of selectedTools\">\n                        <button kendoButton [icon]=\"tool.icon\" (click)=\"performAction(tool.name)\" role=\"button\"></button>\n                    </li>\n\n                    <!-- react moving items has a smoother removal of the style: https://www.telerik.com/kendo-react-ui/components/listbox/ -->\n                </ul>\n            </div>\n            <div class=\"k-list-scroller k-selectable\">\n                <div class=\"{{ listClasses }}\">\n                    <div class=\"k-list-content\">\n                        <ul class=\"k-list-ul\">\n                            <li\n                                class=\"k-list-item\"\n                                *ngFor=\"let item of data; let i = index;\"\n                                kendoListBoxItemSelectable\n                                [index]=\"i\"\n                                [class.k-disabled]=\"itemDisabled(item)\"\n                            >\n                                <ng-template *ngIf=\"itemTemplate; else defaultItemTemplate\"\n                                    [templateContext]=\"{\n                                        templateRef: itemTemplate.templateRef,\n                                        $implicit: item\n                                    }\">\n                                </ng-template>\n                                <ng-template #defaultItemTemplate>\n                                    <span class=\"k-list-item-text\">{{ getText(item) }}</span>\n                                </ng-template>\n                            </li>\n                        </ul>\n                    </div>\n                </div>\n            </div>\n    `\n                }]\n        }], ctorParameters: function () { return [{ type: ListBoxSelectionService }, { type: i0.Renderer2 }, { type: i0.ElementRef }]; }, propDecorators: { listboxClassName: [{\n                type: HostBinding,\n                args: ['class.k-listbox']\n            }], itemTemplate: [{\n                type: ContentChild,\n                args: [ItemTemplateDirective, { static: false }]\n            }], textField: [{\n                type: Input\n            }], data: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], toolbar: [{\n                type: Input\n            }], itemDisabled: [{\n                type: Input\n            }], selectionChange: [{\n                type: Output\n            }], actionClick: [{\n                type: Output\n            }] } });\n\n/**\n * A directive which manages the functoinality of the ListBox tools out of the box, and modifies the provided data accordingly.\n */\nclass DataBindingDirective {\n    constructor(listbox) {\n        this.listbox = listbox;\n        this.actionClickSub = new Subscription();\n        this.selectedBoxSub = new Subscription();\n        this.selectedBox = this.listbox;\n        this.actionClickSub.add(this.listbox.actionClick.subscribe((actionName) => {\n            switch (actionName) {\n                case 'moveUp': {\n                    this.moveVertically('up');\n                    break;\n                }\n                case 'moveDown': {\n                    this.moveVertically('down');\n                    break;\n                }\n                case 'transferTo': {\n                    this.transferItem(this.connectedWith, this.listbox);\n                    break;\n                }\n                case 'transferFrom': {\n                    this.transferItem(this.listbox, this.connectedWith);\n                    break;\n                }\n                case 'transferAllTo': {\n                    this.transferAll(this.listbox, this.connectedWith);\n                    break;\n                }\n                case 'transferAllFrom': {\n                    this.transferAll(this.connectedWith, this.listbox);\n                    break;\n                }\n                case 'remove': {\n                    this.removeItem();\n                    break;\n                }\n                default: {\n                    break;\n                }\n            }\n        }));\n    }\n    /**\n     * @hidden\n     */\n    ngOnChanges(changes) {\n        if (isChanged('connectedWith', changes, false)) {\n            if (!changes.connectedWith.firstChange) {\n                this.selectedBoxSub.unsubscribe();\n                this.selectedBoxSub = new Subscription();\n            }\n            this.selectedBoxSub.add(this.listbox.selectionChange.subscribe(() => {\n                this.selectedBox = this.listbox;\n                this.connectedWith.clearSelection();\n            }));\n            this.selectedBoxSub.add(this.connectedWith.selectionChange.subscribe(() => {\n                this.selectedBox = this.connectedWith;\n                this.listbox.clearSelection();\n            }));\n        }\n    }\n    /**\n     * @hidden\n     */\n    ngOnDestroy() {\n        if (this.actionClickSub) {\n            this.actionClickSub.unsubscribe();\n            this.actionClickSub = null;\n        }\n        if (this.selectedBoxSub) {\n            this.selectedBoxSub.unsubscribe();\n            this.selectedBoxSub = null;\n        }\n    }\n    moveVertically(dir) {\n        const index = this.selectedBox.selectedIndex;\n        if (!isPresent(index)) {\n            return;\n        }\n        const topReached = dir === 'up' && index <= 0;\n        const bottomReached = dir === 'down' && index >= this.selectedBox.data.length - 1;\n        if (topReached || bottomReached) {\n            return;\n        }\n        const newIndex = dir === 'up' ? index - 1 : index + 1;\n        // ES6 Destructuring swap\n        [this.selectedBox.data[newIndex], this.selectedBox.data[index]] = [this.selectedBox.data[index], this.selectedBox.data[newIndex]];\n        this.selectedBox.selectionService.select(newIndex);\n    }\n    removeItem() {\n        const index = this.selectedBox.selectedIndex;\n        if (!isPresent(index)) {\n            return;\n        }\n        this.selectedBox.data.splice(index, 1);\n        this.selectedBox.selectionService.clearSelection();\n    }\n    transferItem(source, target) {\n        const item = source && source.data[source.selectedIndex];\n        if (!item || !target || !source) {\n            return;\n        }\n        target.data.push(item);\n        source.data.splice(source.selectedIndex, 1);\n        source.clearSelection();\n        target.selectItem(target.data.length - 1);\n        this.selectedBox = target;\n    }\n    transferAll(source, target) {\n        if (!target || !source) {\n            return;\n        }\n        target.data.splice(target.data.length, 0, ...source.data.splice(0, source.data.length));\n        target.selectItem(target.data.length - 1);\n        this.selectedBox = target;\n    }\n}\nDataBindingDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: DataBindingDirective, deps: [{ token: ListBoxComponent }], target: i0.ɵɵFactoryTarget.Directive });\nDataBindingDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.2.16\", type: DataBindingDirective, selector: \"[kendoListBoxDataBinding]\", inputs: { connectedWith: \"connectedWith\" }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: DataBindingDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[kendoListBoxDataBinding]'\n                }]\n        }], ctorParameters: function () { return [{ type: ListBoxComponent }]; }, propDecorators: { connectedWith: [{\n                type: Input\n            }] } });\n\n/**\n * Represents the [NgModule](https://angular.io/api/core/NgModule) definition for the ListBox component.\n */\nclass ListBoxModule {\n}\nListBoxModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ListBoxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nListBoxModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ListBoxModule, declarations: [ListBoxComponent, ItemTemplateDirective, ItemSelectableDirective, DataBindingDirective], imports: [ButtonsModule, CommonModule], exports: [ListBoxComponent, ItemTemplateDirective, ItemSelectableDirective, DataBindingDirective] });\nListBoxModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ListBoxModule, imports: [[ButtonsModule, CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.2.16\", ngImport: i0, type: ListBoxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [ButtonsModule, CommonModule],\n                    declarations: [ListBoxComponent, ItemTemplateDirective, ItemSelectableDirective, DataBindingDirective],\n                    exports: [ListBoxComponent, ItemTemplateDirective, ItemSelectableDirective, DataBindingDirective]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DataBindingDirective, ItemSelectableDirective, ItemTemplateDirective, ListBoxComponent, ListBoxModule };\n\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,YAAY,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC3J,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,SAAS,QAAQ,gCAAgC;;AAE1D;AACA;AACA;AAFA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAAC,WAAA,EAAAF,EAAA;EAAAG,SAAA,EAAAF;AAAA;AAAA,SAAAG,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA+B2G5B,EAAE,CAAA6B,gBAAA;IAAF7B,EAAE,CAAA8B,cAAA,QAkSnD,CAAC,eACoD,CAAC;IAnSL9B,EAAE,CAAA+B,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,OAAA,GAAFjC,EAAE,CAAAkC,aAAA,CAAAN,GAAA,EAAAJ,SAAA;MAAA,MAAAW,MAAA,GAAFnC,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAqC,WAAA,CAmSrCF,MAAA,CAAAG,aAAA,CAAAL,OAAA,CAAAM,IAAuB,CAAC;IAAA,EAAC;IAnSUvC,EAAE,CAAAwC,YAAA,CAmSW,CAAC,CACjG,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAO,OAAA,GAAAN,GAAA,CAAAH,SAAA;IApSkFxB,EAAE,CAAAyC,SAAA,CAmShD,CAAC;IAnS6CzC,EAAE,CAAA0C,UAAA,SAAAT,OAAA,CAAAU,IAmShD,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnS6C1B,EAAE,CAAA8B,cAAA,YAgSlC,CAAC,WACzC,CAAC;IAjSuE9B,EAAE,CAAA6C,UAAA,IAAApB,oCAAA,eAkSnD,CAAC;IAlSgDzB,EAAE,CAAAwC,YAAA,CAuSzF,CAAC,CACJ,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAS,MAAA,GAxSyFnC,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,SAAA,EAkSrD,CAAC;IAlSkDzC,EAAE,CAAA0C,UAAA,YAAAP,MAAA,CAAAW,aAkSrD,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAArB,EAAA,EAAAC,GAAA;AAAA,SAAAqB,iCAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlSkD1B,EAAE,CAAA6C,UAAA,IAAAE,8CAAA,yBAwTvE,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAuB,OAAA,GAxToEjD,EAAE,CAAAoC,aAAA,GAAAZ,SAAA;IAAA,MAAAW,MAAA,GAAFnC,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAA0C,UAAA,oBAAF1C,EAAE,CAAAkD,eAAA,IAAA9B,GAAA,EAAAe,MAAA,CAAAgB,YAAA,CAAA5B,WAAA,EAAA0B,OAAA,CAwTxE,CAAC;EAAA;AAAA;AAAA,SAAAG,6CAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxTqE1B,EAAE,CAAA8B,cAAA,cA2T3C,CAAC;IA3TwC9B,EAAE,CAAAqD,MAAA,EA2TxB,CAAC;IA3TqBrD,EAAE,CAAAwC,YAAA,CA2TjB,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAuB,OAAA,GA3TcjD,EAAE,CAAAoC,aAAA,GAAAZ,SAAA;IAAA,MAAAW,MAAA,GAAFnC,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,SAAA,CA2TxB,CAAC;IA3TqBzC,EAAE,CAAAsD,iBAAA,CAAAnB,MAAA,CAAAoB,OAAA,CAAAN,OAAA,CA2TxB,CAAC;EAAA;AAAA;AAAA,SAAAO,+BAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3TqB1B,EAAE,CAAA8B,cAAA,YAmTjF,CAAC;IAnT8E9B,EAAE,CAAA6C,UAAA,IAAAG,gCAAA,gBAwTvE,CAAC,IAAAI,4CAAA,gCAxToEpD,EAAE,CAAAyD,sBA0T5C,CAAC;IA1TyCzD,EAAE,CAAAwC,YAAA,CA6T7E,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAuB,OAAA,GAAAtB,GAAA,CAAAH,SAAA;IAAA,MAAAkC,IAAA,GAAA/B,GAAA,CAAAgC,KAAA;IAAA,MAAAC,sBAAA,GA7T0E5D,EAAE,CAAA6D,WAAA;IAAA,MAAA1B,MAAA,GAAFnC,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAA8D,WAAA,eAAA3B,MAAA,CAAA4B,YAAA,CAAAd,OAAA,CAkTvC,CAAC;IAlToCjD,EAAE,CAAA0C,UAAA,UAAAgB,IAiTnE,CAAC;IAjTgE1D,EAAE,CAAAyC,SAAA,CAoT5C,CAAC;IApTyCzC,EAAE,CAAA0C,UAAA,SAAAP,MAAA,CAAAgB,YAoT5C,CAAC,aAAAS,sBAAuB,CAAC;EAAA;AAAA;AAhV1F,MAAMI,eAAe,GAAG;EACpBzB,IAAI,EAAE,iCAAiC;EACvC0B,WAAW,EAAE,sBAAsB;EACnCC,YAAY,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;EACnDC,WAAW,EAAE,UAAU;EACvBC,OAAO,EAAE,EAAE;EACXC,gBAAgB,EAAE;AACtB,CAAC;;AAED;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,IAAIvE,YAAY,CAAC,CAAC;IAClC,IAAI,CAACwE,aAAa,GAAG,IAAI;EAC7B;EACAC,MAAMA,CAACf,KAAK,EAAE;IACV,IAAI,CAACc,aAAa,GAAGd,KAAK;IAC1B,IAAI,CAACa,QAAQ,CAACG,IAAI,CAAC;MAAEhB,KAAK,EAAE,IAAI,CAACc;IAAc,CAAC,CAAC;EACrD;EACAG,UAAUA,CAACjB,KAAK,EAAE;IACd,OAAOA,KAAK,KAAK,IAAI,CAACc,aAAa;EACvC;EACAI,cAAcA,CAAA,EAAG;IACb,IAAI,CAACJ,aAAa,GAAG,IAAI;EAC7B;AACJ;AACAH,uBAAuB,CAACQ,IAAI,YAAAC,gCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAyFV,uBAAuB;AAAA,CAAoD;AAChMA,uBAAuB,CAACW,KAAK,kBAD8EjF,EAAE,CAAAkF,kBAAA;EAAAC,KAAA,EACYb,uBAAuB;EAAAc,OAAA,EAAvBd,uBAAuB,CAAAQ;AAAA,EAAG;AACnJ;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAF2GrF,EAAE,CAAAsF,iBAAA,CAEjBhB,uBAAuB,EAAc,CAAC;IACtHiB,IAAI,EAAErF;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsF,qBAAqB,CAAC;EACxBjB,WAAWA,CAAChD,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ;AACAiE,qBAAqB,CAACV,IAAI,YAAAW,8BAAAT,iBAAA;EAAA,YAAAA,iBAAA,IAAyFQ,qBAAqB,EA9B7BxF,EAAE,CAAA0F,iBAAA,CA8B6C1F,EAAE,CAAC2F,WAAW;AAAA,CAA4C;AACpNH,qBAAqB,CAACI,IAAI,kBA/BiF5F,EAAE,CAAA6F,iBAAA;EAAAN,IAAA,EA+BNC,qBAAqB;EAAAM,SAAA;AAAA,EAAyD;AACrL;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KAhC2GrF,EAAE,CAAAsF,iBAAA,CAgCjBE,qBAAqB,EAAc,CAAC;IACpHD,IAAI,EAAEpF,SAAS;IACf4F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAET,IAAI,EAAEvF,EAAE,CAAC2F;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE9E;AACA;AACA;AACA,MAAMM,wBAAwB,GAAG,OAAO;AACxC;AACA;AACA;AACA,MAAMC,QAAQ,GAAG,CACb;EACI3D,IAAI,EAAE,QAAQ;EACd4D,KAAK,EAAE,SAAS;EAChBxD,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,IAAI,EAAE,UAAU;EAChB4D,KAAK,EAAE,WAAW;EAClBxD,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,IAAI,EAAE,YAAY;EAClB4D,KAAK,EAAE,eAAe;EACtBxD,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,IAAI,EAAE,cAAc;EACpB4D,KAAK,EAAE,aAAa;EACpBxD,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,IAAI,EAAE,eAAe;EACrB4D,KAAK,EAAE,iBAAiB;EACxBxD,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,IAAI,EAAE,iBAAiB;EACvB4D,KAAK,EAAE,mBAAmB;EAC1BxD,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,IAAI,EAAE,QAAQ;EACd4D,KAAK,EAAE,QAAQ;EACfxD,IAAI,EAAE;AACV,CAAC,CACJ;AACD;AACA;AACA;AACA,MAAMyD,YAAY,GAAG;EACjBC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA,MAAMC,cAAc,GAAG;EACnBC,IAAI,EAAE,wBAAwB;EAC9BC,KAAK,EAAE,yBAAyB;EAChCC,GAAG,EAAE,uBAAuB;EAC5BC,MAAM,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACA,MAAMC,SAAS,GAAIC,KAAK,IAAKA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS;AAClE;AACA;AACA;AACA,MAAMC,QAAQ,GAAIF,KAAK,IAAKD,SAAS,CAACC,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ;AACzE;AACA;AACA;AACA,MAAMG,aAAa,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;EACvC,IAAI,CAACN,SAAS,CAACK,QAAQ,CAAC,EAAE;IACtB,OAAO,IAAI;EACf;EACA,IAAI,CAACL,SAAS,CAACM,KAAK,CAAC,IAAI,CAACH,QAAQ,CAACE,QAAQ,CAAC,EAAE;IAC1C,OAAOA,QAAQ;EACnB;EACA;EACA,MAAME,SAAS,GAAGtG,MAAM,CAACqG,KAAK,CAAC;EAC/B,OAAOC,SAAS,CAACF,QAAQ,CAAC;AAC9B,CAAC;AACD;AACA;AACA;AACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM,KAAK;AACvC;AACA;AACA;AACA,MAAMC,QAAQ,GAAIC,KAAK,IAAK;EACxB,OAAOA,KAAK,CAACC,GAAG,CAACC,IAAI,IAAIvB,QAAQ,CAACwB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpF,IAAI,KAAKkF,IAAI,CAAC,CAAC;AACvE,CAAC;;AAED;AACA;AACA;AACA,MAAMG,uBAAuB,CAAC;EAC1BrD,WAAWA,CAACsD,gBAAgB,EAAE;IAC1B,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;EAC5C;EACA,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACD,gBAAgB,CAACjD,UAAU,CAAC,IAAI,CAACjB,KAAK,CAAC;EACvD;EACAoE,OAAOA,CAACC,KAAK,EAAE;IACXA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,CAACJ,gBAAgB,CAACnD,MAAM,CAAC,IAAI,CAACf,KAAK,CAAC;EAC5C;AACJ;AACAiE,uBAAuB,CAAC9C,IAAI,YAAAoD,gCAAAlD,iBAAA;EAAA,YAAAA,iBAAA,IAAyF4C,uBAAuB,EArJjC5H,EAAE,CAAA0F,iBAAA,CAqJiDpB,uBAAuB;AAAA,CAA4C;AACjOsD,uBAAuB,CAAChC,IAAI,kBAtJ+E5F,EAAE,CAAA6F,iBAAA;EAAAN,IAAA,EAsJJqC,uBAAuB;EAAA9B,SAAA;EAAAqC,QAAA;EAAAC,YAAA,WAAAC,qCAAA3G,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAtJrB1B,EAAE,CAAA+B,UAAA,mBAAAuG,iDAAAC,MAAA;QAAA,OAsJJ5G,GAAA,CAAAoG,OAAA,CAAAQ,MAAc,CAAC;MAAA,CAAO,CAAC;IAAA;IAAA,IAAA7G,EAAA;MAtJrB1B,EAAE,CAAA8D,WAAA,eAAAnC,GAAA,CAAAmG,iBAsJkB,CAAC;IAAA;EAAA;EAAAU,MAAA;IAAA7E,KAAA;EAAA;AAAA,EAA0M;AAC1U;EAAA,QAAA0B,SAAA,oBAAAA,SAAA,KAvJ2GrF,EAAE,CAAAsF,iBAAA,CAuJjBsC,uBAAuB,EAAc,CAAC;IACtHrC,IAAI,EAAEpF,SAAS;IACf4F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAET,IAAI,EAAEjB;IAAwB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEX,KAAK,EAAE,CAAC;MACnG4B,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAE0H,iBAAiB,EAAE,CAAC;MACpBvC,IAAI,EAAElF,WAAW;MACjB0F,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEgC,OAAO,EAAE,CAAC;MACVxC,IAAI,EAAEjF,YAAY;MAClByF,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAM0C,gBAAgB,CAAC;EACnBlE,WAAWA,CAACsD,gBAAgB,EAAEa,QAAQ,EAAEC,WAAW,EAAE;IACjD,IAAI,CAACd,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACa,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B;AACR;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;AACR;AACA;IACQ,IAAI,CAAC/E,YAAY,GAAGsD,mBAAmB;IACvC;AACR;AACA;IACQ,IAAI,CAAC0B,eAAe,GAAG,IAAI9I,YAAY,CAAC,CAAC;IACzC;AACR;AACA;IACQ,IAAI,CAAC+I,WAAW,GAAG,IAAI/I,YAAY,CAAC,CAAC;IACrC;AACR;AACA;IACQ,IAAI,CAAC6C,aAAa,GAAGoD,QAAQ;IAC7B,IAAI,CAAC+C,GAAG,GAAG,IAAIpI,YAAY,CAAC,CAAC;IAC7BD,eAAe,CAACoD,eAAe,CAAC;IAChC,IAAI,CAACkF,eAAe,CAACjD,wBAAwB,CAAC;IAC9C,IAAI,CAACgD,GAAG,CAACE,GAAG,CAAC,IAAI,CAACtB,gBAAgB,CAACrD,QAAQ,CAAC4E,SAAS,CAAEC,CAAC,IAAK;MACzD,IAAI,CAACN,eAAe,CAACpE,IAAI,CAAC0E,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;EACI,IAAIC,OAAOA,CAACC,MAAM,EAAE;IAChB,IAAIC,QAAQ,GAAGvD,wBAAwB;IACvC,IAAI,OAAOsD,MAAM,KAAK,SAAS,EAAE;MAC7B,IAAI,CAACzG,aAAa,GAAGyG,MAAM,GAAGrD,QAAQ,GAAG,EAAE;IAC/C,CAAC,MACI;MACD,IAAI,CAACpD,aAAa,GAAGyG,MAAM,CAACE,KAAK,GAAGnC,QAAQ,CAACiC,MAAM,CAACE,KAAK,CAAC,GAAGvD,QAAQ;MACrE,IAAIqD,MAAM,CAACC,QAAQ,EAAE;QACjBA,QAAQ,GAAGD,MAAM,CAACC,QAAQ;MAC9B;IACJ;IACA,IAAI,CAACN,eAAe,CAACM,QAAQ,CAAC;EAClC;EACA;AACJ;AACA;EACI,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,iBAAiBtD,YAAY,CAAC,IAAI,CAAC0C,IAAI,CAAC,EAAE;EACrD;EACA;AACJ;AACA;EACIa,WAAWA,CAAA,EAAG;IACV,IAAI,CAACV,GAAG,CAACW,WAAW,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;EACItH,aAAaA,CAACuH,UAAU,EAAE;IACtB,IAAI,CAACb,WAAW,CAACrE,IAAI,CAACkF,UAAU,CAAC;EACrC;EACA;AACJ;AACA;EACIC,UAAUA,CAACnG,KAAK,EAAE;IACd,IAAI,CAACkE,gBAAgB,CAACpD,aAAa,GAAGd,KAAK;EAC/C;EACA;AACJ;AACA;EACIkB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACgD,gBAAgB,CAAChD,cAAc,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;EACI,IAAIJ,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACoD,gBAAgB,CAACpD,aAAa;EAC9C;EACA;AACJ;AACA;EACIlB,OAAOA,CAAC2D,QAAQ,EAAE;IACd,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC6C,SAAS,IAAIxJ,SAAS,CAAC,CAAC,EAAE;MAChE,MAAM,IAAIyJ,KAAK,CAAC,+HAA+H,CAAC;IACpJ;IACA,OAAO/C,aAAa,CAACC,QAAQ,EAAE,IAAI,CAAC6C,SAAS,CAAC;EAClD;EACAb,eAAeA,CAACe,GAAG,EAAE;IACjBC,MAAM,CAACC,IAAI,CAAC3D,cAAc,CAAC,CAAC4D,OAAO,CAAEC,SAAS,IAAK;MAC/C,IAAIJ,GAAG,KAAKI,SAAS,EAAE;QACnB,IAAI,CAAC3B,QAAQ,CAAC4B,QAAQ,CAAC,IAAI,CAAC3B,WAAW,CAAC4B,aAAa,EAAE/D,cAAc,CAAC6D,SAAS,CAAC,CAAC;MACrF,CAAC,MACI;QACD,IAAI,CAAC3B,QAAQ,CAAC8B,WAAW,CAAC,IAAI,CAAC7B,WAAW,CAAC4B,aAAa,EAAE/D,cAAc,CAAC6D,SAAS,CAAC,CAAC;MACxF;IACJ,CAAC,CAAC;EACN;AACJ;AACA5B,gBAAgB,CAAC3D,IAAI,YAAA2F,yBAAAzF,iBAAA;EAAA,YAAAA,iBAAA,IAAyFyD,gBAAgB,EA9RnBzI,EAAE,CAAA0F,iBAAA,CA8RmCpB,uBAAuB,GA9R5DtE,EAAE,CAAA0F,iBAAA,CA8RuE1F,EAAE,CAAC0K,SAAS,GA9RrF1K,EAAE,CAAA0F,iBAAA,CA8RgG1F,EAAE,CAAC2K,UAAU;AAAA,CAA4C;AACtQlC,gBAAgB,CAACmC,IAAI,kBA/RsF5K,EAAE,CAAA6K,iBAAA;EAAAtF,IAAA,EA+RXkD,gBAAgB;EAAA3C,SAAA;EAAAgF,cAAA,WAAAC,gCAAArJ,EAAA,EAAAC,GAAA,EAAAqJ,QAAA;IAAA,IAAAtJ,EAAA;MA/RP1B,EAAE,CAAAiL,cAAA,CAAAD,QAAA,EA+R+YxF,qBAAqB;IAAA;IAAA,IAAA9D,EAAA;MAAA,IAAAwJ,EAAA;MA/RtalL,EAAE,CAAAmL,cAAA,CAAAD,EAAA,GAAFlL,EAAE,CAAAoL,WAAA,QAAAzJ,GAAA,CAAAwB,YAAA,GAAA+H,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAlD,QAAA;EAAAC,YAAA,WAAAkD,8BAAA5J,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF1B,EAAE,CAAA8D,WAAA,cAAAnC,GAAA,CAAAiH,gBA+RI,CAAC;IAAA;EAAA;EAAAJ,MAAA;IAAAuB,SAAA;IAAAlB,IAAA;IAAAC,IAAA;IAAAQ,OAAA;IAAAvF,YAAA;EAAA;EAAAwH,OAAA;IAAAxC,eAAA;IAAAC,WAAA;EAAA;EAAAwC,QAAA,GA/RPxL,EAAE,CAAAyL,kBAAA,CA+RkT,CAACnH,uBAAuB,CAAC;EAAAoH,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,0BAAApK,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA/R7U1B,EAAE,CAAA6C,UAAA,IAAAD,+BAAA,gBAgSlC,CAAC;MAhS+B5C,EAAE,CAAA8B,cAAA,YAySxD,CAAC,SACR,CAAC,YACA,CAAC,WACH,CAAC;MA5S6D9B,EAAE,CAAA6C,UAAA,IAAAW,8BAAA,eAmTjF,CAAC;MAnT8ExD,EAAE,CAAAwC,YAAA,CA8TjF,CAAC,CACJ,CAAC,CACL,CAAC,CACL,CAAC;IAAA;IAAA,IAAAd,EAAA;MAjUyF1B,EAAE,CAAA0C,UAAA,SAAAf,GAAA,CAAAmB,aAAA,CAAAiJ,MAAA,IAgSpC,CAAC;MAhSiC/L,EAAE,CAAAyC,SAAA,EA0ShE,CAAC;MA1S6DzC,EAAE,CAAAgM,UAAA,CAAArK,GAAA,CAAA+H,WA0ShE,CAAC;MA1S6D1J,EAAE,CAAAyC,SAAA,EA+SpD,CAAC;MA/SiDzC,EAAE,CAAA0C,UAAA,YAAAf,GAAA,CAAAkH,IA+SpD,CAAC;IAAA;EAAA;EAAAoD,YAAA,GAmBdlL,EAAE,CAACmL,IAAI,EAA0EnL,EAAE,CAACoL,OAAO,EAAgGlL,EAAE,CAACmL,eAAe,EAAmTxE,uBAAuB,EAAyE3G,EAAE,CAACoL,wBAAwB;EAAAC,aAAA;AAAA,EAAkE;AACzuB;EAAA,QAAAjH,SAAA,oBAAAA,SAAA,KAnU2GrF,EAAE,CAAAsF,iBAAA,CAmUjBmD,gBAAgB,EAAc,CAAC;IAC/GlD,IAAI,EAAE/E,SAAS;IACfuF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBuG,SAAS,EAAE,CAACjI,uBAAuB,CAAC;MACpCuH,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtG,IAAI,EAAEjB;IAAwB,CAAC,EAAE;MAAEiB,IAAI,EAAEvF,EAAE,CAAC0K;IAAU,CAAC,EAAE;MAAEnF,IAAI,EAAEvF,EAAE,CAAC2K;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE/B,gBAAgB,EAAE,CAAC;MAC/JrD,IAAI,EAAElF,WAAW;MACjB0F,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE5C,YAAY,EAAE,CAAC;MACfoC,IAAI,EAAE9E,YAAY;MAClBsF,IAAI,EAAE,CAACP,qBAAqB,EAAE;QAAEgH,MAAM,EAAE;MAAM,CAAC;IACnD,CAAC,CAAC;IAAEzC,SAAS,EAAE,CAAC;MACZxE,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEyI,IAAI,EAAE,CAAC;MACPtD,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAE0I,IAAI,EAAE,CAAC;MACPvD,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEkJ,OAAO,EAAE,CAAC;MACV/D,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAE2D,YAAY,EAAE,CAAC;MACfwB,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAE2I,eAAe,EAAE,CAAC;MAClBxD,IAAI,EAAE7E;IACV,CAAC,CAAC;IAAEsI,WAAW,EAAE,CAAC;MACdzD,IAAI,EAAE7E;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAM+L,oBAAoB,CAAC;EACvBlI,WAAWA,CAACmI,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI9L,YAAY,CAAC,CAAC;IACxC,IAAI,CAAC+L,cAAc,GAAG,IAAI/L,YAAY,CAAC,CAAC;IACxC,IAAI,CAACgM,WAAW,GAAG,IAAI,CAACH,OAAO;IAC/B,IAAI,CAACC,cAAc,CAACxD,GAAG,CAAC,IAAI,CAACuD,OAAO,CAAC1D,WAAW,CAACI,SAAS,CAAES,UAAU,IAAK;MACvE,QAAQA,UAAU;QACd,KAAK,QAAQ;UAAE;YACX,IAAI,CAACiD,cAAc,CAAC,IAAI,CAAC;YACzB;UACJ;QACA,KAAK,UAAU;UAAE;YACb,IAAI,CAACA,cAAc,CAAC,MAAM,CAAC;YAC3B;UACJ;QACA,KAAK,YAAY;UAAE;YACf,IAAI,CAACC,YAAY,CAAC,IAAI,CAACC,aAAa,EAAE,IAAI,CAACN,OAAO,CAAC;YACnD;UACJ;QACA,KAAK,cAAc;UAAE;YACjB,IAAI,CAACK,YAAY,CAAC,IAAI,CAACL,OAAO,EAAE,IAAI,CAACM,aAAa,CAAC;YACnD;UACJ;QACA,KAAK,eAAe;UAAE;YAClB,IAAI,CAACC,WAAW,CAAC,IAAI,CAACP,OAAO,EAAE,IAAI,CAACM,aAAa,CAAC;YAClD;UACJ;QACA,KAAK,iBAAiB;UAAE;YACpB,IAAI,CAACC,WAAW,CAAC,IAAI,CAACD,aAAa,EAAE,IAAI,CAACN,OAAO,CAAC;YAClD;UACJ;QACA,KAAK,QAAQ;UAAE;YACX,IAAI,CAACQ,UAAU,CAAC,CAAC;YACjB;UACJ;QACA;UAAS;YACL;UACJ;MACJ;IACJ,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;EACIC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIjM,SAAS,CAAC,eAAe,EAAEiM,OAAO,EAAE,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACA,OAAO,CAACJ,aAAa,CAACK,WAAW,EAAE;QACpC,IAAI,CAACT,cAAc,CAAChD,WAAW,CAAC,CAAC;QACjC,IAAI,CAACgD,cAAc,GAAG,IAAI/L,YAAY,CAAC,CAAC;MAC5C;MACA,IAAI,CAAC+L,cAAc,CAACzD,GAAG,CAAC,IAAI,CAACuD,OAAO,CAAC3D,eAAe,CAACK,SAAS,CAAC,MAAM;QACjE,IAAI,CAACyD,WAAW,GAAG,IAAI,CAACH,OAAO;QAC/B,IAAI,CAACM,aAAa,CAACnI,cAAc,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC;MACH,IAAI,CAAC+H,cAAc,CAACzD,GAAG,CAAC,IAAI,CAAC6D,aAAa,CAACjE,eAAe,CAACK,SAAS,CAAC,MAAM;QACvE,IAAI,CAACyD,WAAW,GAAG,IAAI,CAACG,aAAa;QACrC,IAAI,CAACN,OAAO,CAAC7H,cAAc,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC;IACP;EACJ;EACA;AACJ;AACA;EACI8E,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACgD,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAAC/C,WAAW,CAAC,CAAC;MACjC,IAAI,CAAC+C,cAAc,GAAG,IAAI;IAC9B;IACA,IAAI,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAAChD,WAAW,CAAC,CAAC;MACjC,IAAI,CAACgD,cAAc,GAAG,IAAI;IAC9B;EACJ;EACAE,cAAcA,CAACQ,GAAG,EAAE;IAChB,MAAM3J,KAAK,GAAG,IAAI,CAACkJ,WAAW,CAACpI,aAAa;IAC5C,IAAI,CAACoC,SAAS,CAAClD,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,MAAM4J,UAAU,GAAGD,GAAG,KAAK,IAAI,IAAI3J,KAAK,IAAI,CAAC;IAC7C,MAAM6J,aAAa,GAAGF,GAAG,KAAK,MAAM,IAAI3J,KAAK,IAAI,IAAI,CAACkJ,WAAW,CAAChE,IAAI,CAACkD,MAAM,GAAG,CAAC;IACjF,IAAIwB,UAAU,IAAIC,aAAa,EAAE;MAC7B;IACJ;IACA,MAAMC,QAAQ,GAAGH,GAAG,KAAK,IAAI,GAAG3J,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;IACrD;IACA,CAAC,IAAI,CAACkJ,WAAW,CAAChE,IAAI,CAAC4E,QAAQ,CAAC,EAAE,IAAI,CAACZ,WAAW,CAAChE,IAAI,CAAClF,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAACkJ,WAAW,CAAChE,IAAI,CAAClF,KAAK,CAAC,EAAE,IAAI,CAACkJ,WAAW,CAAChE,IAAI,CAAC4E,QAAQ,CAAC,CAAC;IACjI,IAAI,CAACZ,WAAW,CAAChF,gBAAgB,CAACnD,MAAM,CAAC+I,QAAQ,CAAC;EACtD;EACAP,UAAUA,CAAA,EAAG;IACT,MAAMvJ,KAAK,GAAG,IAAI,CAACkJ,WAAW,CAACpI,aAAa;IAC5C,IAAI,CAACoC,SAAS,CAAClD,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,IAAI,CAACkJ,WAAW,CAAChE,IAAI,CAAC6E,MAAM,CAAC/J,KAAK,EAAE,CAAC,CAAC;IACtC,IAAI,CAACkJ,WAAW,CAAChF,gBAAgB,CAAChD,cAAc,CAAC,CAAC;EACtD;EACAkI,YAAYA,CAACY,MAAM,EAAEC,MAAM,EAAE;IACzB,MAAMC,IAAI,GAAGF,MAAM,IAAIA,MAAM,CAAC9E,IAAI,CAAC8E,MAAM,CAAClJ,aAAa,CAAC;IACxD,IAAI,CAACoJ,IAAI,IAAI,CAACD,MAAM,IAAI,CAACD,MAAM,EAAE;MAC7B;IACJ;IACAC,MAAM,CAAC/E,IAAI,CAACiF,IAAI,CAACD,IAAI,CAAC;IACtBF,MAAM,CAAC9E,IAAI,CAAC6E,MAAM,CAACC,MAAM,CAAClJ,aAAa,EAAE,CAAC,CAAC;IAC3CkJ,MAAM,CAAC9I,cAAc,CAAC,CAAC;IACvB+I,MAAM,CAAC9D,UAAU,CAAC8D,MAAM,CAAC/E,IAAI,CAACkD,MAAM,GAAG,CAAC,CAAC;IACzC,IAAI,CAACc,WAAW,GAAGe,MAAM;EAC7B;EACAX,WAAWA,CAACU,MAAM,EAAEC,MAAM,EAAE;IACxB,IAAI,CAACA,MAAM,IAAI,CAACD,MAAM,EAAE;MACpB;IACJ;IACAC,MAAM,CAAC/E,IAAI,CAAC6E,MAAM,CAACE,MAAM,CAAC/E,IAAI,CAACkD,MAAM,EAAE,CAAC,EAAE,GAAG4B,MAAM,CAAC9E,IAAI,CAAC6E,MAAM,CAAC,CAAC,EAAEC,MAAM,CAAC9E,IAAI,CAACkD,MAAM,CAAC,CAAC;IACvF6B,MAAM,CAAC9D,UAAU,CAAC8D,MAAM,CAAC/E,IAAI,CAACkD,MAAM,GAAG,CAAC,CAAC;IACzC,IAAI,CAACc,WAAW,GAAGe,MAAM;EAC7B;AACJ;AACAnB,oBAAoB,CAAC3H,IAAI,YAAAiJ,6BAAA/I,iBAAA;EAAA,YAAAA,iBAAA,IAAyFyH,oBAAoB,EA3f3BzM,EAAE,CAAA0F,iBAAA,CA2f2C+C,gBAAgB;AAAA,CAA4C;AACpNgE,oBAAoB,CAAC7G,IAAI,kBA5fkF5F,EAAE,CAAA6F,iBAAA;EAAAN,IAAA,EA4fPkH,oBAAoB;EAAA3G,SAAA;EAAA0C,MAAA;IAAAwE,aAAA;EAAA;EAAAxB,QAAA,GA5ffxL,EAAE,CAAAgO,oBAAA;AAAA,EA4fsI;AACnP;EAAA,QAAA3I,SAAA,oBAAAA,SAAA,KA7f2GrF,EAAE,CAAAsF,iBAAA,CA6fjBmH,oBAAoB,EAAc,CAAC;IACnHlH,IAAI,EAAEpF,SAAS;IACf4F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAET,IAAI,EAAEkD;IAAiB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEuE,aAAa,EAAE,CAAC;MACpGzH,IAAI,EAAEnF;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAM6N,aAAa,CAAC;AAEpBA,aAAa,CAACnJ,IAAI,YAAAoJ,sBAAAlJ,iBAAA;EAAA,YAAAA,iBAAA,IAAyFiJ,aAAa;AAAA,CAAkD;AAC1KA,aAAa,CAACE,IAAI,kBA5gByFnO,EAAE,CAAAoO,gBAAA;EAAA7I,IAAA,EA4gBD0I;AAAa,EAAsP;AAC/WA,aAAa,CAACI,IAAI,kBA7gByFrO,EAAE,CAAAsO,gBAAA;EAAAC,OAAA,GA6gBwB,CAACrN,aAAa,EAAEF,YAAY,CAAC;AAAA,EAAI;AACtK;EAAA,QAAAqE,SAAA,oBAAAA,SAAA,KA9gB2GrF,EAAE,CAAAsF,iBAAA,CA8gBjB2I,aAAa,EAAc,CAAC;IAC5G1I,IAAI,EAAE5E,QAAQ;IACdoF,IAAI,EAAE,CAAC;MACCwI,OAAO,EAAE,CAACrN,aAAa,EAAEF,YAAY,CAAC;MACtCwN,YAAY,EAAE,CAAC/F,gBAAgB,EAAEjD,qBAAqB,EAAEoC,uBAAuB,EAAE6E,oBAAoB,CAAC;MACtGgC,OAAO,EAAE,CAAChG,gBAAgB,EAAEjD,qBAAqB,EAAEoC,uBAAuB,EAAE6E,oBAAoB;IACpG,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,oBAAoB,EAAE7E,uBAAuB,EAAEpC,qBAAqB,EAAEiD,gBAAgB,EAAEwF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}