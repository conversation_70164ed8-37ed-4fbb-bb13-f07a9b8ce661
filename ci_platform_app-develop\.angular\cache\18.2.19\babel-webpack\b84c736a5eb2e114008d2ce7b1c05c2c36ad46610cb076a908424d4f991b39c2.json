{"ast": null, "code": "import { APP_CONFIG } from \"../configs/environment.config\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"ng-angular-popup\";\nexport class UserTypeGuard {\n  constructor(service, router, toastr) {\n    this.service = service;\n    this.router = router;\n    this.toastr = toastr;\n  }\n  canActivate() {\n    const tokenpayload = this.service.decodedToken();\n    if (tokenpayload.userType === \"admin\") {\n      return true;\n    } else {\n      this.toastr.error({\n        detail: \"ERROR\",\n        summary: \"You are not authorized to access this page\",\n        duration: APP_CONFIG.toastDuration\n      });\n      this.router.navigate([\"/home\"]);\n      return false;\n    }\n  }\n  static {\n    this.ɵfac = function UserTypeGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserTypeGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.NgToastService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserTypeGuard,\n      factory: UserTypeGuard.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["APP_CONFIG", "UserTypeGuard", "constructor", "service", "router", "toastr", "canActivate", "tokenpayload", "decodedToken", "userType", "error", "detail", "summary", "duration", "toastDuration", "navigate", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "i3", "NgToastService", "factory", "ɵfac", "providedIn"], "sources": ["B:\\BE-D2D\\BE\\Be-sem_7\\SIP\\Project\\ci_platform_app-develop\\src\\app\\main\\guards\\user-type.guard.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\"\nimport { Router } from \"@angular/router\"\nimport { NgToastService } from \"ng-angular-popup\"\nimport { AuthService } from \"../services/auth.service\"\nimport { APP_CONFIG } from \"../configs/environment.config\"\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class UserTypeGuard {\n  constructor(\n    private service: AuthService,\n    public router: Router,\n    public toastr: NgToastService,\n  ) {}\n  canActivate(): boolean {\n    const tokenpayload = this.service.decodedToken()\n    if (tokenpayload.userType === \"admin\") {\n      return true\n    } else {\n      this.toastr.error({ detail: \"ERROR\", summary: \"You are not authorized to access this page\", duration: APP_CONFIG.toastDuration })\n      this.router.navigate([\"/home\"])\n      return false\n    }\n  }\n}\n"], "mappings": "AAIA,SAASA,UAAU,QAAQ,+BAA+B;;;;;AAK1D,OAAM,MAAOC,aAAa;EACxBC,YACUC,OAAoB,EACrBC,MAAc,EACdC,MAAsB;IAFrB,KAAAF,OAAO,GAAPA,OAAO;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;EACZ;EACHC,WAAWA,CAAA;IACT,MAAMC,YAAY,GAAG,IAAI,CAACJ,OAAO,CAACK,YAAY,EAAE;IAChD,IAAID,YAAY,CAACE,QAAQ,KAAK,OAAO,EAAE;MACrC,OAAO,IAAI;IACb,CAAC,MAAM;MACL,IAAI,CAACJ,MAAM,CAACK,KAAK,CAAC;QAAEC,MAAM,EAAE,OAAO;QAAEC,OAAO,EAAE,4CAA4C;QAAEC,QAAQ,EAAEb,UAAU,CAACc;MAAa,CAAE,CAAC;MACjI,IAAI,CAACV,MAAM,CAACW,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MAC/B,OAAO,KAAK;IACd;EACF;;;uCAfWd,aAAa,EAAAe,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;aAAbtB,aAAa;MAAAuB,OAAA,EAAbvB,aAAa,CAAAwB,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}