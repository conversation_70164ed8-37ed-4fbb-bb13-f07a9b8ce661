{"ast": null, "code": "import { isDevMode } from '@angular/core';\n\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\nclass Trigger {\n  constructor(open, close) {\n    this.open = open;\n    this.close = close || open;\n  }\n  isManual() {\n    return this.open === 'manual' || this.close === 'manual';\n  }\n}\nconst DEFAULT_ALIASES = {\n  hover: ['mouseover', 'mouseout'],\n  focus: ['focusin', 'focusout']\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction parseTriggers(triggers, aliases = DEFAULT_ALIASES) {\n  const trimmedTriggers = (triggers || '').trim();\n  if (trimmedTriggers.length === 0) {\n    return [];\n  }\n  const parsedTriggers = trimmedTriggers.split(/\\s+/).map(trigger => trigger.split(':')).map(triggerPair => {\n    const alias = aliases[triggerPair[0]] || triggerPair;\n    return new Trigger(alias[0], alias[1]);\n  });\n  const manualTriggers = parsedTriggers.filter(triggerPair => triggerPair.isManual());\n  if (manualTriggers.length > 1) {\n    throw new Error('Triggers parse error: only one manual trigger is allowed');\n  }\n  if (manualTriggers.length === 1 && parsedTriggers.length > 1) {\n    throw new Error('Triggers parse error: manual trigger can\\'t be mixed with other triggers');\n  }\n  return parsedTriggers;\n}\nfunction listenToTriggers(renderer,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntarget, triggers, showFn, hideFn, toggleFn) {\n  const parsedTriggers = parseTriggers(triggers);\n  const listeners = [];\n  if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n    return Function.prototype;\n  }\n  parsedTriggers.forEach(trigger => {\n    if (trigger.open === trigger.close) {\n      listeners.push(renderer.listen(target, trigger.open, toggleFn));\n      return;\n    }\n    listeners.push(renderer.listen(target, trigger.open, showFn));\n    if (trigger.close) {\n      listeners.push(renderer.listen(target, trigger.close, hideFn));\n    }\n  });\n  return () => {\n    listeners.forEach(unsubscribeFn => unsubscribeFn());\n  };\n}\nfunction listenToTriggersV2(renderer, options) {\n  const parsedTriggers = parseTriggers(options.triggers);\n  const target = options.target;\n  // do nothing\n  if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n    return Function.prototype;\n  }\n  // all listeners\n  const listeners = [];\n  // lazy listeners registration\n  const _registerHide = [];\n  const registerHide = () => {\n    // add hide listeners to unregister array\n    _registerHide.forEach(fn => listeners.push(fn()));\n    // register hide events only once\n    _registerHide.length = 0;\n  };\n  // register open\\close\\toggle listeners\n  parsedTriggers.forEach(trigger => {\n    const useToggle = trigger.open === trigger.close;\n    const showFn = useToggle ? options.toggle : options.show;\n    if (!useToggle && trigger.close && options.hide) {\n      const _hide = renderer.listen(target, trigger.close, options.hide);\n      _registerHide.push(() => _hide);\n    }\n    if (showFn) {\n      listeners.push(renderer.listen(target, trigger.open, () => showFn(registerHide)));\n    }\n  });\n  return () => {\n    listeners.forEach(unsubscribeFn => unsubscribeFn());\n  };\n}\nfunction registerOutsideClick(renderer, options) {\n  if (!options.outsideClick) {\n    return Function.prototype;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return renderer.listen('document', 'click', event => {\n    if (options.target && options.target.contains(event.target)) {\n      return;\n    }\n    if (options.targets && options.targets.some(target => target.contains(event.target))) {\n      return;\n    }\n    if (options.hide) {\n      options.hide();\n    }\n  });\n}\nfunction registerEscClick(renderer, options) {\n  if (!options.outsideEsc) {\n    return Function.prototype;\n  }\n  return renderer.listen('document', 'keyup.esc', event => {\n    if (options.target && options.target.contains(event.target)) {\n      return;\n    }\n    if (options.targets && options.targets.some(target => target.contains(event.target))) {\n      return;\n    }\n    if (options.hide) {\n      options.hide();\n    }\n  });\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * JS version of browser APIs. This library can only run in the browser.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst win = typeof window !== 'undefined' && window || {};\nconst document = win.document;\nconst location = win.location;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst gc = win.gc ? () => win.gc() : () => null;\nconst performance = win.performance ? win.performance : null;\nconst Event = win.Event;\nconst MouseEvent = win.MouseEvent;\nconst KeyboardEvent = win.KeyboardEvent;\nconst EventTarget = win.EventTarget;\nconst History = win.History;\nconst Location = win.Location;\nconst EventListener = win.EventListener;\nvar BsVerions;\n(function (BsVerions) {\n  BsVerions[\"isBs3\"] = \"bs3\";\n  BsVerions[\"isBs4\"] = \"bs4\";\n  BsVerions[\"isBs5\"] = \"bs5\";\n})(BsVerions || (BsVerions = {}));\nlet guessedVersion;\nfunction _guessBsVersion() {\n  if (typeof win.document === 'undefined') {\n    return 'bs4';\n  }\n  const spanEl = win.document.createElement('span');\n  spanEl.innerText = 'testing bs version';\n  spanEl.classList.add('d-none');\n  spanEl.classList.add('pl-1');\n  win.document.head.appendChild(spanEl);\n  const rect = spanEl.getBoundingClientRect();\n  const checkPadding = win.getComputedStyle(spanEl).paddingLeft;\n  if (!rect || rect && rect.top !== 0) {\n    win.document.head.removeChild(spanEl);\n    return 'bs3';\n  }\n  if (checkPadding && parseFloat(checkPadding)) {\n    win.document.head.removeChild(spanEl);\n    return 'bs4';\n  }\n  win.document.head.removeChild(spanEl);\n  return 'bs5';\n}\nfunction setTheme(theme) {\n  guessedVersion = theme;\n}\n// todo: in ngx-bootstrap, bs4 will became a default one\nfunction isBs3() {\n  if (typeof win === 'undefined') {\n    return true;\n  }\n  if (typeof win.__theme === 'undefined') {\n    if (guessedVersion) {\n      return guessedVersion === 'bs3';\n    }\n    guessedVersion = _guessBsVersion();\n    return guessedVersion === 'bs3';\n  }\n  return win.__theme === 'bs3';\n}\nfunction isBs4() {\n  if (isBs3()) return false;\n  if (guessedVersion) return guessedVersion === 'bs4';\n  guessedVersion = _guessBsVersion();\n  return guessedVersion === 'bs4';\n}\nfunction isBs5() {\n  if (isBs3() || isBs4()) return false;\n  if (guessedVersion) return guessedVersion === 'bs5';\n  guessedVersion = _guessBsVersion();\n  return guessedVersion === 'bs5';\n}\nfunction getBsVer() {\n  return {\n    isBs3: isBs3(),\n    isBs4: isBs4(),\n    isBs5: isBs5()\n  };\n}\nfunction currentBsVersion() {\n  const bsVer = getBsVer();\n  const resVersion = Object.keys(bsVer).find(key => bsVer[key]);\n  return BsVerions[resVersion];\n}\nclass LinkedList {\n  constructor() {\n    this.length = 0;\n    this.asArray = [];\n    // Array methods overriding END\n  }\n  get(position) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      return void 0;\n    }\n    let current = this.head;\n    for (let index = 0; index < position; index++) {\n      current = current?.next;\n    }\n    return current?.value;\n  }\n  add(value, position = this.length) {\n    if (position < 0 || position > this.length) {\n      throw new Error('Position is out of the list');\n    }\n    const node = {\n      value,\n      next: undefined,\n      previous: undefined\n    };\n    if (this.length === 0) {\n      this.head = node;\n      this.tail = node;\n      this.current = node;\n    } else {\n      if (position === 0 && this.head) {\n        // first node\n        node.next = this.head;\n        this.head.previous = node;\n        this.head = node;\n      } else if (position === this.length && this.tail) {\n        // last node\n        this.tail.next = node;\n        node.previous = this.tail;\n        this.tail = node;\n      } else {\n        // node in middle\n        const currentPreviousNode = this.getNode(position - 1);\n        const currentNextNode = currentPreviousNode?.next;\n        if (currentPreviousNode && currentNextNode) {\n          currentPreviousNode.next = node;\n          currentNextNode.previous = node;\n          node.previous = currentPreviousNode;\n          node.next = currentNextNode;\n        }\n      }\n    }\n    this.length++;\n    this.createInternalArrayRepresentation();\n  }\n  remove(position = 0) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n    if (position === 0 && this.head) {\n      // first node\n      this.head = this.head.next;\n      if (this.head) {\n        // there is no second node\n        this.head.previous = undefined;\n      } else {\n        // there is no second node\n        this.tail = undefined;\n      }\n    } else if (position === this.length - 1 && this.tail?.previous) {\n      // last node\n      this.tail = this.tail.previous;\n      this.tail.next = undefined;\n    } else {\n      // middle node\n      const removedNode = this.getNode(position);\n      if (removedNode?.next && removedNode.previous) {\n        removedNode.next.previous = removedNode.previous;\n        removedNode.previous.next = removedNode.next;\n      }\n    }\n    this.length--;\n    this.createInternalArrayRepresentation();\n  }\n  set(position, value) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n    const node = this.getNode(position);\n    if (node) {\n      node.value = value;\n      this.createInternalArrayRepresentation();\n    }\n  }\n  toArray() {\n    return this.asArray;\n  }\n  findAll(fn) {\n    let current = this.head;\n    const result = [];\n    if (!current) {\n      return result;\n    }\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return result;\n      }\n      if (fn(current.value, index)) {\n        result.push({\n          index,\n          value: current.value\n        });\n      }\n      current = current.next;\n    }\n    return result;\n  }\n  // Array methods overriding start\n  push(...args) {\n    args.forEach(arg => {\n      this.add(arg);\n    });\n    return this.length;\n  }\n  pop() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const last = this.tail;\n    this.remove(this.length - 1);\n    return last?.value;\n  }\n  unshift(...args) {\n    args.reverse();\n    args.forEach(arg => {\n      this.add(arg, 0);\n    });\n    return this.length;\n  }\n  shift() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const lastItem = this.head?.value;\n    this.remove();\n    return lastItem;\n  }\n  forEach(fn) {\n    let current = this.head;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return;\n      }\n      fn(current.value, index);\n      current = current.next;\n    }\n  }\n  indexOf(value) {\n    let current = this.head;\n    let position = -1;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return position;\n      }\n      if (current.value === value) {\n        position = index;\n        break;\n      }\n      current = current.next;\n    }\n    return position;\n  }\n  some(fn) {\n    let current = this.head;\n    let result = false;\n    while (current && !result) {\n      if (fn(current.value)) {\n        result = true;\n        break;\n      }\n      current = current.next;\n    }\n    return result;\n  }\n  every(fn) {\n    let current = this.head;\n    let result = true;\n    while (current && result) {\n      if (!fn(current.value)) {\n        result = false;\n      }\n      current = current.next;\n    }\n    return result;\n  }\n  toString() {\n    return '[Linked List]';\n  }\n  find(fn) {\n    let current = this.head;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return;\n      }\n      if (fn(current.value, index)) {\n        return current.value;\n      }\n      current = current.next;\n    }\n  }\n  findIndex(fn) {\n    let current = this.head;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return -1;\n      }\n      if (fn(current.value, index)) {\n        return index;\n      }\n      current = current.next;\n    }\n    return -1;\n  }\n  getNode(position) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n    let current = this.head;\n    for (let index = 0; index < position; index++) {\n      current = current?.next;\n    }\n    return current;\n  }\n  createInternalArrayRepresentation() {\n    const outArray = [];\n    let current = this.head;\n    while (current) {\n      outArray.push(current.value);\n      current = current.next;\n    }\n    this.asArray = outArray;\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction OnChange() {\n  const sufix = 'Change';\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return function OnChangeHandler(target, propertyKey) {\n    const _key = ` __${propertyKey}Value`;\n    Object.defineProperty(target, propertyKey, {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      get() {\n        return this[_key];\n      },\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      set(value) {\n        const prevValue = this[_key];\n        this[_key] = value;\n        if (prevValue !== value && this[propertyKey + sufix]) {\n          this[propertyKey + sufix].emit(value);\n        }\n      }\n    });\n  };\n}\nclass Utils {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  static reflow(element) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (bs => bs)(element.offsetHeight);\n  }\n  // source: https://github.com/jquery/jquery/blob/master/src/css/var/getStyles.js\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  static getStyles(elem) {\n    // Support: IE <=11 only, Firefox <=30 (#15098, #14150)\n    // IE throws on elements created in popups\n    // FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n    let view = elem.ownerDocument.defaultView;\n    if (!view || !view.opener) {\n      view = win;\n    }\n    return view.getComputedStyle(elem);\n  }\n  static stackOverflowConfig() {\n    const bsVer = currentBsVersion();\n    return {\n      crossorigin: bsVer !== 'bs3' ? \"anonymous\" : undefined,\n      integrity: bsVer === 'bs5' ? 'sha384-KyZXEAg3QhqLMpG8r+8fhAXLRk2vvoC2f3B09zVXn8CA5QIVfZOJ3BCsw2P0p/We' : bsVer === 'bs4' ? 'sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2' : undefined,\n      cdnLink: bsVer === 'bs5' ? 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/css/bootstrap.min.css' : bsVer === 'bs4' ? 'https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css' : 'https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css'\n    };\n  }\n}\nconst _messagesHash = {};\nconst _hideMsg = typeof console === 'undefined' || !('warn' in console);\nfunction warnOnce(msg) {\n  if (!isDevMode() || _hideMsg || msg in _messagesHash) {\n    return;\n  }\n  _messagesHash[msg] = true;\n  console.warn(msg);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsVerions, LinkedList, OnChange, Trigger, Utils, currentBsVersion, document, getBsVer, isBs3, listenToTriggers, listenToTriggersV2, parseTriggers, registerEscClick, registerOutsideClick, setTheme, warnOnce, win as window };", "map": {"version": 3, "names": ["isDevMode", "<PERSON><PERSON>", "constructor", "open", "close", "<PERSON><PERSON><PERSON><PERSON>", "DEFAULT_ALIASES", "hover", "focus", "parseTriggers", "triggers", "aliases", "trimmedTriggers", "trim", "length", "parsedTriggers", "split", "map", "trigger", "triggerPair", "alias", "manualTriggers", "filter", "Error", "listenToTriggers", "renderer", "target", "showFn", "hideFn", "toggleFn", "listeners", "Function", "prototype", "for<PERSON>ach", "push", "listen", "unsubscribeFn", "listenToTriggersV2", "options", "_registerHide", "registerHide", "fn", "useToggle", "toggle", "show", "hide", "_hide", "registerOutsideClick", "outsideClick", "event", "contains", "targets", "some", "registerEscClick", "outsideEsc", "win", "window", "document", "location", "gc", "performance", "Event", "MouseEvent", "KeyboardEvent", "EventTarget", "History", "Location", "EventListener", "BsVerions", "guessedVersion", "_guessBsVersion", "spanEl", "createElement", "innerText", "classList", "add", "head", "append<PERSON><PERSON><PERSON>", "rect", "getBoundingClientRect", "checkPadding", "getComputedStyle", "paddingLeft", "top", "<PERSON><PERSON><PERSON><PERSON>", "parseFloat", "setTheme", "theme", "isBs3", "__theme", "isBs4", "isBs5", "getBsVer", "currentBsVersion", "bsVer", "resVersion", "Object", "keys", "find", "key", "LinkedList", "asArray", "get", "position", "current", "index", "next", "value", "node", "undefined", "previous", "tail", "currentPreviousNode", "getNode", "currentNextNode", "createInternalArrayRepresentation", "remove", "removedNode", "set", "toArray", "findAll", "result", "args", "arg", "pop", "last", "unshift", "reverse", "shift", "lastItem", "indexOf", "every", "toString", "findIndex", "outArray", "OnChange", "sufix", "OnChangeHandler", "propertyKey", "_key", "defineProperty", "prevValue", "emit", "Utils", "reflow", "element", "bs", "offsetHeight", "getStyles", "elem", "view", "ownerDocument", "defaultView", "opener", "stackOverflowConfig", "crossorigin", "integrity", "cdnLink", "_messagesHash", "_hideMsg", "console", "warnOnce", "msg", "warn"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/ngx-bootstrap/utils/fesm2020/ngx-bootstrap-utils.mjs"], "sourcesContent": ["import { isDevMode } from '@angular/core';\n\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\nclass Trigger {\n    constructor(open, close) {\n        this.open = open;\n        this.close = close || open;\n    }\n    isManual() {\n        return this.open === 'manual' || this.close === 'manual';\n    }\n}\n\nconst DEFAULT_ALIASES = {\n    hover: ['mouseover', 'mouseout'],\n    focus: ['focusin', 'focusout']\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction parseTriggers(triggers, aliases = DEFAULT_ALIASES) {\n    const trimmedTriggers = (triggers || '').trim();\n    if (trimmedTriggers.length === 0) {\n        return [];\n    }\n    const parsedTriggers = trimmedTriggers\n        .split(/\\s+/)\n        .map((trigger) => trigger.split(':'))\n        .map((triggerPair) => {\n        const alias = aliases[triggerPair[0]] || triggerPair;\n        return new Trigger(alias[0], alias[1]);\n    });\n    const manualTriggers = parsedTriggers.filter((triggerPair) => triggerPair.isManual());\n    if (manualTriggers.length > 1) {\n        throw new Error('Triggers parse error: only one manual trigger is allowed');\n    }\n    if (manualTriggers.length === 1 && parsedTriggers.length > 1) {\n        throw new Error('Triggers parse error: manual trigger can\\'t be mixed with other triggers');\n    }\n    return parsedTriggers;\n}\nfunction listenToTriggers(renderer, \n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntarget, triggers, showFn, hideFn, toggleFn) {\n    const parsedTriggers = parseTriggers(triggers);\n    const listeners = [];\n    if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n        return Function.prototype;\n    }\n    parsedTriggers.forEach((trigger) => {\n        if (trigger.open === trigger.close) {\n            listeners.push(renderer.listen(target, trigger.open, toggleFn));\n            return;\n        }\n        listeners.push(renderer.listen(target, trigger.open, showFn));\n        if (trigger.close) {\n            listeners.push(renderer.listen(target, trigger.close, hideFn));\n        }\n    });\n    return () => {\n        listeners.forEach((unsubscribeFn) => unsubscribeFn());\n    };\n}\nfunction listenToTriggersV2(renderer, options) {\n    const parsedTriggers = parseTriggers(options.triggers);\n    const target = options.target;\n    // do nothing\n    if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n        return Function.prototype;\n    }\n    // all listeners\n    const listeners = [];\n    // lazy listeners registration\n    const _registerHide = [];\n    const registerHide = () => {\n        // add hide listeners to unregister array\n        _registerHide.forEach((fn) => listeners.push(fn()));\n        // register hide events only once\n        _registerHide.length = 0;\n    };\n    // register open\\close\\toggle listeners\n    parsedTriggers.forEach((trigger) => {\n        const useToggle = trigger.open === trigger.close;\n        const showFn = useToggle ? options.toggle : options.show;\n        if (!useToggle && trigger.close && options.hide) {\n            const _hide = renderer.listen(target, trigger.close, options.hide);\n            _registerHide.push(() => _hide);\n        }\n        if (showFn) {\n            listeners.push(renderer.listen(target, trigger.open, () => showFn(registerHide)));\n        }\n    });\n    return () => {\n        listeners.forEach((unsubscribeFn) => unsubscribeFn());\n    };\n}\nfunction registerOutsideClick(renderer, options) {\n    if (!options.outsideClick) {\n        return Function.prototype;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return renderer.listen('document', 'click', (event) => {\n        if (options.target && options.target.contains(event.target)) {\n            return;\n        }\n        if (options.targets &&\n            options.targets.some(target => target.contains(event.target))) {\n            return;\n        }\n        if (options.hide) {\n            options.hide();\n        }\n    });\n}\nfunction registerEscClick(renderer, options) {\n    if (!options.outsideEsc) {\n        return Function.prototype;\n    }\n    return renderer.listen('document', 'keyup.esc', (event) => {\n        if (options.target && options.target.contains(event.target)) {\n            return;\n        }\n        if (options.targets &&\n            options.targets.some(target => target.contains(event.target))) {\n            return;\n        }\n        if (options.hide) {\n            options.hide();\n        }\n    });\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * JS version of browser APIs. This library can only run in the browser.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst win = (typeof window !== 'undefined' && window) || {};\nconst document = win.document;\nconst location = win.location;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst gc = win.gc ? () => win.gc() : () => null;\nconst performance = win.performance ? win.performance : null;\nconst Event = win.Event;\nconst MouseEvent = win.MouseEvent;\nconst KeyboardEvent = win.KeyboardEvent;\nconst EventTarget = win.EventTarget;\nconst History = win.History;\nconst Location = win.Location;\nconst EventListener = win.EventListener;\n\nvar BsVerions;\n(function (BsVerions) {\n    BsVerions[\"isBs3\"] = \"bs3\";\n    BsVerions[\"isBs4\"] = \"bs4\";\n    BsVerions[\"isBs5\"] = \"bs5\";\n})(BsVerions || (BsVerions = {}));\nlet guessedVersion;\nfunction _guessBsVersion() {\n    if (typeof win.document === 'undefined') {\n        return 'bs4';\n    }\n    const spanEl = win.document.createElement('span');\n    spanEl.innerText = 'testing bs version';\n    spanEl.classList.add('d-none');\n    spanEl.classList.add('pl-1');\n    win.document.head.appendChild(spanEl);\n    const rect = spanEl.getBoundingClientRect();\n    const checkPadding = win.getComputedStyle(spanEl).paddingLeft;\n    if (!rect || (rect && rect.top !== 0)) {\n        win.document.head.removeChild(spanEl);\n        return 'bs3';\n    }\n    if (checkPadding && parseFloat(checkPadding)) {\n        win.document.head.removeChild(spanEl);\n        return 'bs4';\n    }\n    win.document.head.removeChild(spanEl);\n    return 'bs5';\n}\nfunction setTheme(theme) {\n    guessedVersion = theme;\n}\n// todo: in ngx-bootstrap, bs4 will became a default one\nfunction isBs3() {\n    if (typeof win === 'undefined') {\n        return true;\n    }\n    if (typeof win.__theme === 'undefined') {\n        if (guessedVersion) {\n            return guessedVersion === 'bs3';\n        }\n        guessedVersion = _guessBsVersion();\n        return guessedVersion === 'bs3';\n    }\n    return win.__theme === 'bs3';\n}\nfunction isBs4() {\n    if (isBs3())\n        return false;\n    if (guessedVersion)\n        return guessedVersion === 'bs4';\n    guessedVersion = _guessBsVersion();\n    return guessedVersion === 'bs4';\n}\nfunction isBs5() {\n    if (isBs3() || isBs4())\n        return false;\n    if (guessedVersion)\n        return guessedVersion === 'bs5';\n    guessedVersion = _guessBsVersion();\n    return guessedVersion === 'bs5';\n}\nfunction getBsVer() {\n    return {\n        isBs3: isBs3(),\n        isBs4: isBs4(),\n        isBs5: isBs5()\n    };\n}\nfunction currentBsVersion() {\n    const bsVer = getBsVer();\n    const resVersion = Object.keys(bsVer).find(key => bsVer[key]);\n    return BsVerions[resVersion];\n}\n\nclass LinkedList {\n    constructor() {\n        this.length = 0;\n        this.asArray = [];\n        // Array methods overriding END\n    }\n    get(position) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            return void 0;\n        }\n        let current = this.head;\n        for (let index = 0; index < position; index++) {\n            current = current?.next;\n        }\n        return current?.value;\n    }\n    add(value, position = this.length) {\n        if (position < 0 || position > this.length) {\n            throw new Error('Position is out of the list');\n        }\n        const node = {\n            value,\n            next: undefined,\n            previous: undefined\n        };\n        if (this.length === 0) {\n            this.head = node;\n            this.tail = node;\n            this.current = node;\n        }\n        else {\n            if (position === 0 && this.head) {\n                // first node\n                node.next = this.head;\n                this.head.previous = node;\n                this.head = node;\n            }\n            else if (position === this.length && this.tail) {\n                // last node\n                this.tail.next = node;\n                node.previous = this.tail;\n                this.tail = node;\n            }\n            else {\n                // node in middle\n                const currentPreviousNode = this.getNode(position - 1);\n                const currentNextNode = currentPreviousNode?.next;\n                if (currentPreviousNode && currentNextNode) {\n                    currentPreviousNode.next = node;\n                    currentNextNode.previous = node;\n                    node.previous = currentPreviousNode;\n                    node.next = currentNextNode;\n                }\n            }\n        }\n        this.length++;\n        this.createInternalArrayRepresentation();\n    }\n    remove(position = 0) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            throw new Error('Position is out of the list');\n        }\n        if (position === 0 && this.head) {\n            // first node\n            this.head = this.head.next;\n            if (this.head) {\n                // there is no second node\n                this.head.previous = undefined;\n            }\n            else {\n                // there is no second node\n                this.tail = undefined;\n            }\n        }\n        else if (position === this.length - 1 && this.tail?.previous) {\n            // last node\n            this.tail = this.tail.previous;\n            this.tail.next = undefined;\n        }\n        else {\n            // middle node\n            const removedNode = this.getNode(position);\n            if (removedNode?.next && removedNode.previous) {\n                removedNode.next.previous = removedNode.previous;\n                removedNode.previous.next = removedNode.next;\n            }\n        }\n        this.length--;\n        this.createInternalArrayRepresentation();\n    }\n    set(position, value) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            throw new Error('Position is out of the list');\n        }\n        const node = this.getNode(position);\n        if (node) {\n            node.value = value;\n            this.createInternalArrayRepresentation();\n        }\n    }\n    toArray() {\n        return this.asArray;\n    }\n    findAll(fn) {\n        let current = this.head;\n        const result = [];\n        if (!current) {\n            return result;\n        }\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return result;\n            }\n            if (fn(current.value, index)) {\n                result.push({ index, value: current.value });\n            }\n            current = current.next;\n        }\n        return result;\n    }\n    // Array methods overriding start\n    push(...args) {\n        args.forEach((arg) => {\n            this.add(arg);\n        });\n        return this.length;\n    }\n    pop() {\n        if (this.length === 0) {\n            return undefined;\n        }\n        const last = this.tail;\n        this.remove(this.length - 1);\n        return last?.value;\n    }\n    unshift(...args) {\n        args.reverse();\n        args.forEach((arg) => {\n            this.add(arg, 0);\n        });\n        return this.length;\n    }\n    shift() {\n        if (this.length === 0) {\n            return undefined;\n        }\n        const lastItem = this.head?.value;\n        this.remove();\n        return lastItem;\n    }\n    forEach(fn) {\n        let current = this.head;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return;\n            }\n            fn(current.value, index);\n            current = current.next;\n        }\n    }\n    indexOf(value) {\n        let current = this.head;\n        let position = -1;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return position;\n            }\n            if (current.value === value) {\n                position = index;\n                break;\n            }\n            current = current.next;\n        }\n        return position;\n    }\n    some(fn) {\n        let current = this.head;\n        let result = false;\n        while (current && !result) {\n            if (fn(current.value)) {\n                result = true;\n                break;\n            }\n            current = current.next;\n        }\n        return result;\n    }\n    every(fn) {\n        let current = this.head;\n        let result = true;\n        while (current && result) {\n            if (!fn(current.value)) {\n                result = false;\n            }\n            current = current.next;\n        }\n        return result;\n    }\n    toString() {\n        return '[Linked List]';\n    }\n    find(fn) {\n        let current = this.head;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return;\n            }\n            if (fn(current.value, index)) {\n                return current.value;\n            }\n            current = current.next;\n        }\n    }\n    findIndex(fn) {\n        let current = this.head;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return -1;\n            }\n            if (fn(current.value, index)) {\n                return index;\n            }\n            current = current.next;\n        }\n        return -1;\n    }\n    getNode(position) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            throw new Error('Position is out of the list');\n        }\n        let current = this.head;\n        for (let index = 0; index < position; index++) {\n            current = current?.next;\n        }\n        return current;\n    }\n    createInternalArrayRepresentation() {\n        const outArray = [];\n        let current = this.head;\n        while (current) {\n            outArray.push(current.value);\n            current = current.next;\n        }\n        this.asArray = outArray;\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction OnChange() {\n    const sufix = 'Change';\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return function OnChangeHandler(target, propertyKey) {\n        const _key = ` __${propertyKey}Value`;\n        Object.defineProperty(target, propertyKey, {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            get() {\n                return this[_key];\n            },\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            set(value) {\n                const prevValue = this[_key];\n                this[_key] = value;\n                if (prevValue !== value && this[propertyKey + sufix]) {\n                    this[propertyKey + sufix].emit(value);\n                }\n            }\n        });\n    };\n}\n\nclass Utils {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    static reflow(element) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ((bs) => bs)(element.offsetHeight);\n    }\n    // source: https://github.com/jquery/jquery/blob/master/src/css/var/getStyles.js\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    static getStyles(elem) {\n        // Support: IE <=11 only, Firefox <=30 (#15098, #14150)\n        // IE throws on elements created in popups\n        // FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n        let view = elem.ownerDocument.defaultView;\n        if (!view || !view.opener) {\n            view = win;\n        }\n        return view.getComputedStyle(elem);\n    }\n    static stackOverflowConfig() {\n        const bsVer = currentBsVersion();\n        return {\n            crossorigin: bsVer !== 'bs3' ? \"anonymous\" : undefined,\n            integrity: bsVer === 'bs5' ? 'sha384-KyZXEAg3QhqLMpG8r+8fhAXLRk2vvoC2f3B09zVXn8CA5QIVfZOJ3BCsw2P0p/We' : bsVer === 'bs4' ? 'sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2' : undefined,\n            cdnLink: bsVer === 'bs5' ? 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/css/bootstrap.min.css' : bsVer === 'bs4' ? 'https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css' : 'https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css',\n        };\n    }\n}\n\nconst _messagesHash = {};\nconst _hideMsg = typeof console === 'undefined' || !('warn' in console);\nfunction warnOnce(msg) {\n    if (!isDevMode() || _hideMsg || msg in _messagesHash) {\n        return;\n    }\n    _messagesHash[msg] = true;\n    console.warn(msg);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsVerions, LinkedList, OnChange, Trigger, Utils, currentBsVersion, document, getBsVer, isBs3, listenToTriggers, listenToTriggersV2, parseTriggers, registerEscClick, registerOutsideClick, setTheme, warnOnce, win as window };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAe;;AAEzC;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVC,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACrB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,GAAGA,KAAK,IAAID,IAAI;EAC9B;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACF,IAAI,KAAK,QAAQ,IAAI,IAAI,CAACC,KAAK,KAAK,QAAQ;EAC5D;AACJ;AAEA,MAAME,eAAe,GAAG;EACpBC,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC;EAChCC,KAAK,EAAE,CAAC,SAAS,EAAE,UAAU;AACjC,CAAC;AACD;AACA,SAASC,aAAaA,CAACC,QAAQ,EAAEC,OAAO,GAAGL,eAAe,EAAE;EACxD,MAAMM,eAAe,GAAG,CAACF,QAAQ,IAAI,EAAE,EAAEG,IAAI,CAAC,CAAC;EAC/C,IAAID,eAAe,CAACE,MAAM,KAAK,CAAC,EAAE;IAC9B,OAAO,EAAE;EACb;EACA,MAAMC,cAAc,GAAGH,eAAe,CACjCI,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAAEC,OAAO,IAAKA,OAAO,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CACpCC,GAAG,CAAEE,WAAW,IAAK;IACtB,MAAMC,KAAK,GAAGT,OAAO,CAACQ,WAAW,CAAC,CAAC,CAAC,CAAC,IAAIA,WAAW;IACpD,OAAO,IAAIlB,OAAO,CAACmB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1C,CAAC,CAAC;EACF,MAAMC,cAAc,GAAGN,cAAc,CAACO,MAAM,CAAEH,WAAW,IAAKA,WAAW,CAACd,QAAQ,CAAC,CAAC,CAAC;EACrF,IAAIgB,cAAc,CAACP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIS,KAAK,CAAC,0DAA0D,CAAC;EAC/E;EACA,IAAIF,cAAc,CAACP,MAAM,KAAK,CAAC,IAAIC,cAAc,CAACD,MAAM,GAAG,CAAC,EAAE;IAC1D,MAAM,IAAIS,KAAK,CAAC,0EAA0E,CAAC;EAC/F;EACA,OAAOR,cAAc;AACzB;AACA,SAASS,gBAAgBA,CAACC,QAAQ;AAClC;AACAC,MAAM,EAAEhB,QAAQ,EAAEiB,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EACxC,MAAMd,cAAc,GAAGN,aAAa,CAACC,QAAQ,CAAC;EAC9C,MAAMoB,SAAS,GAAG,EAAE;EACpB,IAAIf,cAAc,CAACD,MAAM,KAAK,CAAC,IAAIC,cAAc,CAAC,CAAC,CAAC,CAACV,QAAQ,CAAC,CAAC,EAAE;IAC7D,OAAO0B,QAAQ,CAACC,SAAS;EAC7B;EACAjB,cAAc,CAACkB,OAAO,CAAEf,OAAO,IAAK;IAChC,IAAIA,OAAO,CAACf,IAAI,KAAKe,OAAO,CAACd,KAAK,EAAE;MAChC0B,SAAS,CAACI,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAER,OAAO,CAACf,IAAI,EAAE0B,QAAQ,CAAC,CAAC;MAC/D;IACJ;IACAC,SAAS,CAACI,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAER,OAAO,CAACf,IAAI,EAAEwB,MAAM,CAAC,CAAC;IAC7D,IAAIT,OAAO,CAACd,KAAK,EAAE;MACf0B,SAAS,CAACI,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAER,OAAO,CAACd,KAAK,EAAEwB,MAAM,CAAC,CAAC;IAClE;EACJ,CAAC,CAAC;EACF,OAAO,MAAM;IACTE,SAAS,CAACG,OAAO,CAAEG,aAAa,IAAKA,aAAa,CAAC,CAAC,CAAC;EACzD,CAAC;AACL;AACA,SAASC,kBAAkBA,CAACZ,QAAQ,EAAEa,OAAO,EAAE;EAC3C,MAAMvB,cAAc,GAAGN,aAAa,CAAC6B,OAAO,CAAC5B,QAAQ,CAAC;EACtD,MAAMgB,MAAM,GAAGY,OAAO,CAACZ,MAAM;EAC7B;EACA,IAAIX,cAAc,CAACD,MAAM,KAAK,CAAC,IAAIC,cAAc,CAAC,CAAC,CAAC,CAACV,QAAQ,CAAC,CAAC,EAAE;IAC7D,OAAO0B,QAAQ,CAACC,SAAS;EAC7B;EACA;EACA,MAAMF,SAAS,GAAG,EAAE;EACpB;EACA,MAAMS,aAAa,GAAG,EAAE;EACxB,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvB;IACAD,aAAa,CAACN,OAAO,CAAEQ,EAAE,IAAKX,SAAS,CAACI,IAAI,CAACO,EAAE,CAAC,CAAC,CAAC,CAAC;IACnD;IACAF,aAAa,CAACzB,MAAM,GAAG,CAAC;EAC5B,CAAC;EACD;EACAC,cAAc,CAACkB,OAAO,CAAEf,OAAO,IAAK;IAChC,MAAMwB,SAAS,GAAGxB,OAAO,CAACf,IAAI,KAAKe,OAAO,CAACd,KAAK;IAChD,MAAMuB,MAAM,GAAGe,SAAS,GAAGJ,OAAO,CAACK,MAAM,GAAGL,OAAO,CAACM,IAAI;IACxD,IAAI,CAACF,SAAS,IAAIxB,OAAO,CAACd,KAAK,IAAIkC,OAAO,CAACO,IAAI,EAAE;MAC7C,MAAMC,KAAK,GAAGrB,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAER,OAAO,CAACd,KAAK,EAAEkC,OAAO,CAACO,IAAI,CAAC;MAClEN,aAAa,CAACL,IAAI,CAAC,MAAMY,KAAK,CAAC;IACnC;IACA,IAAInB,MAAM,EAAE;MACRG,SAAS,CAACI,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAER,OAAO,CAACf,IAAI,EAAE,MAAMwB,MAAM,CAACa,YAAY,CAAC,CAAC,CAAC;IACrF;EACJ,CAAC,CAAC;EACF,OAAO,MAAM;IACTV,SAAS,CAACG,OAAO,CAAEG,aAAa,IAAKA,aAAa,CAAC,CAAC,CAAC;EACzD,CAAC;AACL;AACA,SAASW,oBAAoBA,CAACtB,QAAQ,EAAEa,OAAO,EAAE;EAC7C,IAAI,CAACA,OAAO,CAACU,YAAY,EAAE;IACvB,OAAOjB,QAAQ,CAACC,SAAS;EAC7B;EACA;EACA,OAAOP,QAAQ,CAACU,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGc,KAAK,IAAK;IACnD,IAAIX,OAAO,CAACZ,MAAM,IAAIY,OAAO,CAACZ,MAAM,CAACwB,QAAQ,CAACD,KAAK,CAACvB,MAAM,CAAC,EAAE;MACzD;IACJ;IACA,IAAIY,OAAO,CAACa,OAAO,IACfb,OAAO,CAACa,OAAO,CAACC,IAAI,CAAC1B,MAAM,IAAIA,MAAM,CAACwB,QAAQ,CAACD,KAAK,CAACvB,MAAM,CAAC,CAAC,EAAE;MAC/D;IACJ;IACA,IAAIY,OAAO,CAACO,IAAI,EAAE;MACdP,OAAO,CAACO,IAAI,CAAC,CAAC;IAClB;EACJ,CAAC,CAAC;AACN;AACA,SAASQ,gBAAgBA,CAAC5B,QAAQ,EAAEa,OAAO,EAAE;EACzC,IAAI,CAACA,OAAO,CAACgB,UAAU,EAAE;IACrB,OAAOvB,QAAQ,CAACC,SAAS;EAC7B;EACA,OAAOP,QAAQ,CAACU,MAAM,CAAC,UAAU,EAAE,WAAW,EAAGc,KAAK,IAAK;IACvD,IAAIX,OAAO,CAACZ,MAAM,IAAIY,OAAO,CAACZ,MAAM,CAACwB,QAAQ,CAACD,KAAK,CAACvB,MAAM,CAAC,EAAE;MACzD;IACJ;IACA,IAAIY,OAAO,CAACa,OAAO,IACfb,OAAO,CAACa,OAAO,CAACC,IAAI,CAAC1B,MAAM,IAAIA,MAAM,CAACwB,QAAQ,CAACD,KAAK,CAACvB,MAAM,CAAC,CAAC,EAAE;MAC/D;IACJ;IACA,IAAIY,OAAO,CAACO,IAAI,EAAE;MACdP,OAAO,CAACO,IAAI,CAAC,CAAC;IAClB;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,GAAG,GAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAK,CAAC,CAAC;AAC3D,MAAMC,QAAQ,GAAGF,GAAG,CAACE,QAAQ;AAC7B,MAAMC,QAAQ,GAAGH,GAAG,CAACG,QAAQ;AAC7B;AACA,MAAMC,EAAE,GAAGJ,GAAG,CAACI,EAAE,GAAG,MAAMJ,GAAG,CAACI,EAAE,CAAC,CAAC,GAAG,MAAM,IAAI;AAC/C,MAAMC,WAAW,GAAGL,GAAG,CAACK,WAAW,GAAGL,GAAG,CAACK,WAAW,GAAG,IAAI;AAC5D,MAAMC,KAAK,GAAGN,GAAG,CAACM,KAAK;AACvB,MAAMC,UAAU,GAAGP,GAAG,CAACO,UAAU;AACjC,MAAMC,aAAa,GAAGR,GAAG,CAACQ,aAAa;AACvC,MAAMC,WAAW,GAAGT,GAAG,CAACS,WAAW;AACnC,MAAMC,OAAO,GAAGV,GAAG,CAACU,OAAO;AAC3B,MAAMC,QAAQ,GAAGX,GAAG,CAACW,QAAQ;AAC7B,MAAMC,aAAa,GAAGZ,GAAG,CAACY,aAAa;AAEvC,IAAIC,SAAS;AACb,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,OAAO,CAAC,GAAG,KAAK;EAC1BA,SAAS,CAAC,OAAO,CAAC,GAAG,KAAK;EAC1BA,SAAS,CAAC,OAAO,CAAC,GAAG,KAAK;AAC9B,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,IAAIC,cAAc;AAClB,SAASC,eAAeA,CAAA,EAAG;EACvB,IAAI,OAAOf,GAAG,CAACE,QAAQ,KAAK,WAAW,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,MAAMc,MAAM,GAAGhB,GAAG,CAACE,QAAQ,CAACe,aAAa,CAAC,MAAM,CAAC;EACjDD,MAAM,CAACE,SAAS,GAAG,oBAAoB;EACvCF,MAAM,CAACG,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;EAC9BJ,MAAM,CAACG,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;EAC5BpB,GAAG,CAACE,QAAQ,CAACmB,IAAI,CAACC,WAAW,CAACN,MAAM,CAAC;EACrC,MAAMO,IAAI,GAAGP,MAAM,CAACQ,qBAAqB,CAAC,CAAC;EAC3C,MAAMC,YAAY,GAAGzB,GAAG,CAAC0B,gBAAgB,CAACV,MAAM,CAAC,CAACW,WAAW;EAC7D,IAAI,CAACJ,IAAI,IAAKA,IAAI,IAAIA,IAAI,CAACK,GAAG,KAAK,CAAE,EAAE;IACnC5B,GAAG,CAACE,QAAQ,CAACmB,IAAI,CAACQ,WAAW,CAACb,MAAM,CAAC;IACrC,OAAO,KAAK;EAChB;EACA,IAAIS,YAAY,IAAIK,UAAU,CAACL,YAAY,CAAC,EAAE;IAC1CzB,GAAG,CAACE,QAAQ,CAACmB,IAAI,CAACQ,WAAW,CAACb,MAAM,CAAC;IACrC,OAAO,KAAK;EAChB;EACAhB,GAAG,CAACE,QAAQ,CAACmB,IAAI,CAACQ,WAAW,CAACb,MAAM,CAAC;EACrC,OAAO,KAAK;AAChB;AACA,SAASe,QAAQA,CAACC,KAAK,EAAE;EACrBlB,cAAc,GAAGkB,KAAK;AAC1B;AACA;AACA,SAASC,KAAKA,CAAA,EAAG;EACb,IAAI,OAAOjC,GAAG,KAAK,WAAW,EAAE;IAC5B,OAAO,IAAI;EACf;EACA,IAAI,OAAOA,GAAG,CAACkC,OAAO,KAAK,WAAW,EAAE;IACpC,IAAIpB,cAAc,EAAE;MAChB,OAAOA,cAAc,KAAK,KAAK;IACnC;IACAA,cAAc,GAAGC,eAAe,CAAC,CAAC;IAClC,OAAOD,cAAc,KAAK,KAAK;EACnC;EACA,OAAOd,GAAG,CAACkC,OAAO,KAAK,KAAK;AAChC;AACA,SAASC,KAAKA,CAAA,EAAG;EACb,IAAIF,KAAK,CAAC,CAAC,EACP,OAAO,KAAK;EAChB,IAAInB,cAAc,EACd,OAAOA,cAAc,KAAK,KAAK;EACnCA,cAAc,GAAGC,eAAe,CAAC,CAAC;EAClC,OAAOD,cAAc,KAAK,KAAK;AACnC;AACA,SAASsB,KAAKA,CAAA,EAAG;EACb,IAAIH,KAAK,CAAC,CAAC,IAAIE,KAAK,CAAC,CAAC,EAClB,OAAO,KAAK;EAChB,IAAIrB,cAAc,EACd,OAAOA,cAAc,KAAK,KAAK;EACnCA,cAAc,GAAGC,eAAe,CAAC,CAAC;EAClC,OAAOD,cAAc,KAAK,KAAK;AACnC;AACA,SAASuB,QAAQA,CAAA,EAAG;EAChB,OAAO;IACHJ,KAAK,EAAEA,KAAK,CAAC,CAAC;IACdE,KAAK,EAAEA,KAAK,CAAC,CAAC;IACdC,KAAK,EAAEA,KAAK,CAAC;EACjB,CAAC;AACL;AACA,SAASE,gBAAgBA,CAAA,EAAG;EACxB,MAAMC,KAAK,GAAGF,QAAQ,CAAC,CAAC;EACxB,MAAMG,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,IAAI,CAACC,GAAG,IAAIL,KAAK,CAACK,GAAG,CAAC,CAAC;EAC7D,OAAO/B,SAAS,CAAC2B,UAAU,CAAC;AAChC;AAEA,MAAMK,UAAU,CAAC;EACblG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACY,MAAM,GAAG,CAAC;IACf,IAAI,CAACuF,OAAO,GAAG,EAAE;IACjB;EACJ;EACAC,GAAGA,CAACC,QAAQ,EAAE;IACV,IAAI,IAAI,CAACzF,MAAM,KAAK,CAAC,IAAIyF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAI,IAAI,CAACzF,MAAM,EAAE;MAC9D,OAAO,KAAK,CAAC;IACjB;IACA,IAAI0F,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,KAAK,IAAI6B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,QAAQ,EAAEE,KAAK,EAAE,EAAE;MAC3CD,OAAO,GAAGA,OAAO,EAAEE,IAAI;IAC3B;IACA,OAAOF,OAAO,EAAEG,KAAK;EACzB;EACAhC,GAAGA,CAACgC,KAAK,EAAEJ,QAAQ,GAAG,IAAI,CAACzF,MAAM,EAAE;IAC/B,IAAIyF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,IAAI,CAACzF,MAAM,EAAE;MACxC,MAAM,IAAIS,KAAK,CAAC,6BAA6B,CAAC;IAClD;IACA,MAAMqF,IAAI,GAAG;MACTD,KAAK;MACLD,IAAI,EAAEG,SAAS;MACfC,QAAQ,EAAED;IACd,CAAC;IACD,IAAI,IAAI,CAAC/F,MAAM,KAAK,CAAC,EAAE;MACnB,IAAI,CAAC8D,IAAI,GAAGgC,IAAI;MAChB,IAAI,CAACG,IAAI,GAAGH,IAAI;MAChB,IAAI,CAACJ,OAAO,GAAGI,IAAI;IACvB,CAAC,MACI;MACD,IAAIL,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC3B,IAAI,EAAE;QAC7B;QACAgC,IAAI,CAACF,IAAI,GAAG,IAAI,CAAC9B,IAAI;QACrB,IAAI,CAACA,IAAI,CAACkC,QAAQ,GAAGF,IAAI;QACzB,IAAI,CAAChC,IAAI,GAAGgC,IAAI;MACpB,CAAC,MACI,IAAIL,QAAQ,KAAK,IAAI,CAACzF,MAAM,IAAI,IAAI,CAACiG,IAAI,EAAE;QAC5C;QACA,IAAI,CAACA,IAAI,CAACL,IAAI,GAAGE,IAAI;QACrBA,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACC,IAAI;QACzB,IAAI,CAACA,IAAI,GAAGH,IAAI;MACpB,CAAC,MACI;QACD;QACA,MAAMI,mBAAmB,GAAG,IAAI,CAACC,OAAO,CAACV,QAAQ,GAAG,CAAC,CAAC;QACtD,MAAMW,eAAe,GAAGF,mBAAmB,EAAEN,IAAI;QACjD,IAAIM,mBAAmB,IAAIE,eAAe,EAAE;UACxCF,mBAAmB,CAACN,IAAI,GAAGE,IAAI;UAC/BM,eAAe,CAACJ,QAAQ,GAAGF,IAAI;UAC/BA,IAAI,CAACE,QAAQ,GAAGE,mBAAmB;UACnCJ,IAAI,CAACF,IAAI,GAAGQ,eAAe;QAC/B;MACJ;IACJ;IACA,IAAI,CAACpG,MAAM,EAAE;IACb,IAAI,CAACqG,iCAAiC,CAAC,CAAC;EAC5C;EACAC,MAAMA,CAACb,QAAQ,GAAG,CAAC,EAAE;IACjB,IAAI,IAAI,CAACzF,MAAM,KAAK,CAAC,IAAIyF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAI,IAAI,CAACzF,MAAM,EAAE;MAC9D,MAAM,IAAIS,KAAK,CAAC,6BAA6B,CAAC;IAClD;IACA,IAAIgF,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC3B,IAAI,EAAE;MAC7B;MACA,IAAI,CAACA,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC8B,IAAI;MAC1B,IAAI,IAAI,CAAC9B,IAAI,EAAE;QACX;QACA,IAAI,CAACA,IAAI,CAACkC,QAAQ,GAAGD,SAAS;MAClC,CAAC,MACI;QACD;QACA,IAAI,CAACE,IAAI,GAAGF,SAAS;MACzB;IACJ,CAAC,MACI,IAAIN,QAAQ,KAAK,IAAI,CAACzF,MAAM,GAAG,CAAC,IAAI,IAAI,CAACiG,IAAI,EAAED,QAAQ,EAAE;MAC1D;MACA,IAAI,CAACC,IAAI,GAAG,IAAI,CAACA,IAAI,CAACD,QAAQ;MAC9B,IAAI,CAACC,IAAI,CAACL,IAAI,GAAGG,SAAS;IAC9B,CAAC,MACI;MACD;MACA,MAAMQ,WAAW,GAAG,IAAI,CAACJ,OAAO,CAACV,QAAQ,CAAC;MAC1C,IAAIc,WAAW,EAAEX,IAAI,IAAIW,WAAW,CAACP,QAAQ,EAAE;QAC3CO,WAAW,CAACX,IAAI,CAACI,QAAQ,GAAGO,WAAW,CAACP,QAAQ;QAChDO,WAAW,CAACP,QAAQ,CAACJ,IAAI,GAAGW,WAAW,CAACX,IAAI;MAChD;IACJ;IACA,IAAI,CAAC5F,MAAM,EAAE;IACb,IAAI,CAACqG,iCAAiC,CAAC,CAAC;EAC5C;EACAG,GAAGA,CAACf,QAAQ,EAAEI,KAAK,EAAE;IACjB,IAAI,IAAI,CAAC7F,MAAM,KAAK,CAAC,IAAIyF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAI,IAAI,CAACzF,MAAM,EAAE;MAC9D,MAAM,IAAIS,KAAK,CAAC,6BAA6B,CAAC;IAClD;IACA,MAAMqF,IAAI,GAAG,IAAI,CAACK,OAAO,CAACV,QAAQ,CAAC;IACnC,IAAIK,IAAI,EAAE;MACNA,IAAI,CAACD,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACQ,iCAAiC,CAAC,CAAC;IAC5C;EACJ;EACAI,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClB,OAAO;EACvB;EACAmB,OAAOA,CAAC/E,EAAE,EAAE;IACR,IAAI+D,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,MAAM6C,MAAM,GAAG,EAAE;IACjB,IAAI,CAACjB,OAAO,EAAE;MACV,OAAOiB,MAAM;IACjB;IACA,KAAK,IAAIhB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAAC3F,MAAM,EAAE2F,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV,OAAOiB,MAAM;MACjB;MACA,IAAIhF,EAAE,CAAC+D,OAAO,CAACG,KAAK,EAAEF,KAAK,CAAC,EAAE;QAC1BgB,MAAM,CAACvF,IAAI,CAAC;UAAEuE,KAAK;UAAEE,KAAK,EAAEH,OAAO,CAACG;QAAM,CAAC,CAAC;MAChD;MACAH,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAOe,MAAM;EACjB;EACA;EACAvF,IAAIA,CAAC,GAAGwF,IAAI,EAAE;IACVA,IAAI,CAACzF,OAAO,CAAE0F,GAAG,IAAK;MAClB,IAAI,CAAChD,GAAG,CAACgD,GAAG,CAAC;IACjB,CAAC,CAAC;IACF,OAAO,IAAI,CAAC7G,MAAM;EACtB;EACA8G,GAAGA,CAAA,EAAG;IACF,IAAI,IAAI,CAAC9G,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO+F,SAAS;IACpB;IACA,MAAMgB,IAAI,GAAG,IAAI,CAACd,IAAI;IACtB,IAAI,CAACK,MAAM,CAAC,IAAI,CAACtG,MAAM,GAAG,CAAC,CAAC;IAC5B,OAAO+G,IAAI,EAAElB,KAAK;EACtB;EACAmB,OAAOA,CAAC,GAAGJ,IAAI,EAAE;IACbA,IAAI,CAACK,OAAO,CAAC,CAAC;IACdL,IAAI,CAACzF,OAAO,CAAE0F,GAAG,IAAK;MAClB,IAAI,CAAChD,GAAG,CAACgD,GAAG,EAAE,CAAC,CAAC;IACpB,CAAC,CAAC;IACF,OAAO,IAAI,CAAC7G,MAAM;EACtB;EACAkH,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAClH,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO+F,SAAS;IACpB;IACA,MAAMoB,QAAQ,GAAG,IAAI,CAACrD,IAAI,EAAE+B,KAAK;IACjC,IAAI,CAACS,MAAM,CAAC,CAAC;IACb,OAAOa,QAAQ;EACnB;EACAhG,OAAOA,CAACQ,EAAE,EAAE;IACR,IAAI+D,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,KAAK,IAAI6B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAAC3F,MAAM,EAAE2F,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA/D,EAAE,CAAC+D,OAAO,CAACG,KAAK,EAAEF,KAAK,CAAC;MACxBD,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;EACJ;EACAwB,OAAOA,CAACvB,KAAK,EAAE;IACX,IAAIH,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,IAAI2B,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAIE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAAC3F,MAAM,EAAE2F,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV,OAAOD,QAAQ;MACnB;MACA,IAAIC,OAAO,CAACG,KAAK,KAAKA,KAAK,EAAE;QACzBJ,QAAQ,GAAGE,KAAK;QAChB;MACJ;MACAD,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAOH,QAAQ;EACnB;EACAnD,IAAIA,CAACX,EAAE,EAAE;IACL,IAAI+D,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,IAAI6C,MAAM,GAAG,KAAK;IAClB,OAAOjB,OAAO,IAAI,CAACiB,MAAM,EAAE;MACvB,IAAIhF,EAAE,CAAC+D,OAAO,CAACG,KAAK,CAAC,EAAE;QACnBc,MAAM,GAAG,IAAI;QACb;MACJ;MACAjB,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAOe,MAAM;EACjB;EACAU,KAAKA,CAAC1F,EAAE,EAAE;IACN,IAAI+D,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,IAAI6C,MAAM,GAAG,IAAI;IACjB,OAAOjB,OAAO,IAAIiB,MAAM,EAAE;MACtB,IAAI,CAAChF,EAAE,CAAC+D,OAAO,CAACG,KAAK,CAAC,EAAE;QACpBc,MAAM,GAAG,KAAK;MAClB;MACAjB,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAOe,MAAM;EACjB;EACAW,QAAQA,CAAA,EAAG;IACP,OAAO,eAAe;EAC1B;EACAlC,IAAIA,CAACzD,EAAE,EAAE;IACL,IAAI+D,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,KAAK,IAAI6B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAAC3F,MAAM,EAAE2F,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA,IAAI/D,EAAE,CAAC+D,OAAO,CAACG,KAAK,EAAEF,KAAK,CAAC,EAAE;QAC1B,OAAOD,OAAO,CAACG,KAAK;MACxB;MACAH,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;EACJ;EACA2B,SAASA,CAAC5F,EAAE,EAAE;IACV,IAAI+D,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,KAAK,IAAI6B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAAC3F,MAAM,EAAE2F,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV,OAAO,CAAC,CAAC;MACb;MACA,IAAI/D,EAAE,CAAC+D,OAAO,CAACG,KAAK,EAAEF,KAAK,CAAC,EAAE;QAC1B,OAAOA,KAAK;MAChB;MACAD,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAO,CAAC,CAAC;EACb;EACAO,OAAOA,CAACV,QAAQ,EAAE;IACd,IAAI,IAAI,CAACzF,MAAM,KAAK,CAAC,IAAIyF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAI,IAAI,CAACzF,MAAM,EAAE;MAC9D,MAAM,IAAIS,KAAK,CAAC,6BAA6B,CAAC;IAClD;IACA,IAAIiF,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,KAAK,IAAI6B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,QAAQ,EAAEE,KAAK,EAAE,EAAE;MAC3CD,OAAO,GAAGA,OAAO,EAAEE,IAAI;IAC3B;IACA,OAAOF,OAAO;EAClB;EACAW,iCAAiCA,CAAA,EAAG;IAChC,MAAMmB,QAAQ,GAAG,EAAE;IACnB,IAAI9B,OAAO,GAAG,IAAI,CAAC5B,IAAI;IACvB,OAAO4B,OAAO,EAAE;MACZ8B,QAAQ,CAACpG,IAAI,CAACsE,OAAO,CAACG,KAAK,CAAC;MAC5BH,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,IAAI,CAACL,OAAO,GAAGiC,QAAQ;EAC3B;AACJ;;AAEA;AACA,SAASC,QAAQA,CAAA,EAAG;EAChB,MAAMC,KAAK,GAAG,QAAQ;EACtB;EACA,OAAO,SAASC,eAAeA,CAAC/G,MAAM,EAAEgH,WAAW,EAAE;IACjD,MAAMC,IAAI,GAAG,MAAMD,WAAW,OAAO;IACrC1C,MAAM,CAAC4C,cAAc,CAAClH,MAAM,EAAEgH,WAAW,EAAE;MACvC;MACApC,GAAGA,CAAA,EAAG;QACF,OAAO,IAAI,CAACqC,IAAI,CAAC;MACrB,CAAC;MACD;MACArB,GAAGA,CAACX,KAAK,EAAE;QACP,MAAMkC,SAAS,GAAG,IAAI,CAACF,IAAI,CAAC;QAC5B,IAAI,CAACA,IAAI,CAAC,GAAGhC,KAAK;QAClB,IAAIkC,SAAS,KAAKlC,KAAK,IAAI,IAAI,CAAC+B,WAAW,GAAGF,KAAK,CAAC,EAAE;UAClD,IAAI,CAACE,WAAW,GAAGF,KAAK,CAAC,CAACM,IAAI,CAACnC,KAAK,CAAC;QACzC;MACJ;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AAEA,MAAMoC,KAAK,CAAC;EACR;EACA,OAAOC,MAAMA,CAACC,OAAO,EAAE;IACnB;IACA,CAAEC,EAAE,IAAKA,EAAE,EAAED,OAAO,CAACE,YAAY,CAAC;EACtC;EACA;EACA;EACA,OAAOC,SAASA,CAACC,IAAI,EAAE;IACnB;IACA;IACA;IACA,IAAIC,IAAI,GAAGD,IAAI,CAACE,aAAa,CAACC,WAAW;IACzC,IAAI,CAACF,IAAI,IAAI,CAACA,IAAI,CAACG,MAAM,EAAE;MACvBH,IAAI,GAAG/F,GAAG;IACd;IACA,OAAO+F,IAAI,CAACrE,gBAAgB,CAACoE,IAAI,CAAC;EACtC;EACA,OAAOK,mBAAmBA,CAAA,EAAG;IACzB,MAAM5D,KAAK,GAAGD,gBAAgB,CAAC,CAAC;IAChC,OAAO;MACH8D,WAAW,EAAE7D,KAAK,KAAK,KAAK,GAAG,WAAW,GAAGe,SAAS;MACtD+C,SAAS,EAAE9D,KAAK,KAAK,KAAK,GAAG,yEAAyE,GAAGA,KAAK,KAAK,KAAK,GAAG,yEAAyE,GAAGe,SAAS;MAChNgD,OAAO,EAAE/D,KAAK,KAAK,KAAK,GAAG,yEAAyE,GAAGA,KAAK,KAAK,KAAK,GAAG,yEAAyE,GAAG;IACzM,CAAC;EACL;AACJ;AAEA,MAAMgE,aAAa,GAAG,CAAC,CAAC;AACxB,MAAMC,QAAQ,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAI,EAAE,MAAM,IAAIA,OAAO,CAAC;AACvE,SAASC,QAAQA,CAACC,GAAG,EAAE;EACnB,IAAI,CAAClK,SAAS,CAAC,CAAC,IAAI+J,QAAQ,IAAIG,GAAG,IAAIJ,aAAa,EAAE;IAClD;EACJ;EACAA,aAAa,CAACI,GAAG,CAAC,GAAG,IAAI;EACzBF,OAAO,CAACG,IAAI,CAACD,GAAG,CAAC;AACrB;;AAEA;AACA;AACA;;AAEA,SAAS9F,SAAS,EAAEgC,UAAU,EAAEmC,QAAQ,EAAEtI,OAAO,EAAE8I,KAAK,EAAElD,gBAAgB,EAAEpC,QAAQ,EAAEmC,QAAQ,EAAEJ,KAAK,EAAEhE,gBAAgB,EAAEa,kBAAkB,EAAE5B,aAAa,EAAE4C,gBAAgB,EAAEN,oBAAoB,EAAEuC,QAAQ,EAAE2E,QAAQ,EAAE1G,GAAG,IAAIC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}