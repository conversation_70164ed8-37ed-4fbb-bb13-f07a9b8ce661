{"name": "front-end", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/localize": "^18.2.13", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@auth0/angular-jwt": "^5.1.0", "@progress/kendo-angular-buttons": "^8.0.0", "@progress/kendo-angular-common": "^3.0.0", "@progress/kendo-angular-dialog": "^7.0.0", "@progress/kendo-angular-dropdowns": "^7.0.0", "@progress/kendo-angular-editor": "^4.1.11", "@progress/kendo-angular-inputs": "^10.0.0", "@progress/kendo-angular-intl": "^4.0.0", "@progress/kendo-angular-l10n": "^4.0.0", "@progress/kendo-angular-layout": "^7.0.0", "@progress/kendo-angular-listbox": "^1.0.4", "@progress/kendo-angular-popup": "^5.0.0", "@progress/kendo-angular-progressbar": "^3.0.0", "@progress/kendo-angular-toolbar": "^6.0.0", "@progress/kendo-angular-treeview": "^7.0.0", "@progress/kendo-drawing": "^1.5.12", "@progress/kendo-licensing": "^1.3.5", "@progress/kendo-theme-bootstrap": "^5.11.0", "@progress/kendo-theme-default": "^5.11.0", "bootstrap": "^5.2.2", "dateformat": "^5.0.3", "font-awesome": "^4.7.0", "jquery": "^3.6.1", "moment": "^2.29.4", "ng-angular-popup": "^0.2.0", "ngx-bootstrap": "^9.0.0", "ngx-pagination": "^6.0.2", "ngx-toastr": "^15.2.1", "popper.js": "^1.16.1", "rxjs": "~7.5.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.19", "@angular/cli": "~18.2.19", "@angular/compiler-cli": "^18.2.13", "@types/jasmine": "~4.0.0", "@types/jquery": "^3.5.14", "jasmine-core": "~4.3.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~5.4.5"}}