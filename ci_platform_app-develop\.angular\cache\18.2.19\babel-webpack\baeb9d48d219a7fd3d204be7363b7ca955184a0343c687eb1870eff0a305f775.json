{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, Injectable, Component, HostBinding, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"*\"];\nconst _c1 = a0 => [\"nav-item\", a0];\nfunction TabsetComponent_li_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵlistener(\"click\", function TabsetComponent_li_1_span_4_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const tabz_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r2.removeTab(tabz_r4));\n    });\n    i0.ɵɵtext(1, \" \\u274C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TabsetComponent_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 3);\n    i0.ɵɵlistener(\"keydown\", function TabsetComponent_li_1_Template_li_keydown_0_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.keyNavActions($event, i_r2));\n    });\n    i0.ɵɵelementStart(1, \"a\", 4);\n    i0.ɵɵlistener(\"click\", function TabsetComponent_li_1_Template_a_click_1_listener() {\n      const tabz_r4 = i0.ɵɵrestoreView(_r1).$implicit;\n      return i0.ɵɵresetView(tabz_r4.active = true);\n    });\n    i0.ɵɵelementStart(2, \"span\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TabsetComponent_li_1_span_4_Template, 2, 0, \"span\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tabz_r4 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", tabz_r4.active)(\"disabled\", tabz_r4.disabled);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c1, tabz_r4.customClass || \"\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", tabz_r4.active)(\"disabled\", tabz_r4.disabled);\n    i0.ɵɵattribute(\"aria-controls\", tabz_r4.id ? tabz_r4.id : \"\")(\"aria-selected\", !!tabz_r4.active)(\"id\", tabz_r4.id ? tabz_r4.id + \"-link\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTransclude\", tabz_r4.headingRef);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tabz_r4.heading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", tabz_r4.removable);\n  }\n}\nclass NgTranscludeDirective {\n  constructor(viewRef) {\n    this.viewRef = viewRef;\n  }\n  set ngTransclude(templateRef) {\n    this._ngTransclude = templateRef;\n    if (templateRef) {\n      this.viewRef.createEmbeddedView(templateRef);\n    }\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  get ngTransclude() {\n    return this._ngTransclude;\n  }\n}\nNgTranscludeDirective.ɵfac = function NgTranscludeDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgTranscludeDirective)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n};\nNgTranscludeDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgTranscludeDirective,\n  selectors: [[\"\", \"ngTransclude\", \"\"]],\n  inputs: {\n    ngTransclude: \"ngTransclude\"\n  }\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgTranscludeDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngTransclude]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }];\n  }, {\n    ngTransclude: [{\n      type: Input\n    }]\n  });\n})();\nclass TabsetConfig {\n  constructor() {\n    /** provides default navigation context class: 'tabs' or 'pills' */\n    this.type = 'tabs';\n    /** provides possibility to set keyNavigations enable or disable, by default is enable */\n    this.isKeysAllowed = true;\n    /** aria label for tab list */\n    this.ariaLabel = 'Tabs';\n  }\n}\nTabsetConfig.ɵfac = function TabsetConfig_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TabsetConfig)();\n};\nTabsetConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TabsetConfig,\n  factory: TabsetConfig.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabsetConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n// todo: add active event to tab\n// todo: fix? mixing static and dynamic tabs position tabs in order of creation\nclass TabsetComponent {\n  constructor(config, renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.clazz = true;\n    this.tabs = [];\n    this.classMap = {};\n    /** aria label for tab list */\n    this.ariaLabel = 'Tabs';\n    this.isDestroyed = false;\n    this._vertical = false;\n    this._justified = false;\n    this._type = 'tabs';\n    this._isKeysAllowed = true;\n    Object.assign(this, config);\n  }\n  /** if true tabs will be placed vertically */\n  get vertical() {\n    return this._vertical;\n  }\n  set vertical(value) {\n    this._vertical = value;\n    this.setClassMap();\n  }\n  /** if true tabs fill the container and have a consistent width */\n  get justified() {\n    return this._justified;\n  }\n  set justified(value) {\n    this._justified = value;\n    this.setClassMap();\n  }\n  /** navigation context class: 'tabs' or 'pills' */\n  get type() {\n    return this._type;\n  }\n  set type(value) {\n    this._type = value;\n    this.setClassMap();\n  }\n  get isKeysAllowed() {\n    return this._isKeysAllowed;\n  }\n  set isKeysAllowed(value) {\n    this._isKeysAllowed = value;\n  }\n  ngOnDestroy() {\n    this.isDestroyed = true;\n  }\n  addTab(tab) {\n    this.tabs.push(tab);\n    tab.active = this.tabs.length === 1 && !tab.active;\n  }\n  removeTab(tab, options = {\n    reselect: true,\n    emit: true\n  }) {\n    const index = this.tabs.indexOf(tab);\n    if (index === -1 || this.isDestroyed) {\n      return;\n    }\n    // Select a new tab if the tab to be removed is selected and not destroyed\n    if (options.reselect && tab.active && this.hasAvailableTabs(index)) {\n      const newActiveIndex = this.getClosestTabIndex(index);\n      this.tabs[newActiveIndex].active = true;\n    }\n    if (options.emit) {\n      tab.removed.emit(tab);\n    }\n    this.tabs.splice(index, 1);\n    if (tab.elementRef.nativeElement.parentNode) {\n      this.renderer.removeChild(tab.elementRef.nativeElement.parentNode, tab.elementRef.nativeElement);\n    }\n  }\n  keyNavActions(event, index) {\n    if (!this.isKeysAllowed) {\n      return;\n    }\n    const list = Array.from(this.elementRef.nativeElement.querySelectorAll('.nav-link'));\n    // const activeElList = list.filter((el: HTMLElement) => !el.classList.contains('disabled'));\n    if (event.keyCode === 13 || event.key === 'Enter' || event.keyCode === 32 || event.key === 'Space') {\n      event.preventDefault();\n      const currentTab = list[index % list.length];\n      currentTab.click();\n      return;\n    }\n    if (event.keyCode === 39 || event.key === 'RightArrow') {\n      let nextTab;\n      let shift = 1;\n      do {\n        nextTab = list[(index + shift) % list.length];\n        shift++;\n      } while (nextTab.classList.contains('disabled'));\n      nextTab.focus();\n      return;\n    }\n    if (event.keyCode === 37 || event.key === 'LeftArrow') {\n      let previousTab;\n      let shift = 1;\n      let i = index;\n      do {\n        if (i - shift < 0) {\n          i = list.length - 1;\n          previousTab = list[i];\n          shift = 0;\n        } else {\n          previousTab = list[i - shift];\n        }\n        shift++;\n      } while (previousTab.classList.contains('disabled'));\n      previousTab.focus();\n      return;\n    }\n    if (event.keyCode === 36 || event.key === 'Home') {\n      event.preventDefault();\n      let firstTab;\n      let shift = 0;\n      do {\n        firstTab = list[shift % list.length];\n        shift++;\n      } while (firstTab.classList.contains('disabled'));\n      firstTab.focus();\n      return;\n    }\n    if (event.keyCode === 35 || event.key === 'End') {\n      event.preventDefault();\n      let lastTab;\n      let shift = 1;\n      let i = index;\n      do {\n        if (i - shift < 0) {\n          i = list.length - 1;\n          lastTab = list[i];\n          shift = 0;\n        } else {\n          lastTab = list[i - shift];\n        }\n        shift++;\n      } while (lastTab.classList.contains('disabled'));\n      lastTab.focus();\n      return;\n    }\n    if (event.keyCode === 46 || event.key === 'Delete') {\n      if (this.tabs[index].removable) {\n        this.removeTab(this.tabs[index]);\n        if (list[index + 1]) {\n          list[(index + 1) % list.length].focus();\n          return;\n        }\n        if (list[list.length - 1]) {\n          list[0].focus();\n        }\n      }\n    }\n  }\n  getClosestTabIndex(index) {\n    const tabsLength = this.tabs.length;\n    if (!tabsLength) {\n      return -1;\n    }\n    for (let step = 1; step <= tabsLength; step += 1) {\n      const prevIndex = index - step;\n      const nextIndex = index + step;\n      if (this.tabs[prevIndex] && !this.tabs[prevIndex].disabled) {\n        return prevIndex;\n      }\n      if (this.tabs[nextIndex] && !this.tabs[nextIndex].disabled) {\n        return nextIndex;\n      }\n    }\n    return -1;\n  }\n  hasAvailableTabs(index) {\n    const tabsLength = this.tabs.length;\n    if (!tabsLength) {\n      return false;\n    }\n    for (let i = 0; i < tabsLength; i += 1) {\n      if (!this.tabs[i].disabled && i !== index) {\n        return true;\n      }\n    }\n    return false;\n  }\n  setClassMap() {\n    this.classMap = {\n      'nav-stacked': this.vertical,\n      'flex-column': this.vertical,\n      'nav-justified': this.justified,\n      [`nav-${this.type}`]: true\n    };\n  }\n}\nTabsetComponent.ɵfac = function TabsetComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TabsetComponent)(i0.ɵɵdirectiveInject(TabsetConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nTabsetComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TabsetComponent,\n  selectors: [[\"tabset\"]],\n  hostVars: 2,\n  hostBindings: function TabsetComponent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"tab-container\", ctx.clazz);\n    }\n  },\n  inputs: {\n    vertical: \"vertical\",\n    justified: \"justified\",\n    type: \"type\"\n  },\n  ngContentSelectors: _c0,\n  decls: 4,\n  vars: 3,\n  consts: [[\"role\", \"tablist\", 1, \"nav\", 3, \"click\", \"ngClass\"], [3, \"ngClass\", \"active\", \"disabled\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [1, \"tab-content\"], [3, \"keydown\", \"ngClass\"], [\"href\", \"javascript:void(0);\", \"role\", \"tab\", 1, \"nav-link\", 3, \"click\"], [3, \"ngTransclude\"], [\"class\", \"bs-remove-tab\", 3, \"click\", 4, \"ngIf\"], [1, \"bs-remove-tab\", 3, \"click\"]],\n  template: function TabsetComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"ul\", 0);\n      i0.ɵɵlistener(\"click\", function TabsetComponent_Template_ul_click_0_listener($event) {\n        return $event.preventDefault();\n      });\n      i0.ɵɵtemplate(1, TabsetComponent_li_1_Template, 5, 17, \"li\", 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵprojection(3);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", ctx.classMap);\n      i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngForOf\", ctx.tabs);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, NgTranscludeDirective],\n  styles: [\"[_nghost-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-item.disabled[_ngcontent-%COMP%]   a.disabled[_ngcontent-%COMP%]{cursor:default}\"]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabsetComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tabset',\n      template: \"<ul class=\\\"nav\\\" [ngClass]=\\\"classMap\\\"\\n    (click)=\\\"$event.preventDefault()\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    role=\\\"tablist\\\">\\n  <li *ngFor=\\\"let tabz of tabs; let i = index\\\" [ngClass]=\\\"['nav-item', tabz.customClass || '']\\\"\\n      [class.active]=\\\"tabz.active\\\" [class.disabled]=\\\"tabz.disabled\\\" (keydown)=\\\"keyNavActions($event, i)\\\">\\n    <a href=\\\"javascript:void(0);\\\" class=\\\"nav-link\\\" role=\\\"tab\\\"\\n       [attr.aria-controls]=\\\"tabz.id ? tabz.id : ''\\\"\\n       [attr.aria-selected]=\\\"!!tabz.active\\\"\\n       [attr.id]=\\\"tabz.id ? tabz.id + '-link' : ''\\\"\\n       [class.active]=\\\"tabz.active\\\" [class.disabled]=\\\"tabz.disabled\\\"\\n       (click)=\\\"tabz.active = true\\\">\\n      <span [ngTransclude]=\\\"tabz.headingRef\\\">{{ tabz.heading }}</span>\\n      <span *ngIf=\\\"tabz.removable\\\" (click)=\\\"$event.preventDefault(); removeTab(tabz);\\\" class=\\\"bs-remove-tab\\\"> &#10060;</span>\\n    </a>\\n  </li>\\n</ul>\\n<div class=\\\"tab-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\",\n      styles: [\":host .nav-tabs .nav-item.disabled a.disabled{cursor:default}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: TabsetConfig\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    vertical: [{\n      type: Input\n    }],\n    justified: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    clazz: [{\n      type: HostBinding,\n      args: ['class.tab-container']\n    }]\n  });\n})();\nclass TabDirective {\n  constructor(tabset, elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    /** if true tab can not be activated */\n    this.disabled = false;\n    /** if true tab can be removable, additional button will appear */\n    this.removable = false;\n    /** fired when tab became active, $event:Tab equals to selected instance of Tab component */\n    this.selectTab = new EventEmitter();\n    /** fired when tab became inactive, $event:Tab equals to deselected instance of Tab component */\n    this.deselect = new EventEmitter();\n    /** fired before tab will be removed, $event:Tab equals to instance of removed tab */\n    this.removed = new EventEmitter();\n    this.addClass = true;\n    this.role = 'tabpanel';\n    this._active = false;\n    this._customClass = '';\n    this.tabset = tabset;\n    this.tabset.addTab(this);\n  }\n  /** if set, will be added to the tab's class attribute. Multiple classes are supported. */\n  get customClass() {\n    return this._customClass;\n  }\n  set customClass(customClass) {\n    if (this.customClass) {\n      this.customClass.split(' ').forEach(cssClass => {\n        this.renderer.removeClass(this.elementRef.nativeElement, cssClass);\n      });\n    }\n    this._customClass = customClass ? customClass.trim() : '';\n    if (this.customClass) {\n      this.customClass.split(' ').forEach(cssClass => {\n        this.renderer.addClass(this.elementRef.nativeElement, cssClass);\n      });\n    }\n  }\n  /** tab active state toggle */\n  get active() {\n    return this._active;\n  }\n  set active(active) {\n    if (this._active === active) {\n      return;\n    }\n    if (this.disabled && active || !active) {\n      if (this._active && !active) {\n        this.deselect.emit(this);\n        this._active = active;\n      }\n      return;\n    }\n    this._active = active;\n    this.selectTab.emit(this);\n    this.tabset.tabs.forEach(tab => {\n      if (tab !== this) {\n        tab.active = false;\n      }\n    });\n  }\n  get ariaLabelledby() {\n    return this.id ? `${this.id}-link` : '';\n  }\n  ngOnInit() {\n    this.removable = !!this.removable;\n  }\n  ngOnDestroy() {\n    this.tabset.removeTab(this, {\n      reselect: false,\n      emit: false\n    });\n  }\n}\nTabDirective.ɵfac = function TabDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TabDirective)(i0.ɵɵdirectiveInject(TabsetComponent), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n};\nTabDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TabDirective,\n  selectors: [[\"tab\"], [\"\", \"tab\", \"\"]],\n  hostVars: 7,\n  hostBindings: function TabDirective_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.id)(\"role\", ctx.role)(\"aria-labelledby\", ctx.ariaLabelledby);\n      i0.ɵɵclassProp(\"active\", ctx.active)(\"tab-pane\", ctx.addClass);\n    }\n  },\n  inputs: {\n    heading: \"heading\",\n    id: \"id\",\n    disabled: \"disabled\",\n    removable: \"removable\",\n    customClass: \"customClass\",\n    active: \"active\"\n  },\n  outputs: {\n    selectTab: \"selectTab\",\n    deselect: \"deselect\",\n    removed: \"removed\"\n  },\n  exportAs: [\"tab\"]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'tab, [tab]',\n      exportAs: 'tab'\n    }]\n  }], function () {\n    return [{\n      type: TabsetComponent\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }];\n  }, {\n    heading: [{\n      type: Input\n    }],\n    id: [{\n      type: HostBinding,\n      args: ['attr.id']\n    }, {\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input\n    }],\n    customClass: [{\n      type: Input\n    }],\n    active: [{\n      type: HostBinding,\n      args: ['class.active']\n    }, {\n      type: Input\n    }],\n    selectTab: [{\n      type: Output\n    }],\n    deselect: [{\n      type: Output\n    }],\n    removed: [{\n      type: Output\n    }],\n    addClass: [{\n      type: HostBinding,\n      args: ['class.tab-pane']\n    }],\n    role: [{\n      type: HostBinding,\n      args: ['attr.role']\n    }],\n    ariaLabelledby: [{\n      type: HostBinding,\n      args: ['attr.aria-labelledby']\n    }]\n  });\n})();\n\n/** Should be used to mark <ng-template> element as a template for tab heading */\nclass TabHeadingDirective {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  constructor(templateRef, tab) {\n    tab.headingRef = templateRef;\n  }\n}\nTabHeadingDirective.ɵfac = function TabHeadingDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TabHeadingDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(TabDirective));\n};\nTabHeadingDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TabHeadingDirective,\n  selectors: [[\"\", \"tabHeading\", \"\"]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabHeadingDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tabHeading]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: TabDirective\n    }];\n  }, null);\n})();\nclass TabsModule {\n  static forRoot() {\n    return {\n      ngModule: TabsModule,\n      providers: []\n    };\n  }\n}\nTabsModule.ɵfac = function TabsModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TabsModule)();\n};\nTabsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TabsModule\n});\nTabsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [NgTranscludeDirective, TabDirective, TabsetComponent, TabHeadingDirective],\n      exports: [TabDirective, TabsetComponent, TabHeadingDirective, NgTranscludeDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgTranscludeDirective, TabDirective, TabHeadingDirective, TabsModule, TabsetComponent, TabsetConfig };", "map": {"version": 3, "names": ["i0", "Directive", "Input", "Injectable", "Component", "HostBinding", "EventEmitter", "Output", "NgModule", "i2", "CommonModule", "_c0", "_c1", "a0", "TabsetComponent_li_1_span_4_Template", "rf", "ctx", "_r5", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TabsetComponent_li_1_span_4_Template_span_click_0_listener", "$event", "ɵɵrestoreView", "tabz_r4", "ɵɵnextContext", "$implicit", "ctx_r2", "preventDefault", "ɵɵresetView", "removeTab", "ɵɵtext", "ɵɵelementEnd", "TabsetComponent_li_1_Template", "_r1", "TabsetComponent_li_1_Template_li_keydown_0_listener", "i_r2", "index", "keyNavActions", "TabsetComponent_li_1_Template_a_click_1_listener", "active", "ɵɵtemplate", "ɵɵclassProp", "disabled", "ɵɵproperty", "ɵɵpureFunction1", "customClass", "ɵɵadvance", "ɵɵattribute", "id", "headingRef", "ɵɵtextInterpolate", "heading", "removable", "NgTranscludeDirective", "constructor", "viewRef", "ngTransclude", "templateRef", "_ngTransclude", "createEmbeddedView", "ɵfac", "NgTranscludeDirective_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "TabsetConfig", "isKeysAllowed", "aria<PERSON><PERSON><PERSON>", "TabsetConfig_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "TabsetComponent", "config", "renderer", "elementRef", "clazz", "tabs", "classMap", "isDestroyed", "_vertical", "_justified", "_type", "_isKeysAllowed", "Object", "assign", "vertical", "value", "setClassMap", "justified", "ngOnDestroy", "addTab", "tab", "push", "length", "options", "reselect", "emit", "indexOf", "hasAvailableTabs", "newActiveIndex", "getClosestTabIndex", "removed", "splice", "nativeElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "event", "list", "Array", "from", "querySelectorAll", "keyCode", "key", "currentTab", "click", "nextTab", "shift", "classList", "contains", "focus", "previousTab", "i", "firstTab", "lastTab", "tabsLength", "step", "prevIndex", "nextIndex", "TabsetComponent_Factory", "Renderer2", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "TabsetComponent_HostBindings", "ngContentSelectors", "decls", "vars", "consts", "template", "TabsetComponent_Template", "ɵɵprojectionDef", "TabsetComponent_Template_ul_click_0_listener", "ɵɵprojection", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles", "TabDirective", "tabset", "selectTab", "deselect", "addClass", "role", "_active", "_customClass", "split", "for<PERSON>ach", "cssClass", "removeClass", "trim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "TabDirective_Factory", "TabDirective_HostBindings", "outputs", "exportAs", "TabHeadingDirective", "TabHeadingDirective_Factory", "TemplateRef", "TabsModule", "forRoot", "ngModule", "providers", "TabsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/ngx-bootstrap/tabs/fesm2020/ngx-bootstrap-tabs.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, Injectable, Component, HostBinding, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass NgTranscludeDirective {\n    constructor(viewRef) {\n        this.viewRef = viewRef;\n    }\n    set ngTransclude(templateRef) {\n        this._ngTransclude = templateRef;\n        if (templateRef) {\n            this.viewRef.createEmbeddedView(templateRef);\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    get ngTransclude() {\n        return this._ngTransclude;\n    }\n}\nNgTranscludeDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: NgTranscludeDirective, deps: [{ token: i0.ViewContainerRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgTranscludeDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: NgTranscludeDirective, selector: \"[ngTransclude]\", inputs: { ngTransclude: \"ngTransclude\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: NgTranscludeDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ngTransclude]'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }]; }, propDecorators: { ngTransclude: [{\n                type: Input\n            }] } });\n\nclass TabsetConfig {\n    constructor() {\n        /** provides default navigation context class: 'tabs' or 'pills' */\n        this.type = 'tabs';\n        /** provides possibility to set keyNavigations enable or disable, by default is enable */\n        this.isKeysAllowed = true;\n        /** aria label for tab list */\n        this.ariaLabel = 'Tabs';\n    }\n}\nTabsetConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabsetConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTabsetConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabsetConfig, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabsetConfig, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n\n// todo: add active event to tab\n// todo: fix? mixing static and dynamic tabs position tabs in order of creation\nclass TabsetComponent {\n    constructor(config, renderer, elementRef) {\n        this.renderer = renderer;\n        this.elementRef = elementRef;\n        this.clazz = true;\n        this.tabs = [];\n        this.classMap = {};\n        /** aria label for tab list */\n        this.ariaLabel = 'Tabs';\n        this.isDestroyed = false;\n        this._vertical = false;\n        this._justified = false;\n        this._type = 'tabs';\n        this._isKeysAllowed = true;\n        Object.assign(this, config);\n    }\n    /** if true tabs will be placed vertically */\n    get vertical() {\n        return this._vertical;\n    }\n    set vertical(value) {\n        this._vertical = value;\n        this.setClassMap();\n    }\n    /** if true tabs fill the container and have a consistent width */\n    get justified() {\n        return this._justified;\n    }\n    set justified(value) {\n        this._justified = value;\n        this.setClassMap();\n    }\n    /** navigation context class: 'tabs' or 'pills' */\n    get type() {\n        return this._type;\n    }\n    set type(value) {\n        this._type = value;\n        this.setClassMap();\n    }\n    get isKeysAllowed() {\n        return this._isKeysAllowed;\n    }\n    set isKeysAllowed(value) {\n        this._isKeysAllowed = value;\n    }\n    ngOnDestroy() {\n        this.isDestroyed = true;\n    }\n    addTab(tab) {\n        this.tabs.push(tab);\n        tab.active = this.tabs.length === 1 && !tab.active;\n    }\n    removeTab(tab, options = { reselect: true, emit: true }) {\n        const index = this.tabs.indexOf(tab);\n        if (index === -1 || this.isDestroyed) {\n            return;\n        }\n        // Select a new tab if the tab to be removed is selected and not destroyed\n        if (options.reselect && tab.active && this.hasAvailableTabs(index)) {\n            const newActiveIndex = this.getClosestTabIndex(index);\n            this.tabs[newActiveIndex].active = true;\n        }\n        if (options.emit) {\n            tab.removed.emit(tab);\n        }\n        this.tabs.splice(index, 1);\n        if (tab.elementRef.nativeElement.parentNode) {\n            this.renderer.removeChild(tab.elementRef.nativeElement.parentNode, tab.elementRef.nativeElement);\n        }\n    }\n    keyNavActions(event, index) {\n        if (!this.isKeysAllowed) {\n            return;\n        }\n        const list = Array.from(this.elementRef.nativeElement.querySelectorAll('.nav-link'));\n        // const activeElList = list.filter((el: HTMLElement) => !el.classList.contains('disabled'));\n        if (event.keyCode === 13 || event.key === 'Enter' || event.keyCode === 32 || event.key === 'Space') {\n            event.preventDefault();\n            const currentTab = list[(index) % list.length];\n            currentTab.click();\n            return;\n        }\n        if (event.keyCode === 39 || event.key === 'RightArrow') {\n            let nextTab;\n            let shift = 1;\n            do {\n                nextTab = list[(index + shift) % list.length];\n                shift++;\n            } while (nextTab.classList.contains('disabled'));\n            nextTab.focus();\n            return;\n        }\n        if (event.keyCode === 37 || event.key === 'LeftArrow') {\n            let previousTab;\n            let shift = 1;\n            let i = index;\n            do {\n                if ((i - shift) < 0) {\n                    i = list.length - 1;\n                    previousTab = list[i];\n                    shift = 0;\n                }\n                else {\n                    previousTab = list[i - shift];\n                }\n                shift++;\n            } while (previousTab.classList.contains('disabled'));\n            previousTab.focus();\n            return;\n        }\n        if (event.keyCode === 36 || event.key === 'Home') {\n            event.preventDefault();\n            let firstTab;\n            let shift = 0;\n            do {\n                firstTab = list[shift % list.length];\n                shift++;\n            } while (firstTab.classList.contains('disabled'));\n            firstTab.focus();\n            return;\n        }\n        if (event.keyCode === 35 || event.key === 'End') {\n            event.preventDefault();\n            let lastTab;\n            let shift = 1;\n            let i = index;\n            do {\n                if ((i - shift) < 0) {\n                    i = list.length - 1;\n                    lastTab = list[i];\n                    shift = 0;\n                }\n                else {\n                    lastTab = list[i - shift];\n                }\n                shift++;\n            } while (lastTab.classList.contains('disabled'));\n            lastTab.focus();\n            return;\n        }\n        if (event.keyCode === 46 || event.key === 'Delete') {\n            if (this.tabs[index].removable) {\n                this.removeTab(this.tabs[index]);\n                if (list[index + 1]) {\n                    list[(index + 1) % list.length].focus();\n                    return;\n                }\n                if (list[list.length - 1]) {\n                    list[0].focus();\n                }\n            }\n        }\n    }\n    getClosestTabIndex(index) {\n        const tabsLength = this.tabs.length;\n        if (!tabsLength) {\n            return -1;\n        }\n        for (let step = 1; step <= tabsLength; step += 1) {\n            const prevIndex = index - step;\n            const nextIndex = index + step;\n            if (this.tabs[prevIndex] && !this.tabs[prevIndex].disabled) {\n                return prevIndex;\n            }\n            if (this.tabs[nextIndex] && !this.tabs[nextIndex].disabled) {\n                return nextIndex;\n            }\n        }\n        return -1;\n    }\n    hasAvailableTabs(index) {\n        const tabsLength = this.tabs.length;\n        if (!tabsLength) {\n            return false;\n        }\n        for (let i = 0; i < tabsLength; i += 1) {\n            if (!this.tabs[i].disabled && i !== index) {\n                return true;\n            }\n        }\n        return false;\n    }\n    setClassMap() {\n        this.classMap = {\n            'nav-stacked': this.vertical,\n            'flex-column': this.vertical,\n            'nav-justified': this.justified,\n            [`nav-${this.type}`]: true\n        };\n    }\n}\nTabsetComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabsetComponent, deps: [{ token: TabsetConfig }, { token: i0.Renderer2 }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nTabsetComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TabsetComponent, selector: \"tabset\", inputs: { vertical: \"vertical\", justified: \"justified\", type: \"type\" }, host: { properties: { \"class.tab-container\": \"this.clazz\" } }, ngImport: i0, template: \"<ul class=\\\"nav\\\" [ngClass]=\\\"classMap\\\"\\n    (click)=\\\"$event.preventDefault()\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    role=\\\"tablist\\\">\\n  <li *ngFor=\\\"let tabz of tabs; let i = index\\\" [ngClass]=\\\"['nav-item', tabz.customClass || '']\\\"\\n      [class.active]=\\\"tabz.active\\\" [class.disabled]=\\\"tabz.disabled\\\" (keydown)=\\\"keyNavActions($event, i)\\\">\\n    <a href=\\\"javascript:void(0);\\\" class=\\\"nav-link\\\" role=\\\"tab\\\"\\n       [attr.aria-controls]=\\\"tabz.id ? tabz.id : ''\\\"\\n       [attr.aria-selected]=\\\"!!tabz.active\\\"\\n       [attr.id]=\\\"tabz.id ? tabz.id + '-link' : ''\\\"\\n       [class.active]=\\\"tabz.active\\\" [class.disabled]=\\\"tabz.disabled\\\"\\n       (click)=\\\"tabz.active = true\\\">\\n      <span [ngTransclude]=\\\"tabz.headingRef\\\">{{ tabz.heading }}</span>\\n      <span *ngIf=\\\"tabz.removable\\\" (click)=\\\"$event.preventDefault(); removeTab(tabz);\\\" class=\\\"bs-remove-tab\\\"> &#10060;</span>\\n    </a>\\n  </li>\\n</ul>\\n<div class=\\\"tab-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\", styles: [\":host .nav-tabs .nav-item.disabled a.disabled{cursor:default}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTranscludeDirective, selector: \"[ngTransclude]\", inputs: [\"ngTransclude\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabsetComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'tabset', template: \"<ul class=\\\"nav\\\" [ngClass]=\\\"classMap\\\"\\n    (click)=\\\"$event.preventDefault()\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    role=\\\"tablist\\\">\\n  <li *ngFor=\\\"let tabz of tabs; let i = index\\\" [ngClass]=\\\"['nav-item', tabz.customClass || '']\\\"\\n      [class.active]=\\\"tabz.active\\\" [class.disabled]=\\\"tabz.disabled\\\" (keydown)=\\\"keyNavActions($event, i)\\\">\\n    <a href=\\\"javascript:void(0);\\\" class=\\\"nav-link\\\" role=\\\"tab\\\"\\n       [attr.aria-controls]=\\\"tabz.id ? tabz.id : ''\\\"\\n       [attr.aria-selected]=\\\"!!tabz.active\\\"\\n       [attr.id]=\\\"tabz.id ? tabz.id + '-link' : ''\\\"\\n       [class.active]=\\\"tabz.active\\\" [class.disabled]=\\\"tabz.disabled\\\"\\n       (click)=\\\"tabz.active = true\\\">\\n      <span [ngTransclude]=\\\"tabz.headingRef\\\">{{ tabz.heading }}</span>\\n      <span *ngIf=\\\"tabz.removable\\\" (click)=\\\"$event.preventDefault(); removeTab(tabz);\\\" class=\\\"bs-remove-tab\\\"> &#10060;</span>\\n    </a>\\n  </li>\\n</ul>\\n<div class=\\\"tab-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\", styles: [\":host .nav-tabs .nav-item.disabled a.disabled{cursor:default}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: TabsetConfig }, { type: i0.Renderer2 }, { type: i0.ElementRef }]; }, propDecorators: { vertical: [{\n                type: Input\n            }], justified: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], clazz: [{\n                type: HostBinding,\n                args: ['class.tab-container']\n            }] } });\n\nclass TabDirective {\n    constructor(tabset, elementRef, renderer) {\n        this.elementRef = elementRef;\n        this.renderer = renderer;\n        /** if true tab can not be activated */\n        this.disabled = false;\n        /** if true tab can be removable, additional button will appear */\n        this.removable = false;\n        /** fired when tab became active, $event:Tab equals to selected instance of Tab component */\n        this.selectTab = new EventEmitter();\n        /** fired when tab became inactive, $event:Tab equals to deselected instance of Tab component */\n        this.deselect = new EventEmitter();\n        /** fired before tab will be removed, $event:Tab equals to instance of removed tab */\n        this.removed = new EventEmitter();\n        this.addClass = true;\n        this.role = 'tabpanel';\n        this._active = false;\n        this._customClass = '';\n        this.tabset = tabset;\n        this.tabset.addTab(this);\n    }\n    /** if set, will be added to the tab's class attribute. Multiple classes are supported. */\n    get customClass() {\n        return this._customClass;\n    }\n    set customClass(customClass) {\n        if (this.customClass) {\n            this.customClass.split(' ').forEach((cssClass) => {\n                this.renderer.removeClass(this.elementRef.nativeElement, cssClass);\n            });\n        }\n        this._customClass = customClass ? customClass.trim() : '';\n        if (this.customClass) {\n            this.customClass.split(' ').forEach((cssClass) => {\n                this.renderer.addClass(this.elementRef.nativeElement, cssClass);\n            });\n        }\n    }\n    /** tab active state toggle */\n    get active() {\n        return this._active;\n    }\n    set active(active) {\n        if (this._active === active) {\n            return;\n        }\n        if ((this.disabled && active) || !active) {\n            if (this._active && !active) {\n                this.deselect.emit(this);\n                this._active = active;\n            }\n            return;\n        }\n        this._active = active;\n        this.selectTab.emit(this);\n        this.tabset.tabs.forEach((tab) => {\n            if (tab !== this) {\n                tab.active = false;\n            }\n        });\n    }\n    get ariaLabelledby() {\n        return this.id ? `${this.id}-link` : '';\n    }\n    ngOnInit() {\n        this.removable = !!this.removable;\n    }\n    ngOnDestroy() {\n        this.tabset.removeTab(this, { reselect: false, emit: false });\n    }\n}\nTabDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabDirective, deps: [{ token: TabsetComponent }, { token: i0.ElementRef }, { token: i0.Renderer2 }], target: i0.ɵɵFactoryTarget.Directive });\nTabDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TabDirective, selector: \"tab, [tab]\", inputs: { heading: \"heading\", id: \"id\", disabled: \"disabled\", removable: \"removable\", customClass: \"customClass\", active: \"active\" }, outputs: { selectTab: \"selectTab\", deselect: \"deselect\", removed: \"removed\" }, host: { properties: { \"attr.id\": \"this.id\", \"class.active\": \"this.active\", \"class.tab-pane\": \"this.addClass\", \"attr.role\": \"this.role\", \"attr.aria-labelledby\": \"this.ariaLabelledby\" } }, exportAs: [\"tab\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: 'tab, [tab]', exportAs: 'tab' }]\n        }], ctorParameters: function () { return [{ type: TabsetComponent }, { type: i0.ElementRef }, { type: i0.Renderer2 }]; }, propDecorators: { heading: [{\n                type: Input\n            }], id: [{\n                type: HostBinding,\n                args: ['attr.id']\n            }, {\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], removable: [{\n                type: Input\n            }], customClass: [{\n                type: Input\n            }], active: [{\n                type: HostBinding,\n                args: ['class.active']\n            }, {\n                type: Input\n            }], selectTab: [{\n                type: Output\n            }], deselect: [{\n                type: Output\n            }], removed: [{\n                type: Output\n            }], addClass: [{\n                type: HostBinding,\n                args: ['class.tab-pane']\n            }], role: [{\n                type: HostBinding,\n                args: ['attr.role']\n            }], ariaLabelledby: [{\n                type: HostBinding,\n                args: ['attr.aria-labelledby']\n            }] } });\n\n/** Should be used to mark <ng-template> element as a template for tab heading */\nclass TabHeadingDirective {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    constructor(templateRef, tab) {\n        tab.headingRef = templateRef;\n    }\n}\nTabHeadingDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabHeadingDirective, deps: [{ token: i0.TemplateRef }, { token: TabDirective }], target: i0.ɵɵFactoryTarget.Directive });\nTabHeadingDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TabHeadingDirective, selector: \"[tabHeading]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabHeadingDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[tabHeading]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: TabDirective }]; } });\n\nclass TabsModule {\n    static forRoot() {\n        return {\n            ngModule: TabsModule,\n            providers: []\n        };\n    }\n}\nTabsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTabsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: TabsModule, declarations: [NgTranscludeDirective,\n        TabDirective,\n        TabsetComponent,\n        TabHeadingDirective], imports: [CommonModule], exports: [TabDirective,\n        TabsetComponent,\n        TabHeadingDirective,\n        NgTranscludeDirective] });\nTabsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabsModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TabsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    declarations: [\n                        NgTranscludeDirective,\n                        TabDirective,\n                        TabsetComponent,\n                        TabHeadingDirective\n                    ],\n                    exports: [\n                        TabDirective,\n                        TabsetComponent,\n                        TabHeadingDirective,\n                        NgTranscludeDirective\n                    ]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgTranscludeDirective, TabDirective, TabHeadingDirective, TabsModule, TabsetComponent, TabsetConfig };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACpH,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA,iBAAAA,EAAA;AAAA,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAiByDjB,EAAE,CAAAkB,gBAAA;IAAFlB,EAAE,CAAAmB,cAAA,aAiOijC,CAAC;IAjOpjCnB,EAAE,CAAAoB,UAAA,mBAAAC,2DAAAC,MAAA;MAAFtB,EAAE,CAAAuB,aAAA,CAAAN,GAAA;MAAA,MAAAO,OAAA,GAAFxB,EAAE,CAAAyB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAF3B,EAAE,CAAAyB,aAAA;MAiO8+BH,MAAA,CAAAM,cAAA,CAAsB,CAAC;MAAA,OAjOvgC5B,EAAE,CAAA6B,WAAA,CAiOugCF,MAAA,CAAAG,SAAA,CAAAN,OAAc,CAAC;IAAA,CAAE,CAAC;IAjO3hCxB,EAAE,CAAA+B,MAAA,aAiO0jC,CAAC;IAjO7jC/B,EAAE,CAAAgC,YAAA,CAiOikC,CAAC;EAAA;AAAA;AAAA,SAAAC,8BAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmB,GAAA,GAjOpkClC,EAAE,CAAAkB,gBAAA;IAAFlB,EAAE,CAAAmB,cAAA,WAiO6hB,CAAC;IAjOhiBnB,EAAE,CAAAoB,UAAA,qBAAAe,oDAAAb,MAAA;MAAA,MAAAc,IAAA,GAAFpC,EAAE,CAAAuB,aAAA,CAAAW,GAAA,EAAAG,KAAA;MAAA,MAAAV,MAAA,GAAF3B,EAAE,CAAAyB,aAAA;MAAA,OAAFzB,EAAE,CAAA6B,WAAA,CAiOmgBF,MAAA,CAAAW,aAAA,CAAAhB,MAAA,EAAAc,IAAuB,CAAC;IAAA,CAAC,CAAC;IAjO/hBpC,EAAE,CAAAmB,cAAA,UAiOk3B,CAAC;IAjOr3BnB,EAAE,CAAAoB,UAAA,mBAAAmB,iDAAA;MAAA,MAAAf,OAAA,GAAFxB,EAAE,CAAAuB,aAAA,CAAAW,GAAA,EAAAR,SAAA;MAAA,OAAF1B,EAAE,CAAA6B,WAAA,CAAAL,OAAA,CAAAgB,MAAA,GAiO42B,IAAI;IAAA,CAAC,CAAC;IAjOp3BxC,EAAE,CAAAmB,cAAA,aAiOm6B,CAAC;IAjOt6BnB,EAAE,CAAA+B,MAAA,EAiOq7B,CAAC;IAjOx7B/B,EAAE,CAAAgC,YAAA,CAiO47B,CAAC;IAjO/7BhC,EAAE,CAAAyC,UAAA,IAAA3B,oCAAA,iBAiOijC,CAAC;IAjOpjCd,EAAE,CAAAgC,YAAA,CAiO2kC,CAAC,CAAQ,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAS,OAAA,GAAAR,GAAA,CAAAU,SAAA;IAjOvlC1B,EAAE,CAAA0C,WAAA,WAAAlB,OAAA,CAAAgB,MAiOkd,CAAC,aAAAhB,OAAA,CAAAmB,QAAkC,CAAC;IAjOxf3C,EAAE,CAAA4C,UAAA,YAAF5C,EAAE,CAAA6C,eAAA,KAAAjC,GAAA,EAAAY,OAAA,CAAAsB,WAAA,OAiO4a,CAAC;IAjO/a9C,EAAE,CAAA+C,SAAA,CAiOuyB,CAAC;IAjO1yB/C,EAAE,CAAA0C,WAAA,WAAAlB,OAAA,CAAAgB,MAiOuyB,CAAC,aAAAhB,OAAA,CAAAmB,QAAkC,CAAC;IAjO70B3C,EAAE,CAAAgD,WAAA,kBAAAxB,OAAA,CAAAyB,EAAA,GAAAzB,OAAA,CAAAyB,EAAA,0BAAAzB,OAAA,CAAAgB,MAAA,QAAAhB,OAAA,CAAAyB,EAAA,GAAAzB,OAAA,CAAAyB,EAAA;IAAFjD,EAAE,CAAA+C,SAAA,CAiOk6B,CAAC;IAjOr6B/C,EAAE,CAAA4C,UAAA,iBAAApB,OAAA,CAAA0B,UAiOk6B,CAAC;IAjOr6BlD,EAAE,CAAA+C,SAAA,CAiOq7B,CAAC;IAjOx7B/C,EAAE,CAAAmD,iBAAA,CAAA3B,OAAA,CAAA4B,OAiOq7B,CAAC;IAjOx7BpD,EAAE,CAAA+C,SAAA,CAiOg+B,CAAC;IAjOn+B/C,EAAE,CAAA4C,UAAA,SAAApB,OAAA,CAAA6B,SAiOg+B,CAAC;EAAA;AAAA;AAhP3kC,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA,IAAIC,YAAYA,CAACC,WAAW,EAAE;IAC1B,IAAI,CAACC,aAAa,GAAGD,WAAW;IAChC,IAAIA,WAAW,EAAE;MACb,IAAI,CAACF,OAAO,CAACI,kBAAkB,CAACF,WAAW,CAAC;IAChD;EACJ;EACA;EACA,IAAID,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACE,aAAa;EAC7B;AACJ;AACAL,qBAAqB,CAACO,IAAI,YAAAC,8BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAwFT,qBAAqB,EAA/BtD,EAAE,CAAAgE,iBAAA,CAA+ChE,EAAE,CAACiE,gBAAgB;AAAA,CAA4C;AACxNX,qBAAqB,CAACY,IAAI,kBAD8ElE,EAAE,CAAAmE,iBAAA;EAAAC,IAAA,EACJd,qBAAqB;EAAAe,SAAA;EAAAC,MAAA;IAAAb,YAAA;EAAA;AAAA,EAAuF;AAClN;EAAA,QAAAc,SAAA,oBAAAA,SAAA,KAFwGvE,EAAE,CAAAwE,iBAAA,CAEflB,qBAAqB,EAAc,CAAC;IACnHc,IAAI,EAAEnE,SAAS;IACfwE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEN,IAAI,EAAEpE,EAAE,CAACiE;IAAiB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAER,YAAY,EAAE,CAAC;MACtGW,IAAI,EAAElE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyE,YAAY,CAAC;EACfpB,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACa,IAAI,GAAG,MAAM;IAClB;IACA,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzB;IACA,IAAI,CAACC,SAAS,GAAG,MAAM;EAC3B;AACJ;AACAF,YAAY,CAACd,IAAI,YAAAiB,qBAAAf,iBAAA;EAAA,YAAAA,iBAAA,IAAwFY,YAAY;AAAA,CAAoD;AACzKA,YAAY,CAACI,KAAK,kBAtBsF/E,EAAE,CAAAgF,kBAAA;EAAAC,KAAA,EAsBGN,YAAY;EAAAO,OAAA,EAAZP,YAAY,CAAAd,IAAA;EAAAsB,UAAA,EAAc;AAAM,EAAG;AAChJ;EAAA,QAAAZ,SAAA,oBAAAA,SAAA,KAvBwGvE,EAAE,CAAAwE,iBAAA,CAuBfG,YAAY,EAAc,CAAC;IAC1GP,IAAI,EAAEjE,UAAU;IAChBsE,IAAI,EAAE,CAAC;MACCU,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA,MAAMC,eAAe,CAAC;EAClB7B,WAAWA,CAAC8B,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAE;IACtC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB;IACA,IAAI,CAACb,SAAS,GAAG,MAAM;IACvB,IAAI,CAACc,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,KAAK,GAAG,MAAM;IACnB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1BC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEZ,MAAM,CAAC;EAC/B;EACA;EACA,IAAIa,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACN,SAAS;EACzB;EACA,IAAIM,QAAQA,CAACC,KAAK,EAAE;IAChB,IAAI,CAACP,SAAS,GAAGO,KAAK;IACtB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACA;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACR,UAAU;EAC1B;EACA,IAAIQ,SAASA,CAACF,KAAK,EAAE;IACjB,IAAI,CAACN,UAAU,GAAGM,KAAK;IACvB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACA;EACA,IAAIhC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC0B,KAAK;EACrB;EACA,IAAI1B,IAAIA,CAAC+B,KAAK,EAAE;IACZ,IAAI,CAACL,KAAK,GAAGK,KAAK;IAClB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACA,IAAIxB,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACmB,cAAc;EAC9B;EACA,IAAInB,aAAaA,CAACuB,KAAK,EAAE;IACrB,IAAI,CAACJ,cAAc,GAAGI,KAAK;EAC/B;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACX,WAAW,GAAG,IAAI;EAC3B;EACAY,MAAMA,CAACC,GAAG,EAAE;IACR,IAAI,CAACf,IAAI,CAACgB,IAAI,CAACD,GAAG,CAAC;IACnBA,GAAG,CAAChE,MAAM,GAAG,IAAI,CAACiD,IAAI,CAACiB,MAAM,KAAK,CAAC,IAAI,CAACF,GAAG,CAAChE,MAAM;EACtD;EACAV,SAASA,CAAC0E,GAAG,EAAEG,OAAO,GAAG;IAAEC,QAAQ,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAK,CAAC,EAAE;IACrD,MAAMxE,KAAK,GAAG,IAAI,CAACoD,IAAI,CAACqB,OAAO,CAACN,GAAG,CAAC;IACpC,IAAInE,KAAK,KAAK,CAAC,CAAC,IAAI,IAAI,CAACsD,WAAW,EAAE;MAClC;IACJ;IACA;IACA,IAAIgB,OAAO,CAACC,QAAQ,IAAIJ,GAAG,CAAChE,MAAM,IAAI,IAAI,CAACuE,gBAAgB,CAAC1E,KAAK,CAAC,EAAE;MAChE,MAAM2E,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC5E,KAAK,CAAC;MACrD,IAAI,CAACoD,IAAI,CAACuB,cAAc,CAAC,CAACxE,MAAM,GAAG,IAAI;IAC3C;IACA,IAAImE,OAAO,CAACE,IAAI,EAAE;MACdL,GAAG,CAACU,OAAO,CAACL,IAAI,CAACL,GAAG,CAAC;IACzB;IACA,IAAI,CAACf,IAAI,CAAC0B,MAAM,CAAC9E,KAAK,EAAE,CAAC,CAAC;IAC1B,IAAImE,GAAG,CAACjB,UAAU,CAAC6B,aAAa,CAACC,UAAU,EAAE;MACzC,IAAI,CAAC/B,QAAQ,CAACgC,WAAW,CAACd,GAAG,CAACjB,UAAU,CAAC6B,aAAa,CAACC,UAAU,EAAEb,GAAG,CAACjB,UAAU,CAAC6B,aAAa,CAAC;IACpG;EACJ;EACA9E,aAAaA,CAACiF,KAAK,EAAElF,KAAK,EAAE;IACxB,IAAI,CAAC,IAAI,CAACuC,aAAa,EAAE;MACrB;IACJ;IACA,MAAM4C,IAAI,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnC,UAAU,CAAC6B,aAAa,CAACO,gBAAgB,CAAC,WAAW,CAAC,CAAC;IACpF;IACA,IAAIJ,KAAK,CAACK,OAAO,KAAK,EAAE,IAAIL,KAAK,CAACM,GAAG,KAAK,OAAO,IAAIN,KAAK,CAACK,OAAO,KAAK,EAAE,IAAIL,KAAK,CAACM,GAAG,KAAK,OAAO,EAAE;MAChGN,KAAK,CAAC3F,cAAc,CAAC,CAAC;MACtB,MAAMkG,UAAU,GAAGN,IAAI,CAAEnF,KAAK,GAAImF,IAAI,CAACd,MAAM,CAAC;MAC9CoB,UAAU,CAACC,KAAK,CAAC,CAAC;MAClB;IACJ;IACA,IAAIR,KAAK,CAACK,OAAO,KAAK,EAAE,IAAIL,KAAK,CAACM,GAAG,KAAK,YAAY,EAAE;MACpD,IAAIG,OAAO;MACX,IAAIC,KAAK,GAAG,CAAC;MACb,GAAG;QACCD,OAAO,GAAGR,IAAI,CAAC,CAACnF,KAAK,GAAG4F,KAAK,IAAIT,IAAI,CAACd,MAAM,CAAC;QAC7CuB,KAAK,EAAE;MACX,CAAC,QAAQD,OAAO,CAACE,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC;MAC/CH,OAAO,CAACI,KAAK,CAAC,CAAC;MACf;IACJ;IACA,IAAIb,KAAK,CAACK,OAAO,KAAK,EAAE,IAAIL,KAAK,CAACM,GAAG,KAAK,WAAW,EAAE;MACnD,IAAIQ,WAAW;MACf,IAAIJ,KAAK,GAAG,CAAC;MACb,IAAIK,CAAC,GAAGjG,KAAK;MACb,GAAG;QACC,IAAKiG,CAAC,GAAGL,KAAK,GAAI,CAAC,EAAE;UACjBK,CAAC,GAAGd,IAAI,CAACd,MAAM,GAAG,CAAC;UACnB2B,WAAW,GAAGb,IAAI,CAACc,CAAC,CAAC;UACrBL,KAAK,GAAG,CAAC;QACb,CAAC,MACI;UACDI,WAAW,GAAGb,IAAI,CAACc,CAAC,GAAGL,KAAK,CAAC;QACjC;QACAA,KAAK,EAAE;MACX,CAAC,QAAQI,WAAW,CAACH,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC;MACnDE,WAAW,CAACD,KAAK,CAAC,CAAC;MACnB;IACJ;IACA,IAAIb,KAAK,CAACK,OAAO,KAAK,EAAE,IAAIL,KAAK,CAACM,GAAG,KAAK,MAAM,EAAE;MAC9CN,KAAK,CAAC3F,cAAc,CAAC,CAAC;MACtB,IAAI2G,QAAQ;MACZ,IAAIN,KAAK,GAAG,CAAC;MACb,GAAG;QACCM,QAAQ,GAAGf,IAAI,CAACS,KAAK,GAAGT,IAAI,CAACd,MAAM,CAAC;QACpCuB,KAAK,EAAE;MACX,CAAC,QAAQM,QAAQ,CAACL,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC;MAChDI,QAAQ,CAACH,KAAK,CAAC,CAAC;MAChB;IACJ;IACA,IAAIb,KAAK,CAACK,OAAO,KAAK,EAAE,IAAIL,KAAK,CAACM,GAAG,KAAK,KAAK,EAAE;MAC7CN,KAAK,CAAC3F,cAAc,CAAC,CAAC;MACtB,IAAI4G,OAAO;MACX,IAAIP,KAAK,GAAG,CAAC;MACb,IAAIK,CAAC,GAAGjG,KAAK;MACb,GAAG;QACC,IAAKiG,CAAC,GAAGL,KAAK,GAAI,CAAC,EAAE;UACjBK,CAAC,GAAGd,IAAI,CAACd,MAAM,GAAG,CAAC;UACnB8B,OAAO,GAAGhB,IAAI,CAACc,CAAC,CAAC;UACjBL,KAAK,GAAG,CAAC;QACb,CAAC,MACI;UACDO,OAAO,GAAGhB,IAAI,CAACc,CAAC,GAAGL,KAAK,CAAC;QAC7B;QACAA,KAAK,EAAE;MACX,CAAC,QAAQO,OAAO,CAACN,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC;MAC/CK,OAAO,CAACJ,KAAK,CAAC,CAAC;MACf;IACJ;IACA,IAAIb,KAAK,CAACK,OAAO,KAAK,EAAE,IAAIL,KAAK,CAACM,GAAG,KAAK,QAAQ,EAAE;MAChD,IAAI,IAAI,CAACpC,IAAI,CAACpD,KAAK,CAAC,CAACgB,SAAS,EAAE;QAC5B,IAAI,CAACvB,SAAS,CAAC,IAAI,CAAC2D,IAAI,CAACpD,KAAK,CAAC,CAAC;QAChC,IAAImF,IAAI,CAACnF,KAAK,GAAG,CAAC,CAAC,EAAE;UACjBmF,IAAI,CAAC,CAACnF,KAAK,GAAG,CAAC,IAAImF,IAAI,CAACd,MAAM,CAAC,CAAC0B,KAAK,CAAC,CAAC;UACvC;QACJ;QACA,IAAIZ,IAAI,CAACA,IAAI,CAACd,MAAM,GAAG,CAAC,CAAC,EAAE;UACvBc,IAAI,CAAC,CAAC,CAAC,CAACY,KAAK,CAAC,CAAC;QACnB;MACJ;IACJ;EACJ;EACAnB,kBAAkBA,CAAC5E,KAAK,EAAE;IACtB,MAAMoG,UAAU,GAAG,IAAI,CAAChD,IAAI,CAACiB,MAAM;IACnC,IAAI,CAAC+B,UAAU,EAAE;MACb,OAAO,CAAC,CAAC;IACb;IACA,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAID,UAAU,EAAEC,IAAI,IAAI,CAAC,EAAE;MAC9C,MAAMC,SAAS,GAAGtG,KAAK,GAAGqG,IAAI;MAC9B,MAAME,SAAS,GAAGvG,KAAK,GAAGqG,IAAI;MAC9B,IAAI,IAAI,CAACjD,IAAI,CAACkD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAClD,IAAI,CAACkD,SAAS,CAAC,CAAChG,QAAQ,EAAE;QACxD,OAAOgG,SAAS;MACpB;MACA,IAAI,IAAI,CAAClD,IAAI,CAACmD,SAAS,CAAC,IAAI,CAAC,IAAI,CAACnD,IAAI,CAACmD,SAAS,CAAC,CAACjG,QAAQ,EAAE;QACxD,OAAOiG,SAAS;MACpB;IACJ;IACA,OAAO,CAAC,CAAC;EACb;EACA7B,gBAAgBA,CAAC1E,KAAK,EAAE;IACpB,MAAMoG,UAAU,GAAG,IAAI,CAAChD,IAAI,CAACiB,MAAM;IACnC,IAAI,CAAC+B,UAAU,EAAE;MACb,OAAO,KAAK;IAChB;IACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,UAAU,EAAEH,CAAC,IAAI,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAAC7C,IAAI,CAAC6C,CAAC,CAAC,CAAC3F,QAAQ,IAAI2F,CAAC,KAAKjG,KAAK,EAAE;QACvC,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACA+D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACV,QAAQ,GAAG;MACZ,aAAa,EAAE,IAAI,CAACQ,QAAQ;MAC5B,aAAa,EAAE,IAAI,CAACA,QAAQ;MAC5B,eAAe,EAAE,IAAI,CAACG,SAAS;MAC/B,CAAC,OAAO,IAAI,CAACjC,IAAI,EAAE,GAAG;IAC1B,CAAC;EACL;AACJ;AACAgB,eAAe,CAACvB,IAAI,YAAAgF,wBAAA9E,iBAAA;EAAA,YAAAA,iBAAA,IAAwFqB,eAAe,EAhOnBpF,EAAE,CAAAgE,iBAAA,CAgOmCW,YAAY,GAhOjD3E,EAAE,CAAAgE,iBAAA,CAgO4DhE,EAAE,CAAC8I,SAAS,GAhO1E9I,EAAE,CAAAgE,iBAAA,CAgOqFhE,EAAE,CAAC+I,UAAU;AAAA,CAA4C;AACxP3D,eAAe,CAAC4D,IAAI,kBAjOoFhJ,EAAE,CAAAiJ,iBAAA;EAAA7E,IAAA,EAiOVgB,eAAe;EAAAf,SAAA;EAAA6E,QAAA;EAAAC,YAAA,WAAAC,6BAAArI,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAjOPf,EAAE,CAAA0C,WAAA,kBAAA1B,GAAA,CAAAwE,KAiOI,CAAC;IAAA;EAAA;EAAAlB,MAAA;IAAA4B,QAAA;IAAAG,SAAA;IAAAjC,IAAA;EAAA;EAAAiF,kBAAA,EAAA1I,GAAA;EAAA2I,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAA3I,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAjOPf,EAAE,CAAA2J,eAAA;MAAF3J,EAAE,CAAAmB,cAAA,WAiOuU,CAAC;MAjO1UnB,EAAE,CAAAoB,UAAA,mBAAAwI,6CAAAtI,MAAA;QAAA,OAiOmPA,MAAA,CAAAM,cAAA,CAAsB,CAAC;MAAA,CAAC,CAAC;MAjO9Q5B,EAAE,CAAAyC,UAAA,IAAAR,6BAAA,gBAiO6hB,CAAC;MAjOhiBjC,EAAE,CAAAgC,YAAA,CAiO2lC,CAAC;MAjO9lChC,EAAE,CAAAmB,cAAA,YAiOwnC,CAAC;MAjO3nCnB,EAAE,CAAA6J,YAAA,EAiOqpC,CAAC;MAjOxpC7J,EAAE,CAAAgC,YAAA,CAiO6pC,CAAC;IAAA;IAAA,IAAAjB,EAAA;MAjOhqCf,EAAE,CAAA4C,UAAA,YAAA5B,GAAA,CAAA0E,QAiOkO,CAAC;MAjOrO1F,EAAE,CAAAgD,WAAA,eAAAhC,GAAA,CAAA6D,SAAA;MAAF7E,EAAE,CAAA+C,SAAA,CAiO0W,CAAC;MAjO7W/C,EAAE,CAAA4C,UAAA,YAAA5B,GAAA,CAAAyE,IAiO0W,CAAC;IAAA;EAAA;EAAAqE,YAAA,GAA+6BrJ,EAAE,CAACsJ,OAAO,EAAoFtJ,EAAE,CAACuJ,OAAO,EAAmHvJ,EAAE,CAACwJ,IAAI,EAA6F3G,qBAAqB;EAAA4G,MAAA;AAAA,EAA4D;AACpxD;EAAA,QAAA3F,SAAA,oBAAAA,SAAA,KAlOwGvE,EAAE,CAAAwE,iBAAA,CAkOfY,eAAe,EAAc,CAAC;IAC7GhB,IAAI,EAAEhE,SAAS;IACfqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,QAAQ;MAAE+E,QAAQ,EAAE,u+BAAu+B;MAAES,MAAM,EAAE,CAAC,iEAAiE;IAAE,CAAC;EACjmC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9F,IAAI,EAAEO;IAAa,CAAC,EAAE;MAAEP,IAAI,EAAEpE,EAAE,CAAC8I;IAAU,CAAC,EAAE;MAAE1E,IAAI,EAAEpE,EAAE,CAAC+I;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE7C,QAAQ,EAAE,CAAC;MAC5I9B,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEmG,SAAS,EAAE,CAAC;MACZjC,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEkE,IAAI,EAAE,CAAC;MACPA,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEsF,KAAK,EAAE,CAAC;MACRpB,IAAI,EAAE/D,WAAW;MACjBoE,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0F,YAAY,CAAC;EACf5G,WAAWA,CAAC6G,MAAM,EAAE7E,UAAU,EAAED,QAAQ,EAAE;IACtC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAAC3C,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACU,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACgH,SAAS,GAAG,IAAI/J,YAAY,CAAC,CAAC;IACnC;IACA,IAAI,CAACgK,QAAQ,GAAG,IAAIhK,YAAY,CAAC,CAAC;IAClC;IACA,IAAI,CAAC4G,OAAO,GAAG,IAAI5G,YAAY,CAAC,CAAC;IACjC,IAAI,CAACiK,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACN,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACA,MAAM,CAAC7D,MAAM,CAAC,IAAI,CAAC;EAC5B;EACA;EACA,IAAIzD,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC4H,YAAY;EAC5B;EACA,IAAI5H,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACA,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC6H,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,QAAQ,IAAK;QAC9C,IAAI,CAACvF,QAAQ,CAACwF,WAAW,CAAC,IAAI,CAACvF,UAAU,CAAC6B,aAAa,EAAEyD,QAAQ,CAAC;MACtE,CAAC,CAAC;IACN;IACA,IAAI,CAACH,YAAY,GAAG5H,WAAW,GAAGA,WAAW,CAACiI,IAAI,CAAC,CAAC,GAAG,EAAE;IACzD,IAAI,IAAI,CAACjI,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC6H,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,QAAQ,IAAK;QAC9C,IAAI,CAACvF,QAAQ,CAACiF,QAAQ,CAAC,IAAI,CAAChF,UAAU,CAAC6B,aAAa,EAAEyD,QAAQ,CAAC;MACnE,CAAC,CAAC;IACN;EACJ;EACA;EACA,IAAIrI,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACiI,OAAO;EACvB;EACA,IAAIjI,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,IAAI,CAACiI,OAAO,KAAKjI,MAAM,EAAE;MACzB;IACJ;IACA,IAAK,IAAI,CAACG,QAAQ,IAAIH,MAAM,IAAK,CAACA,MAAM,EAAE;MACtC,IAAI,IAAI,CAACiI,OAAO,IAAI,CAACjI,MAAM,EAAE;QACzB,IAAI,CAAC8H,QAAQ,CAACzD,IAAI,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC4D,OAAO,GAAGjI,MAAM;MACzB;MACA;IACJ;IACA,IAAI,CAACiI,OAAO,GAAGjI,MAAM;IACrB,IAAI,CAAC6H,SAAS,CAACxD,IAAI,CAAC,IAAI,CAAC;IACzB,IAAI,CAACuD,MAAM,CAAC3E,IAAI,CAACmF,OAAO,CAAEpE,GAAG,IAAK;MAC9B,IAAIA,GAAG,KAAK,IAAI,EAAE;QACdA,GAAG,CAAChE,MAAM,GAAG,KAAK;MACtB;IACJ,CAAC,CAAC;EACN;EACA,IAAIwI,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC/H,EAAE,GAAG,GAAG,IAAI,CAACA,EAAE,OAAO,GAAG,EAAE;EAC3C;EACAgI,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC5H,SAAS,GAAG,CAAC,CAAC,IAAI,CAACA,SAAS;EACrC;EACAiD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8D,MAAM,CAACtI,SAAS,CAAC,IAAI,EAAE;MAAE8E,QAAQ,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;EACjE;AACJ;AACAsD,YAAY,CAACtG,IAAI,YAAAqH,qBAAAnH,iBAAA;EAAA,YAAAA,iBAAA,IAAwFoG,YAAY,EAvTbnK,EAAE,CAAAgE,iBAAA,CAuT6BoB,eAAe,GAvT9CpF,EAAE,CAAAgE,iBAAA,CAuTyDhE,EAAE,CAAC+I,UAAU,GAvTxE/I,EAAE,CAAAgE,iBAAA,CAuTmFhE,EAAE,CAAC8I,SAAS;AAAA,CAA4C;AACrPqB,YAAY,CAACjG,IAAI,kBAxTuFlE,EAAE,CAAAmE,iBAAA;EAAAC,IAAA,EAwTb+F,YAAY;EAAA9F,SAAA;EAAA6E,QAAA;EAAAC,YAAA,WAAAgC,0BAAApK,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAxTDf,EAAE,CAAAgD,WAAA,OAAAhC,GAAA,CAAAiC,EAAA,UAAAjC,GAAA,CAAAwJ,IAAA,qBAAAxJ,GAAA,CAAAgK,cAAA;MAAFhL,EAAE,CAAA0C,WAAA,WAAA1B,GAAA,CAAAwB,MAwTF,CAAC,aAAAxB,GAAA,CAAAuJ,QAAD,CAAC;IAAA;EAAA;EAAAjG,MAAA;IAAAlB,OAAA;IAAAH,EAAA;IAAAN,QAAA;IAAAU,SAAA;IAAAP,WAAA;IAAAN,MAAA;EAAA;EAAA4I,OAAA;IAAAf,SAAA;IAAAC,QAAA;IAAApD,OAAA;EAAA;EAAAmE,QAAA;AAAA,EAA4c;AACrjB;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KAzTwGvE,EAAE,CAAAwE,iBAAA,CAyTf2F,YAAY,EAAc,CAAC;IAC1G/F,IAAI,EAAEnE,SAAS;IACfwE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAE2G,QAAQ,EAAE;IAAM,CAAC;EACtD,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjH,IAAI,EAAEgB;IAAgB,CAAC,EAAE;MAAEhB,IAAI,EAAEpE,EAAE,CAAC+I;IAAW,CAAC,EAAE;MAAE3E,IAAI,EAAEpE,EAAE,CAAC8I;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE1F,OAAO,EAAE,CAAC;MAC9IgB,IAAI,EAAElE;IACV,CAAC,CAAC;IAAE+C,EAAE,EAAE,CAAC;MACLmB,IAAI,EAAE/D,WAAW;MACjBoE,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,EAAE;MACCL,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEyC,QAAQ,EAAE,CAAC;MACXyB,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEmD,SAAS,EAAE,CAAC;MACZe,IAAI,EAAElE;IACV,CAAC,CAAC;IAAE4C,WAAW,EAAE,CAAC;MACdsB,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEsC,MAAM,EAAE,CAAC;MACT4B,IAAI,EAAE/D,WAAW;MACjBoE,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,EAAE;MACCL,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEmK,SAAS,EAAE,CAAC;MACZjG,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAE+J,QAAQ,EAAE,CAAC;MACXlG,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAE2G,OAAO,EAAE,CAAC;MACV9C,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEgK,QAAQ,EAAE,CAAC;MACXnG,IAAI,EAAE/D,WAAW;MACjBoE,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAE+F,IAAI,EAAE,CAAC;MACPpG,IAAI,EAAE/D,WAAW;MACjBoE,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEuG,cAAc,EAAE,CAAC;MACjB5G,IAAI,EAAE/D,WAAW;MACjBoE,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM6G,mBAAmB,CAAC;EACtB;EACA/H,WAAWA,CAACG,WAAW,EAAE8C,GAAG,EAAE;IAC1BA,GAAG,CAACtD,UAAU,GAAGQ,WAAW;EAChC;AACJ;AACA4H,mBAAmB,CAACzH,IAAI,YAAA0H,4BAAAxH,iBAAA;EAAA,YAAAA,iBAAA,IAAwFuH,mBAAmB,EAtW3BtL,EAAE,CAAAgE,iBAAA,CAsW2ChE,EAAE,CAACwL,WAAW,GAtW3DxL,EAAE,CAAAgE,iBAAA,CAsWsEmG,YAAY;AAAA,CAA4C;AACxOmB,mBAAmB,CAACpH,IAAI,kBAvWgFlE,EAAE,CAAAmE,iBAAA;EAAAC,IAAA,EAuWNkH,mBAAmB;EAAAjH,SAAA;AAAA,EAA2C;AAClK;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAxWwGvE,EAAE,CAAAwE,iBAAA,CAwWf8G,mBAAmB,EAAc,CAAC;IACjHlH,IAAI,EAAEnE,SAAS;IACfwE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAe,CAAC;EACvC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEN,IAAI,EAAEpE,EAAE,CAACwL;IAAY,CAAC,EAAE;MAAEpH,IAAI,EAAE+F;IAAa,CAAC,CAAC;EAAE,CAAC;AAAA;AAEtG,MAAMsB,UAAU,CAAC;EACb,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,UAAU;MACpBG,SAAS,EAAE;IACf,CAAC;EACL;AACJ;AACAH,UAAU,CAAC5H,IAAI,YAAAgI,mBAAA9H,iBAAA;EAAA,YAAAA,iBAAA,IAAwF0H,UAAU;AAAA,CAAkD;AACnKA,UAAU,CAACK,IAAI,kBAtXyF9L,EAAE,CAAA+L,gBAAA;EAAA3H,IAAA,EAsXFqH;AAAU,EAMjF;AACjCA,UAAU,CAACO,IAAI,kBA7XyFhM,EAAE,CAAAiM,gBAAA;EAAAC,OAAA,GA6XoBxL,YAAY;AAAA,EAAI;AAC9I;EAAA,QAAA6D,SAAA,oBAAAA,SAAA,KA9XwGvE,EAAE,CAAAwE,iBAAA,CA8XfiH,UAAU,EAAc,CAAC;IACxGrH,IAAI,EAAE5D,QAAQ;IACdiE,IAAI,EAAE,CAAC;MACCyH,OAAO,EAAE,CAACxL,YAAY,CAAC;MACvByL,YAAY,EAAE,CACV7I,qBAAqB,EACrB6G,YAAY,EACZ/E,eAAe,EACfkG,mBAAmB,CACtB;MACDc,OAAO,EAAE,CACLjC,YAAY,EACZ/E,eAAe,EACfkG,mBAAmB,EACnBhI,qBAAqB;IAE7B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,qBAAqB,EAAE6G,YAAY,EAAEmB,mBAAmB,EAAEG,UAAU,EAAErG,eAAe,EAAET,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}