{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Portuguese [pt]\n//! author : <PERSON> : https://github.com/jalex79\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var pt = moment.defineLocale('pt', {\n    months: 'janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro'.split('_'),\n    monthsShort: 'jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez'.split('_'),\n    weekdays: 'Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado'.split('_'),\n    weekdaysShort: 'Dom_Seg_Ter_Qua_Qui_Sex_Sáb'.split('_'),\n    weekdaysMin: 'Do_2ª_3ª_4ª_5ª_6ª_Sá'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D [de] MMMM [de] YYYY',\n      LLL: 'D [de] MMMM [de] YYYY HH:mm',\n      LLLL: 'dddd, D [de] MMMM [de] YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Hoje às] LT',\n      nextDay: '[Amanhã às] LT',\n      nextWeek: 'dddd [às] LT',\n      lastDay: '[Ontem às] LT',\n      lastWeek: function () {\n        return this.day() === 0 || this.day() === 6 ? '[Último] dddd [às] LT' // Saturday + Sunday\n        : '[Última] dddd [às] LT'; // Monday - Friday\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'em %s',\n      past: 'há %s',\n      s: 'segundos',\n      ss: '%d segundos',\n      m: 'um minuto',\n      mm: '%d minutos',\n      h: 'uma hora',\n      hh: '%d horas',\n      d: 'um dia',\n      dd: '%d dias',\n      w: 'uma semana',\n      ww: '%d semanas',\n      M: 'um mês',\n      MM: '%d meses',\n      y: 'um ano',\n      yy: '%d anos'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return pt;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "pt", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "day", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Downloads/ci_platfrom_app-develop/ci_platfrom_app-develop/node_modules/moment/locale/pt.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Portuguese [pt]\n//! author : <PERSON> : https://github.com/jalex79\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var pt = moment.defineLocale('pt', {\n        months: 'janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro'.split(\n            '_'\n        ),\n        monthsShort: 'jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez'.split('_'),\n        weekdays:\n            'Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado'.split(\n                '_'\n            ),\n        weekdaysShort: 'Dom_Seg_Ter_Qua_Qui_Sex_Sáb'.split('_'),\n        weekdaysMin: 'Do_2ª_3ª_4ª_5ª_6ª_Sá'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D [de] MMMM [de] YYYY',\n            LLL: 'D [de] MMMM [de] YYYY HH:mm',\n            LLLL: 'dddd, D [de] MMMM [de] YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Hoje às] LT',\n            nextDay: '[Amanhã às] LT',\n            nextWeek: 'dddd [às] LT',\n            lastDay: '[Ontem às] LT',\n            lastWeek: function () {\n                return this.day() === 0 || this.day() === 6\n                    ? '[Último] dddd [às] LT' // Saturday + Sunday\n                    : '[Última] dddd [às] LT'; // Monday - Friday\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'em %s',\n            past: 'há %s',\n            s: 'segundos',\n            ss: '%d segundos',\n            m: 'um minuto',\n            mm: '%d minutos',\n            h: 'uma hora',\n            hh: '%d horas',\n            d: 'um dia',\n            dd: '%d dias',\n            w: 'uma semana',\n            ww: '%d semanas',\n            M: 'um mês',\n            MM: '%d meses',\n            y: 'um ano',\n            yy: '%d anos',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}º/,\n        ordinal: '%dº',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return pt;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,0FAA0F,CAACC,KAAK,CACpG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EACJ,gFAAgF,CAACF,KAAK,CAClF,GACJ,CAAC;IACLG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,uBAAuB;MAC3BC,GAAG,EAAE,6BAA6B;MAClCC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OAAO,IAAI,CAACC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACA,GAAG,CAAC,CAAC,KAAK,CAAC,GACrC,uBAAuB,CAAC;QAAA,EACxB,uBAAuB,CAAC,CAAC;MACnC,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,OAAO;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO7C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}