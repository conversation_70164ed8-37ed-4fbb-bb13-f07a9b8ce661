{"ast": null, "code": "import offsetParent from './offset-parent';\nconst isBodyOffset = element => offsetParent(element) === element.ownerDocument.body;\nexport default isBodyOffset;", "map": {"version": 3, "names": ["offsetParent", "isBodyOffset", "element", "ownerDocument", "body"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/@progress/kendo-popup-common/dist/es2015/is-body-offset.js"], "sourcesContent": ["import offsetParent from './offset-parent';\n\nconst isBodyOffset = (element) => (offsetParent(element) === element.ownerDocument.body);\n\nexport default isBodyOffset;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,iBAAiB;AAE1C,MAAMC,YAAY,GAAIC,OAAO,IAAMF,YAAY,CAACE,OAAO,CAAC,KAAKA,OAAO,CAACC,aAAa,CAACC,IAAK;AAExF,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}