{"ast": null, "code": "import { concat } from '../observable/concat';\nimport { take } from './take';\nimport { ignoreElements } from './ignoreElements';\nimport { mapTo } from './mapTo';\nimport { mergeMap } from './mergeMap';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n  if (subscriptionDelay) {\n    return source => concat(subscriptionDelay.pipe(take(1), ignoreElements()), source.pipe(delayWhen(delayDurationSelector)));\n  }\n  return mergeMap((value, index) => delayDurationSelector(value, index).pipe(take(1), mapTo(value)));\n}", "map": {"version": 3, "names": ["concat", "take", "ignoreElements", "mapTo", "mergeMap", "<PERSON><PERSON>hen", "delayDurationSelector", "subscriptionDelay", "source", "pipe", "value", "index"], "sources": ["B:/BE-D2D/BE/Be-sem_7/SIP/Project/ci_platform_app-develop/node_modules/rxjs/dist/esm/internal/operators/delayWhen.js"], "sourcesContent": ["import { concat } from '../observable/concat';\nimport { take } from './take';\nimport { ignoreElements } from './ignoreElements';\nimport { mapTo } from './mapTo';\nimport { mergeMap } from './mergeMap';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n    if (subscriptionDelay) {\n        return (source) => concat(subscriptionDelay.pipe(take(1), ignoreElements()), source.pipe(delayWhen(delayDurationSelector)));\n    }\n    return mergeMap((value, index) => delayDurationSelector(value, index).pipe(take(1), mapTo(value)));\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAO,SAASC,SAASA,CAACC,qBAAqB,EAAEC,iBAAiB,EAAE;EAChE,IAAIA,iBAAiB,EAAE;IACnB,OAAQC,MAAM,IAAKR,MAAM,CAACO,iBAAiB,CAACE,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,EAAEC,cAAc,CAAC,CAAC,CAAC,EAAEM,MAAM,CAACC,IAAI,CAACJ,SAAS,CAACC,qBAAqB,CAAC,CAAC,CAAC;EAC/H;EACA,OAAOF,QAAQ,CAAC,CAACM,KAAK,EAAEC,KAAK,KAAKL,qBAAqB,CAACI,KAAK,EAAEC,KAAK,CAAC,CAACF,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,EAAEE,KAAK,CAACO,KAAK,CAAC,CAAC,CAAC;AACtG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}