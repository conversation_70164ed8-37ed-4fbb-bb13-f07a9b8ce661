.tab-panel{
  padding:10px;
  border-radius: 4px;
  min-height: 600px;
  background-color: white;
}


.member-tabset > .nav-tabs > li.open > a, .member-tabset > .nav-tabs > li > a {
  border:0;
  background: none !important;
  color: #333333;
}
.member-tabset > .nav-tabs > li.open > a, .member-tabset > .nav-tabs > li:hover > a {
  border:0;
  background: none !important;
  color: #333333;
}
.member-tabset > .nav-tabs > li.open > a > i, .member-tabset > .nav-tabs > li:hover > a > i {
  color: #a6a6a6;
}

.member-tabset > .nav-tabs > li.open .dropdown-menu, .member-tabset > .nav-tabs > li:hover .dropdown-menu {
  margin-top: 0px;
}

.member-tabset > .nav-tabs > li.active{
  border-bottom: 3px solid black;
  position: relative;
}
.member-tabset > .nav-tabs > li.active > a{
  border: 0 !important;
  color: #333333;
}
.member-tabset > .nav-tabs > li.active > a > i {
  color: #404040;
}

.member-tabset > .tab-content{
  margin-top:-3px;
  background-color: #fff;
  border: 0;
  border-top: 1px solid #eee;
  padding:15px 0;
}
